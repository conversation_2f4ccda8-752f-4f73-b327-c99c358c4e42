{"name": "pocket-next-extension", "version": "1.0.0", "description": "Browser extension for Pocket-next", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config=jest.e2e.config.js", "lint": "eslint src/**/*.js", "build": "npm run lint && npm test"}, "devDependencies": {"@types/chrome": "^0.0.253", "@types/jest": "^29.5.0", "eslint": "^8.50.0", "express": "^4.18.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-puppeteer": "^9.0.0", "node-fetch": "^2.7.0", "puppeteer": "^21.0.0", "ws": "^8.18.2"}, "jest": {"testEnvironment": "jsdom", "testTimeout": 10000, "collectCoverageFrom": ["src/**/*.js", "public/**/*.js", "!**/node_modules/**"], "coverageThreshold": {"global": {"branches": 50, "functions": 50, "lines": 50, "statements": 50}}}}