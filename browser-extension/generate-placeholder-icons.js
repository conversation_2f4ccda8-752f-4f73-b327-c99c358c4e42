/**
 * Generate placeholder PNG icons for the browser extension
 */

const fs = require('fs');
const path = require('path');

// Create a simple canvas-based icon generator
function createIcon(size) {
  // Create a simple colored square as a placeholder
  const { createCanvas } = require('canvas');
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // Background gradient
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#4F46E5'); // Indigo
  gradient.addColorStop(1, '#7C3AED'); // Purple
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, size, size);
  
  // Add a bookmark icon shape
  ctx.fillStyle = 'white';
  const margin = size * 0.2;
  const width = size - (margin * 2);
  const height = size - (margin * 2);
  
  // Simple bookmark shape
  ctx.beginPath();
  ctx.moveTo(margin, margin);
  ctx.lineTo(margin + width, margin);
  ctx.lineTo(margin + width, margin + height * 0.85);
  ctx.lineTo(margin + width / 2, margin + height * 0.7);
  ctx.lineTo(margin, margin + height * 0.85);
  ctx.closePath();
  ctx.fill();
  
  return canvas.toBuffer('image/png');
}

// Generate icons in different sizes
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
  try {
    const iconBuffer = createIcon(size);
    const outputPath = path.join(__dirname, 'public', `icon-${size}.png`);
    fs.writeFileSync(outputPath, iconBuffer);
    console.log(`Generated icon-${size}.png`);
  } catch (error) {
    console.error(`Failed to generate icon-${size}.png:`, error);
  }
});