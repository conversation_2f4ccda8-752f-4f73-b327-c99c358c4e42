/**
 * Popup script for Pocket-next browser extension
 */

document.addEventListener('DOMContentLoaded', async () => {
  const saveButton = document.getElementById('saveButton');
  const buttonText = document.getElementById('buttonText');
  const twitterButton = document.getElementById('twitterButton');
  const twitterButtonText = document.getElementById('twitterButtonText');
  const twitterNotice = document.getElementById('twitterNotice');
  const recentList = document.getElementById('recentList');
  const settingsLink = document.getElementById('settingsLink');
  
  // Check current page
  checkCurrentPage();
  
  // Load recent articles
  loadRecentArticles();
  
  // Save button click
  saveButton.addEventListener('click', async () => {
    console.log('Save button clicked');
    // Disable button
    saveButton.disabled = true;
    buttonText.innerHTML = '<span class="spinner"></span> Saving...';
    
    try {
      // Send save command to background
      console.log('Sending save-page command to background');
      const response = await chrome.runtime.sendMessage({ command: 'save-page' });
      console.log('Background response:', response);
      
      // Success
      buttonText.textContent = '✓ Saved!';
      saveButton.style.background = '#4CAF50';
      
      // Reload recent articles
      setTimeout(() => {
        loadRecentArticles();
      }, 500);
      
      // Close popup after short delay
      setTimeout(() => {
        window.close();
      }, 1000);
      
    } catch (error) {
      console.error('Save failed:', error);
      // Error
      buttonText.textContent = '✗ Failed';
      saveButton.style.background = '#F44336';
      
      setTimeout(() => {
        saveButton.disabled = false;
        buttonText.textContent = 'Save Page';
        saveButton.style.background = '#4CAF50';
      }, 2000);
    }
  });
  
  // Twitter button click
  twitterButton.addEventListener('click', async () => {
    // Check if we're on the bookmarks page
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    
    if (!tab.url?.includes('/bookmarks')) {
      // Navigate to bookmarks
      twitterNotice.style.display = 'block';
      chrome.tabs.update(tab.id, { url: 'https://twitter.com/i/bookmarks' });
      window.close();
      return;
    }
    
    // Save bookmarks
    twitterButton.disabled = true;
    twitterButtonText.innerHTML = '<span class="spinner"></span> Capturing...';
    
    try {
      // Send save command to background
      console.log('Sending save-twitter-bookmarks command to background');
      const response = await chrome.runtime.sendMessage({ command: 'save-twitter-bookmarks' });
      console.log('Twitter bookmarks response:', response);
      
      // Success
      twitterButtonText.textContent = '✓ Saved!';
      twitterButton.style.background = '#4CAF50';
      
      // Reload recent articles
      setTimeout(() => {
        loadRecentArticles();
      }, 500);
      
      // Close popup after short delay
      setTimeout(() => {
        window.close();
      }, 1500);
      
    } catch (error) {
      console.error('Twitter bookmarks save failed:', error);
      // Error
      twitterButtonText.textContent = '✗ Failed';
      twitterButton.style.background = '#F44336';
      
      setTimeout(() => {
        twitterButton.disabled = false;
        twitterButtonText.textContent = 'Save Twitter Bookmarks';
        twitterButton.style.background = '#1DA1F2';
      }, 2000);
    }
  });
  
  // Settings link
  settingsLink.addEventListener('click', (e) => {
    e.preventDefault();
    console.log('Settings clicked');
    // Open extension management page since we don't have an options page yet
    chrome.tabs.create({ url: 'chrome://extensions/?id=' + chrome.runtime.id });
  });
  
  // Check if we're on Twitter
  async function checkCurrentPage() {
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    
    if (tab.url && (tab.url.includes('twitter.com') || tab.url.includes('x.com'))) {
      twitterButton.style.display = 'flex';
      
      if (tab.url.includes('/bookmarks')) {
        twitterButtonText.textContent = 'Save All Bookmarks';
        twitterNotice.style.display = 'none';
      } else {
        twitterNotice.style.display = 'block';
      }
    }
  }
  
  // Load recent articles
  async function loadRecentArticles() {
    const { articles = [] } = await chrome.storage.local.get('articles');
    
    if (articles.length === 0) {
      recentList.innerHTML = `
        <div class="recent-item">
          <div class="recent-item-title">No saved articles yet</div>
          <div class="recent-item-url">Click the button above to save your first article</div>
        </div>
      `;
      return;
    }
    
    // Show last 3 articles
    const recentHtml = articles.slice(0, 3).map(article => {
      const typeLabel = article.metadata?.type || 'article';
      return `
        <div class="recent-item" data-url="${article.url}">
          <div class="recent-item-title">${escapeHtml(article.title)}</div>
          <div class="recent-item-url">${escapeHtml(new URL(article.url).hostname)}</div>
          <span class="recent-item-type">${typeLabel}</span>
        </div>
      `;
    }).join('');
    
    recentList.innerHTML = recentHtml;
    
    // Add click handlers
    recentList.querySelectorAll('.recent-item').forEach(item => {
      item.addEventListener('click', () => {
        const url = item.dataset.url;
        if (url) {
          chrome.tabs.create({ url });
        }
      });
    });
  }
  
  // Escape HTML to prevent XSS
  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
});

