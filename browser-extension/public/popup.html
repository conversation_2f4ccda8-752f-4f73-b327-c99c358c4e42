<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 320px;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    
    .header {
      background: #2196F3;
      color: white;
      padding: 16px;
      text-align: center;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
    
    .content {
      padding: 16px;
    }
    
    .save-button, .twitter-button {
      width: 100%;
      padding: 12px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: background 0.2s;
      margin-bottom: 8px;
    }
    
    .save-button:hover {
      background: #45a049;
    }
    
    .save-button:active {
      background: #3d8b40;
    }
    
    .twitter-button {
      background: #1DA1F2;
    }
    
    .twitter-button:hover {
      background: #1991db;
    }
    
    .twitter-button:active {
      background: #1681c4;
    }
    
    .twitter-button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    
    .shortcut {
      margin-top: 16px;
      padding: 12px;
      background: white;
      border-radius: 8px;
      text-align: center;
      color: #666;
      font-size: 14px;
    }
    
    .shortcut kbd {
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 13px;
      border: 1px solid #ddd;
    }
    
    .recent {
      margin-top: 16px;
    }
    
    .recent h2 {
      font-size: 14px;
      color: #666;
      margin: 0 0 8px 0;
      font-weight: 500;
    }
    
    .recent-item {
      background: white;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: background 0.2s;
    }
    
    .recent-item:hover {
      background: #f8f8f8;
    }
    
    .recent-item-title {
      font-size: 14px;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 4px;
    }
    
    .recent-item-url {
      font-size: 12px;
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .recent-item-type {
      display: inline-block;
      font-size: 10px;
      color: #666;
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 4px;
      margin-top: 4px;
    }
    
    .settings-link {
      display: block;
      text-align: center;
      color: #2196F3;
      text-decoration: none;
      font-size: 14px;
      margin-top: 16px;
      padding: 8px;
    }
    
    .settings-link:hover {
      text-decoration: underline;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #ffffff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 0.8s linear infinite;
    }
    
    .twitter-notice {
      font-size: 12px;
      color: #666;
      text-align: center;
      margin-top: 8px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Pocket-next</h1>
  </div>
  
  <div class="content">
    <button class="save-button" id="saveButton">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
        <polyline points="17 21 17 13 7 13 7 21"/>
        <polyline points="7 3 7 8 15 8"/>
      </svg>
      <span id="buttonText">Save Page</span>
    </button>
    
    <button class="twitter-button" id="twitterButton" style="display: none;">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
      </svg>
      <span id="twitterButtonText">Save Twitter Bookmarks</span>
    </button>
    
    <div class="twitter-notice" id="twitterNotice" style="display: none;">
      Navigate to your bookmarks page to save them
    </div>
    
    <div class="shortcut">
      Press <kbd>⌘</kbd> + <kbd>⇧</kbd> + <kbd>S</kbd> to save
    </div>
    
    <div class="recent">
      <h2>Recently Saved</h2>
      <div id="recentList">
        <div class="recent-item">
          <div class="recent-item-title">Loading...</div>
        </div>
      </div>
    </div>
    
    <a href="#" class="settings-link" id="settingsLink">Settings</a>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>