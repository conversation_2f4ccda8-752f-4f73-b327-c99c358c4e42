# Browser Extension Troubleshooting Guide

## Extension Not Working - Checklist

### 1. Reload the Extension
After making any code changes:
1. Open Chrome and go to `chrome://extensions/`
2. Find "Pocket-next" extension
3. Click the refresh icon (🔄) to reload
4. Or toggle the extension off and on

### 2. Check Console for Errors
To see debug output:

**Popup Console:**
1. Right-click on the extension icon
2. Select "Inspect popup"
3. Check the Console tab for errors

**Background Service Worker Console:**
1. Go to `chrome://extensions/`
2. Find "Pocket-next" and click "service worker" link
3. Check the Console tab for errors

**Content Script Console:**
1. Right-click on any webpage
2. Select "Inspect"
3. Check the Console tab for messages starting with `[Extension]`

### 3. Verify Parse Server is Running

The extension requires the parse server to be running:

```bash
cd parse-server
python main.py
```

Test if it's working:
```bash
python test-server.py
```

### 4. Check Environment Setup

Make sure you have:
1. Created `.env` file in parse-server directory
2. Added your OpenAI API key: `OPENAI_API_KEY=your-key-here`

### 5. Native Messaging Setup (Optional)

For direct communication with macOS app:
```bash
cd scripts
./setup-native-messaging.sh
```

## Common Issues

### "Save Page" button does nothing
1. Check if parse server is running
2. Look for errors in popup console
3. Check background service worker console

### "Failed to save" error
1. Verify parse server is accessible at http://localhost:8000
2. Check if OpenAI API key is set correctly
3. Look for rate limiting errors

### Settings link doesn't work
Currently opens extension management page. Full settings page coming soon.

### No feedback when saving
1. Check if content script is injected (look for visual indicator)
2. Verify background script is receiving messages
3. Check parse server logs for errors

## Debug Mode

To enable detailed logging:
1. Open popup console
2. Open background service worker console
3. All operations will log detailed information

## Test Commands

From background service worker console:
```javascript
// Test native messaging
chrome.runtime.sendNativeMessage('com.pocketnext.nativemessaging', {type: 'ping'}, r => console.log(r));

// Test save current tab
chrome.runtime.sendMessage({command: 'save-page'}, r => console.log(r));

// Check stored articles
chrome.storage.local.get('articles', r => console.log(r));
```