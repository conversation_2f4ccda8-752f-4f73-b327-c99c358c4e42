#!/bin/bash

# Generate PNG icons from SVG
# Requires ImageMagick or similar tool

echo "Generating extension icons..."

# Check if we have a tool to convert SVG to PNG
if command -v magick &> /dev/null; then
    echo "Using ImageMagick v7..."
    magick -background none -resize 16x16 public/icon.svg public/icon-16.png
    magick -background none -resize 32x32 public/icon.svg public/icon-32.png
    magick -background none -resize 48x48 public/icon.svg public/icon-48.png
    magick -background none -resize 128x128 public/icon.svg public/icon-128.png
elif command -v convert &> /dev/null; then
    echo "Using ImageMagick (legacy)..."
    convert -background none -resize 16x16 public/icon.svg public/icon-16.png
    convert -background none -resize 32x32 public/icon.svg public/icon-32.png
    convert -background none -resize 48x48 public/icon.svg public/icon-48.png
    convert -background none -resize 128x128 public/icon.svg public/icon-128.png
elif command -v rsvg-convert &> /dev/null; then
    echo "Using rsvg-convert..."
    rsvg-convert -w 16 -h 16 public/icon.svg > public/icon-16.png
    rsvg-convert -w 32 -h 32 public/icon.svg > public/icon-32.png
    rsvg-convert -w 48 -h 48 public/icon.svg > public/icon-48.png
    rsvg-convert -w 128 -h 128 public/icon.svg > public/icon-128.png
else
    echo "No SVG to PNG converter found. Please install ImageMagick or librsvg."
    echo "On macOS: brew install imagemagick"
    echo "On Ubuntu: sudo apt-get install imagemagick"
    exit 1
fi

echo "Icons generated successfully!"
echo "Generated:"
echo "  - public/icon-16.png"
echo "  - public/icon-32.png"
echo "  - public/icon-48.png"
echo "  - public/icon-128.png"