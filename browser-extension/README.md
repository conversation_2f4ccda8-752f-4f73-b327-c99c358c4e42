# Pocket-next Browser Extension

Universal DOM capture browser extension that saves any web content with one click.

## Features

- **One-Click Save**: Click the extension icon or press ⌘+⇧+S
- **Universal Capture**: Captures full DOM, lets AI parse any website
- **Twitter/X Bookmarks**: Save all your Twitter bookmarks with one click
- **Offline Queue**: Saves work even when offline
- **Visual Feedback**: Clear indicators for save success/failure
- **Recent Articles**: Quick access to recently saved content

## Installation

### Development Mode

1. Open Chrome/Edge and go to `chrome://extensions/`
2. Enable "Developer mode" 
3. Click "Load unpacked"
4. Select the `browser-extension` directory

### Firefox

1. Open Firefox and go to `about:debugging`
2. Click "This Firefox"
3. Click "Load Temporary Add-on"
4. Select the `manifest.json` file

## Usage

### Save Current Page

Three ways to save:
1. Click the extension icon
2. Press ⌘+⇧+S (Mac) or Ctrl+Shift+S (Windows/Linux)
3. Right-click → "Save to Pocket-next"

### Save Twitter/X Bookmarks

1. Navigate to twitter.com/i/bookmarks or x.com/i/bookmarks
2. Click the extension icon
3. Click "Save Twitter Bookmarks" button
4. All visible bookmarks will be captured and parsed

Note: Twitter uses infinite scroll, so scroll down to load more bookmarks before saving if you want to capture everything.

### How It Works

1. **Capture**: Extension captures full DOM content
2. **Send**: Sends to parse server (or queues if offline)
3. **Parse**: GPT-4o mini extracts clean article content
4. **Store**: Saved locally and synced to native app

## Configuration

Click the extension icon → Settings to configure:
- Parse server URL
- Sync preferences
- Keyboard shortcuts

## Architecture

```
Browser Extension
    ↓ (DOM capture)
Parse Server (GPT-4o mini)
    ↓ (Structured data)
Native App (macOS/iOS)
    ↓ (Local storage)
CloudKit Sync
```

## Development

### Project Structure

```
browser-extension/
├── manifest.json       # Extension configuration
├── src/
│   ├── background.js   # Service worker
│   └── content-capture.js # DOM capture
└── public/
    ├── popup.html      # Extension popup
    ├── popup.js        # Popup logic
    └── icons/          # Extension icons
```

### Building

For development, load unpacked as described above.

For production:
```bash
# Create zip for Chrome Web Store
zip -r pocket-next-chrome.zip . -x ".*" -x "__MACOSX"

# For Firefox (requires web-ext)
web-ext build
```

### Testing

1. Load extension in development mode
2. Navigate to any webpage
3. Press ⌘+⇧+S to save
4. Check popup for saved articles

## Privacy

- DOM content is only sent to your configured parse server
- No tracking or analytics
- Local storage for offline access
- User ID generated locally

## Troubleshooting

### Save Failed

1. Check parse server is running
2. Verify server URL in settings
3. Check browser console for errors

### Content Not Capturing

Some pages (like Chrome Web Store) restrict extensions. This is normal.

### Offline Behavior

Articles are queued locally and processed when back online.