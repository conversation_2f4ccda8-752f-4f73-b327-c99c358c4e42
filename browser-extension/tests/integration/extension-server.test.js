/**
 * Integration tests for extension-server communication
 * Tests real interactions without mocking
 */

const express = require('express');
const fetch = require('node-fetch');
const path = require('path');

// Mock Chrome APIs before requiring background.js
global.chrome = {
  commands: {
    onCommand: {
      addListener: jest.fn()
    }
  },
  action: {
    onClicked: {
      addListener: jest.fn()
    },
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn()
  },
  runtime: {
    onMessage: {
      addListener: jest.fn()
    },
    onStartup: {
      addListener: jest.fn()
    },
    onInstalled: {
      addListener: jest.fn()
    }
  },
  tabs: {
    query: jest.fn(),
    sendMessage: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  },
  scripting: {
    executeScript: jest.fn()
  },
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn()
    },
    local: {
      get: jest.fn(),
      set: jest.fn()
    }
  },
  contextMenus: {
    create: jest.fn(),
    onClicked: {
      addListener: jest.fn()
    }
  },
  notifications: {
    create: jest.fn()
  },
  identity: {
    getRedirectURL: jest.fn()
  }
};

// Mock global fetch for integration tests
global.fetch = require('node-fetch');

// Clear module cache and import background.js
jest.resetModules();
const background = require('../../src/background.js');

// Real test server
let testServer;
let serverApp;
let serverPort = 3001;

function createTestServer(port = serverPort) {
  return new Promise((resolve) => {
    serverApp = express();
    serverApp.use(express.json());
    
    // Request tracking
    serverApp.locals.requests = [];
    
    // Middleware to track requests
    serverApp.use((req, res, next) => {
      serverApp.locals.requests.push({
        method: req.method,
        url: req.url,
        body: req.body
      });
      next();
    });
    
    // Real parse endpoint
    serverApp.post('/parse', async (req, res) => {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 50));
      
      res.json({
        success: true,
        data: {
          title: req.body.title || 'Parsed Article',
          content: 'This is the parsed content from the real server',
          summary: 'A summary of the article content',
          tags: ['test', 'integration'],
          author: req.body.metadata?.author || 'Unknown Author',
          publishDate: req.body.metadata?.publishDate || new Date().toISOString(),
          keywords: ['parsed', 'content'],
          reading_time: 5
        }
      });
    });
    
    // Real Twitter bookmarks endpoint
    serverApp.post('/parse-twitter-bookmarks', async (req, res) => {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 100));
      
      res.json({
        success: true,
        data: {
          articles: [
            {
              id: '1',
              title: 'Tweet from @user1',
              content: 'First tweet content from bookmarks',
              url: 'https://twitter.com/user1/status/123'
            },
            {
              id: '2',
              title: 'Tweet from @user2',
              content: 'Second tweet content from bookmarks',
              url: 'https://twitter.com/user2/status/456'
            }
          ],
          total_count: 2,
          source: 'twitter_bookmarks'
        }
      });
    });
    
    testServer = serverApp.listen(port, () => {
      resolve();
    });
  });
}

describe('Extension-Server Integration', () => {
  beforeAll(async () => {
    await createTestServer();
  });
  
  afterAll(() => {
    testServer.close();
  });
  
  beforeEach(() => {
    jest.clearAllMocks();
    serverApp.locals.requests = [];
    
    // Default chrome API mocks
    chrome.storage.sync.get.mockResolvedValue({ 
      parseServerUrl: `http://localhost:${serverPort}` 
    });
    chrome.storage.local.get.mockResolvedValue({ articles: [] });
    chrome.storage.local.set.mockResolvedValue();
  });

  describe('Article Saving', () => {
    test('should save article through real server', async () => {
      // Setup
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://example.com/article'
      }]);
      
      chrome.tabs.sendMessage.mockResolvedValue({
        url: 'https://example.com/article',
        title: 'Real Article Title',
        htmlContent: '<p>Real article content</p>',
        metadata: {
          type: 'article',
          author: 'Real Author'
        }
      });
      
      // Execute save using the exported function
      await background.savePage();
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Verify server was called
      expect(serverApp.locals.requests).toContainEqual(
        expect.objectContaining({
          method: 'POST',
          url: '/parse',
          body: expect.objectContaining({
            url: 'https://example.com/article',
            title: 'Real Article Title'
          })
        })
      );
      
      // Verify article was stored
      const storedCall = chrome.storage.local.set.mock.calls.find(
        call => call[0].articles
      );
      
      expect(storedCall[0].articles[0]).toMatchObject({
        title: 'Parsed Article',
        content: 'This is the parsed content from the real server',
        url: 'https://example.com/article'
      });
    });

    test('should handle server timeout', async () => {
      // Stop server temporarily
      testServer.close();
      
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://example.com/timeout'
      }]);
      
      chrome.tabs.sendMessage.mockResolvedValue({
        url: 'https://example.com/timeout',
        title: 'Timeout Test',
        htmlContent: '<p>Test</p>'
      });
      
      await background.savePage();
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Should queue the article
      const queueCall = chrome.storage.local.set.mock.calls.find(
        call => call[0].queue
      );
      
      expect(queueCall[0].queue[0]).toMatchObject({
        url: 'https://example.com/timeout',
        processed: false
      });
      
      // Restart server
      await createTestServer();
    });

    test('should handle concurrent saves', async () => {
      const tabs = [
        { id: 1, url: 'https://example.com/article1' },
        { id: 2, url: 'https://example.com/article2' },
        { id: 3, url: 'https://example.com/article3' }
      ];
      
      // Setup concurrent saves
      const promises = tabs.map(async (tab) => {
        chrome.tabs.query.mockResolvedValueOnce([tab]);
        
        chrome.tabs.sendMessage.mockResolvedValueOnce({
          url: tab.url,
          title: `Article ${tab.id}`,
          htmlContent: `<p>Content ${tab.id}</p>`
        });
        
        return background.savePage();
      });
      
      // Execute all saves concurrently
      await Promise.all(promises);
      
      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Verify all requests were made
      expect(serverApp.locals.requests.filter(r => r.url === '/parse')).toHaveLength(3);
      
      // Verify all articles were stored
      const storedCalls = chrome.storage.local.set.mock.calls.filter(
        call => call[0].articles
      );
      
      expect(storedCalls).toHaveLength(3);
    });
  });

  describe('Twitter Bookmarks', () => {
    test('should save Twitter bookmarks through real server', async () => {
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://twitter.com/i/bookmarks'
      }]);
      
      chrome.tabs.sendMessage.mockResolvedValue({
        url: 'https://twitter.com/i/bookmarks',
        htmlContent: '<article>Tweet content</article>',
        metadata: {
          type: 'twitter-bookmarks',
          hints: { tweetCount: 5 }
        }
      });
      
      await background.saveTwitterBookmarks();
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Verify server was called
      expect(serverApp.locals.requests).toContainEqual(
        expect.objectContaining({
          method: 'POST',
          url: '/parse-twitter-bookmarks'
        })
      );
      
      // Verify multiple articles were stored
      const storedCall = chrome.storage.local.set.mock.calls.find(
        call => call[0].articles
      );
      
      expect(storedCall[0].articles).toHaveLength(2);
      expect(storedCall[0].articles[0]).toMatchObject({
        title: 'Tweet from @user1',
        url: 'https://twitter.com/user1/status/123'
      });
    });
  });

  describe('Queue Processing', () => {
    test('should process queued items when server is available', async () => {
      // Setup queue with items
      chrome.storage.local.get.mockResolvedValue({
        queue: [
          {
            id: 'raw_1',
            url: 'https://example.com/queued1',
            title: 'Queued Article 1',
            htmlContent: '<p>Queued content 1</p>',
            processed: false
          },
          {
            id: 'raw_2',
            url: 'https://example.com/queued2',
            title: 'Queued Article 2',
            htmlContent: '<p>Queued content 2</p>',
            processed: false
          }
        ]
      });
      
      // Trigger queue processing
      await background.processQueue();
      
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Verify both items were processed
      expect(serverApp.locals.requests.filter(r => r.url === '/parse')).toHaveLength(2);
      
      // Verify queue was cleared
      const queueUpdateCall = chrome.storage.local.set.mock.calls.find(
        call => 'queue' in call[0]
      );
      
      expect(queueUpdateCall[0].queue).toHaveLength(0);
    });
  });

  describe('Error Recovery', () => {
    test('should recover from parse errors', async () => {
      // Override parse endpoint to return error for specific URL
      serverApp.post('/parse', (req, res) => {
        if (req.body.url === 'https://example.com/error') {
          res.status(500).json({ error: 'Parse error' });
        } else {
          res.json({
            success: true,
            data: {
              title: 'Success',
              content: 'Content'
            }
          });
        }
      });
      
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://example.com/error'
      }]);
      
      chrome.tabs.sendMessage.mockResolvedValue({
        url: 'https://example.com/error',
        title: 'Error Article',
        htmlContent: '<p>This will error</p>'
      });
      
      await background.savePage();
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Should still queue the raw content
      const queueCall = chrome.storage.local.set.mock.calls.find(
        call => call[0].queue
      );
      
      expect(queueCall[0].queue[0]).toMatchObject({
        url: 'https://example.com/error',
        title: 'Error Article'
      });
    });
  });
});