/**
 * Integration tests for browser extension <-> native app communication
 * Tests the native messaging protocol and data flow
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const WebSocket = require('ws');

describe('Native Messaging Integration', () => {
  let nativeHost;
  let wsClient;
  let messageQueue = [];
  
  // Mock native host executable
  class MockNativeHost {
    constructor() {
      this.process = null;
      this.messageHandlers = new Map();
    }
    
    async start() {
      // In real implementation, this would start the native host
      // For testing, we simulate it
      this.process = {
        stdin: {
          write: (data) => {
            const message = this.parseMessage(data);
            this.handleMessage(message);
          }
        },
        stdout: {
          on: (event, handler) => {
            if (event === 'data') {
              this.messageHandlers.set('data', handler);
            }
          }
        },
        on: (event, handler) => {
          if (event === 'exit') {
            this.messageHandlers.set('exit', handler);
          }
        }
      };
      
      return this.process;
    }
    
    parseMessage(data) {
      // Native messaging format: 4 bytes length + JSON message
      const length = data.readUInt32LE(0);
      const jsonStr = data.slice(4, 4 + length).toString();
      return JSON.parse(jsonStr);
    }
    
    sendMessage(message) {
      const jsonStr = JSON.stringify(message);
      const buffer = Buffer.allocUnsafe(4 + jsonStr.length);
      buffer.writeUInt32LE(jsonStr.length, 0);
      buffer.write(jsonStr, 4);
      
      const handler = this.messageHandlers.get('data');
      if (handler) {
        handler(buffer);
      }
    }
    
    handleMessage(message) {
      // Simulate native app processing
      switch (message.action) {
        case 'save':
          this.sendMessage({
            id: message.id,
            success: true,
            articleId: Date.now().toString()
          });
          break;
          
        case 'sync':
          this.sendMessage({
            id: message.id,
            success: true,
            syncedCount: 5
          });
          break;
          
        case 'getStatus':
          this.sendMessage({
            id: message.id,
            status: 'connected',
            version: '1.0.0'
          });
          break;
          
        default:
          this.sendMessage({
            id: message.id,
            error: 'Unknown action'
          });
      }
    }
    
    stop() {
      const handler = this.messageHandlers.get('exit');
      if (handler) {
        handler(0);
      }
    }
  }
  
  // Helper to encode/decode native messages
  function encodeMessage(message) {
    const jsonStr = JSON.stringify(message);
    const buffer = Buffer.allocUnsafe(4 + jsonStr.length);
    buffer.writeUInt32LE(jsonStr.length, 0);
    buffer.write(jsonStr, 4);
    return buffer;
  }
  
  function decodeMessage(buffer) {
    const length = buffer.readUInt32LE(0);
    const jsonStr = buffer.slice(4, 4 + length).toString();
    return JSON.parse(jsonStr);
  }
  
  beforeAll(async () => {
    // Set up mock native host
    nativeHost = new MockNativeHost();
    
    // Set up WebSocket client for real-time updates
    wsClient = new WebSocket('ws://localhost:8765');
    
    wsClient.on('message', (data) => {
      messageQueue.push(JSON.parse(data));
    });
    
    // Wait for WebSocket connection
    await new Promise((resolve, reject) => {
      wsClient.on('open', resolve);
      wsClient.on('error', reject);
      setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
    }).catch(() => {
      console.log('WebSocket server not available, continuing without real-time updates');
    });
  });
  
  afterAll(async () => {
    if (nativeHost) {
      nativeHost.stop();
    }
    if (wsClient && wsClient.readyState === WebSocket.OPEN) {
      wsClient.close();
    }
  });
  
  beforeEach(() => {
    messageQueue = [];
  });
  
  describe('Native Host Communication', () => {
    test('should establish connection with native host', async () => {
      const process = await nativeHost.start();
      expect(process).toBeTruthy();
      
      // Send test message
      const testMessage = {
        id: '123',
        action: 'getStatus'
      };
      
      process.stdin.write(encodeMessage(testMessage));
      
      // Wait for response
      const response = await new Promise((resolve) => {
        process.stdout.on('data', (data) => {
          resolve(decodeMessage(data));
        });
      });
      
      expect(response.status).toBe('connected');
      expect(response.version).toBe('1.0.0');
    });
    
    test('should save article through native host', async () => {
      const process = await nativeHost.start();
      
      const saveMessage = {
        id: '456',
        action: 'save',
        article: {
          url: 'https://example.com/test',
          title: 'Test Article',
          content: 'Test content',
          timestamp: new Date().toISOString()
        }
      };
      
      process.stdin.write(encodeMessage(saveMessage));
      
      const response = await new Promise((resolve) => {
        process.stdout.on('data', (data) => {
          resolve(decodeMessage(data));
        });
      });
      
      expect(response.success).toBe(true);
      expect(response.articleId).toBeTruthy();
    });
    
    test('should handle native host errors', async () => {
      const process = await nativeHost.start();
      
      const invalidMessage = {
        id: '789',
        action: 'invalidAction'
      };
      
      process.stdin.write(encodeMessage(invalidMessage));
      
      const response = await new Promise((resolve) => {
        process.stdout.on('data', (data) => {
          resolve(decodeMessage(data));
        });
      });
      
      expect(response.error).toBeTruthy();
    });
    
    test('should handle native host disconnection', async () => {
      const process = await nativeHost.start();
      
      const exitPromise = new Promise((resolve) => {
        process.on('exit', (code) => {
          resolve(code);
        });
      });
      
      nativeHost.stop();
      
      const exitCode = await exitPromise;
      expect(exitCode).toBe(0);
    });
  });
  
  describe('Batch Operations', () => {
    test('should batch multiple save operations', async () => {
      const process = await nativeHost.start();
      const articles = [];
      
      // Create 10 test articles
      for (let i = 0; i < 10; i++) {
        articles.push({
          url: `https://example.com/article-${i}`,
          title: `Article ${i}`,
          content: `Content for article ${i}`
        });
      }
      
      // Send batch save request
      const batchMessage = {
        id: 'batch-001',
        action: 'batchSave',
        articles: articles
      };
      
      // Mock batch response
      nativeHost.handleMessage = (message) => {
        if (message.action === 'batchSave') {
          nativeHost.sendMessage({
            id: message.id,
            success: true,
            savedCount: message.articles.length,
            articleIds: message.articles.map((_, i) => `article-${i}`)
          });
        }
      };
      
      process.stdin.write(encodeMessage(batchMessage));
      
      const response = await new Promise((resolve) => {
        process.stdout.on('data', (data) => {
          resolve(decodeMessage(data));
        });
      });
      
      expect(response.success).toBe(true);
      expect(response.savedCount).toBe(10);
      expect(response.articleIds).toHaveLength(10);
    });
  });
  
  describe('Sync Protocol', () => {
    test('should sync articles with native app', async () => {
      const process = await nativeHost.start();
      
      const syncMessage = {
        id: 'sync-001',
        action: 'sync',
        lastSyncTime: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
      };
      
      process.stdin.write(encodeMessage(syncMessage));
      
      const response = await new Promise((resolve) => {
        process.stdout.on('data', (data) => {
          resolve(decodeMessage(data));
        });
      });
      
      expect(response.success).toBe(true);
      expect(response.syncedCount).toBeGreaterThan(0);
    });
    
    test('should handle sync conflicts', async () => {
      const process = await nativeHost.start();
      
      // Mock conflict handling
      nativeHost.handleMessage = (message) => {
        if (message.action === 'resolveConflict') {
          nativeHost.sendMessage({
            id: message.id,
            resolved: true,
            resolution: message.resolution
          });
        }
      };
      
      const conflictMessage = {
        id: 'conflict-001',
        action: 'resolveConflict',
        articleId: 'test-article',
        localVersion: { title: 'Local Title', modifiedAt: '2024-01-01T10:00:00Z' },
        remoteVersion: { title: 'Remote Title', modifiedAt: '2024-01-01T11:00:00Z' },
        resolution: 'remote' // Use remote version
      };
      
      process.stdin.write(encodeMessage(conflictMessage));
      
      const response = await new Promise((resolve) => {
        process.stdout.on('data', (data) => {
          resolve(decodeMessage(data));
        });
      });
      
      expect(response.resolved).toBe(true);
      expect(response.resolution).toBe('remote');
    });
  });
  
  describe('Real-time Updates', () => {
    test('should receive real-time updates via WebSocket', async () => {
      if (!wsClient || wsClient.readyState !== WebSocket.OPEN) {
        console.log('WebSocket not available, skipping real-time test');
        return;
      }
      
      // Simulate article saved in native app
      const update = {
        type: 'articleSaved',
        article: {
          id: 'ws-test-001',
          title: 'WebSocket Test Article',
          savedAt: new Date().toISOString()
        }
      };
      
      // In real scenario, native app would send this
      wsClient.send(JSON.stringify(update));
      
      // Wait for message to be received
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(messageQueue).toHaveLength(1);
      expect(messageQueue[0].type).toBe('articleSaved');
    });
    
    test('should handle WebSocket reconnection', async () => {
      if (!wsClient || wsClient.readyState !== WebSocket.OPEN) {
        return;
      }
      
      // Simulate disconnection
      wsClient.close();
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Reconnect
      const newClient = new WebSocket('ws://localhost:8765');
      
      const connected = await new Promise((resolve) => {
        newClient.on('open', () => resolve(true));
        newClient.on('error', () => resolve(false));
        setTimeout(() => resolve(false), 1000);
      });
      
      if (connected) {
        expect(newClient.readyState).toBe(WebSocket.OPEN);
        newClient.close();
      }
    });
  });
  
  describe('Performance', () => {
    test('should handle high message throughput', async () => {
      const process = await nativeHost.start();
      const messageCount = 100;
      const responses = [];
      
      // Set up response collector
      process.stdout.on('data', (data) => {
        responses.push(decodeMessage(data));
      });
      
      const startTime = Date.now();
      
      // Send many messages rapidly
      for (let i = 0; i < messageCount; i++) {
        const message = {
          id: `perf-${i}`,
          action: 'save',
          article: {
            url: `https://example.com/perf-${i}`,
            title: `Performance Test ${i}`
          }
        };
        
        process.stdin.write(encodeMessage(message));
      }
      
      // Wait for all responses
      await new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (responses.length === messageCount) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
        
        setTimeout(() => {
          clearInterval(checkInterval);
          resolve();
        }, 5000);
      });
      
      const duration = Date.now() - startTime;
      
      expect(responses).toHaveLength(messageCount);
      expect(duration).toBeLessThan(1000); // Should process 100 messages in under 1 second
      
      // Verify all responses are successful
      responses.forEach((response, i) => {
        expect(response.id).toBe(`perf-${i}`);
        expect(response.success).toBe(true);
      });
    });
  });
});