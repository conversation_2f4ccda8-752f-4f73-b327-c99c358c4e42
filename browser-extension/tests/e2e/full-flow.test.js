/**
 * Comprehensive E2E tests for the browser extension
 * Tests the complete user flow from content capture to sync
 */

const puppeteer = require('puppeteer');
const path = require('path');
const express = require('express');
const http = require('http');
const WebSocket = require('ws');

describe('Browser Extension E2E Tests', () => {
  let browser;
  let page;
  let server;
  let wsServer;
  let extensionId;
  let capturedArticles = [];
  
  // Helper to get extension ID
  async function getExtensionId(browser) {
    const targets = await browser.targets();
    const extensionTarget = targets.find(target => 
      target.type() === 'service_worker' && 
      target.url().includes('chrome-extension://')
    );
    
    if (extensionTarget) {
      const extensionUrl = extensionTarget.url();
      return extensionUrl.split('/')[2];
    }
    return null;
  }
  
  // Helper to wait for element
  async function waitForElement(page, selector, timeout = 5000) {
    try {
      await page.waitForSelector(selector, { timeout });
      return true;
    } catch {
      return false;
    }
  }
  
  beforeAll(async () => {
    // Create test server
    const app = express();
    app.use(express.json());
    app.use(express.static(path.join(__dirname, '../fixtures')));
    
    // Parse endpoint
    app.post('/parse', (req, res) => {
      const parsedArticle = {
        title: req.body.title || 'Parsed Article',
        content: req.body.htmlContent ? 'Parsed content from HTML' : 'Default content',
        summary: 'This is a summary of the parsed article',
        keywords: ['test', 'e2e', 'browser-extension'],
        author: req.body.metadata?.author || 'Test Author',
        publish_date: new Date().toISOString(),
        reading_time: 5,
        content_type: 'article'
      };
      
      capturedArticles.push(parsedArticle);
      res.json(parsedArticle);
    });
    
    // Native app endpoint simulation
    app.post('/native/save', (req, res) => {
      res.json({ 
        success: true, 
        article_id: Date.now().toString(),
        message: 'Article saved to native app'
      });
    });
    
    // Server setup
    const httpServer = http.createServer(app);
    
    // WebSocket for real-time updates
    wsServer = new WebSocket.Server({ server: httpServer });
    wsServer.on('connection', (ws) => {
      ws.on('message', (message) => {
        // Broadcast to all clients
        wsServer.clients.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(message);
          }
        });
      });
    });
    
    // Start server
    await new Promise((resolve) => {
      httpServer.listen(0, () => {
        server = httpServer;
        resolve();
      });
    });
    
    const port = server.address().port;
    process.env.TEST_SERVER_PORT = port;
    
    // Launch browser with extension
    const extensionPath = path.join(__dirname, '../../');
    
    // Check if we're in CI environment
    const isCI = process.env.CI === 'true';
    
    browser = await puppeteer.launch({
      headless: isCI ? 'new' : false,
      args: [
        `--disable-extensions-except=${extensionPath}`,
        `--load-extension=${extensionPath}`,
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    });
    
    // Get extension ID
    extensionId = await getExtensionId(browser);
    
    // Give extension time to initialize
    await new Promise(resolve => setTimeout(resolve, 2000));
  }, 30000);
  
  afterAll(async () => {
    if (browser) await browser.close();
    if (wsServer) wsServer.close();
    if (server) server.close();
  });
  
  beforeEach(async () => {
    page = await browser.newPage();
    capturedArticles = [];
  });
  
  afterEach(async () => {
    if (page) await page.close();
  });
  
  describe('Content Capture Flow', () => {
    test('should capture article from webpage', async () => {
      // Navigate to test article page
      await page.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      
      // Wait for content to load
      await page.waitForSelector('h1', { timeout: 5000 });
      
      // Trigger extension popup
      await page.evaluate((extId) => {
        // Simulate clicking extension icon
        chrome.runtime.sendMessage(extId, { action: 'capture' });
      }, extensionId);
      
      // Wait for capture to complete
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if article was captured
      expect(capturedArticles.length).toBe(1);
      expect(capturedArticles[0].title).toBeTruthy();
      expect(capturedArticles[0].content).toBeTruthy();
    });
    
    test('should show success notification after capture', async () => {
      // Navigate to test page
      await page.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      
      // Set up notification listener
      const notificationPromise = page.evaluateOnNewDocument(() => {
        window.notifications = [];
        const originalNotification = window.Notification;
        window.Notification = function(title, options) {
          window.notifications.push({ title, options });
          return new originalNotification(title, options);
        };
      });
      
      // Trigger capture
      await page.evaluate((extId) => {
        chrome.runtime.sendMessage(extId, { action: 'capture' });
      }, extensionId);
      
      // Wait and check for notification
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const notifications = await page.evaluate(() => window.notifications || []);
      const successNotification = notifications.find(n => 
        n.title.includes('Saved') || n.title.includes('Success')
      );
      
      expect(successNotification).toBeTruthy();
    });
    
    test('should handle capture errors gracefully', async () => {
      // Navigate to invalid page
      await page.goto('about:blank');
      
      // Try to capture
      const result = await page.evaluate((extId) => {
        return new Promise((resolve) => {
          chrome.runtime.sendMessage(
            extId, 
            { action: 'capture' },
            response => resolve(response)
          );
        });
      }, extensionId);
      
      // Should handle error
      expect(result).toHaveProperty('error');
    });
  });
  
  describe('Popup Interface', () => {
    test('should open extension popup', async () => {
      if (!extensionId) {
        console.warn('Extension ID not found, skipping popup test');
        return;
      }
      
      // Navigate to extension popup
      const popupUrl = `chrome-extension://${extensionId}/public/popup.html`;
      await page.goto(popupUrl);
      
      // Wait for popup to load
      await waitForElement(page, '#capture-btn', 3000);
      
      // Check popup elements
      const hasCaputeButton = await page.$('#capture-btn') !== null;
      const hasStatus = await page.$('#status') !== null;
      
      expect(hasCaputeButton).toBe(true);
      expect(hasStatus).toBe(true);
    });
    
    test('should capture from popup button', async () => {
      if (!extensionId) return;
      
      // Open test page in one tab
      const targetPage = await browser.newPage();
      await targetPage.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      
      // Open popup in another tab
      await page.goto(`chrome-extension://${extensionId}/public/popup.html`);
      await waitForElement(page, '#capture-btn');
      
      // Click capture button
      await page.click('#capture-btn');
      
      // Wait for capture
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if article was captured
      expect(capturedArticles.length).toBeGreaterThan(0);
      
      await targetPage.close();
    });
  });
  
  describe('Background Script', () => {
    test('should respond to context menu save', async () => {
      // This test simulates right-click save functionality
      await page.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      
      // Simulate context menu action
      const response = await page.evaluate((extId) => {
        return new Promise((resolve) => {
          chrome.runtime.sendMessage(
            extId,
            { 
              action: 'contextMenuSave',
              data: {
                pageUrl: window.location.href,
                selectionText: 'Selected text to save'
              }
            },
            response => resolve(response)
          );
        });
      }, extensionId);
      
      expect(response).toHaveProperty('success');
    });
    
    test('should batch multiple captures', async () => {
      // Open multiple tabs
      const pages = [];
      for (let i = 0; i < 3; i++) {
        const newPage = await browser.newPage();
        await newPage.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html?id=${i}`);
        pages.push(newPage);
      }
      
      // Capture all tabs
      await Promise.all(pages.map(async (p, index) => {
        await p.evaluate((extId) => {
          chrome.runtime.sendMessage(extId, { action: 'capture' });
        }, extensionId);
      }));
      
      // Wait for all captures
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Should have captured all articles
      expect(capturedArticles.length).toBe(3);
      
      // Clean up
      for (const p of pages) {
        await p.close();
      }
    });
  });
  
  describe('Native App Communication', () => {
    test('should send message to native app', async () => {
      // This test verifies native messaging setup
      const response = await page.evaluate((extId) => {
        return new Promise((resolve) => {
          chrome.runtime.sendMessage(
            extId,
            { 
              action: 'sendToNative',
              data: { test: true }
            },
            response => resolve(response)
          );
        });
      }, extensionId);
      
      // Native messaging might not be available in test environment
      if (response && response.error && response.error.includes('native')) {
        console.log('Native messaging not available in test environment');
        expect(true).toBe(true);
      } else {
        expect(response).toBeTruthy();
      }
    });
  });
  
  describe('Storage and Sync', () => {
    test('should store captured articles locally', async () => {
      // Capture an article
      await page.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      
      await page.evaluate((extId) => {
        chrome.runtime.sendMessage(extId, { action: 'capture' });
      }, extensionId);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check local storage
      const storedData = await page.evaluate((extId) => {
        return new Promise((resolve) => {
          chrome.runtime.sendMessage(
            extId,
            { action: 'getStoredArticles' },
            response => resolve(response)
          );
        });
      }, extensionId);
      
      expect(storedData).toBeTruthy();
      // Storage structure depends on implementation
    });
    
    test('should sync articles when online', async () => {
      // Simulate offline capture
      await page.evaluate(() => {
        window.navigator.onLine = false;
      });
      
      // Capture article
      await page.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      await page.evaluate((extId) => {
        chrome.runtime.sendMessage(extId, { action: 'capture' });
      }, extensionId);
      
      // Simulate coming back online
      await page.evaluate(() => {
        window.navigator.onLine = true;
        window.dispatchEvent(new Event('online'));
      });
      
      // Wait for sync
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Articles should be synced
      expect(capturedArticles.length).toBeGreaterThan(0);
    });
  });
  
  describe('Performance', () => {
    test('should capture article within acceptable time', async () => {
      await page.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html`);
      
      const startTime = Date.now();
      
      await page.evaluate((extId) => {
        chrome.runtime.sendMessage(extId, { action: 'capture' });
      }, extensionId);
      
      // Wait for capture to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (capturedArticles.length > 0) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
        
        // Timeout after 5 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          resolve();
        }, 5000);
      });
      
      const captureTime = Date.now() - startTime;
      
      // Should complete within 3 seconds
      expect(captureTime).toBeLessThan(3000);
      expect(capturedArticles.length).toBe(1);
    });
    
    test('should handle multiple simultaneous captures', async () => {
      const capturePromises = [];
      
      // Open 5 tabs simultaneously
      for (let i = 0; i < 5; i++) {
        capturePromises.push((async () => {
          const newPage = await browser.newPage();
          await newPage.goto(`http://localhost:${process.env.TEST_SERVER_PORT}/test-article.html?id=${i}`);
          
          await newPage.evaluate((extId) => {
            chrome.runtime.sendMessage(extId, { action: 'capture' });
          }, extensionId);
          
          await new Promise(resolve => setTimeout(resolve, 1000));
          await newPage.close();
        })());
      }
      
      const startTime = Date.now();
      await Promise.all(capturePromises);
      const totalTime = Date.now() - startTime;
      
      // Should handle concurrent captures efficiently
      expect(capturedArticles.length).toBe(5);
      expect(totalTime).toBeLessThan(5000); // Less than 1 second per article
    });
  });
});