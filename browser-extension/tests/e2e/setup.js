/**
 * E2E test setup
 */

// Set test timeout
jest.setTimeout(30000);

// Suppress console errors during tests
global.console.error = jest.fn();

// Setup Chrome mock for E2E environment
global.chrome = {
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue()
    },
    sync: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue()
    }
  }
};