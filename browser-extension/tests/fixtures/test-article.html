<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="Test Author">
    <meta name="description" content="This is a test article for E2E testing">
    <meta property="og:title" content="Test Article for Browser Extension">
    <meta property="og:description" content="This article is used for testing the browser extension">
    <meta property="og:image" content="https://example.com/test-image.jpg">
    <meta property="article:author" content="Test Author">
    <meta property="article:published_time" content="2024-01-01T12:00:00Z">
    <title>Test Article - Browser Extension E2E Testing</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .content {
            color: #444;
        }
        .highlight {
            background-color: #ffffcc;
            padding: 2px 4px;
        }
        img {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }
        blockquote {
            border-left: 4px solid #ddd;
            padding-left: 20px;
            margin: 20px 0;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <article>
        <header>
            <h1>Understanding Browser Extensions: A Comprehensive Guide</h1>
            <div class="meta">
                <span class="author">By Test Author</span> • 
                <span class="date">January 1, 2024</span> • 
                <span class="reading-time">5 min read</span>
            </div>
        </header>
        
        <section class="content">
            <p class="lead">
                Browser extensions have become an integral part of the modern web browsing experience. 
                They enhance functionality, improve productivity, and customize how we interact with web content.
            </p>
            
            <h2>What Are Browser Extensions?</h2>
            <p>
                Browser extensions are small software programs that customize the browsing experience. 
                They enable users to tailor browser functionality and behavior to individual needs or preferences. 
                Extensions are built using web technologies such as <span class="highlight">HTML</span>, 
                <span class="highlight">CSS</span>, and <span class="highlight">JavaScript</span>.
            </p>
            
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxyZWN0IHdpZHRoPSI4MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjBmMGYwIi8+CiAgICA8dGV4dCB4PSI0MDAiIHk9IjIwMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIyNCI+UGxhY2Vob2xkZXIgSW1hZ2U8L3RleHQ+Cjwvc3ZnPg==" 
                 alt="Browser Extension Architecture Diagram">
            
            <h2>Key Components</h2>
            <p>
                Every browser extension consists of several key components that work together:
            </p>
            
            <ol>
                <li><strong>Manifest File:</strong> The manifest.json file provides important information about the extension.</li>
                <li><strong>Background Scripts:</strong> These run in the background and handle events.</li>
                <li><strong>Content Scripts:</strong> Scripts that run in the context of web pages.</li>
                <li><strong>Popup:</strong> The user interface that appears when clicking the extension icon.</li>
            </ol>
            
            <blockquote>
                "Browser extensions represent one of the most powerful ways to enhance and customize 
                the web browsing experience." - Web Development Expert
            </blockquote>
            
            <h2>Security Considerations</h2>
            <p>
                When developing browser extensions, security should be a top priority. Extensions have 
                access to sensitive user data and can modify web pages. It's crucial to follow best 
                practices such as:
            </p>
            
            <ul>
                <li>Implementing proper content security policies</li>
                <li>Validating all user inputs</li>
                <li>Using HTTPS for all external communications</li>
                <li>Minimizing required permissions</li>
            </ul>
            
            <h2>Future of Browser Extensions</h2>
            <p>
                The landscape of browser extensions continues to evolve. With the adoption of Manifest V3, 
                extensions are becoming more secure and performant. New APIs are being introduced to 
                provide developers with more capabilities while maintaining user privacy and security.
            </p>
            
            <p>
                As web technologies advance, we can expect browser extensions to become even more 
                sophisticated, offering enhanced functionality while maintaining a seamless user experience.
            </p>
        </section>
        
        <footer>
            <p class="tags">
                Tags: browser-extensions, web-development, javascript, chrome, firefox
            </p>
        </footer>
    </article>
    
    <script>
        // Add some dynamic behavior for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test article loaded');
            
            // Simulate dynamic content loading
            setTimeout(() => {
                const dynamicContent = document.createElement('p');
                dynamicContent.className = 'dynamic-content';
                dynamicContent.textContent = 'This content was loaded dynamically.';
                document.querySelector('.content').appendChild(dynamicContent);
            }, 1000);
        });
        
        // Helper for extension testing
        window.getArticleData = function() {
            return {
                title: document.querySelector('h1').textContent,
                author: document.querySelector('.author').textContent,
                content: document.querySelector('.content').textContent,
                url: window.location.href
            };
        };
    </script>
</body>
</html>