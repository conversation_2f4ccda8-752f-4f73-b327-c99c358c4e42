/**
 * Test suite for content-capture.js
 */

// Setup DOM before loading module
document.body.innerHTML = `
  <html>
    <head>
      <title>Test Page</title>
      <meta name="description" content="Test description">
      <meta name="author" content="Test Author">
      <meta property="article:published_time" content="2024-01-01">
    </head>
    <body>
      <h1>Test Article</h1>
      <p>This is paragraph 1</p>
      <p>This is paragraph 2</p>
      <script>console.log('should be removed');</script>
      <style>body { color: red; }</style>
      <nav>Navigation content</nav>
      <article data-testid="tweet">Tweet content</article>
    </body>
  </html>
`;

// Import after setting up DOM
const { UniversalCapture } = require('../../src/content-capture.js');

describe('UniversalCapture', () => {
  let capture;
  
  beforeEach(() => {
    capture = new UniversalCapture();
    // Reset location
    delete window.location;
    window.location = new URL('https://example.com/article');
  });

  describe('capture()', () => {
    test('should capture basic page data', async () => {
      const result = await capture.capture();
      
      expect(result).toHaveProperty('url');
      expect(result).toHaveProperty('title');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('htmlContent');
      expect(result).toHaveProperty('metadata');
      
      expect(result.url).toBe('https://example.com/article');
      expect(result.title).toBe('Test Page');
    });

    test('should capture metadata correctly', async () => {
      const result = await capture.capture();
      
      expect(result.metadata).toMatchObject({
        description: 'Test description',
        author: 'Test Author',
        publishDate: '2024-01-01',
        type: 'article' // URL contains /article which matches article pattern
      });
    });

    test('should include content hints', async () => {
      const result = await capture.capture();
      
      expect(result.metadata.hints).toHaveProperty('pageType');
      expect(result.metadata.hints).toHaveProperty('scrollHeight');
      expect(result.metadata.hints).toHaveProperty('hasInfiniteScroll');
    });
  });

  describe('getCleanDOM()', () => {
    test('should remove scripts and styles', () => {
      const cleaned = capture.getCleanDOM();
      
      expect(cleaned).not.toContain('<script');
      expect(cleaned).not.toContain('console.log');
      expect(cleaned).not.toContain('<style');
      expect(cleaned).not.toContain('color: red');
    });

    test('should preserve navigation elements', () => {
      // Current implementation only removes scripts, styles, iframes, and hidden elements
      const cleaned = capture.getCleanDOM();
      
      expect(cleaned).toContain('<nav');
      expect(cleaned).toContain('Navigation content');
    });

    test('should preserve content elements', () => {
      const cleaned = capture.getCleanDOM();
      
      expect(cleaned).toContain('<h1>Test Article</h1>');
      expect(cleaned).toContain('<p>This is paragraph 1</p>');
      expect(cleaned).toContain('<article');
    });
  });

  describe('detectContentType()', () => {
    test('should detect article type from URL', () => {
      window.location = new URL('https://example.com/article/test');
      const type = capture.detectContentType();
      expect(type).toBe('article');
    });

    test('should detect Twitter type', () => {
      window.location = new URL('https://twitter.com/user/status/123');
      const type = capture.detectContentType();
      expect(type).toBe('twitter');
    });

    test('should detect Twitter bookmarks', () => {
      window.location = new URL('https://twitter.com/i/bookmarks');
      const type = capture.detectContentType();
      expect(type).toBe('twitter-bookmarks');
    });

    test('should detect YouTube type', () => {
      window.location = new URL('https://youtube.com/watch?v=123');
      const type = capture.detectContentType();
      expect(type).toBe('youtube');
    });

    test('should detect GitHub type', () => {
      window.location = new URL('https://github.com/user/repo');
      const type = capture.detectContentType();
      expect(type).toBe('github');
    });

    test('should detect Reddit type', () => {
      window.location = new URL('https://reddit.com/r/programming/comments/123');
      const type = capture.detectContentType();
      expect(type).toBe('reddit');
    });

    test('should detect Medium type', () => {
      window.location = new URL('https://medium.com/@user/article-123');
      const type = capture.detectContentType();
      expect(type).toBe('medium');
    });

    test('should return generic for unknown types', () => {
      window.location = new URL('https://unknownsite.com/page');
      const type = capture.detectContentType();
      expect(type).toBe('generic');
    });
  });

  describe('getContentHints()', () => {
    test('should provide basic hints', () => {
      const hints = capture.getContentHints('generic');
      
      expect(hints).toHaveProperty('pageType', 'generic');
      expect(hints).toHaveProperty('scrollHeight');
      expect(hints).toHaveProperty('hasInfiniteScroll', false);
    });

    test('should provide Twitter-specific hints', () => {
      // Add Twitter-specific elements to DOM
      document.body.innerHTML += '<div data-testid="primaryColumn"></div>';
      
      const hints = capture.getContentHints('twitter');
      
      expect(hints).toHaveProperty('isBookmarksPage', false);
      expect(hints).toHaveProperty('tweetCount');
      expect(hints).toHaveProperty('hasThreads');
    });

    test('should detect Twitter bookmarks page', () => {
      const hints = capture.getContentHints('twitter-bookmarks');
      
      expect(hints).toHaveProperty('isBookmarksPage', true);
    });
  });

  describe('findPublishDate()', () => {
    test('should find publish date from meta tags', () => {
      // The meta tag is in the document setup above
      const metaTag = document.querySelector('meta[property="article:published_time"]');
      expect(metaTag).toBeTruthy();
      expect(metaTag.getAttribute('content')).toBe('2024-01-01');
      
      const date = capture.findPublishDate();
      expect(date).toBe('2024-01-01');
    });

    test('should find date from time elements', () => {
      document.body.innerHTML = '<time datetime="2024-02-01">Feb 1, 2024</time>';
      const date = capture.findPublishDate();
      expect(date).toBe('2024-02-01');
    });

    test('should find date from class selectors', () => {
      document.body.innerHTML = '<div class="published-date">2024-03-01</div>';
      const date = capture.findPublishDate();
      expect(date).toBe('2024-03-01');
    });

    test('should return null when no date found', () => {
      document.body.innerHTML = '<div>No date here</div>';
      const date = capture.findPublishDate();
      expect(date).toBeNull();
    });
  });
});

describe('Chrome Message Handling', () => {
  let messageHandlers = [];
  
  beforeEach(() => {
    messageHandlers = [];
    // Mock chrome.runtime
    global.chrome = {
      runtime: {
        onMessage: {
          addListener: jest.fn((handler) => {
            messageHandlers.push(handler);
          })
        }
      }
    };
  });

  test('should register message listener', () => {
    // Re-load the module to trigger listener registration
    jest.resetModules();
    require('../../src/content-capture.js');
    
    expect(chrome.runtime.onMessage.addListener).toHaveBeenCalled();
    expect(messageHandlers.length).toBeGreaterThan(0);
  });

  test('should handle capture message', async () => {
    const mockSendResponse = jest.fn();
    
    // Re-load module
    jest.resetModules();
    require('../../src/content-capture.js');
    
    // Get the registered handler
    const handler = messageHandlers[0];
    
    // Test capture message
    const result = handler(
      { action: 'capture' },
      {},
      mockSendResponse
    );
    
    expect(result).toBe(true); // Should return true for async response
    
    // Wait for async capture
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(mockSendResponse).toHaveBeenCalledWith(
      expect.objectContaining({
        url: expect.any(String),
        title: expect.any(String),
        timestamp: expect.any(String),
        htmlContent: expect.any(String),
        metadata: expect.objectContaining({
          type: expect.any(String),
          hints: expect.any(Object)
        })
      })
    );
  });

  test('should handle save-feedback message', () => {
    // Mock showSaveIndicator
    let indicatorShown = false;
    global.showSaveIndicator = jest.fn(() => {
      indicatorShown = true;
    });
    
    // Re-load module
    jest.resetModules();
    require('../../src/content-capture.js');
    
    // Get both handlers (capture and save-feedback)
    const handlers = messageHandlers;
    
    // Find save-feedback handler
    handlers.forEach(handler => {
      handler({ action: 'save-feedback', success: true }, {}, () => {});
    });
    
    // Since showSaveIndicator is called internally, we can't directly test it
    // But we can verify the handler doesn't throw
    expect(() => {
      handlers.forEach(handler => {
        handler({ action: 'save-feedback', success: true }, {}, () => {});
      });
    }).not.toThrow();
  });

  test('should ignore unknown messages', () => {
    jest.resetModules();
    require('../../src/content-capture.js');
    
    const handler = messageHandlers[0];
    const mockSendResponse = jest.fn();
    
    const result = handler(
      { action: 'unknown' },
      {},
      mockSendResponse
    );
    
    expect(result).toBeUndefined();
    expect(mockSendResponse).not.toHaveBeenCalled();
  });
});