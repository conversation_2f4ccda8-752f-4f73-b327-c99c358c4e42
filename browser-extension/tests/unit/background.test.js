/**
 * Unit tests for background.js
 */

// Mock Chrome APIs before any imports
global.chrome = {
  commands: {
    onCommand: {
      addListener: jest.fn()
    }
  },
  action: {
    onClicked: {
      addListener: jest.fn()
    },
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn()
  },
  runtime: {
    onMessage: {
      addListener: jest.fn()
    },
    onStartup: {
      addListener: jest.fn()
    },
    onInstalled: {
      addListener: jest.fn()
    }
  },
  tabs: {
    query: jest.fn(),
    sendMessage: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  },
  scripting: {
    executeScript: jest.fn()
  },
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn()
    },
    local: {
      get: jest.fn(),
      set: jest.fn()
    }
  },
  contextMenus: {
    create: jest.fn(),
    onClicked: {
      addListener: jest.fn()
    }
  },
  notifications: {
    create: jest.fn()
  },
  identity: {
    getRedirectURL: jest.fn()
  }
};

// Mock fetch
global.fetch = jest.fn();

// Import the exported functions from background.js
const background = require('../../src/background.js');

describe('Background Script', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    fetch.mockClear();
  });

  describe('savePage()', () => {
    beforeEach(() => {
      // Setup default mocks
      chrome.tabs.query.mockResolvedValue([{ id: 1, url: 'https://example.com' }]);
      chrome.tabs.sendMessage.mockResolvedValue({
        url: 'https://example.com',
        title: 'Test Page',
        htmlContent: '<p>Test</p>',
        metadata: { type: 'article' }
      });
      chrome.storage.sync.get.mockResolvedValue({ parseServerUrl: 'http://localhost:8000' });
      chrome.storage.local.get.mockResolvedValue({ articles: [] });
      chrome.storage.local.set.mockResolvedValue();
      
      fetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            title: 'Parsed Title',
            content: 'Parsed content',
            summary: 'Summary',
            keywords: ['test'],
            reading_time: 5,
            content_type: 'article'
          }
        })
      });
    });

    test('should get active tab', async () => {
      await background.savePage();
      
      expect(chrome.tabs.query).toHaveBeenCalledWith({
        active: true,
        currentWindow: true
      });
    });

    test('should show loading badge', async () => {
      await background.savePage();
      
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        text: '...',
        tabId: 1
      });
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        color: '#2196F3'
      });
    });

    test('should inject content script', async () => {
      await background.savePage();
      
      expect(chrome.scripting.executeScript).toHaveBeenCalledWith({
        target: { tabId: 1 },
        files: ['src/content-capture.js']
      });
    });

    test('should capture page content', async () => {
      await background.savePage();
      
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(1, { action: 'capture' });
    });

    test('should send to parse server', async () => {
      await background.savePage();
      
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/parse',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('https://example.com')
        })
      );
    });

    test('should show success badge', async () => {
      await background.savePage();
      
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        text: '✓',
        tabId: 1
      });
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        color: '#4CAF50'
      });
    });

    test('should store article locally', async () => {
      await background.savePage();
      
      const calls = chrome.storage.local.set.mock.calls;
      const articlesCall = calls.find(call => call[0].articles);
      
      expect(articlesCall).toBeDefined();
      expect(articlesCall[0].articles[0]).toMatchObject({
        title: 'Parsed Title',
        content: 'Parsed content',
        url: 'https://example.com'
      });
    });

    test('should handle no active tab', async () => {
      chrome.tabs.query.mockResolvedValue([]);
      
      await background.savePage();
      
      expect(chrome.tabs.sendMessage).not.toHaveBeenCalled();
    });

    test('should handle capture failure', async () => {
      chrome.tabs.sendMessage.mockRejectedValue(new Error('Tab closed'));
      
      await background.savePage();
      
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        text: '✗',
        tabId: 1
      });
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        color: '#F44336'
      });
    });

    test('should handle parse server error', async () => {
      fetch.mockResolvedValue({
        ok: false,
        status: 500
      });
      
      await background.savePage();
      
      // Should store raw content
      const calls = chrome.storage.local.set.mock.calls;
      const queueCall = calls.find(call => call[0].queue);
      expect(queueCall).toBeDefined();
    });

    test('should handle network error', async () => {
      fetch.mockRejectedValue(new Error('Network error'));
      
      await background.savePage();
      
      // Should show error badge
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        text: '✗',
        tabId: 1
      });
    });
  });

  describe('saveTwitterBookmarks()', () => {
    beforeEach(() => {
      // Setup mocks
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://twitter.com/i/bookmarks'
      }]);
      
      chrome.tabs.sendMessage.mockResolvedValue({
        url: 'https://twitter.com/i/bookmarks',
        htmlContent: '<article>Tweet 1</article><article>Tweet 2</article>',
        metadata: {
          type: 'twitter-bookmarks',
          hints: { tweetCount: 2 }
        }
      });
      
      chrome.storage.sync.get.mockResolvedValue({ parseServerUrl: 'http://localhost:8000' });
      chrome.storage.local.get.mockResolvedValue({ articles: [] });
      
      fetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            articles: [
              { id: '1', title: 'Tweet 1', content: 'Content 1', url: 'https://twitter.com/1' },
              { id: '2', title: 'Tweet 2', content: 'Content 2', url: 'https://twitter.com/2' }
            ],
            total_count: 2,
            source: 'twitter_bookmarks'
          }
        })
      });
    });

    test('should check if on bookmarks page', async () => {
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://twitter.com/user/status/123'
      }]);
      
      await background.saveTwitterBookmarks();
      
      expect(chrome.tabs.sendMessage).not.toHaveBeenCalled();
    });

    test('should use Twitter bookmarks endpoint', async () => {
      await background.saveTwitterBookmarks();
      
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/parse-twitter-bookmarks',
        expect.any(Object)
      );
    });

    test('should save multiple articles', async () => {
      await background.saveTwitterBookmarks();
      
      const calls = chrome.storage.local.set.mock.calls;
      const articlesCall = calls.find(call => call[0].articles);
      
      expect(articlesCall).toBeDefined();
      expect(articlesCall[0].articles).toHaveLength(2);
    });

    test('should show notification for multiple saves', async () => {
      await background.saveTwitterBookmarks();
      
      expect(chrome.notifications.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'basic',
          title: 'Twitter Bookmarks Saved',
          message: 'Successfully saved 2 bookmarks'
        })
      );
    });

    test('should handle Twitter/X domains', async () => {
      chrome.tabs.query.mockResolvedValue([{
        id: 1,
        url: 'https://x.com/i/bookmarks'
      }]);
      
      await background.saveTwitterBookmarks();
      
      expect(chrome.tabs.sendMessage).toHaveBeenCalled();
    });
  });

  describe('Helper Functions', () => {
    test('getParseServerUrl should return default URL', async () => {
      chrome.storage.sync.get.mockResolvedValue({});
      
      const url = await background.getParseServerUrl();
      
      expect(url).toBe('http://localhost:8000');
    });

    test('getParseServerUrl should return stored URL', async () => {
      chrome.storage.sync.get.mockResolvedValue({ parseServerUrl: 'https://custom.server.com' });
      
      const url = await background.getParseServerUrl();
      
      expect(url).toBe('https://custom.server.com');
    });

    test('getUserId should generate new ID if not exists', async () => {
      chrome.storage.sync.get.mockResolvedValue({});
      
      const userId = await background.getUserId();
      
      expect(userId).toMatch(/^user_\d+_[a-z0-9]+$/);
      expect(chrome.storage.sync.set).toHaveBeenCalledWith({ userId });
    });

    test('getUserId should return existing ID', async () => {
      chrome.storage.sync.get.mockResolvedValue({ userId: 'user_123_abc' });
      
      const userId = await background.getUserId();
      
      expect(userId).toBe('user_123_abc');
    });
  });

  describe('Storage Operations', () => {
    test('storeArticle should add article to storage', async () => {
      chrome.storage.local.get.mockResolvedValue({ articles: [] });
      
      const article = {
        id: 'test-1',
        title: 'Test Article',
        url: 'https://example.com',
        content: 'Test content'
      };
      
      await background.storeArticle(article);
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith({
        articles: [article]
      });
    });

    test('storeArticle should limit articles to 1000', async () => {
      const existingArticles = Array(1005).fill({ title: 'Article' });
      chrome.storage.local.get.mockResolvedValue({ articles: existingArticles });
      
      await background.storeArticle({ title: 'New Article' });
      
      const setCall = chrome.storage.local.set.mock.calls[0];
      expect(setCall[0].articles).toHaveLength(1000);
    });

    test('storeRawContent should add to queue', async () => {
      chrome.storage.local.get.mockResolvedValue({ queue: [] });
      
      const rawContent = {
        url: 'https://example.com',
        htmlContent: '<p>Raw HTML</p>',
        title: 'Raw Title'
      };
      
      await background.storeRawContent(rawContent);
      
      const setCall = chrome.storage.local.set.mock.calls[0];
      expect(setCall[0].queue[0]).toMatchObject({
        ...rawContent,
        processed: false
      });
    });
  });

  describe('Queue Processing', () => {
    test('processQueue should process pending items', async () => {
      chrome.storage.local.get.mockResolvedValue({
        queue: [
          { id: 'raw_1', url: 'https://example1.com', htmlContent: '<p>Test1</p>', processed: false },
          { id: 'raw_2', url: 'https://example2.com', htmlContent: '<p>Test2</p>', processed: false }
        ]
      });
      
      chrome.storage.sync.get.mockResolvedValue({ parseServerUrl: 'http://localhost:8000' });
      
      fetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: { title: 'Processed' }
        })
      });
      
      await background.processQueue();
      
      // Should process both items
      expect(fetch).toHaveBeenCalledTimes(2);
      
      // Should update queue
      const setCall = chrome.storage.local.set.mock.calls.find(
        call => 'queue' in call[0]
      );
      expect(setCall[0].queue).toHaveLength(0);
    });

    test('processQueue should keep failed items in queue', async () => {
      chrome.storage.local.get.mockResolvedValue({
        queue: [
          { id: 'raw_1', url: 'https://example1.com', htmlContent: '<p>Test1</p>', processed: false },
          { id: 'raw_2', url: 'https://example2.com', htmlContent: '<p>Test2</p>', processed: false }
        ]
      });
      
      // First succeeds, second fails
      fetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: {} })
        })
        .mockResolvedValueOnce({
          ok: false
        });
      
      await background.processQueue();
      
      // Should update queue with only failed item
      const setCall = chrome.storage.local.set.mock.calls.find(
        call => 'queue' in call[0]
      );
      expect(setCall[0].queue).toHaveLength(1);
      expect(setCall[0].queue[0].id).toBe('raw_2');
    });
  });

  describe('URL Saving', () => {
    test('saveUrl should fetch and save URL content', async () => {
      chrome.storage.sync.get.mockResolvedValue({ parseServerUrl: 'http://localhost:8000' });
      chrome.storage.local.get.mockResolvedValue({ articles: [] });
      
      fetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            title: 'Fetched Article',
            content: 'Content from URL',
            url: 'https://example.com/article'
          }
        })
      });
      
      await background.saveUrl('https://example.com/article', 1);
      
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/parse',
        expect.objectContaining({
          body: expect.stringContaining('https://example.com/article')
        })
      );
      
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        text: '✓',
        tabId: 1
      });
    });
  });
});