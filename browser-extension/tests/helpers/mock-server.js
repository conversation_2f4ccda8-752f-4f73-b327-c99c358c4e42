/**
 * Mock parse server for testing
 */

const express = require('express');
const bodyParser = require('body-parser');

function setupMockServer(port = 3001) {
  const app = express();
  app.use(bodyParser.json());
  
  let requestCount = 0;
  let errorMode = false;
  let rateLimitMode = false;
  let failUrls = new Set();
  const cache = new Map();
  
  // Parse endpoint
  app.post('/parse', (req, res) => {
    requestCount++;
    
    if (rateLimitMode) {
      return res.status(429).json({ error: 'Rate limit exceeded' });
    }
    
    if (errorMode || failUrls.has(req.body.url)) {
      return res.status(500).json({ error: 'Internal server error' });
    }
    
    // Check cache
    const cacheKey = `${req.body.url}:${req.body.content_type}`;
    if (cache.has(cacheKey)) {
      return res.json(cache.get(cacheKey));
    }
    
    // Mock parsed response
    const parsed = {
      title: `Parsed: ${req.body.metadata?.title || 'Unknown'}`,
      content: 'Parsed content from server',
      summary: 'This is a test summary',
      keywords: ['test', 'mock'],
      author: req.body.metadata?.author || null,
      publish_date: req.body.metadata?.publishDate || null,
      reading_time: 5,
      content_type: req.body.content_type
    };
    
    cache.set(cacheKey, parsed);
    res.json(parsed);
  });
  
  // Twitter bookmarks endpoint
  app.post('/parse-twitter-bookmarks', (req, res) => {
    requestCount++;
    
    if (rateLimitMode) {
      return res.status(429).json({ error: 'Rate limit exceeded' });
    }
    
    if (errorMode) {
      return res.status(500).json({ error: 'Internal server error' });
    }
    
    // Extract tweet count from HTML
    const tweetMatches = req.body.html_content.match(/data-testid="tweet"/g) || [];
    const tweetCount = tweetMatches.length;
    
    // Generate mock tweets
    const articles = [];
    for (let i = 0; i < tweetCount; i++) {
      articles.push({
        title: `Tweet by @user${i + 1}`,
        content: `Tweet ${i + 1} content`,
        summary: `Tweet ${i + 1} summary`,
        keywords: ['twitter', 'tweet'],
        author: `User ${i + 1}`,
        publish_date: new Date().toISOString(),
        reading_time: 1,
        content_type: 'tweet'
      });
    }
    
    res.json({
      articles,
      total_count: articles.length,
      source: 'twitter_bookmarks'
    });
  });
  
  // Health check
  app.get('/health', (req, res) => {
    res.json({ status: 'healthy', redis: 'connected' });
  });
  
  const server = app.listen(port);
  
  // Control methods
  return {
    close: () => server.close(),
    getRequestCount: () => requestCount,
    setErrorMode: (enabled) => { errorMode = enabled; },
    setRateLimitMode: (enabled) => { rateLimitMode = enabled; },
    setFailForUrl: (url) => { failUrls.add(url); },
    clearCache: () => { cache.clear(); },
    reset: () => {
      requestCount = 0;
      errorMode = false;
      rateLimitMode = false;
      failUrls.clear();
      cache.clear();
    }
  };
}

module.exports = { setupMockServer };