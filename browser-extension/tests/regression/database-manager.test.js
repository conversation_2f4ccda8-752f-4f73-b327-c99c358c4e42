/**
 * Regression test for DatabaseManager compilation errors
 * Issue: DatabaseManager() constructor calls failed, should use .shared
 * Fix: Made DatabaseManager a singleton with static shared instance
 */

const fs = require('fs');
const path = require('path');

describe('DatabaseManager Singleton Regression Tests', () => {
  const projectRoot = path.join(__dirname, '../../..');

  test('macOS DatabaseManager should use static shared instance', () => {
    const dbManagerPath = path.join(projectRoot, 'macos-app/PocketNext/Services/DatabaseManager.swift');
    const content = fs.readFileSync(dbManagerPath, 'utf8');
    
    // Check for static shared instance
    expect(content).toContain('static let shared = DatabaseManager()');
    expect(content).toContain('actor DatabaseManager');
    
    // Ensure no public init (singleton pattern)
    expect(content).not.toContain('public init()');
  });

  test('all macOS Swift files should use DatabaseManager.shared', () => {
    const servicesDir = path.join(projectRoot, 'macos-app/PocketNext/Services');
    const viewsDir = path.join(projectRoot, 'macos-app/PocketNext/Views');
    
    const checkDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        if (file.endsWith('.swift') && file !== 'DatabaseManager.swift') {
          const filePath = path.join(dir, file);
          const content = fs.readFileSync(filePath, 'utf8');
          
          // If file references DatabaseManager, it should use .shared
          if (content.includes('DatabaseManager') && !content.includes('class DatabaseManager')) {
            expect(content).toContain('DatabaseManager.shared');
            expect(content).not.toMatch(/DatabaseManager\(\)/);
          }
        }
      });
    };
    
    checkDirectory(servicesDir);
    checkDirectory(viewsDir);
  });

  test('iOS app should also use DatabaseManager.shared pattern', () => {
    const iosDbManagerPath = path.join(projectRoot, 'ios-app/PocketNext/Services/DatabaseService.swift');
    if (fs.existsSync(iosDbManagerPath)) {
      const content = fs.readFileSync(iosDbManagerPath, 'utf8');
      
      // Check for proper singleton pattern
      expect(content).toContain('static let shared');
      expect(content).not.toContain('public init()');
    }
  });
});