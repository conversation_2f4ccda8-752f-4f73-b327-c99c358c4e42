/**
 * Regression test for browser extension icon loading issue
 * Issue: Extension failed to load with "Could not load icon" error
 * Fix: Generated proper PNG icons from SVG
 */

const fs = require('fs');
const path = require('path');

describe('Extension Icon Loading Regression Tests', () => {
  const iconSizes = [16, 32, 48, 128];
  const publicDir = path.join(__dirname, '../../public');

  test('all required icon files should exist', () => {
    iconSizes.forEach(size => {
      const iconPath = path.join(publicDir, `icon-${size}.png`);
      expect(fs.existsSync(iconPath)).toBe(true);
    });
  });

  test('icon files should be valid PNG files', () => {
    iconSizes.forEach(size => {
      const iconPath = path.join(publicDir, `icon-${size}.png`);
      const buffer = fs.readFileSync(iconPath);
      
      // PNG files start with these bytes
      const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
      const fileSignature = buffer.slice(0, 8);
      
      expect(fileSignature.equals(pngSignature)).toBe(true);
    });
  });

  test('manifest.json should reference PNG icons not SVG', () => {
    const manifestPath = path.join(__dirname, '../../manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    expect(manifest.icons).toBeDefined();
    expect(manifest.icons['16']).toBe('public/icon-16.png');
    expect(manifest.icons['32']).toBe('public/icon-32.png');
    expect(manifest.icons['48']).toBe('public/icon-48.png');
    expect(manifest.icons['128']).toBe('public/icon-128.png');
    
    // Ensure no SVG references
    const manifestString = JSON.stringify(manifest);
    expect(manifestString).not.toContain('.svg');
  });

  test('manifest.json should not have module type for background script', () => {
    const manifestPath = path.join(__dirname, '../../manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    expect(manifest.background).toBeDefined();
    expect(manifest.background.service_worker).toBe('src/background.js');
    expect(manifest.background.type).toBeUndefined(); // Should not have type: "module"
  });
});