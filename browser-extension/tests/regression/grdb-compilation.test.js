/**
 * Regression test for GRDB compilation errors
 * Issue: GRDB's StatementAuthorizer.swift couldn't find 'strcmp' function
 * Fix: Added bridging header to import C standard library functions
 */

const fs = require('fs');
const path = require('path');

describe('GRDB Compilation Fix Regression Tests', () => {
  const projectRoot = path.join(__dirname, '../../..');

  test('macOS app should have bridging header for C imports', () => {
    const bridgingHeaderPath = path.join(projectRoot, 'macos-app/PocketNext/PocketNext-Bridging-Header.h');
    expect(fs.existsSync(bridgingHeaderPath)).toBe(true);
    
    const content = fs.readFileSync(bridgingHeaderPath, 'utf8');
    // Check for required C standard library imports
    expect(content).toContain('#include <string.h>');
    expect(content).toContain('#include <stdlib.h>');
    expect(content).toContain('#include <stdio.h>');
  });

  test('macOS Xcode project should reference bridging header', () => {
    const pbxprojPath = path.join(projectRoot, 'macos-app/PocketNext.xcodeproj/project.pbxproj');
    const content = fs.readFileSync(pbxprojPath, 'utf8');
    
    expect(content).toContain('SWIFT_OBJC_BRIDGING_HEADER = "PocketNext/PocketNext-Bridging-Header.h"');
  });

  test('Package.swift should use GRDB version 6.29.3 or higher', () => {
    const packagePath = path.join(projectRoot, 'macos-app/Package.swift');
    const content = fs.readFileSync(packagePath, 'utf8');
    
    // Check for GRDB dependency with proper version
    const grdbMatch = content.match(/\.package\(url:\s*"[^"]*GRDB\.swift[^"]*",\s*from:\s*"([^"]+)"\)/);
    expect(grdbMatch).toBeTruthy();
    
    if (grdbMatch) {
      const version = grdbMatch[1];
      const [major, minor, patch] = version.split('.').map(Number);
      
      // Should be at least 6.29.3
      expect(major).toBeGreaterThanOrEqual(6);
      if (major === 6) {
        expect(minor).toBeGreaterThanOrEqual(29);
        if (minor === 29) {
          expect(patch).toBeGreaterThanOrEqual(3);
        }
      }
    }
  });
});