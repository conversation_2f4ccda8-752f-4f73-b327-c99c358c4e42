/**
 * Regression test for missing Xcode project files
 * Issue: No Xcode project files existed, only Swift Package Manager files
 * Fix: Created proper .xcodeproj files for both macOS and iOS apps
 */

const fs = require('fs');
const path = require('path');

describe('Xcode Project Files Regression Tests', () => {
  const projectRoot = path.join(__dirname, '../../..');

  test('macOS app should have Xcode project file', () => {
    const xcodeProjectPath = path.join(projectRoot, 'macos-app/PocketNext.xcodeproj');
    expect(fs.existsSync(xcodeProjectPath)).toBe(true);
    
    const pbxprojPath = path.join(xcodeProjectPath, 'project.pbxproj');
    expect(fs.existsSync(pbxprojPath)).toBe(true);
  });

  test('iOS app should have Xcode project file', () => {
    const xcodeProjectPath = path.join(projectRoot, 'ios-app/PocketNext.xcodeproj');
    expect(fs.existsSync(xcodeProjectPath)).toBe(true);
    
    const pbxprojPath = path.join(xcodeProjectPath, 'project.pbxproj');
    expect(fs.existsSync(pbxprojPath)).toBe(true);
  });

  test('macOS Xcode project should have proper configuration', () => {
    const pbxprojPath = path.join(projectRoot, 'macos-app/PocketNext.xcodeproj/project.pbxproj');
    const content = fs.readFileSync(pbxprojPath, 'utf8');
    
    // Check for essential project settings
    expect(content).toContain('com.pocketnext.macos');
    expect(content).toContain('PRODUCT_NAME = "$(TARGET_NAME)"');
    expect(content).toContain('MACOSX_DEPLOYMENT_TARGET');
    expect(content).toContain('PocketNext.entitlements');
  });

  test('iOS Xcode project should have proper configuration', () => {
    const pbxprojPath = path.join(projectRoot, 'ios-app/PocketNext.xcodeproj/project.pbxproj');
    const content = fs.readFileSync(pbxprojPath, 'utf8');
    
    // Check for essential project settings
    expect(content).toContain('com.pocketnext.ios');
    expect(content).toContain('PRODUCT_NAME = "$(TARGET_NAME)"');
    expect(content).toContain('IPHONEOS_DEPLOYMENT_TARGET');
    expect(content).toContain('Info.plist');
  });

  test('both apps should still have Package.swift for SPM compatibility', () => {
    const macosPackage = path.join(projectRoot, 'macos-app/Package.swift');
    const iosPackage = path.join(projectRoot, 'ios-app/Package.swift');
    
    expect(fs.existsSync(macosPackage)).toBe(true);
    expect(fs.existsSync(iosPackage)).toBe(true);
  });
});