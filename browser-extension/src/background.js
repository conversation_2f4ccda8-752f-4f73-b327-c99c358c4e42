/**
 * Background service worker for Pocket-next browser extension
 * Handles save commands and native messaging
 */

import { isNativeMessagingAvailable, sendCapturedContent } from './native-messaging.js';

// Initialize listeners only in browser context
if (typeof chrome !== 'undefined' && chrome.commands) {
  // Listen for keyboard shortcut
  chrome.commands.onCommand.addListener((command) => {
    if (command === 'save-page') {
      savePage();
    }
  });

  // Listen for extension icon click
  chrome.action.onClicked.addListener(() => {
    savePage();
  });

  // Listen for messages from popup
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('[Background] Received message:', request);
    
    if (request.command === 'save-page') {
      console.log('[Background] Handling save-page command');
      savePage().then(() => {
        console.log('[Background] Save page completed');
        sendResponse({ success: true });
      }).catch(error => {
        console.error('[Background] Save page failed:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true; // Keep channel open for async response
    } else if (request.command === 'save-twitter-bookmarks') {
      console.log('[Background] Handling save-twitter-bookmarks command');
      saveTwitterBookmarks().then(() => {
        console.log('[Background] Save Twitter bookmarks completed');
        sendResponse({ success: true });
      }).catch(error => {
        console.error('[Background] Save Twitter bookmarks failed:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true; // Keep channel open for async response
    }
  });
}

async function savePage() {
  try {
    // Get the active tab
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    
    if (!tab || !tab.id) {
      console.error('No active tab found');
      return;
    }
    
    // Show immediate feedback
    chrome.action.setBadgeText({text: '...', tabId: tab.id});
    chrome.action.setBadgeBackgroundColor({color: '#2196F3'});
    
    // Inject content script if needed
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: ['src/content-capture.js']
      });
    } catch (e) {
      // Script might already be injected
      console.log('Content script may already be injected');
    }
    
    // Capture the page
    const captured = await chrome.tabs.sendMessage(tab.id, {action: 'capture'});
    
    if (!captured) {
      throw new Error('Failed to capture page content');
    }
    
    // Send to native app
    const response = await sendToNativeApp({
      type: 'save',
      data: captured
    });
    
    if (response && response.success) {
      // Success feedback
      chrome.action.setBadgeText({text: '✓', tabId: tab.id});
      chrome.action.setBadgeBackgroundColor({color: '#4CAF50'});
      
      // Send feedback to content script
      chrome.tabs.sendMessage(tab.id, {
        action: 'save-feedback',
        success: true
      });
      
      // Clear badge after 2 seconds
      setTimeout(() => {
        chrome.action.setBadgeText({text: '', tabId: tab.id});
      }, 2000);
    } else {
      throw new Error(response?.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('Failed to save:', error);
    
    // Error feedback
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    if (tab && tab.id) {
      chrome.action.setBadgeText({text: '✗', tabId: tab.id});
      chrome.action.setBadgeBackgroundColor({color: '#F44336'});
      
      // Send feedback to content script
      chrome.tabs.sendMessage(tab.id, {
        action: 'save-feedback',
        success: false
      });
      
      // Clear badge after 2 seconds
      setTimeout(() => {
        chrome.action.setBadgeText({text: '', tabId: tab.id});
      }, 2000);
    }
  }
}

async function sendToNativeApp(message) {
  console.log('[Extension] Attempting to send message:', message.type);
  
  // First, try native messaging to macOS app
  try {
    const nativeAvailable = await isNativeMessagingAvailable();
    console.log('[Extension] Native messaging available:', nativeAvailable);
    
    if (nativeAvailable) {
      console.log('[Extension] Using native messaging');
      const response = await sendCapturedContent(message.data);
      
      if (response && response.success) {
        console.log('[Extension] Native messaging succeeded');
        return response;
      }
    }
  } catch (error) {
    console.log('[Extension] Native messaging failed:', error);
  }
  
  // Fallback to direct parse server communication
  console.log('[Extension] Falling back to parse server');
  try {
    const parseServerUrl = await getParseServerUrl();
    
    // Use different endpoint for Twitter bookmarks
    const endpoint = message.type === 'save-twitter-bookmarks' 
      ? '/parse-twitter-bookmarks' 
      : '/parse';
    
    const response = await fetch(`${parseServerUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: message.data.url,
        html_content: message.data.htmlContent,
        content_type: message.data.metadata.type,
        metadata: {
          ...message.data.metadata,
          user_id: await getUserId()
        }
      })
    });
    
    if (!response.ok) {
      throw new Error(`Parse server error: ${response.status}`);
    }
    
    const parsed = await response.json();
    
    // Handle bulk Twitter bookmarks
    if (parsed.articles && Array.isArray(parsed.articles)) {
      // Store each article
      for (const article of parsed.articles) {
        await storeArticle(article, {
          ...message.data,
          url: article.url || message.data.url,
          title: article.title
        });
      }
      return { success: true, savedCount: parsed.articles.length };
    } else {
      // Single article
      await storeArticle(parsed, message.data);
      return { success: true, article: parsed };
    }
    
  } catch (error) {
    console.error('Native app communication failed:', error);
    
    // Fallback: store raw content for later processing
    await storeRawContent(message.data);
    
    return { success: true, queued: true };
  }
}

async function getParseServerUrl() {
  // Get from storage or use default
  const { parseServerUrl } = await chrome.storage.sync.get('parseServerUrl');
  return parseServerUrl || 'http://localhost:8000';
}

async function getUserId() {
  // Get or generate user ID
  let { userId } = await chrome.storage.sync.get('userId');
  if (!userId) {
    userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await chrome.storage.sync.set({ userId });
  }
  return userId;
}

async function storeArticle(parsed, raw) {
  // Store in extension storage for offline access
  const article = {
    id: `article_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    ...parsed,
    url: raw.url,
    savedAt: new Date().toISOString(),
    rawHtml: raw.htmlContent,
    synced: false
  };
  
  // Get existing articles
  const { articles = [] } = await chrome.storage.local.get('articles');
  
  // Add new article
  articles.unshift(article);
  
  // Keep only last 1000 articles in extension storage
  if (articles.length > 1000) {
    articles.length = 1000;
  }
  
  // Save
  await chrome.storage.local.set({ articles });
  
  return article;
}

async function storeRawContent(data) {
  // Store raw content for later processing
  const item = {
    id: `raw_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    ...data,
    savedAt: new Date().toISOString(),
    processed: false
  };
  
  // Get existing queue
  const { queue = [] } = await chrome.storage.local.get('queue');
  
  // Add to queue
  queue.push(item);
  
  // Save
  await chrome.storage.local.set({ queue });
  
  return item;
}

// Process queued items when online
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onStartup.addListener(processQueue);
  chrome.runtime.onInstalled.addListener(processQueue);
}

async function processQueue() {
  const { queue = [] } = await chrome.storage.local.get('queue');
  
  if (queue.length === 0) return;
  
  const processed = [];
  const failed = [];
  
  for (const item of queue) {
    try {
      const response = await sendToNativeApp({
        type: 'save',
        data: item
      });
      
      if (response && response.success) {
        processed.push(item.id);
      } else {
        failed.push(item);
      }
    } catch (error) {
      failed.push(item);
    }
  }
  
  // Update queue
  await chrome.storage.local.set({ queue: failed });
  
  console.log(`Processed ${processed.length} queued items, ${failed.length} failed`);
}

// Context menu for saving
if (typeof chrome !== 'undefined' && chrome.contextMenus) {
  chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
      id: 'save-to-pocketnext',
      title: 'Save to Pocket-next',
      contexts: ['page', 'link', 'image', 'video']
    });
  });

  chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === 'save-to-pocketnext') {
      if (info.linkUrl) {
        // Save linked page
        saveUrl(info.linkUrl);
      } else {
        // Save current page
        savePage();
      }
    }
  });
}

async function saveUrl(url) {
  // TODO: Implement saving arbitrary URLs
  console.log('Saving URL:', url);
}

async function saveTwitterBookmarks() {
  try {
    // Get the active tab
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    
    if (!tab || !tab.id) {
      console.error('No active tab found');
      return;
    }
    
    // Check if we're on Twitter bookmarks page
    if (!tab.url?.includes('/bookmarks')) {
      console.error('Not on Twitter bookmarks page');
      return;
    }
    
    // Show immediate feedback
    chrome.action.setBadgeText({text: '...', tabId: tab.id});
    chrome.action.setBadgeBackgroundColor({color: '#1DA1F2'});
    
    // Inject content script if needed
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: ['src/content-capture.js']
      });
    } catch (e) {
      console.log('Content script may already be injected');
    }
    
    // Capture the bookmarks page
    const captured = await chrome.tabs.sendMessage(tab.id, {action: 'capture'});
    
    if (!captured) {
      throw new Error('Failed to capture bookmarks');
    }
    
    // Add special handling for Twitter bookmarks
    captured.metadata.isBulkCapture = true;
    captured.metadata.captureNote = 'Twitter Bookmarks Import';
    
    // Send to parse server with special flag
    const response = await sendToNativeApp({
      type: 'save-twitter-bookmarks',
      data: captured
    });
    
    if (response && response.success) {
      // Success feedback
      chrome.action.setBadgeText({text: '✓', tabId: tab.id});
      chrome.action.setBadgeBackgroundColor({color: '#4CAF50'});
      
      // Clear badge after 2 seconds
      setTimeout(() => {
        chrome.action.setBadgeText({text: '', tabId: tab.id});
      }, 2000);
      
      // Show notification if multiple items saved
      if (response.savedCount > 1) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'public/icon-128.png',
          title: 'Twitter Bookmarks Saved',
          message: `Successfully saved ${response.savedCount} bookmarks`
        });
      }
    } else {
      throw new Error(response?.error || 'Unknown error');
    }
    
  } catch (error) {
    console.error('Failed to save Twitter bookmarks:', error);
    
    // Error feedback
    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    if (tab && tab.id) {
      chrome.action.setBadgeText({text: '✗', tabId: tab.id});
      chrome.action.setBadgeBackgroundColor({color: '#F44336'});
      
      setTimeout(() => {
        chrome.action.setBadgeText({text: '', tabId: tab.id});
      }, 2000);
    }
  }
}

// Export functions for testing (only in Node.js environment)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    savePage,
    sendToNativeApp,
    storeArticle,
    processQueue,
    saveTwitterBookmarks,
    getParseServerUrl,
    getUserId,
    storeRawContent,
    saveUrl
  };
}