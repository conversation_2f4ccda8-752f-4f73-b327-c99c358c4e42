/**
 * Universal DOM capture for Pocket-next
 * Captures everything and lets GPT-4o mini figure it out
 */

class UniversalCapture {
  async capture() {
    const contentType = this.detectContentType();
    
    // Capture everything - let GPT-4o mini figure it out
    const capturedData = {
      url: window.location.href,
      title: document.title,
      timestamp: new Date().toISOString(),
      
      // Get the full DOM but clean it first
      htmlContent: this.getCleanDOM(),
      
      // Basic metadata for fallback
      metadata: {
        description: document.querySelector('meta[name="description"]')?.content,
        author: document.querySelector('meta[name="author"]')?.content,
        publishDate: this.findPublishDate(),
        type: contentType,
        // Add hints for specific content types
        hints: this.getContentHints(contentType)
      }
    };
    
    return capturedData;
  }
  
  getContentHints(contentType) {
    const hints = {
      pageType: contentType,
      scrollHeight: document.documentElement.scrollHeight,
      hasInfiniteScroll: false
    };
    
    // Twitter-specific hints
    if (contentType === 'twitter' || contentType === 'twitter-bookmarks') {
      hints.isBookmarksPage = contentType === 'twitter-bookmarks';
      hints.tweetCount = document.querySelectorAll('article[data-testid="tweet"]').length;
      hints.hasThreads = document.querySelectorAll('[data-testid="tweetText"]').length > 1;
      
      // Check for infinite scroll
      const scrollContainer = document.querySelector('[data-testid="primaryColumn"]');
      if (scrollContainer) {
        hints.hasInfiniteScroll = scrollContainer.scrollHeight > window.innerHeight * 2;
      }
    }
    
    return hints;
  }
  
  getCleanDOM() {
    // Clone to avoid modifying the page
    const cloned = document.documentElement.cloneNode(true);
    
    // Remove only truly useless elements
    const removeSelectors = [
      'script', 'style', 'iframe', 'noscript',
      '[style*="display:none"]', '[style*="visibility:hidden"]'
    ];
    
    removeSelectors.forEach(selector => {
      cloned.querySelectorAll(selector).forEach(el => el.remove());
    });
    
    // Get the HTML string
    return cloned.innerHTML;
  }
  
  detectContentType() {
    const url = window.location.href;
    const hostname = window.location.hostname;
    
    const patterns = {
      'twitter': /twitter\.com|x\.com/,
      'youtube': /youtube\.com|youtu\.be/,
      'github': /github\.com/,
      'reddit': /reddit\.com/,
      'medium': /medium\.com/,
      'article': /article|post|blog|news/i
    };
    
    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(url)) {
        // Special handling for Twitter bookmarks page
        if (type === 'twitter' && url.includes('/bookmarks')) {
          return 'twitter-bookmarks';
        }
        return type;
      }
    }
    
    return 'generic';
  }
  
  findPublishDate() {
    // Try common selectors but don't stress about it
    const selectors = [
      'meta[property="article:published_time"]',
      'meta[name="publish_date"]',
      'time[datetime]',
      '.published-date',
      '.post-date'
    ];
    
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.getAttribute('content') || 
               element.getAttribute('datetime') || 
               element.textContent;
      }
    }
    
    return null;
  }
}

// Message handler
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'capture') {
      const capture = new UniversalCapture();
      capture.capture().then(sendResponse);
      return true; // Async response
    }
  });
}

// Visual feedback for save action
function showSaveIndicator(success = true) {
  const indicator = document.createElement('div');
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: ${success ? '#4CAF50' : '#F44336'};
    color: white;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    z-index: 999999;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  `;
  
  indicator.textContent = success ? '✓ Saved to Pocket-next' : '✗ Failed to save';
  
  // Add animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `;
  document.head.appendChild(style);
  
  document.body.appendChild(indicator);
  
  // Remove after 3 seconds
  setTimeout(() => {
    indicator.style.animation = 'slideIn 0.3s ease-out reverse';
    setTimeout(() => {
      indicator.remove();
      style.remove();
    }, 300);
  }, 3000);
}

// Listen for save feedback
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'save-feedback') {
      showSaveIndicator(request.success);
    }
  });
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { UniversalCapture };
}