/**
 * Native messaging handler for communication with macOS app
 */

// Native messaging host ID
const NATIVE_HOST_ID = 'com.pocketnext.nativemessaging';

// Check if native messaging is available
export async function isNativeMessagingAvailable() {
  try {
    // Try to ping the native host
    const response = await chrome.runtime.sendNativeMessage(
      NATIVE_HOST_ID,
      { type: 'ping' }
    );
    console.log('Native messaging ping response:', response);
    return response?.type === 'pong';
  } catch (error) {
    console.log('Native messaging not available:', error);
    return false;
  }
}

// Send message to native app
export async function sendToNativeApp(message) {
  console.log('Sending to native app:', message);
  
  try {
    const response = await chrome.runtime.sendNativeMessage(
      NATIVE_HOST_ID,
      message
    );
    
    console.log('Native app response:', response);
    
    if (response?.type === 'error') {
      throw new Error(response.data || 'Native messaging error');
    }
    
    return response;
  } catch (error) {
    console.error('Native messaging failed:', error);
    throw error;
  }
}

// Get native app status
export async function getNativeAppStatus() {
  try {
    const response = await chrome.runtime.sendNativeMessage(
      NATIVE_HOST_ID,
      { type: 'getStatus' }
    );
    
    return response;
  } catch (error) {
    console.error('Failed to get native app status:', error);
    return null;
  }
}

// Send captured content to native app
export async function sendCapturedContent(capturedData) {
  console.log('Sending captured content to native app');
  
  const message = {
    type: 'capture',
    data: {
      url: capturedData.url,
      title: capturedData.title,
      htmlContent: capturedData.htmlContent,
      timestamp: capturedData.timestamp,
      metadata: capturedData.metadata
    }
  };
  
  return sendToNativeApp(message);
}