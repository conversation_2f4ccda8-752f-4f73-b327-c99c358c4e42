# Debug Native Messaging Setup

## 1. Check Extension ID

After installing the browser extension in Chrome:
1. Go to `chrome://extensions/`
2. Enable "Developer mode"
3. Find "Pocket-next" extension
4. Copy the Extension ID (looks like: `abcdefghijklmnopqrstuvwxyzabcdef`)

## 2. Update Native Host Manifest

The macOS app needs to know your extension ID. Currently it has placeholder IDs.

### Find the manifest file:
```bash
cat ~/Library/Application\ Support/Google/Chrome/NativeMessagingHosts/com.pocketnext.nativemessaging.json
```

### Update it with your extension ID:
```json
{
  "name": "com.pocketnext.nativemessaging",
  "description": "Pocket-next Native Messaging Host",
  "path": "/path/to/PocketNext.app/Contents/MacOS/PocketNext",
  "type": "stdio",
  "allowed_origins": [
    "chrome-extension://YOUR_ACTUAL_EXTENSION_ID/"
  ]
}
```

## 3. Test Native Messaging

### From Chrome Console:

1. Open the extension's background page:
   - Go to `chrome://extensions/`
   - Click "background page" link for Pocket-next

2. In the console, test:
```javascript
// Test ping
chrome.runtime.sendNativeMessage('com.pocketnext.nativemessaging', 
  { type: 'ping' }, 
  response => console.log('Response:', response)
);

// Check Chrome errors
chrome.runtime.lastError
```

## 4. Debug Logging Locations

### Browser Extension Console:
- Look for `[Extension]` prefixed logs
- Check for native messaging errors

### macOS App Console:
- Look for `🔌 NativeMessaging:` prefixed logs
- Run app from Xcode to see all console output

## 5. Common Issues

### "Native host not found"
- Extension ID doesn't match in manifest
- Manifest file in wrong location
- App path incorrect in manifest

### "Specified native messaging host not found"
- App not installed or path wrong
- Manifest JSON syntax error

### No response from native app
- App not handling stdin properly
- Message format mismatch

## 6. Manual Test

### Create test script:
```bash
# test-native-messaging.sh
echo '{"type":"ping"}' | /Applications/PocketNext.app/Contents/MacOS/PocketNext
```

This should output response from the app.

## 7. Fix Steps

1. **Get your extension ID** from Chrome
2. **Update NativeMessaging.swift** with correct ID:
   ```swift
   allowed_origins: [
       "chrome-extension://YOUR_EXTENSION_ID/"
   ]
   ```
3. **Rebuild and run** the macOS app
4. **Reload** the extension
5. **Test** with ping command

## Debug Output Expected

### When working correctly:

**Extension console:**
```
[Extension] Attempting to send message: save
[Extension] Native messaging available: true
[Extension] Using native messaging
[Extension] Native messaging succeeded
```

**macOS app console:**
```
🔌 NativeMessaging: Received data on stdin, size: XX bytes
🔌 NativeMessaging: Message length: XX bytes
🔌 NativeMessaging: ✅ Decoded message type: capture
🔌 NativeMessaging: 📥 Capture message received
🔌 NativeMessaging: Processing capture message
```