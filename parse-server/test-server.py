#!/usr/bin/env python3
"""Test script to verify parse server is working"""

import requests
import json

# Test health endpoint
print("Testing parse server...")
try:
    response = requests.get("http://localhost:8000/health")
    print(f"Health check: {response.json()}")
except Exception as e:
    print(f"❌ Server not running or accessible: {e}")
    print("\nTo start the server:")
    print("cd parse-server")
    print("python main.py")
    exit(1)

# Test parse endpoint with sample data
test_data = {
    "url": "https://example.com/test-article",
    "html_content": """
    <html>
    <body>
        <h1>Test Article</h1>
        <p>This is a test article content for verifying the parse server.</p>
        <p>It should extract this text and create a summary.</p>
    </body>
    </html>
    """,
    "content_type": "article",
    "metadata": {
        "user_id": "test_user",
        "title": "Test Article",
        "description": "A test article for parse server verification"
    }
}

print("\nTesting parse endpoint...")
try:
    response = requests.post(
        "http://localhost:8000/parse",
        json=test_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Parse successful!")
        print(f"Title: {result['title']}")
        print(f"Summary: {result['summary']}")
        print(f"Keywords: {', '.join(result['keywords'])}")
    else:
        print(f"❌ Parse failed with status {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"❌ Parse request failed: {e}")

print("\nServer is working correctly! ✅")