"""
End-to-End tests for the Parse Server API
Tests complete user flows from article submission to retrieval
"""

import pytest
import asyncio
import aiohttp
import time
import json
from datetime import datetime
from typing import List, Dict, Any
import websockets
from concurrent.futures import ThreadPoolExecutor
import requests
from bs4 import BeautifulSoup


class TestParseServerE2E:
    """E2E tests for parse server functionality"""
    
    @pytest.fixture
    async def api_client(self):
        """Create an async HTTP client session"""
        async with aiohttp.ClientSession() as session:
            yield session
    
    @pytest.fixture
    def test_articles(self):
        """Sample articles for testing"""
        return [
            {
                "url": "https://example.com/article1",
                "title": "Understanding Async Python",
                "htmlContent": """
                <html>
                    <head><title>Understanding Async Python</title></head>
                    <body>
                        <h1>Understanding Async Python</h1>
                        <p>Async programming in Python has revolutionized how we write concurrent code.</p>
                        <p>With async/await syntax, we can write cleaner, more efficient programs.</p>
                    </body>
                </html>
                """,
                "metadata": {
                    "author": "Python Expert",
                    "publishDate": "2024-01-01",
                    "type": "article"
                }
            },
            {
                "url": "https://example.com/article2",
                "title": "Machine Learning Basics",
                "htmlContent": """
                <html>
                    <head><title>Machine Learning Basics</title></head>
                    <body>
                        <h1>Machine Learning Basics</h1>
                        <p>Machine learning is transforming industries across the globe.</p>
                        <p>From healthcare to finance, ML applications are everywhere.</p>
                    </body>
                </html>
                """,
                "metadata": {
                    "author": "ML Researcher",
                    "publishDate": "2024-01-02",
                    "type": "article"
                }
            }
        ]
    
    @pytest.mark.asyncio
    async def test_complete_article_flow(self, api_client, test_server):
        """Test complete flow: submit -> parse -> retrieve"""
        article_data = {
            "url": "https://test.com/complete-flow",
            "title": "Complete Flow Test",
            "htmlContent": "<html><body><h1>Test Article</h1><p>Content here</p></body></html>",
            "metadata": {"author": "Test Author"}
        }
        
        # Submit article for parsing
        async with api_client.post(
            f"{test_server}/parse",
            json=article_data
        ) as response:
            assert response.status == 200
            result = await response.json()
            
            # Verify parsed result
            assert result["title"] == article_data["title"]
            assert "content" in result
            assert "summary" in result
            assert len(result["keywords"]) > 0
            assert result["reading_time"] > 0
    
    @pytest.mark.asyncio
    async def test_batch_processing(self, api_client, test_server, test_articles):
        """Test batch processing of multiple articles"""
        start_time = time.time()
        
        # Submit all articles concurrently
        tasks = []
        for article in test_articles:
            task = api_client.post(f"{test_server}/parse", json=article)
            tasks.append(task)
        
        # Wait for all to complete
        responses = await asyncio.gather(*tasks)
        
        # Verify all successful
        for response in responses:
            assert response.status == 200
            data = await response.json()
            assert "content" in data
            assert "summary" in data
        
        # Should complete quickly
        duration = time.time() - start_time
        assert duration < 5.0  # All articles parsed within 5 seconds
    
    @pytest.mark.asyncio
    async def test_websocket_updates(self, test_server):
        """Test real-time updates via WebSocket"""
        ws_url = test_server.replace("http", "ws") + "/ws"
        
        try:
            async with websockets.connect(ws_url) as websocket:
                # Send test article
                article = {
                    "url": "https://test.com/ws-test",
                    "title": "WebSocket Test Article"
                }
                
                await websocket.send(json.dumps({
                    "action": "parse",
                    "data": article
                }))
                
                # Wait for parsing complete message
                response = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=5.0
                )
                
                data = json.loads(response)
                assert data["type"] == "parse_complete"
                assert data["article"]["title"] == article["title"]
                
        except (websockets.exceptions.WebSocketException, OSError):
            pytest.skip("WebSocket server not available")
    
    @pytest.mark.asyncio
    async def test_concurrent_parsing(self, api_client, test_server):
        """Test server handling concurrent parse requests"""
        num_requests = 20
        
        # Create different articles
        articles = []
        for i in range(num_requests):
            articles.append({
                "url": f"https://test.com/concurrent-{i}",
                "title": f"Concurrent Article {i}",
                "htmlContent": f"<html><body><p>Content {i}</p></body></html>"
            })
        
        # Submit all concurrently
        start_time = time.time()
        tasks = [
            api_client.post(f"{test_server}/parse", json=article)
            for article in articles
        ]
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successes
        success_count = 0
        for response in responses:
            if not isinstance(response, Exception) and response.status == 200:
                success_count += 1
        
        duration = time.time() - start_time
        
        # Should handle most requests successfully
        assert success_count >= num_requests * 0.9  # 90% success rate
        assert duration < 10.0  # Complete within 10 seconds
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, api_client, test_server):
        """Test server recovery from errors"""
        
        # Send malformed request
        async with api_client.post(
            f"{test_server}/parse",
            json={"invalid": "data"}
        ) as response:
            assert response.status == 400
            error = await response.json()
            assert "error" in error
        
        # Server should still work after error
        valid_article = {
            "url": "https://test.com/after-error",
            "title": "Valid Article After Error",
            "htmlContent": "<html><body><p>Content</p></body></html>"
        }
        
        async with api_client.post(
            f"{test_server}/parse",
            json=valid_article
        ) as response:
            assert response.status == 200
            result = await response.json()
            assert result["title"] == valid_article["title"]
    
    @pytest.mark.asyncio
    async def test_content_extraction_quality(self, api_client, test_server):
        """Test quality of content extraction"""
        
        # Complex HTML with various elements
        complex_html = """
        <html>
        <head>
            <title>Complex Article Test</title>
            <meta name="author" content="Test Author">
            <meta name="description" content="Testing content extraction">
        </head>
        <body>
            <nav>Navigation menu - should be ignored</nav>
            <article>
                <h1>Main Article Title</h1>
                <p class="byline">By Test Author on January 1, 2024</p>
                
                <p>This is the first paragraph with <strong>important</strong> content.</p>
                
                <blockquote>
                    "This is a meaningful quote that should be preserved."
                </blockquote>
                
                <h2>Subsection Title</h2>
                <p>More content in a subsection with <a href="#">links</a>.</p>
                
                <ul>
                    <li>List item 1</li>
                    <li>List item 2</li>
                    <li>List item 3</li>
                </ul>
                
                <code>def example_code(): pass</code>
                
                <p>Final paragraph with conclusion.</p>
            </article>
            <footer>Footer content - should be ignored</footer>
            <div class="ads">Advertisement - should be ignored</div>
        </body>
        </html>
        """
        
        article = {
            "url": "https://test.com/complex",
            "title": "Complex Article Test",
            "htmlContent": complex_html
        }
        
        async with api_client.post(
            f"{test_server}/parse",
            json=article
        ) as response:
            assert response.status == 200
            result = await response.json()
            
            # Check content quality
            content = result["content"]
            
            # Should include main content
            assert "first paragraph" in content
            assert "important" in content
            assert "meaningful quote" in content
            assert "Subsection Title" in content
            
            # Should exclude navigation and ads
            assert "Navigation menu" not in content
            assert "Advertisement" not in content
            assert "Footer content" not in content
            
            # Should preserve structure
            assert "List item 1" in content
            assert result["reading_time"] > 0
            assert len(result["keywords"]) > 3
    
    @pytest.mark.asyncio
    async def test_different_content_types(self, api_client, test_server):
        """Test parsing different types of content"""
        
        content_types = [
            {
                "type": "news",
                "html": """
                <article>
                    <h1>Breaking News: Test Event Occurs</h1>
                    <time>2024-01-01 10:00</time>
                    <p>In a stunning development, test events have occurred.</p>
                </article>
                """
            },
            {
                "type": "blog",
                "html": """
                <div class="blog-post">
                    <h1>My Thoughts on Testing</h1>
                    <div class="meta">Posted by Blogger on Jan 1</div>
                    <p>Today I want to share my thoughts on testing...</p>
                </div>
                """
            },
            {
                "type": "documentation",
                "html": """
                <div class="docs">
                    <h1>API Documentation</h1>
                    <h2>Endpoints</h2>
                    <pre><code>GET /api/articles</code></pre>
                    <p>Returns a list of articles.</p>
                </div>
                """
            }
        ]
        
        for content_type in content_types:
            article = {
                "url": f"https://test.com/{content_type['type']}",
                "title": f"Test {content_type['type']} Content",
                "htmlContent": content_type["html"],
                "metadata": {"type": content_type["type"]}
            }
            
            async with api_client.post(
                f"{test_server}/parse",
                json=article
            ) as response:
                assert response.status == 200
                result = await response.json()
                
                # Should identify content type
                assert result["content_type"] == content_type["type"]
                assert len(result["content"]) > 0
                assert result["summary"] is not None
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, api_client, test_server):
        """Test rate limiting behavior"""
        
        # Send many requests rapidly
        article = {
            "url": "https://test.com/rate-limit",
            "title": "Rate Limit Test",
            "htmlContent": "<p>Test</p>"
        }
        
        tasks = []
        for i in range(50):  # 50 rapid requests
            task = api_client.post(f"{test_server}/parse", json=article)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count different response types
        success_count = 0
        rate_limited_count = 0
        
        for response in responses:
            if not isinstance(response, Exception):
                if response.status == 200:
                    success_count += 1
                elif response.status == 429:  # Too Many Requests
                    rate_limited_count += 1
        
        # Should have some rate limiting
        assert rate_limited_count > 0 or success_count == 50
        
        # Wait for rate limit to reset
        await asyncio.sleep(2)
        
        # Should work again
        async with api_client.post(
            f"{test_server}/parse",
            json=article
        ) as response:
            assert response.status == 200
    
    @pytest.mark.asyncio
    async def test_metrics_endpoint(self, api_client, test_server):
        """Test metrics collection and reporting"""
        
        # Parse some articles first
        for i in range(5):
            article = {
                "url": f"https://test.com/metrics-{i}",
                "title": f"Metrics Test {i}",
                "htmlContent": f"<p>Content {i}</p>"
            }
            
            async with api_client.post(
                f"{test_server}/parse",
                json=article
            ) as response:
                assert response.status == 200
        
        # Check metrics
        async with api_client.get(f"{test_server}/metrics") as response:
            assert response.status == 200
            metrics = await response.json()
            
            # Should have metrics data
            assert "total_requests" in metrics
            assert metrics["total_requests"] >= 5
            assert "average_parse_time" in metrics
            assert metrics["average_parse_time"] > 0
            assert "success_rate" in metrics
            assert metrics["success_rate"] > 0
    
    @pytest.mark.asyncio
    async def test_long_running_parse(self, api_client, test_server):
        """Test handling of long/complex articles"""
        
        # Create a very long article
        long_content = "<p>This is a paragraph. </p>" * 1000
        
        article = {
            "url": "https://test.com/long-article",
            "title": "Very Long Article",
            "htmlContent": f"<html><body>{long_content}</body></html>"
        }
        
        start_time = time.time()
        
        async with api_client.post(
            f"{test_server}/parse",
            json=article,
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            assert response.status == 200
            result = await response.json()
            
            duration = time.time() - start_time
            
            # Should complete within reasonable time
            assert duration < 10.0
            
            # Should still produce valid result
            assert len(result["content"]) > 1000
            assert result["reading_time"] > 20  # Long article
            assert len(result["summary"]) > 0
            assert len(result["summary"]) < len(result["content"])


class TestCrossAppIntegration:
    """Tests for integration between browser extension, native apps, and parse server"""
    
    @pytest.mark.asyncio
    async def test_full_system_flow(self, api_client, test_server):
        """Test complete flow across all components"""
        
        # 1. Browser extension captures article
        capture_data = {
            "url": "https://example.com/full-flow-test",
            "title": "Full System Flow Test",
            "htmlContent": """
            <html>
                <body>
                    <h1>Full System Flow Test</h1>
                    <p>This tests the complete flow from browser to app.</p>
                </body>
            </html>
            """,
            "metadata": {
                "capturedBy": "browser-extension",
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        # 2. Send to parse server
        async with api_client.post(
            f"{test_server}/parse",
            json=capture_data
        ) as response:
            assert response.status == 200
            parsed_article = await response.json()
        
        # 3. Simulate native app receiving parsed article
        native_sync_data = {
            "action": "sync",
            "articles": [parsed_article],
            "source": "browser-extension",
            "syncId": "test-sync-001"
        }
        
        # 4. Verify article is properly formatted for native app
        assert "title" in parsed_article
        assert "content" in parsed_article
        assert "summary" in parsed_article
        assert "keywords" in parsed_article
        assert "reading_time" in parsed_article
        
        # 5. Simulate sync confirmation
        sync_confirmation = {
            "syncId": "test-sync-001",
            "status": "completed",
            "articlesReceived": 1,
            "platform": "ios"
        }
        
        # This would normally go through native messaging
        # but we verify the data structure is correct
        assert sync_confirmation["articlesReceived"] == len(native_sync_data["articles"])
    
    @pytest.mark.asyncio
    async def test_offline_sync_scenario(self, api_client, test_server):
        """Test offline capture and later sync"""
        
        # Simulate articles captured while offline
        offline_articles = []
        for i in range(5):
            offline_articles.append({
                "url": f"https://example.com/offline-{i}",
                "title": f"Offline Article {i}",
                "htmlContent": f"<p>Captured while offline {i}</p>",
                "metadata": {
                    "capturedOffline": True,
                    "captureTime": datetime.utcnow().isoformat()
                }
            })
        
        # When coming back online, batch process
        parse_tasks = []
        for article in offline_articles:
            task = api_client.post(f"{test_server}/parse", json=article)
            parse_tasks.append(task)
        
        # Process all articles
        responses = await asyncio.gather(*parse_tasks)
        parsed_articles = []
        
        for response in responses:
            assert response.status == 200
            parsed = await response.json()
            parsed_articles.append(parsed)
        
        # Verify all articles were processed
        assert len(parsed_articles) == 5
        
        # Simulate batch sync to native app
        batch_sync = {
            "action": "batchSync",
            "articles": parsed_articles,
            "syncType": "offline-recovery"
        }
        
        # All articles should have required fields
        for article in batch_sync["articles"]:
            assert all(field in article for field in 
                      ["title", "content", "summary", "keywords"])
    
    @pytest.mark.asyncio
    async def test_conflict_resolution(self, api_client, test_server):
        """Test handling of sync conflicts between devices"""
        
        # Same article captured on different devices
        device_a_capture = {
            "url": "https://example.com/conflict-test",
            "title": "Article from Device A",
            "htmlContent": "<p>Content from device A</p>",
            "metadata": {
                "deviceId": "device-a",
                "timestamp": "2024-01-01T10:00:00Z"
            }
        }
        
        device_b_capture = {
            "url": "https://example.com/conflict-test",  # Same URL
            "title": "Article from Device B - Updated",
            "htmlContent": "<p>Content from device B with updates</p>",
            "metadata": {
                "deviceId": "device-b",
                "timestamp": "2024-01-01T10:05:00Z"  # 5 minutes later
            }
        }
        
        # Parse both versions
        async with api_client.post(
            f"{test_server}/parse",
            json=device_a_capture
        ) as response:
            assert response.status == 200
            version_a = await response.json()
        
        async with api_client.post(
            f"{test_server}/parse",
            json=device_b_capture
        ) as response:
            assert response.status == 200
            version_b = await response.json()
        
        # Conflict resolution (latest wins)
        if device_b_capture["metadata"]["timestamp"] > device_a_capture["metadata"]["timestamp"]:
            resolved_version = version_b
        else:
            resolved_version = version_a
        
        assert resolved_version["title"] == "Article from Device B - Updated"
        
        # Verify merge metadata
        assert resolved_version is not None
        
    
    def test_performance_under_load(self):
        """Test system performance under heavy load"""
        
        # This would typically be run separately as a load test
        # Here we demonstrate the test structure
        
        def parse_article(session, url, article_num):
            """Parse a single article"""
            article = {
                "url": f"{url}/load-test-{article_num}",
                "title": f"Load Test Article {article_num}",
                "htmlContent": f"<p>Content for load test {article_num}</p>"
            }
            
            response = session.post(f"{url}/parse", json=article)
            return response.status_code == 200
        
        # Would use this with concurrent.futures or similar
        # to simulate many users
        # Example structure:
        """
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = []
            for i in range(1000):
                future = executor.submit(parse_article, session, test_server, i)
                futures.append(future)
            
            results = [f.result() for f in futures]
            success_rate = sum(results) / len(results)
            assert success_rate > 0.95  # 95% success rate under load
        """
        
        # For unit test, just verify structure
        assert callable(parse_article)