import pytest
from bs4 import BeautifulSoup
import json
from datetime import datetime
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTTPException

from main import (
    preprocess_html,
    create_parsing_prompt,
    fallback_parse,
    RateLimiter,
    ParseRequest,
    PARSING_SYSTEM_PROMPT
)


class TestPreprocessHTML:
    """Test HTML preprocessing functionality."""
    
    def test_preprocess_removes_scripts_and_styles(self):
        """Test that scripts and styles are removed."""
        html = """
        <html>
            <head>
                <script>alert('test');</script>
                <style>body { color: red; }</style>
            </head>
            <body>
                <p>Content</p>
                <script>console.log('test');</script>
            </body>
        </html>
        """
        
        result = preprocess_html(html)
        assert "<script>" not in result
        assert "<style>" not in result
        assert "alert" not in result
        assert "console.log" not in result
        assert "Content" in result
    
    def test_preprocess_removes_attributes(self):
        """Test that unnecessary attributes are removed."""
        html = """
        <div id="test" class="container" style="color: red;" data-value="123">
            <p class="text" onclick="handleClick()">Content</p>
            <a href="/page" class="link">Link</a>
        </div>
        """
        
        result = preprocess_html(html)
        # The preprocessing extracts text content, not HTML
        assert "Content" in result
        # The Link text should be extracted (href is preserved for links)
        # Check that no style/event attributes remain
        assert 'style=' not in result
        assert 'onclick=' not in result
        assert 'data-value=' not in result
    
    def test_preprocess_collapses_whitespace(self):
        """Test that excessive whitespace is collapsed."""
        html = """
        <p>This    has     multiple     spaces</p>
        <div>
            
            
            Lots of newlines
            
            
        </div>
        """
        
        result = preprocess_html(html)
        assert "multiple     spaces" not in result
        assert "This has multiple spaces" in result
        # Should not have excessive newlines
        lines = result.strip().split('\n')
        assert all(line.strip() != '' for line in lines)
    
    def test_preprocess_removes_comments(self):
        """Test that HTML comments are removed."""
        html = """
        <div>
            <!-- This is a comment -->
            <p>Content</p>
            <!-- Another comment -->
        </div>
        """
        
        result = preprocess_html(html)
        assert "<!--" not in result
        assert "comment" not in result
        assert "Content" in result
    
    def test_preprocess_handles_malformed_html(self):
        """Test preprocessing of malformed HTML."""
        html = "<p>Unclosed paragraph <div>Nested content</p></div>"
        
        # Should not raise an error
        result = preprocess_html(html)
        assert "Unclosed paragraph" in result
        assert "Nested content" in result
    
    def test_preprocess_preserves_important_content(self):
        """Test that important content is preserved."""
        html = """
        <article>
            <h1>Main Title</h1>
            <h2>Subtitle</h2>
            <p>First paragraph with <strong>emphasis</strong>.</p>
            <blockquote>A quote</blockquote>
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
            </ul>
        </article>
        """
        
        result = preprocess_html(html)
        assert "Main Title" in result
        assert "Subtitle" in result
        assert "First paragraph" in result
        assert "emphasis" in result
        assert "A quote" in result
        assert "Item 1" in result
        assert "Item 2" in result


class TestCreateParsingPrompt:
    """Test prompt creation for different content types."""
    
    def test_article_prompt(self):
        """Test prompt creation for articles."""
        prompt = create_parsing_prompt("<p>Article content</p>", "article", "https://example.com/article")
        
        assert "URL: https://example.com/article" in prompt
        assert "<p>Article content</p>" in prompt
        assert "web content" in prompt.lower()
        assert "clean, readable format" in prompt
    
    def test_twitter_prompt(self):
        """Test prompt creation for Twitter content."""
        prompt = create_parsing_prompt("<div>Tweet content</div>", "twitter", "https://twitter.com/user/status/123")
        
        assert "Twitter/X thread" in prompt
        assert "URL: https://twitter.com/user/status/123" in prompt
        assert "<div>Tweet content</div>" in prompt
        assert "flowing narrative" in prompt
        assert "thread structure" in prompt
    
    def test_youtube_prompt(self):
        """Test prompt creation for YouTube content."""
        prompt = create_parsing_prompt("<div>Video content</div>", "youtube", "https://youtube.com/watch?v=123")
        
        assert "YouTube page/transcript" in prompt
        assert "URL: https://youtube.com/watch?v=123" in prompt
        assert "<div>Video content</div>" in prompt
        assert "organizing by topics" in prompt
    
    def test_generic_prompt(self):
        """Test prompt creation for generic content."""
        prompt = create_parsing_prompt("<div>Generic content</div>", "unknown", "https://example.com")
        
        assert "URL: https://example.com" in prompt
        assert "<div>Generic content</div>" in prompt
        assert "web content" in prompt.lower()
        assert "clean, readable format" in prompt


class TestFallbackParse:
    """Test fallback parsing functionality."""
    
    def test_fallback_parse_basic_article(self):
        """Test fallback parsing of a basic article."""
        html = """
        <html>
            <head>
                <title>Test Article Title</title>
                <meta name="author" content="John Doe">
                <meta name="description" content="Article description">
            </head>
            <body>
                <h1>Main Heading</h1>
                <p>First paragraph of content.</p>
                <p>Second paragraph with more text to increase reading time.</p>
            </body>
        </html>
        """
        
        request = ParseRequest(
            url="https://example.com/article",
            html_content=html,
            content_type="article",
            metadata={"title": "Test Article Title", "author": "John Doe"}
        )
        
        result = fallback_parse(request)
        
        assert "Test Article Title" in result.title
        assert result.author == "John Doe"
        assert "First paragraph" in result.content
        assert "Second paragraph" in result.content
        assert "https://example.com/article" in result.summary
        assert result.reading_time >= 1
        assert result.content_type == "article"
    
    def test_fallback_parse_no_metadata(self):
        """Test fallback parsing without metadata."""
        html = """
        <html>
            <body>
                <h1>Article Without Metadata</h1>
                <p>Some content here.</p>
            </body>
        </html>
        """
        
        request = ParseRequest(
            url="https://example.com",
            html_content=html,
            content_type="article",
            metadata={}
        )
        
        result = fallback_parse(request)
        
        assert "Article Without Metadata" in result.title  # From h1
        assert result.author is None
        assert "Some content here" in result.content
        assert "https://example.com" in result.summary
    
    def test_fallback_parse_empty_html(self):
        """Test fallback parsing with empty HTML."""
        request = ParseRequest(
            url="https://example.com",
            html_content="",
            content_type="article",
            metadata={}
        )
        
        result = fallback_parse(request)
        
        assert result.title == "https://example.com"  # Falls back to URL
        assert result.content == ""  # No content to extract
        assert result.reading_time == 1  # Default minimum
    
    def test_fallback_parse_reading_time_calculation(self):
        """Test reading time calculation."""
        # Create content with approximately 250 words (1 minute reading time)
        words = ["word"] * 250
        html = f"<p>{' '.join(words)}</p>"
        
        request = ParseRequest(
            url="https://example.com",
            html_content=html,
            content_type="article",
            metadata={}
        )
        
        result = fallback_parse(request)
        assert result.reading_time == 1
        
        # Test with ~500 words (2 minutes)
        words = ["word"] * 500
        html = f"<p>{' '.join(words)}</p>"
        
        request.html_content = html
        result = fallback_parse(request)
        assert result.reading_time == 2
    
    def test_fallback_parse_with_metadata(self):
        """Test fallback parsing with metadata."""
        request = ParseRequest(
            url="https://example.com",
            html_content="<p>Some content</p>",
            content_type="article",
            metadata={
                "title": "Metadata Title",
                "author": "Metadata Author",
                "publishDate": "2024-01-01"
            }
        )
        
        result = fallback_parse(request)
        
        assert result.title == "Metadata Title"
        assert result.author == "Metadata Author"
        assert result.publish_date == "2024-01-01"
        assert result.keywords == ["article", "unprocessed"]


class TestRateLimiter:
    """Test rate limiting functionality."""
    
    @pytest.mark.asyncio
    async def test_rate_limiter_allows_initial_requests(self):
        """Test that rate limiter allows initial requests."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 5
        
        user_id = "test_user"
        # First 5 requests should pass without raising exception
        for i in range(5):
            await limiter.check_rate_async(user_id)  # Should not raise
    
    @pytest.mark.asyncio
    async def test_rate_limiter_blocks_excess_requests(self):
        """Test that rate limiter blocks requests over limit."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 3
        
        user_id = "test_user"
        # First 3 requests should pass
        for i in range(3):
            await limiter.check_rate_async(user_id)
        
        # 4th request should raise HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await limiter.check_rate_async(user_id)
        assert exc_info.value.status_code == 429
        assert "rate limit" in exc_info.value.detail.lower()
    
    @pytest.mark.asyncio
    async def test_rate_limiter_different_users(self):
        """Test that rate limits are per-user."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 2
        
        # User 1 makes 2 requests
        await limiter.check_rate_async("user1")
        await limiter.check_rate_async("user1")
        
        # 3rd request from user1 should fail
        with pytest.raises(HTTPException):
            await limiter.check_rate_async("user1")
        
        # User 2 should still be able to make requests
        await limiter.check_rate_async("user2")
        await limiter.check_rate_async("user2")
    
    @pytest.mark.asyncio
    @patch('time.time')
    async def test_rate_limiter_window_expiry(self, mock_time):
        """Test that rate limit resets after window expires."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 2
        
        # Set initial time
        mock_time.return_value = 1000
        
        user_id = "test_user"
        await limiter.check_rate_async(user_id)
        await limiter.check_rate_async(user_id)
        
        # 3rd request should fail
        with pytest.raises(HTTPException):
            await limiter.check_rate_async(user_id)
        
        # Advance time past the window (61 seconds)
        mock_time.return_value = 1061
        
        # Should be able to make requests again
        await limiter.check_rate_async(user_id)  # Should not raise


class TestParsingSystemPrompt:
    """Test the parsing system prompt constant."""
    
    def test_system_prompt_contains_required_fields(self):
        """Test that system prompt specifies all required fields."""
        required_fields = [
            "title", "content", "summary", "keywords",
            "author", "publish_date", "reading_time", "content_type"
        ]
        
        for field in required_fields:
            assert field in PARSING_SYSTEM_PROMPT
    
    def test_system_prompt_json_structure(self):
        """Test that system prompt includes JSON structure example."""
        assert "{" in PARSING_SYSTEM_PROMPT
        assert "}" in PARSING_SYSTEM_PROMPT
        assert '"title": "string"' in PARSING_SYSTEM_PROMPT
        assert '"keywords": ["array"' in PARSING_SYSTEM_PROMPT
    
    def test_system_prompt_guidelines(self):
        """Test that system prompt includes important guidelines."""
        assert "markdown" in PARSING_SYSTEM_PROMPT.lower()
        assert "Twitter/X threads" in PARSING_SYSTEM_PROMPT
        assert "YouTube" in PARSING_SYSTEM_PROMPT
        assert "250 words per minute" in PARSING_SYSTEM_PROMPT
        assert "null for missing fields" in PARSING_SYSTEM_PROMPT


class TestPreprocessHTMLEdgeCases:
    """Test edge cases for HTML preprocessing."""
    
    def test_preprocess_very_long_content(self):
        """Test that very long content is truncated."""
        # Create content longer than 40k chars
        long_content = "<p>" + "word " * 10000 + "</p>"
        
        result = preprocess_html(long_content)
        
        assert len(result) <= 40100  # Allow for "..." at end
        assert result.endswith("...")
    
    def test_preprocess_nested_structures(self):
        """Test preprocessing of deeply nested HTML."""
        html = """
        <div>
            <div>
                <div>
                    <p>Deeply nested content</p>
                    <ul>
                        <li>Item 1
                            <ul>
                                <li>Nested item</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        """
        
        result = preprocess_html(html)
        assert "Deeply nested content" in result
        assert "Item 1" in result
        assert "Nested item" in result
    
    def test_preprocess_special_characters(self):
        """Test handling of special characters."""
        html = """
        <p>&amp; &lt; &gt; &quot; &#39; &nbsp;</p>
        <p>Unicode: 你好 مرحبا שלום</p>
        <p>Emoji: 😀 🚀 ❤️</p>
        """
        
        result = preprocess_html(html)
        assert "&" in result
        assert "<" in result
        assert ">" in result
        assert "你好" in result
        assert "😀" in result
    
    def test_preprocess_code_blocks(self):
        """Test preservation of code blocks."""
        html = """
        <pre><code>
def hello_world():
    print("Hello, World!")
        </code></pre>
        <code>inline_code()</code>
        """
        
        result = preprocess_html(html)
        assert "def hello_world():" in result
        assert 'print("Hello, World!")' in result
        assert "inline_code()" in result