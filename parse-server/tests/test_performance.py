import pytest
import asyncio
import time
from unittest.mock import patch, AsyncMock

from main import RateLimiter, preprocess_html


class TestPerformance:
    """Test performance characteristics of the parse server."""
    
    @pytest.mark.asyncio
    async def test_concurrent_rate_limiting(self):
        """Test rate limiter under concurrent load."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 10
        
        # Create 20 concurrent requests
        async def make_request(user_id, request_num):
            try:
                await limiter.check_rate_async(user_id)
                return True
            except:
                return False
        
        # Test with same user
        tasks = [make_request("user1", i) for i in range(20)]
        results = await asyncio.gather(*tasks)
        
        # Should allow 10 and block 10
        assert sum(results) == 10
        assert results.count(False) == 10
    
    @pytest.mark.asyncio
    async def test_rate_limiter_different_users_concurrent(self):
        """Test rate limiter with multiple users concurrently."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 5
        
        async def make_requests_for_user(user_id, count):
            results = []
            for i in range(count):
                try:
                    await limiter.check_rate_async(user_id)
                    results.append(True)
                except:
                    results.append(False)
            return results
        
        # 3 users making 7 requests each
        tasks = [
            make_requests_for_user("user1", 7),
            make_requests_for_user("user2", 7),
            make_requests_for_user("user3", 7)
        ]
        
        all_results = await asyncio.gather(*tasks)
        
        # Each user should get 5 successes and 2 failures
        for user_results in all_results:
            assert sum(user_results) == 5
            assert user_results.count(False) == 2
    
    def test_html_preprocessing_performance(self):
        """Test HTML preprocessing with large documents."""
        # Create a large HTML document
        large_html = "<html><body>"
        for i in range(1000):
            large_html += f"""
            <div class="article-{i}" data-id="{i}" style="color: red;">
                <h2 id="heading-{i}">Section {i}</h2>
                <p class="content" onclick="handleClick({i})">
                    This is paragraph {i} with some content.
                    It has multiple sentences to make it more realistic.
                    And even more text to increase the size.
                </p>
                <script>console.log('script {i}');</script>
                <style>.class-{i} {{ color: blue; }}</style>
            </div>
            """
        large_html += "</body></html>"
        
        start_time = time.time()
        result = preprocess_html(large_html)
        end_time = time.time()
        
        # Should process in reasonable time (less than 1 second)
        assert end_time - start_time < 1.0
        
        # Should remove scripts and styles
        assert "<script>" not in result
        assert "<style>" not in result
        assert "console.log" not in result
        
        # Should preserve content
        assert "Section" in result
        assert "paragraph" in result
        
        # Should be truncated if too long
        assert len(result) <= 40100
    
    @pytest.mark.asyncio
    async def test_parse_endpoint_concurrent_requests(self, client, mock_openai_client, mock_redis):
        """Test parse endpoint under concurrent load."""
        # Mock rate limiter to allow all requests
        with patch("main.rate_limiter.check_rate", new_callable=AsyncMock):
            requests = []
            for i in range(10):
                request = {
                    "url": f"https://example.com/article{i}",
                    "html_content": f"<p>Article {i} content</p>",
                    "content_type": "article",
                    "metadata": {"user_id": f"user{i}"}
                }
                requests.append(request)
            
            # Make concurrent requests
            async def make_parse_request(req):
                response = client.post("/parse", json=req)
                return response
            
            # Use regular client.post (sync) calls
            responses = []
            for req in requests:
                response = client.post("/parse", json=req)
                responses.append(response)
            
            # All should succeed
            for response in responses:
                assert response.status_code == 200
    
    def test_memory_efficient_html_processing(self):
        """Test that HTML processing doesn't create excessive memory usage."""
        # Create HTML with repetitive content
        repetitive_html = "<div>" + "<p>Same content</p>" * 10000 + "</div>"
        
        # Process should handle this efficiently
        result = preprocess_html(repetitive_html)
        
        # Result should be truncated
        assert len(result) <= 40100
        assert result.endswith("...")
    
    @pytest.mark.asyncio
    async def test_rate_limiter_cleanup(self):
        """Test that rate limiter cleans up old entries."""
        limiter = RateLimiter()
        limiter.max_requests_per_minute = 100
        
        with patch('time.time') as mock_time:
            # Initial time
            mock_time.return_value = 1000
            
            # Make some requests
            for i in range(10):
                await limiter.check_rate_async(f"user{i}")
            
            # Advance time by 30 seconds
            mock_time.return_value = 1030
            
            # Make more requests
            for i in range(10):
                await limiter.check_rate_async(f"user{i+10}")
            
            # Check that deque has 20 entries
            assert len(limiter.requests) == 20
            
            # Advance time by 61 seconds from start
            mock_time.return_value = 1061
            
            # Make a new request - this should trigger cleanup
            await limiter.check_rate_async("cleanup_user")
            
            # Old requests should be cleaned up
            assert len(limiter.requests) == 11  # 10 from 30s ago + 1 new