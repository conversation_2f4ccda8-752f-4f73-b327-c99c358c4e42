import pytest
import json
from unittest.mock import patch, AsyncMock
from freezegun import freeze_time


class TestHealthEndpoint:
    """Test the health check endpoint."""
    
    def test_health_check_success(self, client, mock_redis):
        """Test successful health check."""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "healthy", "redis": "connected"}
    
    def test_health_check_redis_down(self, client):
        """Test health check when Red<PERSON> is down."""
        with patch("main.cache", None):
            response = client.get("/health")
            assert response.status_code == 200
            assert response.json() == {"status": "healthy", "redis": "not configured"}


class TestRootEndpoint:
    """Test the root endpoint."""
    
    def test_root_endpoint(self, client):
        """Test root endpoint returns API info."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Pocket-next Parse Server"
        assert "version" in data
        assert "endpoints" in data
        assert "/parse" in data["endpoints"]
        assert "/health" in data["endpoints"]


class TestParseEndpoint:
    """Test the main parse endpoint."""
    
    @pytest.mark.asyncio
    async def test_parse_article_success(self, client, sample_parse_request, mock_openai_client, mock_redis, rate_limiter_mock):
        """Test successful article parsing."""
        response = client.post("/parse", json=sample_parse_request.model_dump())
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert data["title"] == "Test Article Title"
        assert data["content"] == "This is the main content of the article."
        assert data["summary"] == "A brief summary of the article."
        assert data["keywords"] == ["test", "article", "mock"]
        assert data["author"] == "Test Author"
        assert data["publish_date"] == "2024-01-01"
        assert data["reading_time"] == 5
        assert data["content_type"] == "article"
        
        # Verify OpenAI was called
        mock_openai_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parse_with_cache_hit(self, client, sample_parse_request, mock_openai_client, mock_redis, rate_limiter_mock):
        """Test parsing with cache hit."""
        # Pre-populate cache
        cached_article = {
            "title": "Cached Article",
            "content": "Cached content",
            "summary": "Cached summary",
            "keywords": ["cached"],
            "author": "Cache Author",
            "publish_date": "2024-01-01",
            "reading_time": 3,
            "content_type": "article"
        }
        
        import hashlib
        cache_key = hashlib.md5(
            f"{sample_parse_request.url}:{sample_parse_request.html_content[:1000]}".encode()
        ).hexdigest()
        
        mock_redis.set(f"parsed:{cache_key}", json.dumps(cached_article), ex=604800)
        
        # Make request
        response = client.post("/parse", json=sample_parse_request.model_dump())
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Cached Article"
        
        # Verify OpenAI was NOT called
        mock_openai_client.chat.completions.create.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_parse_rate_limit_exceeded(self, client, sample_parse_request):
        """Test rate limiting."""
        from fastapi import HTTPException
        with patch("main.rate_limiter.check_rate_async", side_effect=HTTPException(status_code=429, detail="Rate limit exceeded")):
            response = client.post("/parse", json=sample_parse_request.model_dump())
            assert response.status_code == 429
            assert "rate limit" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_parse_twitter_content(self, client, mock_openai_client, mock_redis, rate_limiter_mock):
        """Test parsing Twitter/X content."""
        request = {
            "url": "https://twitter.com/user/status/123",
            "html_content": "<div class='tweet'>Test tweet content</div>",
            "content_type": "twitter",
            "metadata": {
                "user_id": "test_user"
            }
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 200
        
        # Verify Twitter-specific prompt was used
        call_args = mock_openai_client.chat.completions.create.call_args
        messages = call_args[1]["messages"]
        assert any("Twitter" in msg["content"] for msg in messages)
    
    @pytest.mark.asyncio
    async def test_parse_youtube_content(self, client, mock_openai_client, mock_redis, rate_limiter_mock):
        """Test parsing YouTube content."""
        request = {
            "url": "https://youtube.com/watch?v=123",
            "html_content": "<div>YouTube video content</div>",
            "content_type": "youtube",
            "metadata": {
                "user_id": "test_user"
            }
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 200
        
        # Verify YouTube-specific prompt was used
        call_args = mock_openai_client.chat.completions.create.call_args
        messages = call_args[1]["messages"]
        assert any("YouTube" in msg["content"] for msg in messages)
    
    @pytest.mark.asyncio
    async def test_parse_openai_error_fallback(self, client, sample_parse_request, mock_redis, rate_limiter_mock):
        """Test fallback parsing when OpenAI fails."""
        with patch("main.openai_client.chat.completions.create", side_effect=Exception("OpenAI error")):
            response = client.post("/parse", json=sample_parse_request.model_dump())
            
            assert response.status_code == 200
            data = response.json()
            
            # Should use fallback parsing
            assert "Test Article" in data["title"]  # From HTML title
            assert len(data["content"]) > 0
            assert data["content_type"] == "article"
    
    @pytest.mark.asyncio
    async def test_parse_invalid_json_from_openai(self, client, sample_parse_request, mock_redis, rate_limiter_mock):
        """Test handling of invalid JSON from OpenAI."""
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.choices = [
            AsyncMock(message=AsyncMock(content="Invalid JSON{{"))
        ]
        mock_client.chat.completions.create = AsyncMock(return_value=mock_response)
        
        with patch("main.openai_client", mock_client):
            response = client.post("/parse", json=sample_parse_request.model_dump())
            
            assert response.status_code == 200
            # Should fall back to basic parsing
            data = response.json()
            assert len(data["title"]) > 0
    
    def test_parse_missing_required_fields(self, client):
        """Test parsing with missing required fields."""
        invalid_request = {
            "url": "https://example.com",
            # Missing html_content and other required fields
        }
        
        response = client.post("/parse", json=invalid_request)
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_parse_empty_html_content(self, client, mock_redis, rate_limiter_mock):
        """Test parsing with empty HTML content."""
        request = {
            "url": "https://example.com",
            "html_content": "",
            "content_type": "article",
            "metadata": {
                "user_id": "test_user"
            }
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 200
        
        data = response.json()
        # Should handle empty content gracefully
        assert data["title"] != ""
        assert data["content_type"] == "article"
    
    @pytest.mark.asyncio
    async def test_parse_malformed_html(self, client, mock_redis, rate_limiter_mock):
        """Test parsing with malformed HTML."""
        request = {
            "url": "https://example.com",
            "html_content": "<p>Unclosed tag <div>Nested</p></div>",
            "content_type": "article",
            "metadata": {"user_id": "test_user"}
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 200
        
        data = response.json()
        assert "Unclosed tag" in data["content"] or "Nested" in data["content"]
    
    @pytest.mark.asyncio
    async def test_parse_different_content_types(self, client, mock_openai_client, mock_redis, rate_limiter_mock):
        """Test parsing different content types."""
        content_types = ["article", "twitter", "youtube", "unknown"]
        
        for i, content_type in enumerate(content_types):
            # Update mock response for each content type
            mock_response = AsyncMock()
            mock_response.choices = [
                AsyncMock(
                    message=AsyncMock(
                        content=json.dumps({
                            "title": f"Test {content_type} Title",
                            "content": f"This is the main content for {content_type}.",
                            "summary": f"A brief summary of the {content_type}.",
                            "keywords": ["test", content_type],
                            "author": "Test Author",
                            "publish_date": "2024-01-01",
                            "reading_time": 5,
                            "content_type": content_type
                        })
                    )
                )
            ]
            mock_openai_client.chat.completions.create.return_value = mock_response
            
            request = {
                "url": f"https://example.com/{content_type}{i}",  # Make URLs unique
                "html_content": f"<p>Content for {content_type}</p>",
                "content_type": content_type,
                "metadata": {"user_id": "test_user"}
            }
            
            response = client.post("/parse", json=request)
            assert response.status_code == 200
            data = response.json()
            assert data["content_type"] == content_type
    
    @pytest.mark.asyncio
    async def test_parse_large_metadata(self, client, mock_openai_client, mock_redis, rate_limiter_mock):
        """Test parsing with large metadata."""
        request = {
            "url": "https://example.com",
            "html_content": "<p>Test content</p>",
            "content_type": "article",
            "metadata": {
                "user_id": "test_user",
                "title": "Test Title",
                "author": "Test Author",
                "publishDate": "2024-01-01",
                "extra_field": "This should be ignored"
            }
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 200