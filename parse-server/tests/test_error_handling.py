import pytest
import json
from unittest.mock import patch, As<PERSON><PERSON><PERSON>, MagicMock
from fastapi import HTTPException
import redis

from main import app, ParseRequest, fallback_parse


class TestErrorHandling:
    """Test error handling and edge cases."""
    
    @pytest.mark.asyncio
    async def test_parse_with_redis_connection_error(self, client, sample_parse_request, mock_openai_client):
        """Test parsing when Redis connection fails."""
        # Mock Redis to raise connection error
        with patch("main.cache") as mock_cache:
            mock_cache.get.side_effect = redis.ConnectionError("Redis connection failed")
            mock_cache.setex.side_effect = redis.ConnectionError("Redis connection failed")
            
            # Should still work without cache
            response = client.post("/parse", json=sample_parse_request.model_dump())
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_parse_with_openai_timeout(self, client, sample_parse_request, mock_redis, rate_limiter_mock):
        """Test handling of OpenAI API timeout."""
        with patch("main.openai_client.chat.completions.create") as mock_create:
            mock_create.side_effect = TimeoutError("Request timed out")
            
            response = client.post("/parse", json=sample_parse_request.model_dump())
            
            # Should fall back to basic parsing
            assert response.status_code == 200
            data = response.json()
            assert len(data["title"]) > 0
            assert data["content_type"] == "article"
    
    @pytest.mark.asyncio
    async def test_parse_with_invalid_api_key(self, client, sample_parse_request, mock_redis, rate_limiter_mock):
        """Test handling of invalid OpenAI API key."""
        with patch("main.openai_client.chat.completions.create") as mock_create:
            mock_create.side_effect = Exception("Invalid API key")
            
            response = client.post("/parse", json=sample_parse_request.model_dump())
            
            # Should fall back to basic parsing
            assert response.status_code == 200
            data = response.json()
            assert "extracted from" in data["summary"].lower()
    
    def test_fallback_parse_with_none_values(self):
        """Test fallback parsing with None values in metadata."""
        request = ParseRequest(
            url="https://example.com",
            html_content="<p>Content</p>",
            content_type="article",
            metadata={"title": None, "author": None}
        )
        
        result = fallback_parse(request)
        
        assert result.title == "https://example.com"  # Falls back to URL
        assert result.author is None
    
    def test_fallback_parse_with_broken_html(self):
        """Test fallback parsing with severely broken HTML."""
        request = ParseRequest(
            url="https://example.com",
            html_content="<<><p>>&&&<broken>>>",
            content_type="article",
            metadata={}
        )
        
        # Should not raise an exception
        result = fallback_parse(request)
        assert isinstance(result.title, str)
        assert isinstance(result.content, str)
    
    @pytest.mark.asyncio
    async def test_parse_with_extremely_large_request(self, client, mock_redis, rate_limiter_mock):
        """Test handling of extremely large requests."""
        # Create a very large HTML content (5MB)
        large_content = "<p>" + "x" * (5 * 1024 * 1024) + "</p>"
        
        request = {
            "url": "https://example.com",
            "html_content": large_content,
            "content_type": "article",
            "metadata": {"user_id": "test_user"}
        }
        
        response = client.post("/parse", json=request)
        
        # Should handle large content
        assert response.status_code == 200
        data = response.json()
        # Content should be truncated
        assert len(data["content"]) <= 5000
    
    @pytest.mark.asyncio
    async def test_parse_with_unicode_errors(self, client, mock_redis, rate_limiter_mock):
        """Test handling of Unicode encoding errors."""
        request = {
            "url": "https://example.com",
            "html_content": "<p>Invalid UTF-8: \udcff\udcfe</p>",
            "content_type": "article",
            "metadata": {"user_id": "test_user"}
        }
        
        # Should handle invalid Unicode gracefully
        response = client.post("/parse", json=request)
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_health_check_with_redis_timeout(self, client):
        """Test health check when Redis times out."""
        with patch("main.cache") as mock_cache:
            mock_cache.ping.side_effect = redis.TimeoutError("Timeout")
            
            response = client.get("/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_parse_with_circular_references(self, client, mock_redis, rate_limiter_mock):
        """Test parsing HTML with circular references."""
        html = """
        <div id="parent">
            <div id="child">
                <a href="#parent">Back to parent</a>
            </div>
        </div>
        """ * 100  # Repeat to create complex structure
        
        request = {
            "url": "https://example.com",
            "html_content": html,
            "content_type": "article",
            "metadata": {"user_id": "test_user"}
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_parse_with_missing_content_type(self, client, mock_redis, rate_limiter_mock):
        """Test parsing when content_type is missing."""
        request = {
            "url": "https://example.com",
            "html_content": "<p>Content</p>",
            "metadata": {"user_id": "test_user"}
            # Missing content_type
        }
        
        response = client.post("/parse", json=request)
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_parse_with_invalid_url(self, client, mock_redis, rate_limiter_mock):
        """Test parsing with invalid URL format."""
        request = {
            "url": "not-a-valid-url",
            "html_content": "<p>Content</p>",
            "content_type": "article",
            "metadata": {"user_id": "test_user"}
        }
        
        # Should still process the content
        response = client.post("/parse", json=request)
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_concurrent_rate_limit_edge_case(self, client):
        """Test rate limiting edge case with exact limit."""
        with patch("main.rate_limiter.max_requests_per_minute", 3):
            request = {
                "url": "https://example.com",
                "html_content": "<p>Content</p>",
                "content_type": "article",
                "metadata": {"user_id": "edge_case_user"}
            }
            
            # Make exactly 3 requests
            for i in range(3):
                response = client.post("/parse", json=request)
                assert response.status_code == 200
            
            # 4th request should fail
            response = client.post("/parse", json=request)
            assert response.status_code == 429
    
    def test_preprocess_html_with_invalid_encoding(self):
        """Test HTML preprocessing with invalid encoding."""
        from main import preprocess_html
        
        # HTML with mixed encodings
        html = b"<p>\xc3\xa9\xff\xfe</p>".decode('utf-8', errors='ignore')
        
        # Should not raise an exception
        result = preprocess_html(html)
        assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_parse_with_openai_partial_response(self, client, sample_parse_request, mock_redis, rate_limiter_mock):
        """Test handling of partial/incomplete OpenAI response."""
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.choices = [
            AsyncMock(message=AsyncMock(content='{"title": "Test", "content": "Incomplete'))
        ]
        mock_client.chat.completions.create = AsyncMock(return_value=mock_response)
        
        with patch("main.openai_client", mock_client):
            response = client.post("/parse", json=sample_parse_request.model_dump())
            
            # Should fall back to basic parsing
            assert response.status_code == 200
            data = response.json()
            assert len(data["title"]) > 0