import pytest
import os
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from fakeredis import FakeRedis
import json

# Set test environment
os.environ["TESTING"] = "true"
os.environ["OPENAI_API_KEY"] = "test-key"

from main import app, ParseRequest, ParsedArticle


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    fake_redis = FakeRedis(decode_responses=True)
    with patch("main.cache", fake_redis):
        yield fake_redis


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client."""
    mock_client = AsyncMock()
    
    # Mock chat completion response
    mock_response = AsyncMock()
    mock_response.choices = [
        AsyncMock(
            message=AsyncMock(
                content=json.dumps({
                    "title": "Test Article Title",
                    "content": "This is the main content of the article.",
                    "summary": "A brief summary of the article.",
                    "keywords": ["test", "article", "mock"],
                    "author": "Test Author",
                    "publish_date": "2024-01-01",
                    "reading_time": 5,
                    "content_type": "article"
                })
            )
        )
    ]
    
    mock_client.chat.completions.create = AsyncMock(return_value=mock_response)
    
    with patch("main.openai_client", mock_client):
        yield mock_client


@pytest.fixture
def sample_parse_request():
    """Sample parse request data."""
    return ParseRequest(
        url="https://example.com/article",
        html_content="<html><head><title>Test Article</title></head><body><h1>Test Article</h1><p>Content here</p></body></html>",
        content_type="article",
        metadata={
            "title": "Test Article",
            "author": "Test Author",
            "publishDate": "2024-01-01",
            "user_id": "test_user_123"
        }
    )


@pytest.fixture
def sample_twitter_html():
    """Sample Twitter/X HTML content."""
    return """
    <html>
        <head><title>Twitter Post</title></head>
        <body>
            <div class="tweet">
                <span class="author">@testuser</span>
                <p>This is a test tweet with some content</p>
                <time>2024-01-01</time>
            </div>
        </body>
    </html>
    """


@pytest.fixture
def sample_youtube_html():
    """Sample YouTube HTML content."""
    return """
    <html>
        <head>
            <title>Test Video - YouTube</title>
            <meta name="description" content="This is a test video description">
        </head>
        <body>
            <div id="description">Full video description here</div>
            <div class="transcript">Video transcript content</div>
        </body>
    </html>
    """


@pytest.fixture
def rate_limiter_mock():
    """Mock rate limiter to always allow requests."""
    with patch("main.rate_limiter.check_rate_async", new_callable=AsyncMock):
        yield


@pytest.fixture
async def test_server():
    """Test server URL for E2E tests."""
    # For testing, we'll use the TestClient which handles the server internally
    return "http://testserver"