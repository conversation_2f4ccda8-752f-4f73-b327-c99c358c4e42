#!/bin/bash

echo "🚀 Starting Pocket-next Parse Server..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo ""
    echo "Creating .env from .env.example..."
    cp .env.example .env
    echo ""
    echo "Please edit .env and add your OpenAI API key:"
    echo "  OPENAI_API_KEY=your-actual-key-here"
    echo ""
    exit 1
fi

# Check if API key is set
if grep -q "your-openai-api-key-here" .env; then
    echo "❌ OpenAI API key not configured!"
    echo ""
    echo "Please edit .env and add your actual OpenAI API key:"
    echo "  OPENAI_API_KEY=your-actual-key-here"
    echo ""
    exit 1
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Start the server
echo "✅ Starting server on http://localhost:8000"
echo "📝 API docs available at: http://localhost:8000/docs"
echo ""
python main.py