"""Lightweight parse server using GPT-4o mini for universal content parsing."""
import os
import re
import json
import hashlib
import time
from typing import Dict, List, Optional
from collections import deque
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import redis
from openai import AsyncOpenAI
from bs4 import BeautifulSoup
import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="Pocket-next Parse Server", version="1.0.0")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Redis for caching
try:
    cache = redis.Redis(
        host=os.getenv("REDIS_HOST", "localhost"),
        port=int(os.getenv("REDIS_PORT", 6379)),
        decode_responses=True
    )
    cache.ping()
except:
    cache = None
    print("Warning: Redis not available, caching disabled")

# Initialize OpenAI client
api_key = os.environ.get("OPENAI_API_KEY")
if not api_key:
    print("⚠️  Warning: OPENAI_API_KEY not found in environment variables")
    print("   Please create a .env file with: OPENAI_API_KEY=your-key-here")
    print("   Or set the environment variable: export OPENAI_API_KEY=your-key-here")
else:
    print(f"✅ OpenAI API key loaded (starts with: {api_key[:7]}...)")

openai_client = AsyncOpenAI(api_key=api_key)

# Request/Response models
class ParseRequest(BaseModel):
    url: str
    html_content: str
    content_type: str
    metadata: Dict

class ParsedArticle(BaseModel):
    title: str
    content: str  # Clean, readable content
    summary: str  # 2-3 sentence summary
    keywords: List[str]  # 5-10 searchable keywords
    author: Optional[str]
    publish_date: Optional[str]
    reading_time: int  # minutes
    content_type: str  # article, tweet_thread, video, etc.


# Rate limiter
class RateLimiter:
    def __init__(self, redis_client=None, max_requests=100, window_seconds=60):
        self.cache = redis_client
        self.requests = deque()
        self.max_requests_per_minute = max_requests
        self.window_seconds = window_seconds
    
    def check_rate(self, user_id: str) -> bool:
        """Check if user is within rate limit. Returns True if allowed, False if blocked."""
        if not self.cache:
            # If no Redis, always allow (for development)
            return True
        
        try:
            key = f"rate_limit:{user_id}"
            current = self.cache.incr(key)
            if current == 1:
                self.cache.expire(key, self.window_seconds)
            return current <= self.max_requests_per_minute
        except:
            # If Redis fails, allow the request
            return True
    
    async def check_rate_async(self, user_id: str):
        """Async version that raises HTTPException if rate limit exceeded."""
        now = time.time()
        # Remove old requests
        while self.requests and self.requests[0][0] < now - self.window_seconds:
            self.requests.popleft()
        
        # Count user's requests
        user_count = sum(1 for t, uid in self.requests if uid == user_id)
        if user_count >= self.max_requests_per_minute:
            raise HTTPException(429, "Rate limit exceeded")
        
        self.requests.append((now, user_id))

rate_limiter = RateLimiter(cache)


# Parsing prompt
PARSING_SYSTEM_PROMPT = """You are an expert content parser for a read-it-later app. 
Extract and structure content into a clean, readable format.

CRITICAL: Return ONLY valid JSON matching this exact structure:
{
  "title": "string",
  "content": "string - main content in clean markdown format",
  "summary": "string - 2-3 sentence summary of key points",
  "keywords": ["array", "of", "5-10", "searchable", "keywords"],
  "author": "string or null",
  "publish_date": "string or null", 
  "reading_time": number,
  "content_type": "string"
}

Guidelines:
- Extract the actual article/post content, not navigation or ads
- Convert to clean markdown format for readability
- For Twitter/X threads, combine tweets into coherent narrative
- For YouTube, create article from transcript if available
- Preserve code blocks and important formatting
- Generate keywords that capture topics, technologies, people, and themes
- Estimate reading time: ~250 words per minute
- Use null for missing fields, never omit fields"""


def preprocess_html(html: str) -> str:
    """Aggressively clean HTML to minimize tokens."""
    soup = BeautifulSoup(html, 'html.parser')
    
    # Remove all non-content elements
    for tag in soup(['script', 'style', 'meta', 'link', 'noscript', 
                     'header', 'nav', 'footer', 'aside', 'form', 
                     'button', 'input', 'select', 'textarea']):
        tag.decompose()
    
    # Remove all attributes except href for links
    for tag in soup.find_all(True):
        if tag.name == 'a' and tag.has_attr('href'):
            tag.attrs = {'href': tag['href']}
        else:
            tag.attrs = {}
    
    # Get text with some structure preserved
    text_parts = []
    for element in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
                                  'li', 'blockquote', 'pre', 'code']):
        text = element.get_text(strip=True)
        if text:
            if element.name.startswith('h'):
                text_parts.append(f"\n## {text}\n")
            elif element.name == 'li':
                text_parts.append(f"• {text}")
            elif element.name in ['blockquote', 'pre', 'code']:
                text_parts.append(f"\n{text}\n")
            else:
                text_parts.append(text)
    
    # Join with proper spacing
    content = '\n\n'.join(text_parts)
    
    # Final cleanup
    content = re.sub(r'\n{3,}', '\n\n', content)  # Remove excessive newlines
    content = re.sub(r' {2,}', ' ', content)  # Remove excessive spaces
    
    # Limit to ~10k tokens worth of text (~40k chars)
    if len(content) > 40000:
        content = content[:40000] + "..."
    
    return content


def create_parsing_prompt(text: str, content_type: str, url: str) -> str:
    """Create optimized prompt for content type."""
    if content_type == "twitter":
        return f"""Parse this Twitter/X thread into an article:
URL: {url}

Content:
{text}

Combine tweets into a flowing narrative while preserving the thread structure."""
    
    elif content_type == "twitter-bookmarks":
        return f"""Parse this Twitter/X bookmarks page:
URL: {url}

Content:
{text}

This is a bookmarks page containing multiple tweets. Extract EACH tweet as a separate item and return them as a JSON array. For each tweet:
- Extract the tweet text, author, date
- Preserve thread connections if visible
- Note any media attachments
- Generate appropriate keywords

Return as: {{"items": [array of parsed tweets], "total_count": number}}"""
    
    elif content_type == "youtube":
        return f"""Parse this YouTube page/transcript:
URL: {url}

Content:
{text}

Create a readable article from the video content, organizing by topics."""
    
    else:
        return f"""Parse this web content:
URL: {url}

Content:
{text}

Extract the main article/content in a clean, readable format."""


def fallback_parse(request: ParseRequest) -> ParsedArticle:
    """Basic fallback when GPT parsing fails."""
    soup = BeautifulSoup(request.html_content, 'html.parser')
    
    # Try to get basic info
    title = request.metadata.get('title') or soup.find('h1')
    if title and hasattr(title, 'get_text'):
        title = title.get_text()
    else:
        title = title or request.url
    
    # Get all text
    text = soup.get_text()
    words = len(text.split())
    
    # Ensure strings are properly encoded
    safe_title = str(title)[:200].encode('utf-8', errors='ignore').decode('utf-8')
    safe_content = text[:5000].encode('utf-8', errors='ignore').decode('utf-8')
    
    return ParsedArticle(
        title=safe_title,
        content=safe_content,  # First 5k chars
        summary="Content extracted from " + request.url,
        keywords=[request.content_type, "unprocessed"],
        author=request.metadata.get('author'),
        publish_date=request.metadata.get('publishDate'),
        reading_time=max(1, words // 250),
        content_type=request.content_type
    )


@app.post("/parse", response_model=ParsedArticle)
async def parse_content(request: ParseRequest):
    """Parse DOM content into structured article data."""
    # Rate limiting
    user_id = request.metadata.get("user_id", "anonymous")
    await rate_limiter.check_rate_async(user_id)
    
    # Check cache first
    if cache:
        # Handle potential Unicode encoding issues
        try:
            cache_content = f"{request.url}:{request.html_content[:1000]}"
            cache_key = hashlib.md5(cache_content.encode('utf-8', errors='ignore')).hexdigest()
        except Exception:
            # Fallback to URL-only cache key if content causes issues
            cache_key = hashlib.md5(request.url.encode('utf-8')).hexdigest()
        
        try:
            cached = cache.get(f"parsed:{cache_key}")
            if cached:
                return ParsedArticle.model_validate_json(cached)
        except Exception:
            # If cache fails, continue without cache
            pass
    
    # Preprocess HTML aggressively - this is KEY for cost savings
    cleaned_text = preprocess_html(request.html_content)
    
    # Create optimized prompt
    prompt = create_parsing_prompt(cleaned_text, request.content_type, request.url)
    
    try:
        response = await openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": PARSING_SYSTEM_PROMPT},
                {"role": "user", "content": prompt}
            ],
            temperature=0,  # Deterministic
            response_format={"type": "json_object"}
        )
        
        # Parse response
        content = response.choices[0].message.content
        parsed = ParsedArticle.model_validate_json(content)
        
        # Cache for 7 days
        if cache:
            try:
                cache.setex(f"parsed:{cache_key}", 604800, parsed.model_dump_json())
            except:
                # If caching fails, continue without caching
                pass
        
        return parsed
        
    except Exception as e:
        print(f"GPT parsing failed: {e}")
        # Fallback to basic extraction
        return fallback_parse(request)


@app.post("/parse-twitter-bookmarks")
async def parse_twitter_bookmarks(request: ParseRequest):
    """Parse Twitter bookmarks page into multiple articles."""
    # Rate limiting with higher limit for bulk operations
    user_id = request.metadata.get("user_id", "anonymous")
    
    # Special handling for Twitter bookmarks
    if request.content_type != "twitter-bookmarks":
        raise HTTPException(400, "This endpoint is for Twitter bookmarks only")
    
    # Preprocess HTML
    cleaned_text = preprocess_html(request.html_content)
    
    # Create specialized prompt for bookmarks
    prompt = f"""Parse this Twitter/X bookmarks page and extract ALL individual tweets:
URL: {request.url}

Content:
{cleaned_text}

Extract each tweet as a separate item with:
- Tweet content/text
- Author name and handle
- Date/time if available
- Whether it's part of a thread
- Any media descriptions

Return JSON with this structure:
{{
  "tweets": [
    {{
      "content": "tweet text",
      "author": "author name",
      "handle": "@handle",
      "date": "date string or null",
      "is_thread": boolean,
      "has_media": boolean,
      "keywords": ["relevant", "keywords"]
    }}
  ],
  "total_extracted": number
}}
"""
    
    try:
        response = await openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Extract individual tweets from Twitter bookmarks page. Return ONLY valid JSON."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )
        
        # Parse response
        content = response.choices[0].message.content
        parsed_data = json.loads(content)
        
        # Convert each tweet to an article
        articles = []
        for tweet in parsed_data.get("tweets", []):
            article = ParsedArticle(
                title=f"Tweet by {tweet.get('author', 'Unknown')}",
                content=tweet.get('content', ''),
                summary=tweet['content'][:200] + "..." if len(tweet['content']) > 200 else tweet['content'],
                keywords=tweet.get('keywords', ['twitter', 'bookmark']),
                author=tweet.get('author'),
                publish_date=tweet.get('date'),
                reading_time=1,  # Tweets are quick reads
                content_type="tweet"
            )
            articles.append(article)
        
        return {
            "articles": articles,
            "total_count": len(articles),
            "source": "twitter_bookmarks"
        }
        
    except Exception as e:
        print(f"Twitter bookmarks parsing failed: {e}")
        # Fallback - treat as single article
        fallback = fallback_parse(request)
        return {
            "articles": [fallback],
            "total_count": 1,
            "source": "twitter_bookmarks_fallback"
        }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    redis_status = "not configured"
    if cache:
        try:
            cache.ping()
            redis_status = "connected"
        except:
            redis_status = "error"
    
    return {
        "status": "healthy",
        "redis": redis_status
    }


@app.get("/")
async def root():
    """Root endpoint with API info."""
    return {
        "name": "Pocket-next Parse Server",
        "version": "1.0.0",
        "endpoints": {
            "/parse": "POST - Parse DOM content into structured article",
            "/health": "GET - Health check",
            "/docs": "GET - API documentation"
        }
    }


if __name__ == "__main__":
    # Check if API key is available before starting
    if not api_key:
        print("\n❌ Cannot start server without OpenAI API key!")
        print("\nTo fix this:")
        print("1. Copy .env.example to .env:")
        print("   cp .env.example .env")
        print("2. Edit .env and add your OpenAI API key")
        print("3. Run the server again")
        exit(1)
    
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    
    print(f"\n🚀 Starting Pocket-next Parse Server on {host}:{port}")
    print(f"📝 API documentation available at: http://localhost:{port}/docs")
    
    uvicorn.run(app, host=host, port=port)