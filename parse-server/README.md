# Pocket-next Parse Server

A lightweight FastAPI server that uses GPT-4o mini to intelligently parse DOM content from any website into clean, structured article data.

## Features

- **Universal Parsing**: Handles any website without site-specific parsers
- **Cost Effective**: ~$0.0005 per article with aggressive preprocessing
- **Fast**: Redis caching with 7-day TTL
- **Rate Limited**: 100 requests/minute per user
- **Reliable**: Graceful fallback for parsing failures
- **Twitter Support**: Special handling for Twitter bookmarks bulk import

## Quick Start

### Option 1: Using the Start Script (Easiest)

```bash
# 1. Create .env file
cp .env.example .env

# 2. Edit .env and add your OpenAI API key
#    OPENAI_API_KEY=sk-...your-actual-key-here...

# 3. Start the server
./start-server.sh
```

### Option 2: Manual Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Create and configure `.env` file:
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

3. Run the server:
```bash
python main.py
```

The server will be available at `http://localhost:8000`

### Using Docker Compose (Recommended)

1. Create `.env` file:
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

2. Run the server:
```bash
docker-compose up
```

The server will be available at `http://localhost:8000`.

### Manual Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables:
```bash
export OPENAI_API_KEY=your_openai_api_key_here
export REDIS_HOST=localhost  # Optional
export REDIS_PORT=6379       # Optional
```

3. Run the server:
```bash
uvicorn main:app --reload
```

## API Usage

### Parse Endpoint

**POST** `/parse`

Parses DOM content into structured article data.

#### Request Body:
```json
{
  "url": "https://example.com/article",
  "html_content": "<html>...</html>",
  "content_type": "article",
  "metadata": {
    "title": "Optional title",
    "author": "Optional author",
    "publishDate": "2024-01-01",
    "user_id": "user123"
  }
}
```

#### Response:
```json
{
  "title": "Article Title",
  "content": "Clean markdown content...",
  "summary": "A 2-3 sentence summary of the article.",
  "keywords": ["keyword1", "keyword2", "keyword3"],
  "author": "Author Name",
  "publish_date": "2024-01-01",
  "reading_time": 5,
  "content_type": "article"
}
```

### Health Check

**GET** `/health`

Returns server health status.

## Cost Optimization

The server aggressively preprocesses HTML to reduce token usage:

1. **Removes non-content elements**: scripts, styles, navigation, ads
2. **Strips attributes**: Keeps only essential attributes like hrefs
3. **Preserves structure**: Maintains headings, paragraphs, lists
4. **Limits length**: Caps at ~40k characters (roughly 10k tokens)

This preprocessing reduces token usage by 60-80%, resulting in costs of ~$0.0005 per article.

## Rate Limiting

- 100 requests per minute per user_id
- Pass user_id in metadata for proper rate limiting
- Returns 429 status when limit exceeded

## Caching

- Redis caching with 7-day TTL
- Cache key based on URL and content hash
- Dramatically reduces API costs for popular content

## Deployment

### Environment Variables

- `OPENAI_API_KEY` (required): Your OpenAI API key
- `REDIS_HOST` (optional): Redis host (default: localhost)
- `REDIS_PORT` (optional): Redis port (default: 6379)

### Production Deployment

The server is designed to run on:
- Google Cloud Run
- AWS Lambda (with adapter)
- Vercel
- Any container platform

Resource requirements:
- Memory: 512MB
- CPU: 0.5 cores
- Scales horizontally

### Monitoring

Monitor these metrics:
- Request rate and latency
- Cache hit rate
- OpenAI API usage and costs
- Error rates

## Error Handling

The server handles errors gracefully:
1. **OpenAI failures**: Falls back to basic text extraction
2. **Rate limits**: Returns clear 429 error
3. **Invalid HTML**: Attempts basic parsing
4. **Cache failures**: Continues without caching

## Development

### Running Tests

```bash
# Install dev dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/
```

### API Documentation

Interactive API documentation available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`