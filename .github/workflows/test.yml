name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-ios:
    name: iOS Tests
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Select Xcode
      run: sudo xcode-select -s /Applications/Xcode.app
      
    - name: Build and Test iOS
      run: |
        cd ios-app
        xcodebuild -project PocketNext.xcodeproj \
                   -scheme PocketNext \
                   -destination 'platform=iOS Simulator,name=iPhone 15' \
                   -enableCodeCoverage YES \
                   test
                   
    - name: Upload iOS Coverage
      uses: codecov/codecov-action@v3
      with:
        xcode: true
        xcode_archive_path: ios-app/test-results.xcresult
        
  test-macos:
    name: macOS Tests
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Select Xcode
      run: sudo xcode-select -s /Applications/Xcode.app
      
    - name: Build and Test macOS
      run: |
        cd macos-app
        xcodebuild -project PocketNext.xcodeproj \
                   -scheme PocketNext \
                   -destination 'platform=macOS' \
                   -enableCodeCoverage YES \
                   test
                   
    - name: Upload macOS Coverage
      uses: codecov/codecov-action@v3
      with:
        xcode: true
        xcode_archive_path: macos-app/test-results.xcresult