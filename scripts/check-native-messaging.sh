#!/bin/bash

echo "🔍 Checking Native Messaging Setup"
echo "================================="

# Check Chrome manifest
CHROME_MANIFEST="$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.pocketnext.nativemessaging.json"
echo ""
echo "1. Chrome Native Host Manifest:"
if [ -f "$CHROME_MANIFEST" ]; then
    echo "✅ Found at: $CHROME_MANIFEST"
    echo "📄 Content:"
    cat "$CHROME_MANIFEST" | jq . 2>/dev/null || cat "$CHROME_MANIFEST"
    
    # Check if app path exists
    APP_PATH=$(cat "$CHROME_MANIFEST" | jq -r .path 2>/dev/null || grep -o '"path"[[:space:]]*:[[:space:]]*"[^"]*"' "$CHROME_MANIFEST" | cut -d'"' -f4)
    echo ""
    echo "🔗 App path: $APP_PATH"
    if [ -f "$APP_PATH" ]; then
        echo "✅ App executable exists"
    else
        echo "❌ App executable NOT FOUND at specified path"
    fi
else
    echo "❌ Not found - Native host not registered"
fi

# Check Firefox manifest
FIREFOX_MANIFEST="$HOME/Library/Application Support/Mozilla/NativeMessagingHosts/com.pocketnext.nativemessaging.json"
echo ""
echo "2. Firefox Native Host Manifest:"
if [ -f "$FIREFOX_MANIFEST" ]; then
    echo "✅ Found at: $FIREFOX_MANIFEST"
else
    echo "❌ Not found"
fi

# Check if PocketNext app is installed
echo ""
echo "3. PocketNext App:"
if [ -d "/Applications/PocketNext.app" ]; then
    echo "✅ Found at: /Applications/PocketNext.app"
    PLIST="/Applications/PocketNext.app/Contents/Info.plist"
    if [ -f "$PLIST" ]; then
        VERSION=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "$PLIST" 2>/dev/null)
        echo "📱 Version: $VERSION"
    fi
else
    echo "❌ Not found in /Applications"
    
    # Check build directory
    BUILD_APP=$(find ~/Library/Developer/Xcode/DerivedData -name "PocketNext.app" -type d 2>/dev/null | head -1)
    if [ -n "$BUILD_APP" ]; then
        echo "🔨 Found in build directory: $BUILD_APP"
    fi
fi

# Instructions
echo ""
echo "📝 Next Steps:"
echo "============="
echo ""
echo "1. Install browser extension and get Extension ID:"
echo "   - Open chrome://extensions/"
echo "   - Find 'Pocket-next' and copy its ID"
echo ""
echo "2. Update the manifest with correct extension ID:"
echo "   - Edit: $CHROME_MANIFEST"
echo "   - Replace 'YOUR_EXTENSION_ID' with actual ID"
echo ""
echo "3. Update the app path if needed:"
echo "   - Should point to the actual PocketNext executable"
echo ""
echo "4. Test with:"
echo "   echo '{\"type\":\"ping\"}' | /path/to/PocketNext"