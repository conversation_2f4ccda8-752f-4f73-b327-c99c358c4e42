# Pocket-next: Product Requirements Document (PRD) - UPDATED

---

## 1. Purpose  
Capture anything in a click, turn it into a personal, searchable knowledge base, and resurface what matters—summarized, ranked, and ready to revisit or chat about.

---

## 2. Target Users
- **Knowledge workers & students** who save large volumes of links.  
- **Content-curious professionals** (research, product, finance) needing fast recall.  
- **Heavy X / Twitter users** who rely on bookmarks.
- **Apple ecosystem users** who value privacy and seamless sync.

---

## 3. Core Value Propositions

| Need                | Current Pain                           | Our Solution                                               |
|---------------------|----------------------------------------|------------------------------------------------------------|
| Friction-free capture | Manual copy/paste, inbox clutter        | 1-click browser button / **⌘⇧S** global hot-key             |
| Content overload    | Saved links never read                 | Instant **TL;DR** via GPT-4o mini parsing                  |
| Hard to recall      | Bookmarks vanish in folders            | Local AI search with privacy-first embeddings               |
| Platform silos      | Twitter, browser, and mobile are separate | Unified CloudKit sync across Apple devices                  |
| Privacy concerns    | Cloud services see all your data        | Local embeddings, encrypted sync, minimal server touch      |
| Parsing complexity  | Site-specific parsers break constantly  | Universal GPT-4o mini parsing handles ANY content           |

---

## 4. Feature Set

| Area | **Must-have (MVP)** | **Nice-to-have (v2+)** |
|------|--------------------|------------------------|
| **Capture** | Chrome & Safari extensions with DOM capture, iOS Share-sheet, macOS **⌘⇧S** | Firefox extension, Windows support |
| **Parsing** | GPT-4o mini universal parser (no manual parsers), aggressive HTML preprocessing | Batch processing optimizations |
| **Intelligence** | Server-side parsing, client-side embeddings, hybrid search (keyword + semantic) | Personalized ranking, reading time predictions |
| **Storage** | Local SQLite with FTS5, compressed HTML storage | Incremental sync, conflict resolution |
| **Sync** | CloudKit for Apple ecosystem, encrypted assets | Cross-platform sync options |
| **Search** | Instant local search <100ms, hybrid keyword + semantic | Natural language queries, search suggestions |
| **Privacy** | Embeddings never leave device, minimal server contact | Self-hosted parse server option |
| **Cost** | <$0.001 per saved article with caching | Bulk processing discounts |

---

## 5. User Journey (Happy Path)

1. Install browser extension → see save indicator.  
2. Hit **⌘⇧S** on any page → instant DOM capture → "Saved ✅".  
3. Parse server extracts clean content in <2 seconds.  
4. Local app generates embeddings privately.  
5. Search instantly finds content by keyword or concept.  
6. CloudKit syncs seamlessly to all Apple devices.

---

## 6. Technical Architecture (Revised)

### Three-Tier Architecture:

**Browser Extension (Universal Capture)**
- Manifest V3 Chrome/Safari extension
- DOM capture with minimal preprocessing
- Native messaging to macOS/iOS app
- Zero parsing logic in extension

**Lightweight Parse Server (GPT-4o mini)**
- Minimal FastAPI server
- Aggressive HTML preprocessing (60-80% token reduction)
- GPT-4o mini for universal parsing
- Redis caching (7-day TTL)
- Rate limiting per user
- Deployed on Cloud Run/Vercel

**Native Apps (Local Intelligence)**
- SwiftUI macOS/iOS apps
- SQLite with FTS5 for storage
- Local embedding generation (SimilaritySearchKit)
- Hybrid search (keyword + semantic)
- CloudKit sync with encryption
- <100ms search performance

### Data Flow:
```
Browser → Native App → Parse Server → Local Processing → CloudKit
  (DOM)     (Queue)    (GPT-4o mini)    (Embeddings)     (Sync)
```

---

## 7. Success Metrics (30-Day Cohort)

| Metric | Target |
|--------|--------|
| **Capture success rate** | ≥ 99% |
| **Parse accuracy** | ≥ 95% |
| **Search response time** | < 100ms |
| **Cost per article** | < $0.001 |
| **Sync reliability** | ≥ 99.9% |

---

## 8. Launch Plan

| Phase | Timeline | Scope |
|-------|----------|-------|
| **Phase 1: Core MVP** | Weeks 1-3 | Browser extension, parse server, local storage |
| **Phase 2: Intelligence** | Weeks 4-6 | Local embeddings, hybrid search, CloudKit sync |
| **Phase 3: Polish** | Weeks 7-8 | iOS app, performance optimization, monitoring |

---

## 9. Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| GPT-4o mini failures | Fallback to basic text extraction |
| API cost overruns | Aggressive caching, rate limiting |
| CloudKit limitations | Progressive sync, conflict resolution |
| Privacy concerns | Local-first design, minimal server contact |

---

## 10. Key Technical Decisions

1. **Why GPT-4o mini?** Universal parsing without maintenance, $0.0005/article
2. **Why local embeddings?** Privacy-first, no cloud dependency
3. **Why SQLite + FTS5?** Fast, reliable, works offline
4. **Why CloudKit?** Native Apple sync, no custom backend
5. **Why DOM capture?** Get everything, let AI figure it out

---

## 11. Cost Projections

- 1,000 users (50 saves/month): $22.50/month
- 10,000 users: $225/month  
- 100,000 users: $450/month (with 80% cache rate)
- Infrastructure: $80-250/month

Total: <$1000/month for 100k active users

---

## 12. Privacy Guarantees

- **Embeddings**: Generated locally, never sent to servers
- **Content**: Encrypted in CloudKit, minimal server processing
- **Search**: All queries processed locally
- **Sync**: End-to-end encryption with user keys
- **Parse Server**: Stateless, no user data storage