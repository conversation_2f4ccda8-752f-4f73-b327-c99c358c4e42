#!/bin/bash

echo "🔧 Setting up Native Messaging for Pocket-next"
echo "============================================="

# Get Chrome extension ID
echo ""
echo "1. First, install the browser extension:"
echo "   - Open Chrome"
echo "   - Go to chrome://extensions/"
echo "   - Enable Developer mode"
echo "   - Click 'Load unpacked'"
echo "   - Select: $(pwd)/browser-extension"
echo ""
read -p "Have you installed the extension? (y/n) " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Please install the extension first."
    exit 1
fi

echo ""
read -p "Enter your Chrome Extension ID: " EXTENSION_ID

if [ -z "$EXTENSION_ID" ]; then
    echo "Extension ID is required!"
    exit 1
fi

# Find the app
APP_PATH=""
if [ -d "/Applications/PocketNext.app" ]; then
    APP_PATH="/Applications/PocketNext.app/Contents/MacOS/PocketNext"
else
    # Look for build version
    BUILD_APP=$(find ~/Library/Developer/Xcode/DerivedData -name "PocketNext.app" -type d 2>/dev/null | head -1)
    if [ -n "$BUILD_APP" ]; then
        APP_PATH="$BUILD_APP/Contents/MacOS/PocketNext"
    fi
fi

if [ -z "$APP_PATH" ] || [ ! -f "$APP_PATH" ]; then
    echo "❌ PocketNext app not found!"
    echo "Please build the app first."
    exit 1
fi

echo "✅ Found app at: $APP_PATH"

# Create manifest
MANIFEST_DIR="$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts"
mkdir -p "$MANIFEST_DIR"

MANIFEST_FILE="$MANIFEST_DIR/com.pocketnext.nativemessaging.json"

cat > "$MANIFEST_FILE" << EOF
{
  "name": "com.pocketnext.nativemessaging",
  "description": "Pocket-next Native Messaging Host",
  "path": "$APP_PATH",
  "type": "stdio",
  "allowed_origins": [
    "chrome-extension://$EXTENSION_ID/"
  ]
}
EOF

echo "✅ Created manifest at: $MANIFEST_FILE"
echo ""
echo "📋 Manifest content:"
cat "$MANIFEST_FILE" | jq .

# Test the setup
echo ""
echo "🧪 Testing native messaging..."
echo '{"type":"ping"}' | "$APP_PATH" 2>&1 | head -5

echo ""
echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Restart Chrome"
echo "2. Click the Pocket-next extension icon"
echo "3. Check the extension console for connection status"
echo ""
echo "To debug, open extension background page console and run:"
echo "chrome.runtime.sendNativeMessage('com.pocketnext.nativemessaging', {type: 'ping'}, r => console.log(r))"