# Pocket-next: Product Requirements Document (PRD)

---

## 1. Purpose  
Capture anything in a click, turn it into a personal, searchable knowledge base, and resurface what matters—summarized, ranked, and ready to revisit or chat about.

---

## 2. Target Users
- **Knowledge workers & students** who save large volumes of links.  
- **Content-curious professionals** (research, product, finance) needing fast recall.  
- **Heavy X / Twitter users** who rely on bookmarks.

---

## 3. Core Value Propositions

| Need                | Current Pain                           | Our Solution                                               |
|---------------------|----------------------------------------|------------------------------------------------------------|
| Friction-free capture | Manual copy/paste, inbox clutter        | 1-click browser button / **⌘⇧S** global hot-key             |
| Content overload    | Saved links never read                 | Instant **TL;DR** + daily / weekly digests                 |
| Hard to recall      | Bookmarks vanish in folders            | AI auto-tagging & **chat retrieval**                       |
| Platform silos      | Twitter, browser, and mobile are separate | Unified cloud library, **X bookmark import**               |

---

## 4. Feature Set

| Area | **Must-have (MVP)** | **Nice-to-have (v2+)** |
|------|--------------------|------------------------|
| **Capture** | Chrome & Safari extensions, iOS Share-sheet, macOS **⌘⇧S** | macOS OCR to capture on-screen text |
| **Ingestion** | Parse URL → fetch article / video transcript, strip boilerplate, store offline | PDF, YouTube auto-transcripts, email forwarding |
| **Intelligence** | LLM summary (150-word TL;DR + key bullets); auto-tagging | Sentiment & reading-time scores; interest-based ranking |
| **Knowledge Base** | Full-text search; filter by tag/source/date | Graph view of related concepts |
| **Feed & Digest** | Mobile feed (chronological & "Top For You"); daily / weekly email digest | Audio read-outs; Siri-style briefing |
| **Archive** | *Dismiss* → moves to **Read** but remains searchable | Spaced-repetition resurfacing |
| **Chat** | ChatGPT-style interface over personal corpus | Fall-through to web search when answer missing |
| **Integrations** | "Log in with X" to pull Twitter bookmarks; Readwise export | Notion & Obsidian push; Kindle highlights import |
| **Privacy & Sync** | End-to-end encryption at rest; iCloud fallback | Self-hosted option for enterprises |

---

## 5. User Journey (Happy Path)

1. Install extension → pin **Save** button.  
2. Hit **⌘⇧S** on an interesting article → toast "Saved ✅".  
3. Phone buzzes at 8 pm: digest email of today's five saves with 2-line summaries.  
4. On commute, open iOS app → swipe card, read TL;DR, tap **Open** if time.  
5. Later: type "Explain fusion energy?" in Chat → answer cites past article with source link.

---

## 6. Technical Architecture (High-Level)

- **Clients**: Native SwiftUI macOS app, SwiftUI iOS app, Chrome/Safari browser extensions.  
- **Backend API**: Python (FastAPI) with type hints, async/await for high performance.  
- **Capture Service**: Python async worker receives URLs and queues processing jobs.  
- **Crawler & Parser**: Python BeautifulSoup/Newspaper3k → HTML → Markdown storage.  
- **LLM Services**: Async Python calls to OpenAI API for summaries & tags.  
- **Vector Store**: `pgvector` extension in Postgres for semantic chat look-ups.  
- **Sync**: WebSocket real-time sync; AES-GCM end-to-end encryption with client-side keys.  
- **Cron Digests**: Python scheduler with Jinja2 templates → AWS SES / Mailgun.

---

## 7. Success Metrics (30-Day Cohort)

| Metric | Target |
|--------|--------|
| **Save-to-read ratio** | ≥ 25 % |
| **WAU / MAU** | ≥ 45 % |
| **Chat helpfulness rating** | ≥ 4 / 5 |

---

## 8. Launch Plan

| Phase | Timeline | Scope |
|-------|----------|-------|
| **Private Alpha** | Weeks 1-4 | Mac & Chrome only, 100 invited users |
| **Public Beta** | Weeks 5-10 | Add iOS feed + daily digest, open wait-list |
| **v1 Launch** | Week 12 | Twitter import, subscription ($5/mo beyond 1 000 items) |

---

## 9. Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| Site blockers / paywalls | Mercury parser fallback, user cookie pass-through |
| LLM cost spikes | Local summarizer for bulk, GPT-4o only on-demand |
| Twitter API changes | Cache tokens, graceful degradation to scraping |
| Privacy concerns | Zero-knowledge encryption, SOC 2 roadmap |

---

## 10. Open Questions

1. Final product **name** (Memory Lane? SnipIt? Keeply?).  
2. Should **chat** be pay-walled or usage-metered?  
3. Store **full videos** or transcripts only?

---

## 11. Technical Implementation Details

### 11.1 Database Schema
- **Users**: Authentication, preferences, subscription status
- **Items**: Saved content with metadata (URL, title, content, summary, tags)
- **Embeddings**: Vector representations for semantic search
- **Digests**: Scheduled digest history and preferences
- **Chat_Sessions**: Conversation history and context

### 11.2 API Endpoints
- `/api/save` - Capture new content
- `/api/items` - CRUD operations for saved items
- `/api/search` - Full-text and semantic search
- `/api/chat` - Chat interface with RAG
- `/api/digest` - Generate and send digests
- `/api/sync` - Real-time synchronization

### 11.3 Security Requirements
- End-to-end encryption for all user content
- OAuth 2.0 for third-party integrations
- Rate limiting on all API endpoints
- Content Security Policy (CSP) headers
- Regular security audits and penetration testing

### 11.4 Performance Requirements
- Content capture: < 3 seconds from click to save
- Search results: < 500ms response time
- Chat responses: < 2 seconds for cached content
- Mobile app startup: < 2 seconds cold start
- 99.9% uptime SLA

### 11.5 Scalability Considerations
- Horizontal scaling for API servers
- CDN for static assets and cached content
- Database read replicas for search operations
- Queue-based processing for LLM operations
- Auto-scaling based on usage patterns

---

### Mock-up Reference  
*(See attached four-screen design: Save pop-over, Home feed, Article detail, Chat view.)* 

---

## 12. UX & Design Philosophy – "Invisible Until Needed"

### 12.1 Design Philosophy: Radical Simplicity & Invisible Intelligence
Looking at this as a UX design challenge, saving should feel like breathing—effortless and automatic. The interface should disappear when capturing, but become richly contextual when consuming.

### 12.2 Core Screen Architecture

#### 1. **Capture Layer (Overlay System)**
```
┌─ Browser Extension ─┐    ┌─ Global Hotkey ─┐    ┌─ Mobile Share ─┐
│ Floating save dot   │    │ ⌘⇧S anywhere    │    │ iOS share sheet │
│ Auto-appears on     │    │ Minimal popup    │    │ One-tap save    │
│ article detection   │    │ with preview     │    │ with haptic     │
└─────────────────────┘    └──────────────────┘    └─────────────────┘
```
**Key Innovation**: The save button should **predict** when you want to save something (e.g., hover over an article for 3+ seconds ⇒ gentle pulse; scroll to the end ⇒ subtle glow).

#### 2. **Home Feed (The River)**
```
┌─────────────────────────────────────┐
│ ◯ Search                    ⚙️ ⭐️   │ ← Minimal header
├─────────────────────────────────────┤
│ 📰 TL;DR: AI discovers fusion...    │ ← Card with smart preview
│    2 min read • 3 related saves     │
│    [Expand] [Archive] [💬]          │
├─────────────────────────────────────┤
│ 🎥 Video: How React Query works     │
│    ▶️ 12:34 • Auto-transcript       │
│    [Watch] [Archive] [💬]           │
└─────────────────────────────────────┘
```
**Key Innovation**: **Adaptive cards** that change based on content type and your behavior.

#### 3. **Reading View (Immersive)**
```
┌─────────────────────────────────────┐
│ ← Back                    💬 🔗 ⋯   │
├─────────────────────────────────────┤
│                                     │
│     Article content in beautiful    │
│     typography with AI highlights   │
│     of key concepts                 │
│                                     │
│ ┌─ AI Summary ─────────────────────┐ │
│ │ Key insight: Fusion energy...    │ │
│ │ Related to 3 other saves         │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```
**Key Innovation**: **Contextual AI annotations** inline, connecting this content to your other saves.

#### 4. **Chat Interface (Conversational Knowledge)**
```
┌─────────────────────────────────────┐
│ 💬 Chat with your knowledge         │
├─────────────────────────────────────┤
│ You: "Explain quantum computing"    │
│                                     │
│ 🤖: Based on your 3 saved articles  │
│     about quantum computing...      │
│     [Source: MIT article you saved] │
│     [Source: IBM blog from last...] │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Ask anything about your saves   │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```
**Key Innovation**: **Source-aware responses** with one-tap access to original content.

### 12.3 Micro-Interactions That Matter
1. **The Save Animation**  – Browser: gentle "whoosh"; Mobile: haptic "catch"; Hotkey: unobtrusive toast.
2. **Smart Previews**       – Hover states show AI-generated previews; progressive disclosure from summary → key points → full content; related saves appear subtly.
3. **Gesture Language**     – Swipe right: Archive (with undo); Swipe left: Share/open; Long-press: Quick actions; Pull-to-refresh: new digests.

### 12.4 Advanced UX Concepts
* **Ambient Intelligence** – e.g., morning briefing mode surfacing trending saves among friends.
* **Contextual Resurfacing** – location-, time-, and project-aware resurfacing of saves.
* **Social Proof Layer** – show how many in your network saved an item with quick comments.

### 12.5 Platform-Specific Innovations
* **macOS** – Menu-bar quick preview, Spotlight search integration, Touch Bar quick actions.
* **iOS**  – Home-screen widget for daily digest, Siri Shortcuts for voice saving, Apple Watch triage.
* **Browser Extension** – Reading-progress sync, annotation tools, smart suggestions based on current page.

### 12.6 "Jony Ive" Principles Applied
1. **Ruthless Simplification** – every screen has one primary action.
2. **Invisible Technology** – AI feels magical, not mechanical.
3. **Human-Centered** – designed for moments of curiosity, not just storage.
4. **Emotional Connection** – saving feels like collecting treasures.
5. **Seamless Integration** – experience feels native on every platform.

### 12.7 Missing Screens to Design
1. **Onboarding Flow** – 3-step setup with instant value.
2. **Settings / Preferences** – AI tuning, digest cadence, privacy controls.
3. **Search Results** – unified view across content types.
4. **Collections / Tags** – visual organization system.
5. **Analytics Dashboard** – insights on reading habits.
6. **Offline Mode** – graceful degradation when offline.

--- 