#!/bin/bash

# Fix Xcode package dependencies
echo "Fixing Xcode package dependencies..."

# iOS Project
echo "Resolving iOS project packages..."
cd /Users/<USER>/git/read-later-ai/ios-app
rm -rf ~/Library/Developer/Xcode/DerivedData/PocketNext-*
xcodebuild -resolvePackageDependencies -project PocketNext.xcodeproj

# macOS Project  
echo "Resolving macOS project packages..."
cd /Users/<USER>/git/read-later-ai/macos-app
rm -rf ~/Library/Developer/Xcode/DerivedData/PocketNext-*
xcodebuild -resolvePackageDependencies -project PocketNext.xcodeproj

echo "Done! The package warnings in Xcode should be resolved."
echo "You may need to:"
echo "1. Close Xcode completely"
echo "2. Open the project again"
echo "3. Wait for Xcode to finish downloading packages (check the activity viewer)"
echo "4. If issues persist, try: File > Packages > Reset Package Caches"