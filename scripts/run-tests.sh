#!/bin/bash

# Script to run tests for iOS and macOS apps with coverage reporting

set -e

echo "🧪 Running iOS App Tests..."
cd ios-app

# Clean build folder
xcodebuild clean \
    -project PocketNext.xcodeproj \
    -scheme PocketNext \
    -destination 'platform=iOS Simulator,name=iPhone 15' \
    > /dev/null 2>&1

# Run tests with coverage
xcodebuild test \
    -project PocketNext.xcodeproj \
    -scheme PocketNext \
    -destination 'platform=iOS Simulator,name=iPhone 15' \
    -enableCodeCoverage YES \
    -resultBundlePath ../test-results/ios-tests.xcresult \
    | xcpretty --test --color || true

echo ""
echo "🧪 Running macOS App Tests..."
cd ../macos-app

# Clean build folder
xcodebuild clean \
    -project PocketNext.xcodeproj \
    -scheme PocketNext \
    -destination 'platform=macOS' \
    > /dev/null 2>&1

# Run tests with coverage
xcodebuild test \
    -project PocketNext.xcodeproj \
    -scheme PocketNext \
    -destination 'platform=macOS' \
    -enableCodeCoverage YES \
    -resultBundlePath ../test-results/macos-tests.xcresult \
    | xcpretty --test --color || true

echo ""
echo "📊 Generating Coverage Reports..."
cd ..

# Create coverage directory
mkdir -p coverage

# Extract coverage data (requires xcov gem)
if command -v xcov &> /dev/null; then
    xcov --project ios-app/PocketNext.xcodeproj \
         --scheme PocketNext \
         --output_directory coverage/ios \
         --json_report \
         > /dev/null 2>&1 || true
         
    xcov --project macos-app/PocketNext.xcodeproj \
         --scheme PocketNext \
         --output_directory coverage/macos \
         --json_report \
         > /dev/null 2>&1 || true
         
    echo "✅ Coverage reports generated in coverage/"
else
    echo "⚠️  Install xcov for detailed coverage reports: gem install xcov"
fi

echo ""
echo "✅ All tests completed!"