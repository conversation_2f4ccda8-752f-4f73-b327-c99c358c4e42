# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Cursor and MCP configuration
.cursor/
mcp.json

# OS specific
.DS_Store

# Task files
tasks.json
tasks/

# Swift/Xcode
.build/
*.xcuserdata/
*.xcworkspace
DerivedData/
.swiftpm/
build/
macos-app/build/
ios-app/build/

# Test coverage
coverage/
browser-extension/coverage/ 