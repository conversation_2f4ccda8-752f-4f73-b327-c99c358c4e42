# Cleanup Inventory: Components to Remove

## Database Components
- [ ] PostgreSQL configuration and connections
  - `app/core/database.py` - SQLAlchemy async setup
  - `app/core/config.py` - DATABASE_URL and DB settings
  - `.env` files with database credentials
  
- [ ] Database Models
  - `app/models/user.py` - User authentication model
  - `app/models/item.py` - Item model with embeddings
  - `app/models/chat.py` - Chat session model
  - `app/models/digest.py` - Digest model
  - `app/models/base.py` - Base model class

- [ ] Alembic Migrations
  - `alembic/` directory
  - `alembic.ini` configuration
  - All migration files

## API Endpoints (to remove or simplify)
- [ ] Authentication endpoints (`app/api/endpoints/auth.py`)
- [ ] User management
- [ ] Chat endpoints (`app/api/endpoints/chat.py`)
- [ ] Digest endpoints (`app/api/endpoints/digest.py`)
- [ ] Complex item management (`app/api/endpoints/items.py`)
- [ ] Server-side search (`app/api/endpoints/search.py`)

## Services (to remove)
- [ ] AI Service with direct OpenAI integration (`app/services/ai.py`)
- [ ] Server-side embedding generation
- [ ] Cache service for embeddings (`app/services/cache.py`)
- [ ] Capture service (`app/services/capture.py`)
- [ ] Search service (`app/services/search.py`)
- [ ] Local summarizer (`app/services/local_summarizer.py`)

## Dependencies to Remove
- [ ] SQLAlchemy and asyncpg
- [ ] pgvector
- [ ] Redis (for old caching)
- [ ] OpenAI SDK (will only be in parse server)
- [ ] BeautifulSoup (moving to parse server)
- [ ] tiktoken
- [ ] nltk
- [ ] PyJWT (no auth needed)

## Tests to Remove/Update
- [ ] Database model tests
- [ ] Integration tests with PostgreSQL
- [ ] Search service tests
- [ ] AI service tests

## Configuration to Update
- [ ] Remove database configuration
- [ ] Remove Redis configuration
- [ ] Simplify environment variables
- [ ] Update requirements.txt