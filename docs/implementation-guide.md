# Implementation Guide: Building Pocket-next

A step-by-step guide to implementing the Pocket-next architecture from scratch.

## Overview

Pocket-next consists of three main components that work together:
1. **Browser Extension** - Captures DOM content
2. **Parse Server** - Extracts structured data using GPT-4o mini  
3. **Native Apps** - Store locally and sync via CloudKit

## Prerequisites

- **Development Tools**:
  - Python 3.11+ (parse server)
  - Node.js 18+ (extension development)  
  - Xcode 15+ (native apps)
  - Docker (optional)

- **API Keys**:
  - OpenAI API key (for GPT-4o mini)
  - Apple Developer account (for CloudKit)

- **Services**:
  - Redis (optional, for caching)

## Step 1: Parse Server Setup

The parse server is the brain that makes universal parsing possible.

### 1.1 Create Project

```bash
mkdir parse-server && cd parse-server
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 1.2 Install Dependencies

```bash
pip install fastapi uvicorn openai beautifulsoup4 redis python-dotenv
```

### 1.3 Environment Configuration

Create `.env`:
```env
OPENAI_API_KEY=sk-...your-key...
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 1.4 Implement Server

Key implementation points:
- Aggressive HTML preprocessing (reduce tokens by 60-80%)
- Structured JSON output from GPT-4o mini
- Redis caching with 7-day TTL
- Rate limiting per user

See `/parse-server/main.py` for full implementation.

### 1.5 Test Locally

```bash
uvicorn main:app --reload

# Test with curl
curl -X POST http://localhost:8000/parse \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com",
    "html_content": "<html>...</html>",
    "content_type": "article",
    "metadata": {}
  }'
```

### 1.6 Deploy

Using Docker:
```bash
docker build -t parse-server .
docker run -p 8000:8000 -e OPENAI_API_KEY=$OPENAI_API_KEY parse-server
```

Or deploy to Cloud Run, Vercel, etc.

## Step 2: Browser Extension

The extension captures DOM content from any webpage.

### 2.1 Project Structure

```
browser-extension/
├── manifest.json
├── src/
│   ├── background.js    # Service worker
│   └── content-capture.js # DOM capture
└── public/
    ├── popup.html       # Extension UI
    └── icons/          # Extension icons
```

### 2.2 Key Files

**manifest.json** - Extension configuration
**content-capture.js** - Universal DOM capture
**background.js** - Handles save commands

### 2.3 Install for Development

1. Open Chrome → `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select `browser-extension` directory

### 2.4 Test Extension

1. Navigate to any webpage
2. Press ⌘+⇧+S (or click extension icon)
3. Verify "Saved ✓" feedback

## Step 3: Native macOS App

The native app provides local storage and search.

### 3.1 Create Xcode Project

1. Open Xcode → Create New Project
2. Choose macOS → App
3. Select SwiftUI interface
4. Enable CloudKit capability

### 3.2 Key Components

```swift
// ContentProcessor.swift
class ContentProcessor {
    func processContent(_ captured: CapturedContent) async throws -> ProcessedArticle {
        // 1. Send to parse server
        let parsed = try await sendToParseServer(captured)
        
        // 2. Generate embeddings locally  
        let embeddings = try await generateLocalEmbeddings(parsed.content)
        
        // 3. Store in SQLite
        try await storage.save(article)
        
        // 4. Queue for CloudKit sync
        try await syncQueue.add(article)
    }
}
```

### 3.3 SQLite Schema

```sql
CREATE TABLE articles (
    id TEXT PRIMARY KEY,
    url TEXT UNIQUE,
    title TEXT,
    content TEXT,
    summary TEXT,
    keywords TEXT,
    captured_at REAL,
    is_archived INTEGER DEFAULT 0
);

CREATE VIRTUAL TABLE articles_fts USING fts5(
    title, content, summary, keywords
);
```

### 3.4 Local Search

```swift
// SearchEngine.swift
func search(_ query: String) async throws -> [SearchResult] {
    // Parallel keyword and semantic search
    async let keywordResults = performKeywordSearch(query)
    async let semanticResults = performSemanticSearch(query)
    
    // Combine and rank
    return rankResults(
        keyword: try await keywordResults,
        semantic: try await semanticResults
    )
}
```

### 3.5 CloudKit Sync

```swift
// SyncEngine.swift
func syncArticle(_ article: ProcessedArticle) async throws {
    let record = CKRecord(recordType: "Article")
    
    // Store metadata
    record["url"] = article.url
    record["title"] = article.title
    
    // Store content as CKAsset
    let contentAsset = CKAsset(fileURL: contentTempURL)
    record["contentAsset"] = contentAsset
    
    try await database.save(record)
}
```

## Step 4: Integration

### 4.1 Browser → Parse Server

Browser extension captures DOM and sends to parse server:

```javascript
// In background.js
const response = await fetch(`${parseServerUrl}/parse`, {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        url: captured.url,
        html_content: captured.htmlContent,
        content_type: captured.metadata.type,
        metadata: captured.metadata
    })
});
```

### 4.2 Parse Server → Native App

Native app receives parsed content via native messaging:

```swift
// Native messaging handler
func handleMessage(_ message: BrowserMessage) async {
    switch message.type {
    case .save:
        let article = try await contentProcessor.process(message.data)
        // Article is now stored locally and queued for sync
    }
}
```

### 4.3 Native App → CloudKit

Automatic sync happens in background:

```swift
// Background sync
func performBackgroundSync() async {
    let unsyncedArticles = try await storage.fetchUnsynced()
    
    for article in unsyncedArticles {
        try await syncEngine.sync(article)
        try await storage.markSynced(article.id)
    }
}
```

## Step 5: Testing

### 5.1 End-to-End Test

1. Install browser extension
2. Start parse server locally
3. Run native app in Xcode
4. Save a webpage with ⌘+⇧+S
5. Verify article appears in native app
6. Search for content
7. Check sync to other devices

### 5.2 Performance Tests

- **Save latency**: Should be <3 seconds
- **Search latency**: Should be <100ms
- **Parse accuracy**: >95% success rate

### 5.3 Cost Verification

Monitor parse server logs:
```python
# Add to parse server
logger.info(f"Tokens used: {response.usage.total_tokens}")
# Should average ~2500 tokens per article
```

## Deployment Checklist

- [ ] Parse server deployed with HTTPS
- [ ] Redis configured for production
- [ ] Browser extension published to stores
- [ ] Native app code signed
- [ ] CloudKit container configured
- [ ] Monitoring and alerts set up

## Common Issues

### Extension Not Capturing

- Check content script injection
- Verify permissions in manifest.json
- Check for CSP restrictions

### Parse Server Errors

- Verify OpenAI API key
- Check rate limits
- Monitor token usage

### Sync Issues

- Verify CloudKit entitlements
- Check network connectivity
- Review CloudKit dashboard

## Next Steps

1. **Optimize**: Reduce token usage further
2. **Enhance**: Add more content types
3. **Expand**: Build iOS app
4. **Monitor**: Track usage and costs

## Resources

- [OpenAI API Docs](https://platform.openai.com/docs)
- [Chrome Extension Docs](https://developer.chrome.com/docs/extensions/mv3/)
- [CloudKit Documentation](https://developer.apple.com/documentation/cloudkit)
- [SQLite FTS5](https://www.sqlite.org/fts5.html)

Remember: The beauty of this architecture is its simplicity. Let AI handle the complex parsing while keeping user data private and search blazingly fast!