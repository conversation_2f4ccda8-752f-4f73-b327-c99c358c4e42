# iOS Test Implementation Plan

## Phase 1: Core Service Tests (Immediate Priority)

### 1. HybridVectorStorageTests.swift

```swift
import XCTest
import GRDB
@testable import PocketNext

class HybridVectorStorageTests: XCTestCase {
    var sut: HybridVectorStorage!
    var mockDatabaseManager: MockDatabaseManager!
    var mockCloudKitSync: MockCloudKitSyncService!
    var mockEmbeddingService: MockEmbeddingService!
    
    // Test cases to implement:
    // - testProcessArticleGeneratesEmbedding
    // - testProcessArticleSkipsIfEmbeddingExists
    // - testSearchSimilarArticles
    // - testRebuildIndexForAllArticles
    // - testHandleModelVersionChange
    // - testCompressionForCloudKit
    // - testPerformanceWithLargeDataset
}
```

### 2. SearchEngineTests.swift

```swift
import XCTest
@testable import PocketNext

class SearchEngineTests: XCTestCase {
    var sut: SearchEngine!
    var mockDatabase: MockDatabaseManager!
    var mockHybridStorage: MockHybridVectorStorage!
    
    // Test cases to implement:
    // - testHybridSearchCombinesResults
    // - testKeywordSearchAccuracy
    // - testSemanticSearchRelevance
    // - testResultRankingAlgorithm
    // - testSearchWithEmptyQuery
    // - testSearchPerformance
    // - testConcurrentSearchRequests
}
```

### 3. VectorOperationsTests.swift

```swift
import XCTest
import Accelerate
@testable import PocketNext

class VectorOperationsTests: XCTestCase {
    // Test cases to implement:
    // - testEmbeddingCompression
    // - testEmbeddingDecompression
    // - testCompressionRatio
    // - testSimilarityCalculation
    // - testCosineSimilarityAccuracy
    // - testPerformanceOptimization
    // - testHandleEdgeCases
}
```

## Phase 2: Updated Existing Tests

### 1. DatabaseManagerTests Updates

Add these test methods to existing DatabaseManagerTests:

```swift
// New test methods to add:
func testSaveArticleWithEmbedding() async throws
func testFetchArticlesWithEmbeddings() async throws
func testSearchByVector() async throws
func testUpdateEmbeddingData() async throws
func testBulkEmbeddingOperations() async throws
```

### 2. CloudKitSyncServiceTests Updates

Add these test methods:

```swift
// New test methods to add:
func testSyncArticleWithEmbedding() async throws
func testCompressedEmbeddingSync() async throws
func testEmbeddingConflictResolution() async throws
func testBatchEmbeddingSync() async throws
```

## Phase 3: Mock Implementations

### 1. MockEmbeddingService

```swift
class MockEmbeddingService: EmbeddingServiceProtocol {
    var shouldSucceed = true
    var mockEmbedding: [Float] = []
    var generateEmbeddingCallCount = 0
    var lastGeneratedText: String?
    
    func generateEmbedding(for text: String) async throws -> [Float] {
        generateEmbeddingCallCount += 1
        lastGeneratedText = text
        
        if shouldSucceed {
            return mockEmbedding.isEmpty ? generateMockEmbedding() : mockEmbedding
        } else {
            throw EmbeddingError.generationFailed
        }
    }
    
    private func generateMockEmbedding() -> [Float] {
        // Generate deterministic embedding based on input
        return Array(repeating: 0.1, count: 1536)
    }
}
```

### 2. MockHybridVectorStorage

```swift
class MockHybridVectorStorage: HybridVectorStorageProtocol {
    var processArticleCallCount = 0
    var searchCallCount = 0
    var mockSearchResults: [(Article, Float)] = []
    
    func processArticle(_ article: Article) async throws {
        processArticleCallCount += 1
    }
    
    func searchSimilar(to query: String, limit: Int) async throws -> [Article] {
        searchCallCount += 1
        return mockSearchResults.map { $0.0 }
    }
}
```

## Test Data Setup

### 1. Test Embeddings Generator

```swift
struct TestEmbeddings {
    static func similar() -> ([Float], [Float]) {
        let base = Array(repeating: 0.5, count: 1536)
        var similar = base
        similar[0] = 0.6 // Slight variation
        return (base, similar)
    }
    
    static func dissimilar() -> ([Float], [Float]) {
        let vec1 = Array(repeating: 0.1, count: 1536)
        let vec2 = Array(repeating: 0.9, count: 1536)
        return (vec1, vec2)
    }
    
    static func orthogonal() -> ([Float], [Float]) {
        var vec1 = Array(repeating: 0.0, count: 1536)
        var vec2 = Array(repeating: 0.0, count: 1536)
        vec1[0] = 1.0
        vec2[1] = 1.0
        return (vec1, vec2)
    }
}
```

### 2. Test Article Factory

```swift
extension Article {
    static func testArticleWithEmbedding(
        title: String = "Test Article",
        embedding: [Float]? = nil
    ) -> Article {
        var article = Article(url: "https://test.com", title: title)
        if let embedding = embedding {
            article.embeddingData = VectorOperations.compressEmbedding(embedding)
            article.hasLocalEmbedding = true
            article.embeddingModelVersion = "test-v1"
        }
        return article
    }
}
```

## Performance Test Suite

```swift
class VectorSearchPerformanceTests: XCTestCase {
    func testSearchPerformanceWith1000Articles() {
        measure {
            // Test implementation
        }
    }
    
    func testEmbeddingGenerationPerformance() {
        measure {
            // Test implementation
        }
    }
    
    func testIndexRebuildPerformance() {
        measure {
            // Test implementation
        }
    }
}
```

## Implementation Timeline

1. **Week 1**: Implement core service tests (HybridVectorStorage, SearchEngine)
2. **Week 2**: Update existing tests and create mocks
3. **Week 3**: Performance tests and integration tests
4. **Week 4**: UI layer tests and final integration

## Success Criteria

- All new services have >80% test coverage
- All tests pass consistently
- Performance benchmarks established
- Mock implementations are reusable
- Integration tests cover critical user flows