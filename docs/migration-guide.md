# Migration Guide: From PostgreSQL Backend to Browser Extension + Parse Server

This guide explains the architectural changes and migration path from the old PostgreSQL-based backend to the new browser extension + parse server architecture.

## Architecture Changes

### Old Architecture
- **Backend**: Python FastAPI with PostgreSQL + pgvector
- **Processing**: Server-side AI processing and embedding generation
- **Storage**: Centralized PostgreSQL database
- **Auth**: JWT-based authentication
- **Sync**: Custom WebSocket sync

### New Architecture
- **Capture**: Browser extension with DOM capture
- **Processing**: Lightweight parse server (GPT-4o mini)
- **Storage**: Local SQLite on device
- **Auth**: CloudKit/device-based
- **Sync**: CloudKit automatic sync

## Key Benefits of Migration

1. **Privacy**: Embeddings never leave device
2. **Cost**: ~10x reduction in operational costs
3. **Performance**: <100ms local search
4. **Reliability**: Works offline
5. **Simplicity**: No user management needed

## Migration Steps

### Phase 1: Data Export (If Applicable)

If you have existing data in PostgreSQL:

```python
# Export script example
import json
from sqlalchemy import create_engine

engine = create_engine(DATABASE_URL)

# Export items
items = []
with engine.connect() as conn:
    result = conn.execute("SELECT * FROM items")
    for row in result:
        items.append({
            "url": row.url,
            "title": row.title,
            "content": row.content,
            "summary": row.summary,
            "tags": row.tags,
            "created_at": row.created_at.isoformat()
        })

with open("export.json", "w") as f:
    json.dump(items, f)
```

### Phase 2: Install New Components

1. **Parse Server**:
```bash
cd parse-server
docker-compose up -d
```

2. **Browser Extension**:
- Chrome: Load unpacked from `/browser-extension`
- Configure parse server URL in extension settings

3. **Native App**:
- Build and install macOS/iOS app
- Import data if needed

### Phase 3: Data Import (Optional)

If migrating existing data:

```swift
// Import in native app
func importLegacyData(from url: URL) async throws {
    let data = try Data(contentsOf: url)
    let items = try JSONDecoder().decode([LegacyItem].self, from: data)
    
    for item in items {
        // Send to parse server for processing
        let parsed = try await parseServer.parse(item)
        
        // Store locally
        try await storage.save(parsed)
    }
}
```

### Phase 4: Remove Old Infrastructure

1. **Backup Database**:
```bash
pg_dump $DATABASE_URL > backup.sql
```

2. **Shutdown Services**:
```bash
docker-compose down
systemctl stop pocket-next-backend
```

3. **Clean Up**:
- Remove PostgreSQL database
- Cancel cloud services
- Update DNS records

## User Migration

### For End Users

1. **Install Browser Extension**
2. **Download Native App**
3. **Import Data** (if applicable)
4. **Start Saving!**

### Communication Template

> Dear Pocket-next Users,
> 
> We're excited to announce a major upgrade that puts your privacy first:
> 
> **What's New:**
> - 🔒 Your data never leaves your device
> - ⚡ 10x faster search
> - 💰 Free for most users
> - 🌐 Works offline
> 
> **Action Required:**
> 1. Install our new browser extension
> 2. Download the native app
> 3. Import your existing saves (optional)
> 
> The old system will remain available until [DATE] for data export.

## Technical Considerations

### API Compatibility

The new system has different APIs:

**Old**: `/api/items/save`
```json
{
  "url": "https://example.com",
  "user_id": "123"
}
```

**New**: `/parse`
```json
{
  "url": "https://example.com",
  "html_content": "<html>...",
  "content_type": "article",
  "metadata": {}
}
```

### Search Differences

**Old**: Server-side PostgreSQL + pgvector
**New**: Local SQLite FTS5 + embeddings

Search syntax remains similar but is now processed locally.

### Authentication Changes

**Old**: Email/password with JWT tokens
**New**: Device-based with CloudKit sync

No more passwords to manage!

## Rollback Plan

If issues arise:

1. **Parse Server**: Can run alongside old backend
2. **Browser Extension**: Can be disabled
3. **Data**: Export functionality available
4. **Old Backend**: Keep running for 30 days

## FAQ

**Q: Will I lose my data?**
A: No, we provide export/import tools.

**Q: What about Android/Windows?**
A: Coming soon! Web app available as fallback.

**Q: Is it really more private?**
A: Yes! Only parsing uses cloud, everything else is local.

**Q: What if parse server is down?**
A: Extension queues saves for later processing.

## Support

- GitHub Issues: [github.com/pocket-next/issues](https://github.com/pocket-next/issues)
- Email: <EMAIL>
- Documentation: [docs.pocket-next.com](https://docs.pocket-next.com)

## Timeline

- **Week 1-2**: Parse server deployment
- **Week 3-4**: Browser extension release
- **Week 5-6**: Native app launch
- **Week 7-8**: Old system deprecation
- **Week 12**: Old system shutdown

Remember: The new architecture is designed to be simpler, more private, and more reliable. We're excited for you to try it!