# Pocket-next Architecture

## Overview

Pocket-next uses a distributed architecture that prioritizes privacy, performance, and cost-effectiveness. The system consists of three main components:

1. **Browser Extension**: Captures DOM content from any webpage
2. **Parse Server**: Uses GPT-4o mini to extract structured data
3. **Native Apps**: Store data locally and generate embeddings privately

## Architecture Diagram

```
┌─────────────────┐     ┌──────────────┐     ┌─────────────┐     ┌──────────┐
│                 │     │              │     │             │     │          │
│ Browser         │────▶│ Parse Server │────▶│ Native App  │────▶│ CloudKit │
│ Extension       │     │ (GPT-4o mini)│     │ (Local AI)  │     │  (Sync)  │
│                 │     │              │     │             │     │          │
└─────────────────┘     └──────────────┘     └─────────────┘     └──────────┘
     DOM Capture         Intelligent Parse    Local Storage       Encrypted Sync
```

## Component Details

### 1. Browser Extension

**Purpose**: Universal content capture without site-specific parsers

**Key Features**:
- Manifest V3 architecture
- DOM capture with minimal preprocessing
- Offline queue for reliability
- Native messaging bridge

**Implementation**:
```javascript
// Capture everything, let AI figure it out
const capturedData = {
  url: window.location.href,
  title: document.title,
  htmlContent: this.getCleanDOM(),
  metadata: this.extractMetadata()
};
```

**Security**:
- Minimal permissions (activeTab only)
- No persistent background pages
- Content script injection on-demand

### 2. Parse Server

**Purpose**: Transform raw DOM into structured, readable content

**Key Features**:
- GPT-4o mini for universal parsing
- Aggressive HTML preprocessing (60-80% token reduction)
- Redis caching with 7-day TTL
- Rate limiting per user

**Cost Optimization**:
```python
def preprocess_html(html: str) -> str:
    # Remove non-content elements
    # Strip unnecessary attributes
    # Preserve semantic structure
    # Result: ~2000 tokens average
```

**Parsing Strategy**:
1. Remove scripts, styles, navigation
2. Extract semantic HTML structure
3. Send to GPT-4o mini with specific prompt
4. Return structured JSON

**Example Response**:
```json
{
  "title": "Article Title",
  "content": "Clean markdown content...",
  "summary": "2-3 sentence summary",
  "keywords": ["AI", "parsing", "web"],
  "author": "Jane Doe",
  "reading_time": 5
}
```

### 3. Native Apps (macOS/iOS)

**Purpose**: Local-first storage with privacy-preserving search

**Key Features**:
- SQLite with FTS5 for full-text search
- Local embedding generation
- Hybrid search (keyword + semantic)
- CloudKit sync

**Database Schema**:
```sql
-- Simplified, optimized schema
CREATE TABLE articles (
    id TEXT PRIMARY KEY,
    url TEXT UNIQUE,
    title TEXT,
    content TEXT,        -- Parsed markdown
    summary TEXT,
    keywords TEXT,       -- JSON array
    captured_at REAL,
    is_archived INTEGER DEFAULT 0
);

CREATE TABLE embeddings (
    article_id TEXT PRIMARY KEY,
    embedding BLOB,      -- Compressed float array
    model_version TEXT
);

CREATE VIRTUAL TABLE articles_fts USING fts5(
    title, content, summary, keywords
);
```

**Local Embedding Generation**:
```swift
// Using SimilaritySearchKit or Core ML
let embedding = try await embeddingModel.embed(article.content)
// Embeddings never leave device
```

### 4. CloudKit Sync

**Purpose**: Seamless sync across Apple devices

**Implementation**:
- CKRecord for metadata
- CKAsset for large content
- Automatic conflict resolution
- End-to-end encryption

## Data Flow

### Save Flow

1. **User Action**: Press ⌘+⇧+S on any webpage
2. **DOM Capture**: Extension captures full page content
3. **Parse Request**: Send to parse server with metadata
4. **AI Processing**: GPT-4o mini extracts structured data
5. **Local Storage**: Native app stores parsed content
6. **Embedding Generation**: Local AI creates embeddings
7. **Sync**: CloudKit syncs to other devices

### Search Flow

1. **Query Input**: User types search query
2. **Parallel Search**:
   - Keyword search via SQLite FTS5
   - Semantic search via local embeddings
3. **Result Ranking**: Combine scores with weights
4. **Display**: Show results in <100ms

## Performance Characteristics

### Latency
- **Save**: 1-3 seconds (mainly parsing)
- **Search**: <100ms (local index)
- **Sync**: Near-instant (CloudKit push)

### Storage
- **Per Article**: ~1KB compressed
- **Embeddings**: ~6KB per article (quantized)
- **Total**: ~10MB per 1000 articles

### Cost
- **Parse Server**: ~$0.0005 per article
- **Infrastructure**: $80-250/month for 100k users
- **User Cost**: Free tier covers most users

## Security & Privacy

### Data Protection
1. **Minimal Server Contact**: Only for parsing
2. **Local Embeddings**: Never sent to cloud
3. **Encrypted Sync**: User-controlled keys
4. **No Analytics**: Zero tracking

### Threat Model
- **Server Compromise**: Limited impact (no user data stored)
- **Extension Compromise**: Limited permissions
- **Sync Interception**: End-to-end encrypted

## Scalability

### Horizontal Scaling
- **Parse Server**: Stateless, scales infinitely
- **Cache Layer**: Redis cluster if needed
- **Native Apps**: No scaling needed (local)

### Bottlenecks
1. **OpenAI API**: Rate limits (mitigated by caching)
2. **Redis Memory**: Cache size (use LRU eviction)
3. **CloudKit**: Apple's limits (generous for our use)

## Monitoring & Observability

### Key Metrics
- **Parse Success Rate**: >95% target
- **Cache Hit Rate**: >80% target
- **API Latency**: p95 <2s
- **Cost per Article**: <$0.001

### Error Handling
1. **Parse Failures**: Fallback to basic extraction
2. **Offline Mode**: Queue for later processing
3. **Sync Conflicts**: Last-write wins
4. **Rate Limits**: Exponential backoff

## Future Enhancements

### Short Term
- [ ] Batch parsing for efficiency
- [ ] Smarter HTML preprocessing
- [ ] Progressive web app

### Long Term
- [ ] Self-hosted parse server option
- [ ] Alternative LLM providers
- [ ] Cross-platform native apps
- [ ] Collaborative features

## Technology Choices

### Why GPT-4o mini?
- **Universal**: Handles any content type
- **Cost**: 10x cheaper than GPT-4
- **Speed**: Fast enough for real-time
- **Quality**: Excellent for extraction tasks

### Why Local Embeddings?
- **Privacy**: User data never leaves device
- **Speed**: No network latency
- **Cost**: No per-query charges
- **Control**: User owns their data

### Why CloudKit?
- **Native**: Built into Apple platforms
- **Free**: Generous quotas
- **Reliable**: Apple's infrastructure
- **Privacy**: End-to-end encryption support

## Conclusion

This architecture balances:
- **Privacy**: Local-first with minimal cloud contact
- **Performance**: <100ms search, <3s save
- **Cost**: <$0.001 per article at scale
- **Reliability**: Offline-first with sync
- **Simplicity**: No complex infrastructure

The key insight: Let AI handle the messy parsing while keeping user data private and search blazingly fast.