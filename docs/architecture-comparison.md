# Architecture Comparison: Old vs New Approach

## Key Differences

### Old Approach (Current Implementation)
1. **Backend-Heavy**: Python FastAPI backend handling all processing
2. **Direct OpenAI Integration**: Backend directly calls OpenAI for summarization and embeddings
3. **PostgreSQL + pgvector**: Using PostgreSQL with pgvector extension for storage and vector search
4. **Server-Side Processing**: All content parsing and AI processing happens on backend
5. **Traditional Web App**: Focus on web application with potential native apps later

### New Approach (Pivot)
1. **Browser Extension First**: Chrome/Safari extension captures DOM directly
2. **Lightweight Parse Server**: Minimal server using GPT-4o mini for parsing only
3. **Native Apps + Local Storage**: SQLite on device with local embeddings
4. **Privacy-First**: Embeddings generated locally, never sent to cloud
5. **CloudKit Sync**: Apple-native sync instead of custom backend
6. **Cost-Optimized**: ~$0.0005 per article vs higher costs with full OpenAI integration

## What Needs to Change

### 1. Remove/Delete
- PostgreSQL database setup and models
- pgvector integration
- Full FastAPI backend (keep only parse server functionality)
- Server-side embedding generation
- Server-side search implementation
- User authentication system (replaced by CloudKit)
- Digest generation features
- Chat interface backend

### 2. Add/Create
- Browser extension (Chrome/Safari)
- Lightweight parse server with GPT-4o mini
- Native macOS/iOS apps
- Local SQLite database
- Local embedding generation
- CloudKit sync implementation
- Native messaging bridge

### 3. Modify
- Simplify content capture to DOM-based approach
- Replace complex parsing with GPT-4o mini
- Move search to client-side SQLite FTS5
- Shift from web-first to native-first

## Migration Tasks

1. **Phase 1: Clean Up Old Code**
   - Remove PostgreSQL models and migrations
   - Delete unused API endpoints
   - Remove server-side AI processing
   - Clean up authentication system

2. **Phase 2: Build Parse Server**
   - Create minimal FastAPI server for parsing
   - Implement GPT-4o mini integration
   - Add Redis caching
   - Deploy as lightweight service

3. **Phase 3: Browser Extension**
   - Create manifest v3 extension
   - Implement DOM capture
   - Add native messaging

4. **Phase 4: Native Apps**
   - Swift implementation for macOS/iOS
   - Local SQLite storage
   - Local embedding generation
   - CloudKit sync

5. **Phase 5: Migration**
   - Export data from old system
   - Import into new architecture
   - Verify data integrity