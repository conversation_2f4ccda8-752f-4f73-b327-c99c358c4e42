# Pocket-next Final Implementation Guide: DOM Capture + GPT-4o mini Parsing

## Architecture Overview

A streamlined approach that captures everything in the browser, sends raw DOM to a lightweight parse server running GPT-4o mini, generates embeddings locally for privacy, and syncs across devices with CloudKit. No manual parsers, no site-specific code - just intelligent AI parsing that handles any content.

```
Browser Extension → Native App → Parse Server → Local Processing → CloudKit Sync
   (Capture DOM)    (Queue)    (GPT-4o mini)    (Embeddings)      (Encrypted)
```

## 1. Browser Extension: Universal DOM Capture

### Core Implementation
```javascript
// manifest.json (v3)
{
  "manifest_version": 3,
  "name": "Pocket-next",
  "permissions": ["activeTab", "storage", "nativeMessaging"],
  "host_permissions": ["<all_urls>"],
  "background": {
    "service_worker": "background.js"
  },
  "commands": {
    "save-page": {
      "suggested_key": {
        "default": "Ctrl+Shift+S",
        "mac": "Command+Shift+S"
      }
    }
  }
}
```

### Universal Content Capture
```javascript
// content-capture.js
class UniversalCapture {
  async capture() {
    // Capture everything - let GPT-4o mini figure it out
    const capturedData = {
      url: window.location.href,
      title: document.title,
      timestamp: new Date().toISOString(),
      
      // Get the full DOM but clean it first
      htmlContent: this.getCleanDOM(),
      
      // Basic metadata for fallback
      metadata: {
        description: document.querySelector('meta[name="description"]')?.content,
        author: document.querySelector('meta[name="author"]')?.content,
        publishDate: this.findPublishDate(),
        type: this.detectContentType()
      }
    };
    
    return capturedData;
  }
  
  getCleanDOM() {
    // Clone to avoid modifying the page
    const cloned = document.documentElement.cloneNode(true);
    
    // Remove only truly useless elements
    const removeSelectors = [
      'script', 'style', 'iframe', 'noscript',
      '[style*="display:none"]', '[style*="visibility:hidden"]'
    ];
    
    removeSelectors.forEach(selector => {
      cloned.querySelectorAll(selector).forEach(el => el.remove());
    });
    
    // Get the HTML string
    return cloned.innerHTML;
  }
  
  detectContentType() {
    const url = window.location.href;
    const patterns = {
      'twitter': /twitter\.com|x\.com/,
      'youtube': /youtube\.com|youtu\.be/,
      'github': /github\.com/,
      'reddit': /reddit\.com/,
      'medium': /medium\.com/,
      'article': /article|post|blog|news/i
    };
    
    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(url)) return type;
    }
    
    return 'generic';
  }
  
  findPublishDate() {
    // Try common selectors but don't stress about it
    const selectors = [
      'meta[property="article:published_time"]',
      'meta[name="publish_date"]',
      'time[datetime]',
      '.published-date',
      '.post-date'
    ];
    
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.getAttribute('content') || 
               element.getAttribute('datetime') || 
               element.textContent;
      }
    }
    
    return null;
  }
}

// Message handler
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'capture') {
    const capture = new UniversalCapture();
    capture.capture().then(sendResponse);
    return true; // Async response
  }
});
```

### Native Messaging Bridge
```javascript
// background.js
chrome.commands.onCommand.addListener((command) => {
  if (command === 'save-page') {
    savePage();
  }
});

chrome.action.onClicked.addListener(() => {
  savePage();
});

async function savePage() {
  const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
  
  // Show immediate feedback
  chrome.action.setBadgeText({text: '...', tabId: tab.id});
  
  try {
    // Capture the page
    const captured = await chrome.tabs.sendMessage(tab.id, {action: 'capture'});
    
    // Send to native app
    chrome.runtime.sendNativeMessage('com.pocketnext.app', {
      type: 'save',
      data: captured
    }, (response) => {
      if (response && response.success) {
        chrome.action.setBadgeText({text: '✓', tabId: tab.id});
        setTimeout(() => {
          chrome.action.setBadgeText({text: '', tabId: tab.id});
        }, 2000);
      }
    });
  } catch (error) {
    chrome.action.setBadgeText({text: '✗', tabId: tab.id});
    console.error('Failed to save:', error);
  }
}
```

## 2. Parse Server: Intelligent GPT-4o mini Processing

### Minimal Server Implementation
```python
# parse_server.py
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional
import openai
import asyncio
import redis
import hashlib
from bs4 import BeautifulSoup
import re

app = FastAPI()
cache = redis.Redis(decode_responses=True)

# Keep API key server-side
openai.api_key = os.environ.get("OPENAI_API_KEY")

class ParseRequest(BaseModel):
    url: str
    html_content: str
    content_type: str
    metadata: Dict

class ParsedArticle(BaseModel):
    title: str
    content: str  # Clean, readable content
    summary: str  # 2-3 sentence summary
    keywords: List[str]  # 5-10 searchable keywords
    author: Optional[str]
    publish_date: Optional[str]
    reading_time: int  # minutes
    content_type: str  # article, tweet_thread, video, etc.

@app.post("/parse", response_model=ParsedArticle)
async def parse_content(request: ParseRequest):
    # Check cache first
    cache_key = hashlib.md5(f"{request.url}:{request.html_content[:1000]}".encode()).hexdigest()
    cached = cache.get(f"parsed:{cache_key}")
    if cached:
        return ParsedArticle.parse_raw(cached)
    
    # Preprocess HTML aggressively - this is KEY for cost savings
    cleaned_text = preprocess_html(request.html_content)
    
    # Create optimized prompt
    prompt = create_parsing_prompt(cleaned_text, request.content_type, request.url)
    
    try:
        response = await openai.ChatCompletion.acreate(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": PARSING_SYSTEM_PROMPT},
                {"role": "user", "content": prompt}
            ],
            temperature=0,  # Deterministic
            response_format={"type": "json_object"}
        )
        
        parsed = ParsedArticle.parse_raw(response.choices[0].message.content)
        
        # Cache for 7 days
        cache.setex(f"parsed:{cache_key}", 604800, parsed.json())
        
        return parsed
        
    except Exception as e:
        # Fallback to basic extraction
        return fallback_parse(request)

def preprocess_html(html: str) -> str:
    """Aggressively clean HTML to minimize tokens"""
    soup = BeautifulSoup(html, 'html.parser')
    
    # Remove all non-content elements
    for tag in soup(['script', 'style', 'meta', 'link', 'noscript', 
                     'header', 'nav', 'footer', 'aside', 'form', 
                     'button', 'input', 'select', 'textarea']):
        tag.decompose()
    
    # Remove all attributes except href for links
    for tag in soup.find_all(True):
        if tag.name == 'a' and tag.has_attr('href'):
            tag.attrs = {'href': tag['href']}
        else:
            tag.attrs = {}
    
    # Get text with some structure preserved
    text_parts = []
    for element in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
                                  'li', 'blockquote', 'pre', 'code']):
        text = element.get_text(strip=True)
        if text:
            if element.name.startswith('h'):
                text_parts.append(f"\n## {text}\n")
            elif element.name == 'li':
                text_parts.append(f"• {text}")
            elif element.name in ['blockquote', 'pre', 'code']:
                text_parts.append(f"\n{text}\n")
            else:
                text_parts.append(text)
    
    # Join with proper spacing
    content = '\n\n'.join(text_parts)
    
    # Final cleanup
    content = re.sub(r'\n{3,}', '\n\n', content)  # Remove excessive newlines
    content = re.sub(r' {2,}', ' ', content)  # Remove excessive spaces
    
    # Limit to ~10k tokens worth of text (~40k chars)
    if len(content) > 40000:
        content = content[:40000] + "..."
    
    return content

PARSING_SYSTEM_PROMPT = """You are an expert content parser for a read-it-later app. 
Extract and structure content into a clean, readable format.

CRITICAL: Return ONLY valid JSON matching this exact structure:
{
  "title": "string",
  "content": "string - main content in clean markdown format",
  "summary": "string - 2-3 sentence summary of key points",
  "keywords": ["array", "of", "5-10", "searchable", "keywords"],
  "author": "string or null",
  "publish_date": "string or null", 
  "reading_time": number,
  "content_type": "string"
}

Guidelines:
- Extract the actual article/post content, not navigation or ads
- Convert to clean markdown format for readability
- For Twitter/X threads, combine tweets into coherent narrative
- For YouTube, create article from transcript if available
- Preserve code blocks and important formatting
- Generate keywords that capture topics, technologies, people, and themes
- Estimate reading time: ~250 words per minute
- Use null for missing fields, never omit fields"""

def create_parsing_prompt(text: str, content_type: str, url: str) -> str:
    if content_type == "twitter":
        return f"""Parse this Twitter/X thread into an article:
URL: {url}

Content:
{text}

Combine tweets into a flowing narrative while preserving the thread structure."""
    
    elif content_type == "youtube":
        return f"""Parse this YouTube page/transcript:
URL: {url}

Content:
{text}

Create a readable article from the video content, organizing by topics."""
    
    else:
        return f"""Parse this web content:
URL: {url}

Content:
{text}

Extract the main article/content in a clean, readable format."""

def fallback_parse(request: ParseRequest) -> ParsedArticle:
    """Basic fallback when GPT parsing fails"""
    soup = BeautifulSoup(request.html_content, 'html.parser')
    
    # Try to get basic info
    title = request.metadata.get('title') or soup.find('h1')?.get_text() or request.url
    
    # Get all text
    text = soup.get_text()
    words = len(text.split())
    
    return ParsedArticle(
        title=title[:200],
        content=text[:5000],  # First 5k chars
        summary="Content extracted from " + request.url,
        keywords=[request.content_type, "unprocessed"],
        author=request.metadata.get('author'),
        publish_date=request.metadata.get('publishDate'),
        reading_time=max(1, words // 250),
        content_type=request.content_type
    )

# Rate limiting per user
from collections import deque
import time

class RateLimiter:
    def __init__(self):
        self.requests = deque()
        self.max_requests_per_minute = 100
    
    async def check_rate(self, user_id: str):
        now = time.time()
        # Remove old requests
        while self.requests and self.requests[0][0] < now - 60:
            self.requests.popleft()
        
        # Count user's requests
        user_count = sum(1 for t, uid in self.requests if uid == user_id)
        if user_count >= self.max_requests_per_minute:
            raise HTTPException(429, "Rate limit exceeded")
        
        self.requests.append((now, user_id))

rate_limiter = RateLimiter()

@app.middleware("http")
async def rate_limit_middleware(request, call_next):
    if request.url.path == "/parse":
        user_id = request.headers.get("X-User-ID", "anonymous")
        await rate_limiter.check_rate(user_id)
    return await call_next(request)
```

### Deployment Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  parse-server:
    build: .
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_URL=redis://redis:6379
    ports:
      - "8000:8000"
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
  
  redis:
    image: redis:alpine
    volumes:
      - redis-data:/data

volumes:
  redis-data:
```

## 3. Native App: Local Processing & Storage

### Swift Implementation
```swift
// ContentProcessor.swift
import Foundation
import NaturalLanguage
import CoreML

class ContentProcessor {
    private let parseServerURL = "https://parse.pocketnext.com"
    private let embeddingModel: SimilarityIndex
    
    init() async throws {
        // Initialize local embedding model
        self.embeddingModel = try await SimilarityIndex(
            model: .distilbert,
            metric: .cosineSimilarity
        )
    }
    
    func processContent(_ captured: CapturedContent) async throws -> ProcessedArticle {
        // Step 1: Send to parse server
        let parsed = try await sendToParseServer(captured)
        
        // Step 2: Generate embeddings locally for privacy
        let embeddings = try await generateLocalEmbeddings(parsed.content)
        
        // Step 3: Create final article
        let article = ProcessedArticle(
            id: UUID(),
            url: captured.url,
            title: parsed.title,
            content: parsed.content,
            summary: parsed.summary,
            keywords: parsed.keywords,
            author: parsed.author,
            publishDate: parsed.publishDate,
            readingTime: parsed.readingTime,
            contentType: parsed.contentType,
            embeddings: embeddings,
            rawHTML: compressHTML(captured.htmlContent),
            capturedAt: Date()
        )
        
        // Step 4: Store locally
        try await storage.save(article)
        
        // Step 5: Queue for sync
        try await syncQueue.add(article)
        
        return article
    }
    
    private func sendToParseServer(_ content: CapturedContent) async throws -> ParsedResponse {
        var request = URLRequest(url: URL(string: "\(parseServerURL)/parse")!)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(getUserID(), forHTTPHeaderField: "X-User-ID")
        
        let payload = ParseRequest(
            url: content.url,
            htmlContent: content.htmlContent,
            contentType: content.metadata.type,
            metadata: content.metadata
        )
        
        request.httpBody = try JSONEncoder().encode(payload)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ParseError.serverError
        }
        
        return try JSONDecoder().decode(ParsedResponse.self, from: data)
    }
    
    private func generateLocalEmbeddings(_ text: String) async throws -> [Float] {
        // Use local model for privacy
        let embedding = try await embeddingModel.embed(text)
        return embedding.vector
    }
    
    private func compressHTML(_ html: String) -> Data {
        // Compress for storage efficiency
        let data = html.data(using: .utf8)!
        return (try? data.compressed(using: .zlib)) ?? data
    }
}
```

### Optimized Database Schema
```sql
-- Simplified schema focused on essentials
CREATE TABLE articles (
    id TEXT PRIMARY KEY,
    url TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,        -- Parsed markdown content
    summary TEXT,
    keywords TEXT,                -- JSON array
    author TEXT,
    publish_date TEXT,
    reading_time INTEGER,
    content_type TEXT,
    raw_html BLOB,               -- Compressed original
    captured_at REAL NOT NULL,
    last_accessed REAL,
    is_archived INTEGER DEFAULT 0,
    sync_status INTEGER DEFAULT 0
);

-- Separate table for embeddings (performance)
CREATE TABLE embeddings (
    article_id TEXT PRIMARY KEY,
    embedding BLOB NOT NULL,      -- Compressed float array
    model_version TEXT,
    FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE
);

-- Full-text search
CREATE VIRTUAL TABLE articles_fts USING fts5(
    title, content, summary, keywords,
    content=articles,
    content_rowid=rowid
);

-- Indexes for performance
CREATE INDEX idx_articles_captured ON articles(captured_at DESC);
CREATE INDEX idx_articles_archived ON articles(is_archived, captured_at DESC);
CREATE INDEX idx_articles_url ON articles(url);

-- Triggers to keep FTS in sync
CREATE TRIGGER articles_ai AFTER INSERT ON articles BEGIN
    INSERT INTO articles_fts(rowid, title, content, summary, keywords)
    VALUES (new.rowid, new.title, new.content, new.summary, new.keywords);
END;

CREATE TRIGGER articles_ad AFTER DELETE ON articles BEGIN
    DELETE FROM articles_fts WHERE rowid = old.rowid;
END;

CREATE TRIGGER articles_au AFTER UPDATE ON articles BEGIN
    UPDATE articles_fts 
    SET title = new.title, content = new.content, 
        summary = new.summary, keywords = new.keywords
    WHERE rowid = new.rowid;
END;
```

### Search Implementation
```swift
// SearchEngine.swift
class SearchEngine {
    private let db: SQLiteDatabase
    private let embeddingIndex: SimilarityIndex
    
    func search(_ query: String) async throws -> [SearchResult] {
        // Parallel keyword and semantic search
        async let keywordResults = performKeywordSearch(query)
        async let semanticResults = performSemanticSearch(query)
        
        // Combine and rank
        let keyword = try await keywordResults
        let semantic = try await semanticResults
        
        return rankResults(keyword: keyword, semantic: semantic)
    }
    
    private func performKeywordSearch(_ query: String) async throws -> [(Article, Double)] {
        let sql = """
            SELECT a.*, bm25(articles_fts) as score
            FROM articles a
            JOIN articles_fts ON a.rowid = articles_fts.rowid
            WHERE articles_fts MATCH ? AND a.is_archived = 0
            ORDER BY score
            LIMIT 50
        """
        
        return try db.query(sql, [query])
    }
    
    private func performSemanticSearch(_ query: String) async throws -> [(Article, Double)] {
        // Generate query embedding
        let queryEmbedding = try await embeddingIndex.embed(query)
        
        // For now, load all embeddings (optimize with vector DB later)
        let allEmbeddings = try await db.query("""
            SELECT e.article_id, e.embedding, a.title
            FROM embeddings e
            JOIN articles a ON e.article_id = a.id
            WHERE a.is_archived = 0
        """)
        
        // Calculate similarities
        let results = allEmbeddings.compactMap { row in
            let similarity = cosineSimilarity(queryEmbedding, row.embedding)
            return (row.article_id, similarity)
        }
        
        // Return top results
        return results
            .sorted { $0.1 > $1.1 }
            .prefix(50)
            .compactMap { try? loadArticle($0.0) }
    }
    
    private func rankResults(keyword: [(Article, Double)], 
                           semantic: [(Article, Double)]) -> [SearchResult] {
        // Combine scores with weights
        var scoreMap: [String: Double] = [:]
        
        // Keyword results get 0.6 weight
        for (article, score) in keyword {
            scoreMap[article.id] = score * 0.6
        }
        
        // Semantic results get 0.4 weight
        for (article, score) in semantic {
            scoreMap[article.id, default: 0] += score * 0.4
        }
        
        // Sort by combined score
        return scoreMap
            .sorted { $0.value > $1.value }
            .prefix(20)
            .compactMap { try? loadArticle($0.key) }
    }
}
```

## 4. CloudKit Sync: Simple & Reliable

### Sync Implementation
```swift
// SyncEngine.swift
class SyncEngine {
    private let container = CKContainer.default()
    private let database = CKContainer.default().privateCloudDatabase
    
    func setupSync() async throws {
        // Subscribe to changes
        let subscription = CKDatabaseSubscription(subscriptionID: "articles")
        subscription.notificationInfo = CKSubscription.NotificationInfo()
        subscription.notificationInfo?.shouldSendContentAvailable = true
        
        try await database.save(subscription)
    }
    
    func syncArticle(_ article: ProcessedArticle) async throws {
        let record = CKRecord(recordType: "Article", 
                             recordID: CKRecord.ID(recordName: article.id.uuidString))
        
        // Metadata in record
        record["url"] = article.url
        record["title"] = article.title
        record["summary"] = article.summary
        record["capturedAt"] = article.capturedAt
        record["contentType"] = article.contentType
        
        // Large data as CKAssets
        let contentData = article.content.data(using: .utf8)!
        let contentURL = FileManager.default.temporaryDirectory
            .appendingPathComponent("\(article.id)-content.txt")
        try contentData.write(to: contentURL)
        record["contentAsset"] = CKAsset(fileURL: contentURL)
        
        // Compressed embeddings
        let embeddingData = compressEmbeddings(article.embeddings)
        let embeddingURL = FileManager.default.temporaryDirectory
            .appendingPathComponent("\(article.id)-embeddings.bin")
        try embeddingData.write(to: embeddingURL)
        record["embeddingAsset"] = CKAsset(fileURL: embeddingURL)
        
        try await database.save(record)
    }
    
    private func compressEmbeddings(_ embeddings: [Float]) -> Data {
        // Quantize to Int8 for 4x compression
        let quantized = embeddings.map { Int8(max(-127, min(127, $0 * 127))) }
        return Data(bytes: quantized, count: quantized.count)
    }
}
```

## 5. Cost Analysis

### GPT-4o mini Parsing Costs
With aggressive HTML preprocessing:
- Average article: ~2,000 input tokens, ~500 output tokens
- Cost per article: $0.00045 (0.045 cents)

Monthly costs by usage:
- 1,000 users (50 saves/month): $22.50
- 10,000 users: $225
- 100,000 users: $2,250

With 80% cache hit rate:
- 100,000 users: ~$450/month

### Infrastructure Costs
- Parse Server (Cloud Run): $50-200/month (auto-scaling)
- Redis Cache (1GB): $30/month
- Total: $80-250/month for 100k users

## 6. Implementation Timeline

### Phase 1: Core MVP (3 weeks)
- Browser extension with universal capture
- Basic parse server with GPT-4o mini
- Local SQLite storage
- Simple keyword search

### Phase 2: Intelligence (3 weeks)
- Local embeddings with SimilaritySearchKit
- Hybrid search implementation
- CloudKit sync setup
- Performance optimizations

### Phase 3: Polish (2 weeks)
- iOS app with share sheet
- Reading experience improvements
- Batch processing optimizations
- Error handling and monitoring

## 7. Key Technical Decisions

### Why This Architecture Works

1. **Universal Parsing**: GPT-4o mini handles ANY website without manual parsers
2. **Cost Effective**: ~$0.0005 per article with caching
3. **Privacy First**: Embeddings never leave device, only parsing uses cloud
4. **Scalable**: Stateless server, client-side storage
5. **Reliable**: Graceful fallbacks, original content always preserved

### Critical Implementation Details

1. **HTML Preprocessing is Essential**: Strip tags aggressively to reduce tokens by 60-80%
2. **Cache Everything**: 7-day cache dramatically reduces API costs
3. **Structured Output**: Use OpenAI's JSON mode for consistent parsing
4. **Local Embeddings**: SimilaritySearchKit or Core ML for privacy
5. **Batch Sync**: Don't sync every save immediately

### Security & Privacy

```swift
// Encryption for sensitive content
extension ProcessedArticle {
    func encrypt(with key: SymmetricKey) throws -> EncryptedArticle {
        let sealedBox = try AES.GCM.seal(
            content.data(using: .utf8)!,
            using: key
        )
        return EncryptedArticle(
            id: id,
            encryptedContent: sealedBox.combined!,
            metadata: PublicMetadata(title: title, url: url)
        )
    }
}
```

## Summary

This final architecture delivers a powerful read-it-later app that:
- Captures ANY web content with zero manual parsing
- Uses GPT-4o mini for intelligent, universal content extraction
- Keeps user data private with local embeddings
- Costs <$0.001 per saved article
- Syncs seamlessly across Apple devices
- Provides instant keyword + semantic search

The key insight: Let AI handle the messy parsing while focusing engineering effort on user experience, performance, and privacy. No more brittle site-specific parsers - just intelligent content understanding that adapts to any website automatically.