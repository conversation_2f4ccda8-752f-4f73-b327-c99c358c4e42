# Claude Development Guidelines for Pocket-next

## Core Development Rules

### 1. Test-Driven Development
- **ALWAYS** write tests for any new code
- **NEVER** comment out failing tests - fix them instead
- **NEVER** skip or disable tests to avoid failures
- Follow the testing pyramid:
  - **Unit Tests** (70%): Test individual functions/methods in isolation
  - **Integration Tests** (20%): Test API endpoints and service interactions
  - **End-to-End Tests** (10%): Test critical user flows
- Write functional tests for business logic
- Use VCR for recording external API responses when needed
- Aim for >80% code coverage
- All tests must pass before committing code

### 2. Pre-Commit Checklist
Before committing ANY code:
- [ ] Run all tests and ensure they pass
- [ ] Check test coverage is sufficient (>80%)
- [ ] Run linter/formatter
- [ ] Run type checker (mypy for Python, TypeScript checks)
- [ ] Run build process
- [ ] Verify no secrets/keys in code
- [ ] Ensure code follows existing patterns

### 3. Code Quality Standards
- Follow existing code conventions in the codebase
- Use type hints in Python (FastAPI)
- Use proper error handling and logging
- Write self-documenting code (minimal comments)
- Keep functions small and focused
- DRY principle - Don't Repeat Yourself

### 4. Security Best Practices
- Never commit secrets, API keys, or credentials
- Use environment variables for configuration
- Implement proper authentication/authorization
- Validate all user inputs
- Use parameterized queries (no SQL injection)
- Enable CORS properly for production

### 5. Testing Commands
```bash
# Python backend
cd backend
pytest tests/unit/
pytest tests/integration/
pytest --cov=app --cov-report=html
mypy app/
ruff check .
ruff format .

# Frontend (when implemented)
# npm test
# npm run test:coverage
# npm run lint
# npm run typecheck
# npm run build
```

### 6. Git Workflow
- Make atomic commits with clear messages
- Reference task IDs in commit messages
- Create feature branches for new work
- Run full test suite before pushing
- Only merge to main when all checks pass

### 7. Documentation
- Update API documentation when adding endpoints
- Document complex business logic
- Keep README files current
- Document environment setup requirements

### 8. Performance Considerations
- Use async/await in Python for I/O operations
- Implement proper database indexing
- Use connection pooling
- Cache frequently accessed data
- Profile before optimizing

### 9. Before Moving to Next Task
- [ ] Current task is 100% complete
- [ ] All tests pass
- [ ] Code is committed and pushed
- [ ] Task status updated in task manager
- [ ] Any blockers documented

### 10. IMPORTANT: Commit and Push Policy
**ALWAYS commit and push your work after completing any significant task or subtask:**
- After implementing a new feature
- After fixing bugs or tests
- After refactoring code
- At the end of each work session
- Before moving to the next task

**Commit Message Format:**
```
feat/fix/refactor: Brief description of changes

- Detailed bullet points of what was done
- Include task numbers if applicable
- Note any important decisions or trade-offs

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
```

## Project-Specific Rules

### Backend (Python/FastAPI)
- Use Pydantic models for validation
- Implement proper exception handling
- Use dependency injection for services
- Follow RESTful conventions
- Document endpoints with OpenAPI

### Frontend (SwiftUI)
- Follow MVVM architecture
- Use Combine for reactive programming
- Implement proper state management
- Handle offline scenarios gracefully
- Test on multiple screen sizes

### Database
- Always use migrations for schema changes
- Never store passwords in plain text
- Use proper indexes for search queries
- Implement soft deletes where appropriate
- Regular backups in production

## Critical Reminders
1. **NEVER** skip tests to save time
2. **ALWAYS** consider security implications
3. **CHECK** for existing implementations before writing new code
4. **VERIFY** changes work across all platforms
5. **ENSURE** backward compatibility
6. **COMMIT AND PUSH** work after each task is completed and fully tested