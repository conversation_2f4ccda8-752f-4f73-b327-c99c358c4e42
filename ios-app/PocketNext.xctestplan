{"configurations": [{"id": "6D8E6C5E-4D6B-4F5E-8C5C-9C5E5D8E6C5E", "name": "Configuration 1", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 60, "maximumTestRepetitions": 3, "testRepetitionMode": "retryOnFailure"}}], "defaultOptions": {"codeCoverage": {"targets": [{"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNext", "name": "PocketNext"}]}, "commandLineArgumentEntries": [{"argument": "-com.apple.CoreData.SQLDebug 0"}], "environmentVariableEntries": [{"key": "GRDB_SQLITE_SUPPRESS_WAL_JOURNAL_MODE_ERRORS", "value": "1"}], "testExecutionOrdering": "random"}, "testTargets": [{"target": {"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNextTests", "name": "PocketNextTests"}}], "version": 1}