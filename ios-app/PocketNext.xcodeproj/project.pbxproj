// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		2A1B2C3D2B5A6E7F00234567 /* PocketNextApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C3C2B5A6E7F00234567 /* PocketNextApp.swift */; };
		2A1B2C3F2B5A6E8100234567 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C3E2B5A6E8100234567 /* ContentView.swift */; };
		2A1B2C412B5A6E8200234567 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2A1B2C402B5A6E8200234567 /* Assets.xcassets */; };
		2A1B2C442B5A6E8200234567 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2A1B2C432B5A6E8200234567 /* Preview Assets.xcassets */; };
		2A1B2C4B2B5A6F0000234567 /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C4A2B5A6F0000234567 /* AppState.swift */; };
		2A1B2C4D2B5A6F0100234567 /* Article.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C4C2B5A6F0100234567 /* Article.swift */; };
		2A1B2C502B5A6F2000234567 /* DatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C4F2B5A6F2000234567 /* DatabaseManager.swift */; };
		2A1B2C522B5A6F2100234567 /* CloudKitSyncService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C512B5A6F2100234567 /* CloudKitSyncService.swift */; };
		2A1B2C542B5A6F2200234567 /* BackgroundTaskService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C532B5A6F2200234567 /* BackgroundTaskService.swift */; };
		2A1B2C562B5A6F2300234567 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C552B5A6F2300234567 /* NotificationService.swift */; };
		2A1B2C582B5A6F2400234567 /* OfflineContentService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C572B5A6F2400234567 /* OfflineContentService.swift */; };
		2A1B2C5B2B5A6F4000234567 /* HomeFeedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C5A2B5A6F4000234567 /* HomeFeedView.swift */; };
		2A1B2C5D2B5A6F4100234567 /* ArticleCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C5C2B5A6F4100234567 /* ArticleCard.swift */; };
		2A1B2C5F2B5A6F4200234567 /* ChatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C5E2B5A6F4200234567 /* ChatView.swift */; };
		2A1B2C612B5A6F4300234567 /* DigestView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C602B5A6F4300234567 /* DigestView.swift */; };
		2A1B2C632B5A6F4400234567 /* FilterSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C622B5A6F4400234567 /* FilterSheet.swift */; };
		2A1B2C652B5A6F4500234567 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C642B5A6F4500234567 /* ProfileView.swift */; };
		2A1B2C672B5A6F4600234567 /* ReadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C662B5A6F4600234567 /* ReadingView.swift */; };
		2A1B2C692B5A6F4700234567 /* SearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A1B2C682B5A6F4700234567 /* SearchView.swift */; };
		2A1B2C6C2B5A6F8000234567 /* GRDB in Frameworks */ = {isa = PBXBuildFile; productRef = 2A1B2C6B2B5A6F8000234567 /* GRDB */; };
		2A1B2C6F2B5A6F8500234567 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 2A1B2C6E2B5A6F8500234567 /* Alamofire */; };
		2A1B2C722B5A6F8A00234567 /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = 2A1B2C712B5A6F8A00234567 /* Kingfisher */; };
		2A1B2C752B5A6F8F00234567 /* SwiftyJSON in Frameworks */ = {isa = PBXBuildFile; productRef = 2A1B2C742B5A6F8F00234567 /* SwiftyJSON */; };
		2A1B2C782B5A6F9400234567 /* Markdown in Frameworks */ = {isa = PBXBuildFile; productRef = 2A1B2C772B5A6F9400234567 /* Markdown */; };
		2A1B2C7B2B5A6F9900234567 /* Algorithms in Frameworks */ = {isa = PBXBuildFile; productRef = 2A1B2C7A2B5A6F9900234567 /* Algorithms */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2A1B2C392B5A6E7F00234567 /* PocketNext.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PocketNext.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2A1B2C3C2B5A6E7F00234567 /* PocketNextApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PocketNextApp.swift; sourceTree = "<group>"; };
		2A1B2C3E2B5A6E8100234567 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		2A1B2C402B5A6E8200234567 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2A1B2C432B5A6E8200234567 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		2A1B2C4A2B5A6F0000234567 /* AppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppState.swift; sourceTree = "<group>"; };
		2A1B2C4C2B5A6F0100234567 /* Article.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Article.swift; sourceTree = "<group>"; };
		2A1B2C4F2B5A6F2000234567 /* DatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseManager.swift; sourceTree = "<group>"; };
		2A1B2C512B5A6F2100234567 /* CloudKitSyncService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloudKitSyncService.swift; sourceTree = "<group>"; };
		2A1B2C532B5A6F2200234567 /* BackgroundTaskService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackgroundTaskService.swift; sourceTree = "<group>"; };
		2A1B2C552B5A6F2300234567 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		2A1B2C572B5A6F2400234567 /* OfflineContentService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfflineContentService.swift; sourceTree = "<group>"; };
		2A1B2C5A2B5A6F4000234567 /* HomeFeedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeFeedView.swift; sourceTree = "<group>"; };
		2A1B2C5C2B5A6F4100234567 /* ArticleCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ArticleCard.swift; sourceTree = "<group>"; };
		2A1B2C5E2B5A6F4200234567 /* ChatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatView.swift; sourceTree = "<group>"; };
		2A1B2C602B5A6F4300234567 /* DigestView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DigestView.swift; sourceTree = "<group>"; };
		2A1B2C622B5A6F4400234567 /* FilterSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterSheet.swift; sourceTree = "<group>"; };
		2A1B2C642B5A6F4500234567 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		2A1B2C662B5A6F4600234567 /* ReadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReadingView.swift; sourceTree = "<group>"; };
		2A1B2C682B5A6F4700234567 /* SearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchView.swift; sourceTree = "<group>"; };
		2A1B2C7C2B5A6FA000234567 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2A1B2C362B5A6E7F00234567 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2A1B2C6C2B5A6F8000234567 /* GRDB in Frameworks */,
				2A1B2C6F2B5A6F8500234567 /* Alamofire in Frameworks */,
				2A1B2C722B5A6F8A00234567 /* Kingfisher in Frameworks */,
				2A1B2C752B5A6F8F00234567 /* SwiftyJSON in Frameworks */,
				2A1B2C782B5A6F9400234567 /* Markdown in Frameworks */,
				2A1B2C7B2B5A6F9900234567 /* Algorithms in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2A1B2C302B5A6E7F00234567 = {
			isa = PBXGroup;
			children = (
				2A1B2C3B2B5A6E7F00234567 /* PocketNext */,
				2A1B2C3A2B5A6E7F00234567 /* Products */,
			);
			sourceTree = "<group>";
		};
		2A1B2C3A2B5A6E7F00234567 /* Products */ = {
			isa = PBXGroup;
			children = (
				2A1B2C392B5A6E7F00234567 /* PocketNext.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2A1B2C3B2B5A6E7F00234567 /* PocketNext */ = {
			isa = PBXGroup;
			children = (
				2A1B2C3C2B5A6E7F00234567 /* PocketNextApp.swift */,
				2A1B2C7C2B5A6FA000234567 /* Info.plist */,
				2A1B2C402B5A6E8200234567 /* Assets.xcassets */,
				2A1B2C492B5A6EF000234567 /* Models */,
				2A1B2C4E2B5A6F1000234567 /* Services */,
				2A1B2C592B5A6F3000234567 /* Views */,
				2A1B2C422B5A6E8200234567 /* Preview Content */,
			);
			path = PocketNext;
			sourceTree = "<group>";
		};
		2A1B2C422B5A6E8200234567 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				2A1B2C432B5A6E8200234567 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		2A1B2C492B5A6EF000234567 /* Models */ = {
			isa = PBXGroup;
			children = (
				2A1B2C4A2B5A6F0000234567 /* AppState.swift */,
				2A1B2C4C2B5A6F0100234567 /* Article.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		2A1B2C4E2B5A6F1000234567 /* Services */ = {
			isa = PBXGroup;
			children = (
				2A1B2C4F2B5A6F2000234567 /* DatabaseManager.swift */,
				2A1B2C512B5A6F2100234567 /* CloudKitSyncService.swift */,
				2A1B2C532B5A6F2200234567 /* BackgroundTaskService.swift */,
				2A1B2C552B5A6F2300234567 /* NotificationService.swift */,
				2A1B2C572B5A6F2400234567 /* OfflineContentService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		2A1B2C592B5A6F3000234567 /* Views */ = {
			isa = PBXGroup;
			children = (
				2A1B2C3E2B5A6E8100234567 /* ContentView.swift */,
				2A1B2C5A2B5A6F4000234567 /* HomeFeedView.swift */,
				2A1B2C5C2B5A6F4100234567 /* ArticleCard.swift */,
				2A1B2C5E2B5A6F4200234567 /* ChatView.swift */,
				2A1B2C602B5A6F4300234567 /* DigestView.swift */,
				2A1B2C622B5A6F4400234567 /* FilterSheet.swift */,
				2A1B2C642B5A6F4500234567 /* ProfileView.swift */,
				2A1B2C662B5A6F4600234567 /* ReadingView.swift */,
				2A1B2C682B5A6F4700234567 /* SearchView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2A1B2C382B5A6E7F00234567 /* PocketNext */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A1B2C482B5A6E8200234567 /* Build configuration list for PBXNativeTarget "PocketNext" */;
			buildPhases = (
				2A1B2C352B5A6E7F00234567 /* Sources */,
				2A1B2C362B5A6E7F00234567 /* Frameworks */,
				2A1B2C372B5A6E7F00234567 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PocketNext;
			packageProductDependencies = (
				2A1B2C6B2B5A6F8000234567 /* GRDB */,
				2A1B2C6E2B5A6F8500234567 /* Alamofire */,
				2A1B2C712B5A6F8A00234567 /* Kingfisher */,
				2A1B2C742B5A6F8F00234567 /* SwiftyJSON */,
				2A1B2C772B5A6F9400234567 /* Markdown */,
				2A1B2C7A2B5A6F9900234567 /* Algorithms */,
			);
			productName = PocketNext;
			productReference = 2A1B2C392B5A6E7F00234567 /* PocketNext.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2A1B2C312B5A6E7F00234567 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					2A1B2C382B5A6E7F00234567 = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 2A1B2C342B5A6E7F00234567 /* Build configuration list for PBXProject "PocketNext" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2A1B2C302B5A6E7F00234567;
			packageReferences = (
				2A1B2C6A2B5A6F8000234567 /* XCRemoteSwiftPackageReference "GRDB.swift" */,
				2A1B2C6D2B5A6F8500234567 /* XCRemoteSwiftPackageReference "Alamofire" */,
				2A1B2C702B5A6F8A00234567 /* XCRemoteSwiftPackageReference "Kingfisher" */,
				2A1B2C732B5A6F8F00234567 /* XCRemoteSwiftPackageReference "SwiftyJSON" */,
				2A1B2C762B5A6F9400234567 /* XCRemoteSwiftPackageReference "swift-markdown" */,
				2A1B2C792B5A6F9900234567 /* XCRemoteSwiftPackageReference "swift-algorithms" */,
			);
			productRefGroup = 2A1B2C3A2B5A6E7F00234567 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2A1B2C382B5A6E7F00234567 /* PocketNext */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2A1B2C372B5A6E7F00234567 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2A1B2C442B5A6E8200234567 /* Preview Assets.xcassets in Resources */,
				2A1B2C412B5A6E8200234567 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2A1B2C352B5A6E7F00234567 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2A1B2C3F2B5A6E8100234567 /* ContentView.swift in Sources */,
				2A1B2C3D2B5A6E7F00234567 /* PocketNextApp.swift in Sources */,
				2A1B2C4B2B5A6F0000234567 /* AppState.swift in Sources */,
				2A1B2C4D2B5A6F0100234567 /* Article.swift in Sources */,
				2A1B2C502B5A6F2000234567 /* DatabaseManager.swift in Sources */,
				2A1B2C522B5A6F2100234567 /* CloudKitSyncService.swift in Sources */,
				2A1B2C542B5A6F2200234567 /* BackgroundTaskService.swift in Sources */,
				2A1B2C562B5A6F2300234567 /* NotificationService.swift in Sources */,
				2A1B2C582B5A6F2400234567 /* OfflineContentService.swift in Sources */,
				2A1B2C5B2B5A6F4000234567 /* HomeFeedView.swift in Sources */,
				2A1B2C5D2B5A6F4100234567 /* ArticleCard.swift in Sources */,
				2A1B2C5F2B5A6F4200234567 /* ChatView.swift in Sources */,
				2A1B2C612B5A6F4300234567 /* DigestView.swift in Sources */,
				2A1B2C632B5A6F4400234567 /* FilterSheet.swift in Sources */,
				2A1B2C652B5A6F4500234567 /* ProfileView.swift in Sources */,
				2A1B2C672B5A6F4600234567 /* ReadingView.swift in Sources */,
				2A1B2C692B5A6F4700234567 /* SearchView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2A1B2C462B5A6E8200234567 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2A1B2C472B5A6E8200234567 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2A1B2C492B5A6E8200234567 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PocketNext/PocketNext.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PocketNext/Preview Content\"";
				DEVELOPMENT_TEAM = 25853VT55X;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PocketNext/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PocketNext;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pocketnext.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "PocketNext/PocketNext-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2A1B2C4A2B5A6E8200234567 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PocketNext/PocketNext.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PocketNext/Preview Content\"";
				DEVELOPMENT_TEAM = 25853VT55X;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PocketNext/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PocketNext;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pocketnext.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "PocketNext/PocketNext-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2A1B2C342B5A6E7F00234567 /* Build configuration list for PBXProject "PocketNext" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A1B2C462B5A6E8200234567 /* Debug */,
				2A1B2C472B5A6E8200234567 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A1B2C482B5A6E8200234567 /* Build configuration list for PBXNativeTarget "PocketNext" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A1B2C492B5A6E8200234567 /* Debug */,
				2A1B2C4A2B5A6E8200234567 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		2A1B2C6A2B5A6F8000234567 /* XCRemoteSwiftPackageReference "GRDB.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/groue/GRDB.swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.29.3;
			};
		};
		2A1B2C6D2B5A6F8500234567 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.8.0;
			};
		};
		2A1B2C702B5A6F8A00234567 /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 7.10.0;
			};
		};
		2A1B2C732B5A6F8F00234567 /* XCRemoteSwiftPackageReference "SwiftyJSON" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyJSON/SwiftyJSON.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		2A1B2C762B5A6F9400234567 /* XCRemoteSwiftPackageReference "swift-markdown" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-markdown.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.2.0;
			};
		};
		2A1B2C792B5A6F9900234567 /* XCRemoteSwiftPackageReference "swift-algorithms" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-algorithms.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		2A1B2C6B2B5A6F8000234567 /* GRDB */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A1B2C6A2B5A6F8000234567 /* XCRemoteSwiftPackageReference "GRDB.swift" */;
			productName = GRDB;
		};
		2A1B2C6E2B5A6F8500234567 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A1B2C6D2B5A6F8500234567 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		2A1B2C712B5A6F8A00234567 /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A1B2C702B5A6F8A00234567 /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
		2A1B2C742B5A6F8F00234567 /* SwiftyJSON */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A1B2C732B5A6F8F00234567 /* XCRemoteSwiftPackageReference "SwiftyJSON" */;
			productName = SwiftyJSON;
		};
		2A1B2C772B5A6F9400234567 /* Markdown */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A1B2C762B5A6F9400234567 /* XCRemoteSwiftPackageReference "swift-markdown" */;
			productName = Markdown;
		};
		2A1B2C7A2B5A6F9900234567 /* Algorithms */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2A1B2C792B5A6F9900234567 /* XCRemoteSwiftPackageReference "swift-algorithms" */;
			productName = Algorithms;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2A1B2C312B5A6E7F00234567 /* Project object */;
}
