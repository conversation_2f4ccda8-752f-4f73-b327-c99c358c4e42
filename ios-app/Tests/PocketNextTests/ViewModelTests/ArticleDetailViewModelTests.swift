import XCTest
import Combine
@testable import PocketNext

@MainActor
class ArticleDetailViewModelTests: XCTestCase {
    var sut: ArticleDetailViewModel!
    var mockDatabaseManager: MockDatabaseManager!
    var mockOfflineService: MockOfflineContentService!
    var testArticle: Article!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        try await super.setUp()
        
        mockDatabaseManager = MockDatabaseManager()
        mockOfflineService = MockOfflineContentService()
        cancellables = Set<AnyCancellable>()
        
        testArticle = Article(url: "https://test.com", title: "Test Article")
        testArticle.content = "This is test content for the article."
        
        sut = ArticleDetailViewModel(article: testArticle)
        // Inject mocks
    }
    
    override func tearDown() {
        sut = nil
        mockDatabaseManager = nil
        mockOfflineService = nil
        testArticle = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization() {
        // Then
        XCTAssertEqual(sut.article.id, testArticle.id)
        XCTAssertEqual(sut.article.title, testArticle.title)
        XCTAssertFalse(sut.isLoading)
        XCTAssertEqual(sut.readingProgress, 0.0)
        XCTAssertEqual(sut.fontSize, 16)
        XCTAssertEqual(sut.fontFamily, "System")
        XCTAssertFalse(sut.isDarkMode)
    }
    
    // MARK: - Reading Progress Tests
    
    func testUpdateReadingProgress() async {
        // Given
        let newProgress = 0.5
        
        // When
        await sut.updateReadingProgress(newProgress)
        
        // Then
        XCTAssertEqual(sut.readingProgress, newProgress)
        XCTAssertEqual(sut.article.readProgress, newProgress)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    func testMarkAsRead() async {
        // Given
        XCTAssertFalse(sut.article.isRead)
        
        // When
        await sut.markAsRead()
        
        // Then
        XCTAssertTrue(sut.article.isRead)
        XCTAssertEqual(sut.article.readProgress, 1.0)
        XCTAssertTrue(mockDatabaseManager.markAsReadCalled)
        XCTAssertEqual(mockDatabaseManager.markedAsReadId, testArticle.id)
    }
    
    func testAutoMarkAsReadWhenProgressComplete() async {
        // Given
        XCTAssertFalse(sut.article.isRead)
        
        // When
        await sut.updateReadingProgress(0.95) // 95% triggers auto-mark as read
        
        // Then
        XCTAssertTrue(sut.article.isRead)
        XCTAssertTrue(mockDatabaseManager.markAsReadCalled)
    }
    
    // MARK: - Favorite Tests
    
    func testToggleFavorite() async {
        // Given
        XCTAssertFalse(sut.article.isFavorite)
        
        // When
        await sut.toggleFavorite()
        
        // Then
        XCTAssertTrue(sut.article.isFavorite)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
        
        // When - toggle again
        await sut.toggleFavorite()
        
        // Then
        XCTAssertFalse(sut.article.isFavorite)
    }
    
    // MARK: - Archive Tests
    
    func testArchiveArticle() async {
        // Given
        XCTAssertFalse(sut.article.isArchived)
        
        // When
        await sut.archiveArticle()
        
        // Then
        XCTAssertTrue(sut.article.isArchived)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    func testUnarchiveArticle() async {
        // Given
        sut.article.isArchived = true
        
        // When
        await sut.unarchiveArticle()
        
        // Then
        XCTAssertFalse(sut.article.isArchived)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    // MARK: - Share Tests
    
    func testShareArticle() {
        // When
        let shareItems = sut.shareArticle()
        
        // Then
        XCTAssertEqual(shareItems.count, 2)
        XCTAssertTrue(shareItems.contains(where: { $0 is String }))
        XCTAssertTrue(shareItems.contains(where: { $0 is URL }))
    }
    
    // MARK: - Font Settings Tests
    
    func testIncreaseFontSize() {
        // Given
        let initialSize = sut.fontSize
        
        // When
        sut.increaseFontSize()
        
        // Then
        XCTAssertEqual(sut.fontSize, initialSize + 2)
        
        // Test max limit
        sut.fontSize = 28
        sut.increaseFontSize()
        XCTAssertEqual(sut.fontSize, 30) // Should cap at 30
    }
    
    func testDecreaseFontSize() {
        // Given
        sut.fontSize = 16
        
        // When
        sut.decreaseFontSize()
        
        // Then
        XCTAssertEqual(sut.fontSize, 14)
        
        // Test min limit
        sut.fontSize = 12
        sut.decreaseFontSize()
        XCTAssertEqual(sut.fontSize, 10) // Should stop at 10
    }
    
    func testChangeFontFamily() {
        // Given
        let fonts = ["System", "Georgia", "Helvetica", "Times New Roman"]
        
        // When & Then
        for font in fonts {
            sut.fontFamily = font
            XCTAssertEqual(sut.fontFamily, font)
        }
    }
    
    func testToggleDarkMode() {
        // Given
        XCTAssertFalse(sut.isDarkMode)
        
        // When
        sut.isDarkMode = true
        
        // Then
        XCTAssertTrue(sut.isDarkMode)
    }
    
    // MARK: - Offline Content Tests
    
    func testDownloadForOffline() async {
        // Given
        XCTAssertFalse(sut.article.isDownloadedForOffline)
        mockOfflineService.shouldSucceed = true
        
        // When
        await sut.downloadForOffline()
        
        // Then
        XCTAssertTrue(sut.article.isDownloadedForOffline)
        XCTAssertTrue(mockOfflineService.downloadArticleCalled)
    }
    
    func testDownloadForOfflineFailure() async {
        // Given
        mockOfflineService.shouldSucceed = false
        
        // When
        await sut.downloadForOffline()
        
        // Then
        XCTAssertFalse(sut.article.isDownloadedForOffline)
        XCTAssertNotNil(sut.errorMessage)
    }
    
    func testRemoveOfflineContent() async {
        // Given
        sut.article.isDownloadedForOffline = true
        mockOfflineService.shouldSucceed = true
        
        // When
        await sut.removeOfflineContent()
        
        // Then
        XCTAssertFalse(sut.article.isDownloadedForOffline)
        XCTAssertTrue(mockOfflineService.deleteOfflineContentCalled)
    }
    
    // MARK: - Navigation Tests
    
    func testNavigateToLink() {
        // Given
        let testURL = URL(string: "https://example.com")!
        
        // When
        sut.navigateToLink(testURL)
        
        // Then
        XCTAssertEqual(sut.selectedURL, testURL)
        XCTAssertTrue(sut.showingWebView)
    }
    
    // MARK: - Content Processing Tests
    
    func testProcessedContent() {
        // Given
        sut.article.content = "<p>Test <a href='https://example.com'>link</a></p>"
        sut.fontSize = 18
        sut.fontFamily = "Georgia"
        sut.isDarkMode = true
        
        // When
        let processed = sut.processedContent
        
        // Then
        XCTAssertTrue(processed.contains("font-size: 18px"))
        XCTAssertTrue(processed.contains("font-family: Georgia"))
        XCTAssertTrue(processed.contains("background-color: #1c1c1e")) // Dark mode
        XCTAssertTrue(processed.contains("color: #ffffff")) // Dark mode text
    }
    
    // MARK: - Highlighting Tests
    
    func testAddHighlight() async {
        // Given
        let selectedText = "test content"
        let highlightColor = "yellow"
        
        // When
        await sut.addHighlight(text: selectedText, color: highlightColor)
        
        // Then
        XCTAssertEqual(sut.highlights.count, 1)
        XCTAssertEqual(sut.highlights.first?.text, selectedText)
        XCTAssertEqual(sut.highlights.first?.color, highlightColor)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    func testRemoveHighlight() async {
        // Given
        let highlight = ArticleHighlight(text: "test", color: "yellow", position: 0)
        sut.highlights = [highlight]
        
        // When
        await sut.removeHighlight(highlight)
        
        // Then
        XCTAssertTrue(sut.highlights.isEmpty)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    // MARK: - Notes Tests
    
    func testAddNote() async {
        // Given
        let noteText = "This is an important note"
        
        // When
        await sut.addNote(noteText)
        
        // Then
        XCTAssertEqual(sut.notes.count, 1)
        XCTAssertEqual(sut.notes.first?.text, noteText)
        XCTAssertNotNil(sut.notes.first?.createdAt)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    func testUpdateNote() async {
        // Given
        let note = ArticleNote(text: "Original note", createdAt: Date())
        sut.notes = [note]
        let updatedText = "Updated note"
        
        // When
        await sut.updateNote(note, newText: updatedText)
        
        // Then
        XCTAssertEqual(sut.notes.first?.text, updatedText)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    func testDeleteNote() async {
        // Given
        let note = ArticleNote(text: "Note to delete", createdAt: Date())
        sut.notes = [note]
        
        // When
        await sut.deleteNote(note)
        
        // Then
        XCTAssertTrue(sut.notes.isEmpty)
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
    }
    
    // MARK: - Error Handling Tests
    
    func testHandleDatabaseError() async {
        // Given
        mockDatabaseManager.shouldThrowError = true
        
        // When
        await sut.markAsRead()
        
        // Then
        XCTAssertNotNil(sut.errorMessage)
        XCTAssertTrue(sut.errorMessage?.contains("Failed to update") ?? false)
    }
}

// MARK: - Mock Classes

class MockOfflineContentService: OfflineContentService {
    var shouldSucceed = true
    var downloadArticleCalled = false
    var deleteOfflineContentCalled = false
    var downloadedArticle: Article?
    
    override func downloadArticleForOffline(_ article: Article) async throws -> Article {
        downloadArticleCalled = true
        downloadedArticle = article
        
        if shouldSucceed {
            var updated = article
            updated.isDownloadedForOffline = true
            updated.downloadedAt = Date()
            return updated
        } else {
            throw OfflineContentError.downloadFailed
        }
    }
    
    override func deleteOfflineContent(for article: Article) async throws -> Article {
        deleteOfflineContentCalled = true
        
        if shouldSucceed {
            var updated = article
            updated.isDownloadedForOffline = false
            updated.downloadedAt = nil
            return updated
        } else {
            throw OfflineContentError.deletionFailed
        }
    }
}

// MARK: - Supporting Types

struct ArticleHighlight: Identifiable, Equatable {
    let id = UUID()
    let text: String
    let color: String
    let position: Int
}

struct ArticleNote: Identifiable, Equatable {
    let id = UUID()
    var text: String
    let createdAt: Date
    
    static func == (lhs: ArticleNote, rhs: ArticleNote) -> Bool {
        lhs.id == rhs.id
    }
}