import XCTest
import Combine
@testable import PocketNext

@MainActor
class SearchViewModelTests: XCTestCase {
    var sut: SearchViewModel!
    var mockDatabaseManager: MockDatabaseManager!
    var mockSearchEngine: MockSearchEngine!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        try await super.setUp()
        
        mockDatabaseManager = MockDatabaseManager()
        mockSearchEngine = MockSearchEngine()
        cancellables = Set<AnyCancellable>()
        
        sut = SearchViewModel()
        // Inject mocks
    }
    
    override func tearDown() {
        sut = nil
        mockDatabaseManager = nil
        mockSearchEngine = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testInitialState() {
        // Then
        XCTAssertTrue(sut.searchResults.isEmpty)
        XCTAssertFalse(sut.isSearching)
        XCTAssertTrue(sut.recentSearches.isEmpty)
        XCTAssertNil(sut.errorMessage)
    }
    
    // MARK: - Search Tests
    
    func testSearchWithValidQuery() async {
        // Given
        let query = "SwiftUI"
        let expectedResults = [
            Article(url: "https://test1.com", title: "SwiftUI Tutorial"),
            Article(url: "https://test2.com", title: "Advanced SwiftUI")
        ]
        mockSearchEngine.resultsToReturn = expectedResults
        
        // When
        await sut.search(query: query, filter: .all)
        
        // Then
        XCTAssertEqual(sut.searchResults.count, 2)
        XCTAssertFalse(sut.isSearching)
        XCTAssertTrue(sut.recentSearches.contains(query))
    }
    
    func testSearchWithEmptyQuery() async {
        // Given
        let query = ""
        
        // When
        await sut.search(query: query, filter: .all)
        
        // Then
        XCTAssertTrue(sut.searchResults.isEmpty)
        XCTAssertFalse(mockSearchEngine.searchCalled)
    }
    
    func testSearchShowsLoadingState() async {
        // Given
        let expectation = expectation(description: "Loading states")
        var loadingStates: [Bool] = []
        
        sut.$isSearching
            .sink { isSearching in
                loadingStates.append(isSearching)
                if loadingStates.count == 3 {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await sut.search(query: "test", filter: .all)
        
        // Then
        await fulfillment(of: [expectation], timeout: 1.0)
        XCTAssertEqual(loadingStates, [false, true, false])
    }
    
    // MARK: - Filter Tests
    
    func testSearchWithUnreadFilter() async {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Read Article"),
            Article(url: "https://test2.com", title: "Unread Article")
        ]
        articles[0].isRead = true
        mockSearchEngine.resultsToReturn = articles
        
        // When
        await sut.search(query: "Article", filter: .unread)
        
        // Then
        XCTAssertEqual(mockSearchEngine.lastFilter, .unread)
        XCTAssertEqual(sut.searchResults.count, 2) // Mock returns all, filtering happens in view
    }
    
    func testSearchWithArchivedFilter() async {
        // Given
        mockSearchEngine.resultsToReturn = [
            Article(url: "https://test.com", title: "Archived Article")
        ]
        
        // When
        await sut.search(query: "test", filter: .archived)
        
        // Then
        XCTAssertEqual(mockSearchEngine.lastFilter, .archived)
    }
    
    func testSearchWithFavoritesFilter() async {
        // Given
        mockSearchEngine.resultsToReturn = [
            Article(url: "https://test.com", title: "Favorite Article")
        ]
        
        // When
        await sut.search(query: "test", filter: .favorites)
        
        // Then
        XCTAssertEqual(mockSearchEngine.lastFilter, .favorites)
    }
    
    func testSearchWithTagsFilter() async {
        // Given
        var article = Article(url: "https://test.com", title: "Tagged Article")
        article.tags = ["Swift", "iOS"]
        mockSearchEngine.resultsToReturn = [article]
        
        // When
        await sut.search(query: "Swift", filter: .tags)
        
        // Then
        XCTAssertEqual(mockSearchEngine.lastFilter, .tags)
    }
    
    // MARK: - Recent Searches Tests
    
    func testAddToRecentSearches() async {
        // Given
        let searches = ["Swift", "UIKit", "SwiftUI"]
        
        // When
        for search in searches {
            await sut.search(query: search, filter: .all)
        }
        
        // Then
        XCTAssertEqual(sut.recentSearches.count, 3)
        XCTAssertEqual(sut.recentSearches.first, "SwiftUI") // Most recent first
    }
    
    func testRecentSearchesLimit() async {
        // Given
        let searches = (1...15).map { "Search \($0)" }
        
        // When
        for search in searches {
            await sut.search(query: search, filter: .all)
        }
        
        // Then
        XCTAssertEqual(sut.recentSearches.count, 10) // Should limit to 10
        XCTAssertEqual(sut.recentSearches.first, "Search 15") // Most recent
        XCTAssertFalse(sut.recentSearches.contains("Search 1")) // Oldest removed
    }
    
    func testNoDuplicateRecentSearches() async {
        // Given
        let query = "SwiftUI"
        
        // When
        await sut.search(query: query, filter: .all)
        await sut.search(query: "Other", filter: .all)
        await sut.search(query: query, filter: .all) // Same query again
        
        // Then
        XCTAssertEqual(sut.recentSearches.count, 2)
        XCTAssertEqual(sut.recentSearches.first, query) // Should be moved to top
    }
    
    func testClearRecentSearches() {
        // Given
        sut.recentSearches = ["Search1", "Search2", "Search3"]
        
        // When
        sut.clearRecentSearches()
        
        // Then
        XCTAssertTrue(sut.recentSearches.isEmpty)
    }
    
    // MARK: - Clear Search Tests
    
    func testClearSearch() {
        // Given
        sut.searchResults = [
            Article(url: "https://test.com", title: "Test Article")
        ]
        
        // When
        sut.clearSearch()
        
        // Then
        XCTAssertTrue(sut.searchResults.isEmpty)
    }
    
    // MARK: - Error Handling Tests
    
    func testSearchError() async {
        // Given
        mockSearchEngine.shouldThrowError = true
        
        // When
        await sut.search(query: "test", filter: .all)
        
        // Then
        XCTAssertTrue(sut.searchResults.isEmpty)
        XCTAssertNotNil(sut.errorMessage)
        XCTAssertFalse(sut.isSearching)
    }
    
    // MARK: - Search Suggestions Tests
    
    func testLoadSearchSuggestions() async {
        // Given
        let suggestions = ["Swift", "SwiftUI", "Swift Concurrency"]
        mockSearchEngine.suggestionsToReturn = suggestions
        
        // When
        await sut.loadSuggestions(for: "Swi")
        
        // Then
        XCTAssertEqual(sut.searchSuggestions.count, 3)
        XCTAssertEqual(sut.searchSuggestions, suggestions)
    }
    
    func testClearSuggestionsOnEmptyQuery() async {
        // Given
        sut.searchSuggestions = ["Test1", "Test2"]
        
        // When
        await sut.loadSuggestions(for: "")
        
        // Then
        XCTAssertTrue(sut.searchSuggestions.isEmpty)
    }
    
    // MARK: - Advanced Search Tests
    
    func testSearchWithDateRange() async {
        // Given
        let startDate = Date().addingTimeInterval(-7 * 24 * 60 * 60) // 7 days ago
        let endDate = Date()
        
        // When
        await sut.searchWithDateRange(
            query: "test",
            startDate: startDate,
            endDate: endDate,
            filter: .all
        )
        
        // Then
        XCTAssertTrue(mockSearchEngine.searchCalled)
        XCTAssertEqual(mockSearchEngine.lastDateRange?.start, startDate)
        XCTAssertEqual(mockSearchEngine.lastDateRange?.end, endDate)
    }
    
    func testSearchByDomain() async {
        // Given
        let domain = "example.com"
        mockSearchEngine.resultsToReturn = [
            Article(url: "https://example.com/article1", title: "Article 1"),
            Article(url: "https://example.com/article2", title: "Article 2")
        ]
        
        // When
        await sut.searchByDomain(domain)
        
        // Then
        XCTAssertTrue(mockSearchEngine.searchCalled)
        XCTAssertEqual(mockSearchEngine.lastDomain, domain)
        XCTAssertEqual(sut.searchResults.count, 2)
    }
    
    func testSearchByAuthor() async {
        // Given
        let author = "John Doe"
        mockSearchEngine.resultsToReturn = [
            Article(url: "https://test.com", title: "Article by John")
        ]
        
        // When
        await sut.searchByAuthor(author)
        
        // Then
        XCTAssertTrue(mockSearchEngine.searchCalled)
        XCTAssertEqual(mockSearchEngine.lastAuthor, author)
    }
}

// MARK: - Mock Classes

class MockSearchEngine: SearchEngine {
    var searchCalled = false
    var resultsToReturn: [Article] = []
    var suggestionsToReturn: [String] = []
    var shouldThrowError = false
    var lastQuery: String?
    var lastFilter: SearchFilter?
    var lastDateRange: (start: Date, end: Date)?
    var lastDomain: String?
    var lastAuthor: String?
    
    override func search(
        query: String,
        filter: SearchFilter,
        dateRange: (start: Date, end: Date)? = nil
    ) async throws -> [Article] {
        searchCalled = true
        lastQuery = query
        lastFilter = filter
        lastDateRange = dateRange
        
        if shouldThrowError {
            throw SearchError.searchFailed
        }
        
        return resultsToReturn
    }
    
    override func getSuggestions(for query: String) async -> [String] {
        return suggestionsToReturn
    }
    
    override func searchByDomain(_ domain: String) async throws -> [Article] {
        searchCalled = true
        lastDomain = domain
        
        if shouldThrowError {
            throw SearchError.searchFailed
        }
        
        return resultsToReturn
    }
    
    override func searchByAuthor(_ author: String) async throws -> [Article] {
        searchCalled = true
        lastAuthor = author
        
        if shouldThrowError {
            throw SearchError.searchFailed
        }
        
        return resultsToReturn
    }
}

enum SearchError: LocalizedError {
    case searchFailed
    
    var errorDescription: String? {
        switch self {
        case .searchFailed:
            return "Search failed"
        }
    }
}