import XCTest
import Combine
@testable import PocketNext

@MainActor
class ArticleListViewModelTests: XCTestCase {
    var sut: ArticleListViewModel!
    var mockDatabaseManager: MockDatabaseManager!
    var mockSyncService: MockCloudKitSyncService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        try await super.setUp()
        
        mockDatabaseManager = MockDatabaseManager()
        mockSyncService = MockCloudKitSyncService()
        cancellables = Set<AnyCancellable>()
        
        sut = ArticleListViewModel()
        // Inject mocks
    }
    
    override func tearDown() {
        sut = nil
        mockDatabaseManager = nil
        mockSyncService = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialState() {
        // Then
        XCTAssertTrue(sut.articles.isEmpty)
        XCTAssertFalse(sut.isLoading)
        XCTAssertNil(sut.errorMessage)
        XCTAssertEqual(sut.selectedFilter, .all)
        XCTAssertEqual(sut.sortOrder, .newest)
    }
    
    // MARK: - Loading Articles Tests
    
    func testLoadArticlesSuccess() async {
        // Given
        let testArticles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2"),
            Article(url: "https://test3.com", title: "Article 3")
        ]
        mockDatabaseManager.articlesToReturn = testArticles
        
        // When
        await sut.loadArticles()
        
        // Then
        XCTAssertEqual(sut.articles.count, 3)
        XCTAssertFalse(sut.isLoading)
        XCTAssertNil(sut.errorMessage)
    }
    
    func testLoadArticlesFailure() async {
        // Given
        mockDatabaseManager.shouldThrowError = true
        
        // When
        await sut.loadArticles()
        
        // Then
        XCTAssertTrue(sut.articles.isEmpty)
        XCTAssertFalse(sut.isLoading)
        XCTAssertNotNil(sut.errorMessage)
    }
    
    func testLoadArticlesShowsLoadingState() async {
        // Given
        let expectation = expectation(description: "Loading state shown")
        var loadingStates: [Bool] = []
        
        sut.$isLoading
            .sink { isLoading in
                loadingStates.append(isLoading)
                if loadingStates.count == 3 { // false (init) -> true -> false
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await sut.loadArticles()
        
        // Then
        await fulfillment(of: [expectation], timeout: 1.0)
        XCTAssertEqual(loadingStates, [false, true, false])
    }
    
    // MARK: - Filtering Tests
    
    func testFilterUnreadArticles() async {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Read Article"),
            Article(url: "https://test2.com", title: "Unread Article 1"),
            Article(url: "https://test3.com", title: "Unread Article 2")
        ]
        articles[0].isRead = true
        mockDatabaseManager.articlesToReturn = articles
        
        await sut.loadArticles()
        
        // When
        sut.selectedFilter = .unread
        
        // Then
        XCTAssertEqual(sut.filteredArticles.count, 2)
        XCTAssertTrue(sut.filteredArticles.allSatisfy { !$0.isRead })
    }
    
    func testFilterArchivedArticles() async {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Active Article"),
            Article(url: "https://test2.com", title: "Archived Article")
        ]
        articles[1].isArchived = true
        mockDatabaseManager.articlesToReturn = articles
        
        await sut.loadArticles()
        
        // When
        sut.selectedFilter = .archived
        
        // Then
        XCTAssertEqual(sut.filteredArticles.count, 1)
        XCTAssertTrue(sut.filteredArticles[0].isArchived)
    }
    
    func testFilterFavoriteArticles() async {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Regular Article"),
            Article(url: "https://test2.com", title: "Favorite Article 1"),
            Article(url: "https://test3.com", title: "Favorite Article 2")
        ]
        articles[1].isFavorite = true
        articles[2].isFavorite = true
        mockDatabaseManager.articlesToReturn = articles
        
        await sut.loadArticles()
        
        // When
        sut.selectedFilter = .favorites
        
        // Then
        XCTAssertEqual(sut.filteredArticles.count, 2)
        XCTAssertTrue(sut.filteredArticles.allSatisfy { $0.isFavorite })
    }
    
    // MARK: - Sorting Tests
    
    func testSortByNewest() async {
        // Given
        let now = Date()
        var articles = [
            Article(url: "https://test1.com", title: "Old Article"),
            Article(url: "https://test2.com", title: "New Article"),
            Article(url: "https://test3.com", title: "Middle Article")
        ]
        articles[0].savedAt = now.addingTimeInterval(-3600) // 1 hour ago
        articles[1].savedAt = now // Now
        articles[2].savedAt = now.addingTimeInterval(-1800) // 30 minutes ago
        
        mockDatabaseManager.articlesToReturn = articles
        await sut.loadArticles()
        
        // When
        sut.sortOrder = .newest
        
        // Then
        let sorted = sut.filteredArticles
        XCTAssertEqual(sorted[0].title, "New Article")
        XCTAssertEqual(sorted[1].title, "Middle Article")
        XCTAssertEqual(sorted[2].title, "Old Article")
    }
    
    func testSortByOldest() async {
        // Given
        let now = Date()
        var articles = [
            Article(url: "https://test1.com", title: "Old Article"),
            Article(url: "https://test2.com", title: "New Article"),
            Article(url: "https://test3.com", title: "Middle Article")
        ]
        articles[0].savedAt = now.addingTimeInterval(-3600)
        articles[1].savedAt = now
        articles[2].savedAt = now.addingTimeInterval(-1800)
        
        mockDatabaseManager.articlesToReturn = articles
        await sut.loadArticles()
        
        // When
        sut.sortOrder = .oldest
        
        // Then
        let sorted = sut.filteredArticles
        XCTAssertEqual(sorted[0].title, "Old Article")
        XCTAssertEqual(sorted[1].title, "Middle Article")
        XCTAssertEqual(sorted[2].title, "New Article")
    }
    
    func testSortByTitle() async {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "Zebra Article"),
            Article(url: "https://test2.com", title: "Apple Article"),
            Article(url: "https://test3.com", title: "Middle Article")
        ]
        
        mockDatabaseManager.articlesToReturn = articles
        await sut.loadArticles()
        
        // When
        sut.sortOrder = .title
        
        // Then
        let sorted = sut.filteredArticles
        XCTAssertEqual(sorted[0].title, "Apple Article")
        XCTAssertEqual(sorted[1].title, "Middle Article")
        XCTAssertEqual(sorted[2].title, "Zebra Article")
    }
    
    // MARK: - Search Tests
    
    func testSearchArticles() async {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "SwiftUI Tutorial"),
            Article(url: "https://test2.com", title: "UIKit Guide"),
            Article(url: "https://test3.com", title: "Swift Concurrency")
        ]
        
        mockDatabaseManager.articlesToReturn = articles
        await sut.loadArticles()
        
        // When
        sut.searchText = "Swift"
        
        // Then
        let filtered = sut.filteredArticles
        XCTAssertEqual(filtered.count, 2)
        XCTAssertTrue(filtered.allSatisfy { $0.title.contains("Swift") })
    }
    
    func testSearchCaseInsensitive() async {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "SWIFT Tutorial"),
            Article(url: "https://test2.com", title: "swift guide"),
            Article(url: "https://test3.com", title: "Swift Development")
        ]
        
        mockDatabaseManager.articlesToReturn = articles
        await sut.loadArticles()
        
        // When
        sut.searchText = "swift"
        
        // Then
        XCTAssertEqual(sut.filteredArticles.count, 3)
    }
    
    func testSearchEmptyQuery() async {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2")
        ]
        
        mockDatabaseManager.articlesToReturn = articles
        await sut.loadArticles()
        
        // When
        sut.searchText = ""
        
        // Then
        XCTAssertEqual(sut.filteredArticles.count, 2)
    }
    
    // MARK: - Article Actions Tests
    
    func testMarkArticleAsRead() async {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        mockDatabaseManager.articlesToReturn = [article]
        await sut.loadArticles()
        
        // When
        await sut.markAsRead(article)
        
        // Then
        XCTAssertTrue(mockDatabaseManager.markAsReadCalled)
        XCTAssertEqual(mockDatabaseManager.markedAsReadId, article.id)
    }
    
    func testToggleFavorite() async {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.isFavorite = false
        mockDatabaseManager.articlesToReturn = [article]
        await sut.loadArticles()
        
        // When
        await sut.toggleFavorite(article)
        
        // Then
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
        XCTAssertTrue(mockDatabaseManager.lastUpdatedArticle?.isFavorite ?? false)
    }
    
    func testArchiveArticle() async {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        mockDatabaseManager.articlesToReturn = [article]
        await sut.loadArticles()
        
        // When
        await sut.archiveArticle(article)
        
        // Then
        XCTAssertTrue(mockDatabaseManager.updateArticleCalled)
        XCTAssertTrue(mockDatabaseManager.lastUpdatedArticle?.isArchived ?? false)
    }
    
    func testDeleteArticle() async {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        mockDatabaseManager.articlesToReturn = [article]
        await sut.loadArticles()
        
        // When
        await sut.deleteArticle(article)
        
        // Then
        XCTAssertTrue(mockDatabaseManager.deleteArticleCalled)
        XCTAssertEqual(mockDatabaseManager.deletedArticleId, article.id)
    }
    
    // MARK: - Sync Tests
    
    func testRefreshTriggersSync() async {
        // Given
        mockSyncService.isCloudKitAvailable = true
        
        // When
        await sut.refresh()
        
        // Then
        XCTAssertTrue(mockSyncService.startSyncCalled)
    }
    
    func testHandleSyncError() async {
        // Given
        mockSyncService.shouldFailSync = true
        
        // When
        await sut.refresh()
        
        // Then
        XCTAssertNotNil(sut.errorMessage)
    }
    
    // MARK: - Statistics Tests
    
    func testLoadStatistics() async {
        // Given
        let stats = ArticleStatistics(
            total: 100,
            unread: 25,
            todayRead: 5,
            weeklyReadTime: 120
        )
        mockDatabaseManager.statisticsToReturn = stats
        
        // When
        await sut.loadStatistics()
        
        // Then
        XCTAssertEqual(sut.statistics?.total, 100)
        XCTAssertEqual(sut.statistics?.unread, 25)
        XCTAssertEqual(sut.statistics?.todayRead, 5)
        XCTAssertEqual(sut.statistics?.weeklyReadTime, 120)
    }
}

// MARK: - Mock Classes

class MockDatabaseManager: DatabaseManager {
    var articlesToReturn: [Article] = []
    var statisticsToReturn: ArticleStatistics?
    var shouldThrowError = false
    var markAsReadCalled = false
    var markedAsReadId: String?
    var updateArticleCalled = false
    var lastUpdatedArticle: Article?
    var deleteArticleCalled = false
    var deletedArticleId: String?
    
    override func fetchAllArticles() async throws -> [Article] {
        if shouldThrowError {
            throw DatabaseError.notInitialized
        }
        return articlesToReturn
    }
    
    override func markAsRead(_ articleId: String) async throws {
        markAsReadCalled = true
        markedAsReadId = articleId
    }
    
    override func updateArticle(_ article: Article) async throws {
        updateArticleCalled = true
        lastUpdatedArticle = article
    }
    
    override func deleteArticle(_ articleId: String) async throws {
        deleteArticleCalled = true
        deletedArticleId = articleId
    }
    
    override func getStatistics() async throws -> ArticleStatistics {
        if let stats = statisticsToReturn {
            return stats
        }
        return ArticleStatistics(total: 0, unread: 0, todayRead: 0, weeklyReadTime: 0)
    }
}

class MockCloudKitSyncService: CloudKitSyncService {
    var isCloudKitAvailable = true
    var startSyncCalled = false
    var shouldFailSync = false
    
    override func startSync() async {
        startSyncCalled = true
        if shouldFailSync {
            lastError = .networkError(NSError(domain: "Test", code: 0))
        }
    }
}