import XCTest
import GRDB
@testable import PocketNext

class DatabaseManagerSchemaTests: XCTestCase {
    var databaseManager: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        databaseManager = DatabaseManager()
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        databaseManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Schema Tests
    
    func testArticlesTableHasAllRequiredColumns() async throws {
        // Verify articles table has all required columns including new embedding fields
        let columns = try await databaseManager.read { db in
            try db.columns(in: "articles").map { $0.name }
        }
        
        let requiredColumns = [
            "id", "url", "title", "content", "summary", "keywords",
            "author", "publishDate", "readingTime", "contentType",
            "capturedAt", "lastAccessedAt", "isArchived", "syncStatus",
            "embeddingData", "embeddingModelVersion", "hasLocalEmbedding"
        ]
        
        for column in requiredColumns {
            XCTAssertTrue(columns.contains(column), "Missing column: \(column)")
        }
    }
    
    func testLocalVectorIndexTableExists() async throws {
        // Verify local_vector_index table exists
        let tableExists = try await databaseManager.read { db in
            try db.tableExists("local_vector_index")
        }
        
        XCTAssertTrue(tableExists)
        
        // Check columns
        let columns = try await databaseManager.read { db in
            try db.columns(in: "local_vector_index").map { $0.name }
        }
        
        let requiredColumns = ["articleId", "embedding", "magnitude", "modelVersion", "createdAt"]
        for column in requiredColumns {
            XCTAssertTrue(columns.contains(column), "Missing column: \(column)")
        }
    }
    
    func testIndexesAreCreated() async throws {
        // Verify performance indexes exist
        let indexes = try await databaseManager.read { db in
            try String.fetchAll(db, sql: """
                SELECT name FROM sqlite_master 
                WHERE type='index' 
                AND tbl_name IN ('articles', 'local_vector_index')
            """)
        }
        
        // Should have indexes for performance
        XCTAssertGreaterThan(indexes.count, 0)
    }
    
    // MARK: - Article Operations with Embeddings
    
    func testSaveArticleWithEmbedding() async throws {
        // Given
        let article = Article.testArticleWithEmbedding()
        
        // When
        try await databaseManager.save(article)
        
        // Then
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched)
        XCTAssertNotNil(fetched?.embeddingData)
        XCTAssertEqual(fetched?.embeddingModelVersion, article.embeddingModelVersion)
        XCTAssertEqual(fetched?.hasLocalEmbedding, article.hasLocalEmbedding)
    }
    
    func testUpdateArticleEmbedding() async throws {
        // Given - article without embedding
        var article = Article.testArticle()
        try await databaseManager.save(article)
        
        // When - update with embedding
        let embedding = MockDataGenerator.generateMockEmbeddings()
        article.embeddingData = VectorOperations.compressEmbedding(embedding)
        article.embeddingModelVersion = "test-v1"
        article.hasLocalEmbedding = true
        
        try await databaseManager.updateArticle(article)
        
        // Then
        let updated = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(updated?.embeddingData)
        XCTAssertEqual(updated?.embeddingModelVersion, "test-v1")
        XCTAssertTrue(updated?.hasLocalEmbedding ?? false)
    }
    
    // MARK: - Vector Index Operations
    
    func testLocalVectorIndexCRUD() async throws {
        // Given
        let article = Article.testArticle()
        try await databaseManager.save(article)
        
        let embedding = MockDataGenerator.generateMockEmbeddings()
        let normalized = VectorOperations.normalize(embedding)
        let magnitude = VectorOperations.magnitude(normalized)
        
        let vectorIndex = LocalVectorIndex(
            articleId: article.id,
            embedding: normalized,
            magnitude: magnitude,
            modelVersion: "test-v1",
            createdAt: Date()
        )
        
        // When - save vector index
        try await databaseManager.write { db in
            try vectorIndex.save(db)
        }
        
        // Then - fetch and verify
        let fetched = try await databaseManager.read { db in
            try LocalVectorIndex.filter(LocalVectorIndex.Columns.articleId == article.id).fetchOne(db)
        }
        
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.articleId, article.id)
        XCTAssertEqual(fetched?.modelVersion, "test-v1")
        XCTAssertEqual(fetched?.magnitude, magnitude, accuracy: 0.0001)
    }
    
    func testVectorIndexCascadeDelete() async throws {
        // Given - article with vector index
        let article = Article.testArticle()
        try await databaseManager.save(article)
        
        let vectorIndex = LocalVectorIndex(
            articleId: article.id,
            embedding: [0.1, 0.2, 0.3],
            magnitude: 0.5,
            modelVersion: "test",
            createdAt: Date()
        )
        
        try await databaseManager.write { db in
            try vectorIndex.save(db)
        }
        
        // When - delete article
        try await databaseManager.deleteArticle(id: article.id)
        
        // Then - vector index should be deleted too
        let remainingIndex = try await databaseManager.read { db in
            try LocalVectorIndex.filter(LocalVectorIndex.Columns.articleId == article.id).fetchOne(db)
        }
        
        XCTAssertNil(remainingIndex)
    }
    
    // MARK: - Migration Tests
    
    func testMigrationAddsNewColumns() async throws {
        // This tests that the migration logic properly adds new columns
        // In a real scenario, we'd test upgrading from an old schema
        
        // Verify we can save articles with all new fields
        let article = Article.testArticleWithEmbedding()
        try await databaseManager.save(article)
        
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched)
        XCTAssertNotNil(fetched?.embeddingData)
    }
    
    // MARK: - Performance Tests
    
    func testBulkArticleOperationsWithEmbeddings() async throws {
        // Test performance with multiple articles
        let articles = (0..<50).map { i in
            Article.testArticleWithEmbedding(
                title: "Bulk Article \(i)",
                keywords: ["bulk", "test", "\(i)"]
            )
        }
        
        let start = CFAbsoluteTimeGetCurrent()
        
        // Save all articles
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let saveTime = CFAbsoluteTimeGetCurrent() - start
        
        // Should complete in reasonable time
        XCTAssertLessThan(saveTime, 2.0) // Less than 2 seconds for 50 articles
        
        // Verify all saved
        let count = try await databaseManager.read { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertGreaterThanOrEqual(count, 50)
    }
    
    // MARK: - Edge Cases
    
    func testHandlesLargeEmbeddingData() async throws {
        // Test with larger embedding dimensions
        let largeEmbedding = Array(repeating: Float(0.5), count: 3072)
        let compressedData = VectorOperations.compressEmbedding(largeEmbedding)
        
        let article = Article.testArticle(
            embeddingData: compressedData,
            embeddingModelVersion: "large-model"
        )
        
        // Should handle without issues
        try await databaseManager.save(article)
        
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched?.embeddingData)
        
        // Verify decompression works
        let decompressed = VectorOperations.decompressEmbedding(fetched!.embeddingData!)
        XCTAssertEqual(decompressed?.count, 3072)
    }
}