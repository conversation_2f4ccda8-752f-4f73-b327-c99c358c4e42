import XCTest
import UserNotifications
@testable import PocketNext

class NotificationServiceTests: XCTestCase {
    var sut: NotificationService!
    var mockNotificationCenter: MockUNUserNotificationCenter!
    
    override func setUp() {
        super.setUp()
        mockNotificationCenter = MockUNUserNotificationCenter()
        sut = NotificationService()
        // We'll need to inject the mock notification center
    }
    
    override func tearDown() {
        sut = nil
        mockNotificationCenter = nil
        super.tearDown()
    }
    
    // MARK: - Authorization Tests
    
    func testRequestAuthorizationGranted() async throws {
        // Given
        mockNotificationCenter.shouldGrantAuthorization = true
        
        // When
        try await sut.requestAuthorization()
        
        // Then
        XCTAssertTrue(sut.isAuthorized)
        XCTAssertTrue(mockNotificationCenter.requestAuthorizationCalled)
    }
    
    func testRequestAuthorizationDenied() async throws {
        // Given
        mockNotificationCenter.shouldGrantAuthorization = false
        
        // When
        try await sut.requestAuthorization()
        
        // Then
        XCTAssertFalse(sut.isAuthorized)
    }
    
    func testCheckAuthorizationStatus() async {
        // Given
        mockNotificationCenter.authorizationStatus = .authorized
        
        // When
        sut.checkAuthorizationStatus()
        
        // Wait for async operation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        // Then
        XCTAssertTrue(sut.isAuthorized)
    }
    
    // MARK: - Digest Notification Tests
    
    func testSendDigestNotificationWhenAuthorized() async {
        // Given
        sut.isAuthorized = true
        
        // When
        await sut.sendDigestNotification()
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 1)
        let notification = mockNotificationCenter.scheduledNotifications.first
        XCTAssertEqual(notification?.content.title, "Your Daily Digest is Ready")
        XCTAssertEqual(notification?.content.categoryIdentifier, "DIGEST")
    }
    
    func testSendDigestNotificationWhenNotAuthorized() async {
        // Given
        sut.isAuthorized = false
        
        // When
        await sut.sendDigestNotification()
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 0)
    }
    
    // MARK: - Reading Reminder Tests
    
    func testScheduleReadingReminderWhenEnabled() async {
        // Given
        sut.isAuthorized = true
        sut.notificationSettings.readingReminders = true
        let reminderDate = Date().addingTimeInterval(3600) // 1 hour from now
        
        // When
        await sut.scheduleReadingReminder(at: reminderDate)
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 1)
        let notification = mockNotificationCenter.scheduledNotifications.first
        XCTAssertEqual(notification?.content.categoryIdentifier, "REMINDER")
        XCTAssertNotNil(notification?.trigger as? UNCalendarNotificationTrigger)
    }
    
    func testScheduleReadingReminderWhenDisabled() async {
        // Given
        sut.isAuthorized = true
        sut.notificationSettings.readingReminders = false
        
        // When
        await sut.scheduleReadingReminder(at: Date())
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 0)
    }
    
    // MARK: - Article Saved Notification Tests
    
    func testSendArticleSavedNotificationWhenEnabled() async {
        // Given
        sut.isAuthorized = true
        sut.notificationSettings.saveConfirmations = true
        let articleTitle = "Test Article"
        
        // When
        await sut.sendArticleSavedNotification(title: articleTitle)
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 1)
        let notification = mockNotificationCenter.scheduledNotifications.first
        XCTAssertEqual(notification?.content.title, "Article Saved")
        XCTAssertEqual(notification?.content.body, articleTitle)
    }
    
    func testSendArticleSavedNotificationWhenDisabled() async {
        // Given
        sut.isAuthorized = true
        sut.notificationSettings.saveConfirmations = false
        
        // When
        await sut.sendArticleSavedNotification(title: "Test")
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 0)
    }
    
    // MARK: - Sync Notification Tests
    
    func testSendSyncCompletedNotification() async {
        // Given
        sut.isAuthorized = true
        sut.notificationSettings.syncNotifications = true
        let itemCount = 5
        
        // When
        await sut.sendSyncCompletedNotification(itemCount: itemCount)
        
        // Then
        XCTAssertEqual(mockNotificationCenter.scheduledNotifications.count, 1)
        let notification = mockNotificationCenter.scheduledNotifications.first
        XCTAssertEqual(notification?.content.title, "Sync Completed")
        XCTAssertEqual(notification?.content.body, "\(itemCount) items synced across your devices")
    }
    
    // MARK: - Notification Management Tests
    
    func testGetPendingNotifications() async {
        // Given
        let mockRequests = [
            createMockNotificationRequest(identifier: "test1", title: "Test 1"),
            createMockNotificationRequest(identifier: "test2", title: "Test 2")
        ]
        mockNotificationCenter.pendingRequests = mockRequests
        
        // When
        await sut.getPendingNotifications()
        
        // Wait for async operation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        // Then
        XCTAssertEqual(sut.pendingNotifications.count, 2)
        XCTAssertEqual(sut.pendingNotifications[0].identifier, "test1")
        XCTAssertEqual(sut.pendingNotifications[1].identifier, "test2")
    }
    
    func testCancelNotification() {
        // Given
        let identifier = "test-notification"
        
        // When
        sut.cancelNotification(identifier: identifier)
        
        // Then
        XCTAssertTrue(mockNotificationCenter.removePendingNotificationsCalled)
        XCTAssertEqual(mockNotificationCenter.removedIdentifiers, [identifier])
    }
    
    func testCancelAllNotifications() {
        // Given
        sut.pendingNotifications = [
            PendingNotification(identifier: "test1", title: "Test 1", body: "", trigger: nil),
            PendingNotification(identifier: "test2", title: "Test 2", body: "", trigger: nil)
        ]
        
        // When
        sut.cancelAllNotifications()
        
        // Then
        XCTAssertTrue(mockNotificationCenter.removeAllPendingNotificationsCalled)
        XCTAssertTrue(sut.pendingNotifications.isEmpty)
    }
    
    // MARK: - Settings Tests
    
    func testUpdateSettings() async {
        // Given
        var newSettings = NotificationSettings()
        newSettings.digestNotifications = false
        newSettings.readingReminders = true
        newSettings.digestFrequency = .weekly
        
        // When
        sut.updateSettings(newSettings)
        
        // Then
        XCTAssertEqual(sut.notificationSettings.digestNotifications, false)
        XCTAssertEqual(sut.notificationSettings.readingReminders, true)
        XCTAssertEqual(sut.notificationSettings.digestFrequency, .weekly)
    }
    
    // MARK: - Helper Methods
    
    private func createMockNotificationRequest(identifier: String, title: String) -> UNNotificationRequest {
        let content = UNMutableNotificationContent()
        content.title = title
        return UNNotificationRequest(identifier: identifier, content: content, trigger: nil)
    }
}

// MARK: - Mock Classes

class MockUNUserNotificationCenter: UNUserNotificationCenter {
    var shouldGrantAuthorization = true
    var requestAuthorizationCalled = false
    var authorizationStatus: UNAuthorizationStatus = .notDetermined
    var scheduledNotifications: [UNNotificationRequest] = []
    var pendingRequests: [UNNotificationRequest] = []
    var removePendingNotificationsCalled = false
    var removeAllPendingNotificationsCalled = false
    var removedIdentifiers: [String] = []
    
    override func requestAuthorization(options: UNAuthorizationOptions, completionHandler: @escaping (Bool, Error?) -> Void) {
        requestAuthorizationCalled = true
        completionHandler(shouldGrantAuthorization, nil)
    }
    
    override func getNotificationSettings(completionHandler: @escaping (UNNotificationSettings) -> Void) {
        // Return mock settings
        let settings = MockNotificationSettings(authorizationStatus: authorizationStatus)
        completionHandler(settings)
    }
    
    override func add(_ request: UNNotificationRequest, withCompletionHandler completionHandler: ((Error?) -> Void)? = nil) {
        scheduledNotifications.append(request)
        completionHandler?(nil)
    }
    
    override func pendingNotificationRequests(completionHandler: @escaping ([UNNotificationRequest]) -> Void) {
        completionHandler(pendingRequests)
    }
    
    override func removePendingNotificationRequests(withIdentifiers identifiers: [String]) {
        removePendingNotificationsCalled = true
        removedIdentifiers = identifiers
    }
    
    override func removeAllPendingNotificationRequests() {
        removeAllPendingNotificationsCalled = true
        scheduledNotifications.removeAll()
    }
}

class MockNotificationSettings: UNNotificationSettings {
    private let _authorizationStatus: UNAuthorizationStatus
    
    init(authorizationStatus: UNAuthorizationStatus) {
        self._authorizationStatus = authorizationStatus
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var authorizationStatus: UNAuthorizationStatus {
        return _authorizationStatus
    }
}

// MARK: - Notification Delegate Tests

class NotificationDelegateTests: XCTestCase {
    var delegate: NotificationDelegate!
    
    override func setUp() {
        super.setUp()
        delegate = NotificationDelegate()
    }
    
    override func tearDown() {
        delegate = nil
        super.tearDown()
    }
    
    func testWillPresentNotification() {
        // Given
        let expectation = expectation(description: "Completion handler called")
        var presentationOptions: UNNotificationPresentationOptions?
        
        // When
        delegate.userNotificationCenter(
            UNUserNotificationCenter.current(),
            willPresent: createMockNotification(),
            withCompletionHandler: { options in
                presentationOptions = options
                expectation.fulfill()
            }
        )
        
        // Then
        waitForExpectations(timeout: 1.0)
        XCTAssertNotNil(presentationOptions)
        XCTAssertTrue(presentationOptions?.contains(.banner) ?? false)
        XCTAssertTrue(presentationOptions?.contains(.sound) ?? false)
        XCTAssertTrue(presentationOptions?.contains(.badge) ?? false)
    }
    
    func testDidReceiveViewDigestAction() {
        // Given
        let expectation = expectation(description: "Completion handler called")
        let response = createMockNotificationResponse(actionIdentifier: "VIEW_DIGEST")
        
        // When
        delegate.userNotificationCenter(
            UNUserNotificationCenter.current(),
            didReceive: response,
            withCompletionHandler: {
                expectation.fulfill()
            }
        )
        
        // Then
        waitForExpectations(timeout: 1.0)
        // Verify navigation happened (would need to mock navigation)
    }
    
    func testDidReceiveReadNowAction() {
        // Given
        let expectation = expectation(description: "Completion handler called")
        let response = createMockNotificationResponse(actionIdentifier: "READ_NOW")
        
        // When
        delegate.userNotificationCenter(
            UNUserNotificationCenter.current(),
            didReceive: response,
            withCompletionHandler: {
                expectation.fulfill()
            }
        )
        
        // Then
        waitForExpectations(timeout: 1.0)
    }
    
    func testDidReceiveSnoozeAction() {
        // Given
        let expectation = expectation(description: "Completion handler called")
        let response = createMockNotificationResponse(actionIdentifier: "SNOOZE")
        
        // When
        delegate.userNotificationCenter(
            UNUserNotificationCenter.current(),
            didReceive: response,
            withCompletionHandler: {
                expectation.fulfill()
            }
        )
        
        // Then
        waitForExpectations(timeout: 1.0)
    }
    
    // MARK: - Helper Methods
    
    private func createMockNotification() -> UNNotification {
        let content = UNMutableNotificationContent()
        content.title = "Test Notification"
        let request = UNNotificationRequest(identifier: "test", content: content, trigger: nil)
        // Note: UNNotification doesn't have a public initializer, so we'd need to use a mock
        return MockNotification(request: request)
    }
    
    private func createMockNotificationResponse(actionIdentifier: String) -> UNNotificationResponse {
        let notification = createMockNotification()
        return MockNotificationResponse(
            notification: notification,
            actionIdentifier: actionIdentifier
        )
    }
}

// Additional mock classes would be needed for UNNotification and UNNotificationResponse
class MockNotification: UNNotification {
    private let _request: UNNotificationRequest
    
    init(request: UNNotificationRequest) {
        self._request = request
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var request: UNNotificationRequest {
        return _request
    }
}

class MockNotificationResponse: UNNotificationResponse {
    private let _notification: UNNotification
    private let _actionIdentifier: String
    
    init(notification: UNNotification, actionIdentifier: String) {
        self._notification = notification
        self._actionIdentifier = actionIdentifier
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var notification: UNNotification {
        return _notification
    }
    
    override var actionIdentifier: String {
        return _actionIdentifier
    }
}