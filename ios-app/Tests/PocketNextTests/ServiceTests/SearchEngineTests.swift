import XCTest
@testable import PocketNext

class SearchEngineTests: XCTestCase {
    var searchEngine: SearchEngine!
    var database: DatabaseManager!
    var hybridStorage: HybridVectorStorage!
    var mockEmbeddingService: MockEmbeddingService!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Setup dependencies
        database = DatabaseManager()
        try await database.initialize()
        
        hybridStorage = HybridVectorStorage.shared
        mockEmbeddingService = MockEmbeddingService()
        
        searchEngine = SearchEngine(database: database, hybridStorage: hybridStorage)
        
        // Populate test data
        try await populateTestData()
    }
    
    override func tearDown() async throws {
        searchEngine = nil
        database = nil
        hybridStorage = nil
        mockEmbeddingService = nil
        try await super.tearDown()
    }
    
    // MARK: - Test Data
    
    private func populateTestData() async throws {
        let articles = [
            Article.testArticle(
                title: "Getting Started with SwiftUI",
                content: "SwiftUI is Apple's modern UI framework for building apps across all Apple platforms.",
                keywords: ["swiftui", "ios", "apple", "ui"],
                author: "<PERSON> Johnson"
            ),
            Article.testArticle(
                title: "Advanced Swift Concurrency",
                content: "Learn about actors, async/await, and structured concurrency in Swift.",
                keywords: ["swift", "concurrency", "async", "actors"],
                author: "Bob Smith"
            ),
            Article.testArticle(
                title: "Core Data Best Practices",
                content: "Master Core Data with these essential tips and patterns.",
                keywords: ["coredata", "ios", "database", "persistence"],
                author: "Carol White"
            ),
            Article.testArticle(
                title: "Python for Data Science",
                content: "Introduction to using Python for data analysis and machine learning.",
                keywords: ["python", "data science", "machine learning"],
                author: "David Brown"
            )
        ]
        
        for article in articles {
            try await database.save(article)
            
            // Process with embeddings for semantic search
            mockEmbeddingService.mockEmbedding = generateMockEmbedding(for: article.title)
            try await hybridStorage.processArticle(article)
        }
    }
    
    // MARK: - Basic Search Tests
    
    func testKeywordSearch() async throws {
        // When
        let results = try await searchEngine.search("SwiftUI")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertTrue(results.first?.article.title.contains("SwiftUI") ?? false)
        XCTAssertTrue(results.allSatisfy { $0.matchTypes.contains(.keyword) })
    }
    
    func testSemanticSearch() async throws {
        // Given - search for conceptually related content
        mockEmbeddingService.mockEmbedding = generateMockEmbedding(for: "iOS development")
        
        // When
        let results = try await searchEngine.search("iOS development")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        // Should find iOS-related articles even without exact keyword match
        let iosArticles = results.filter { result in
            result.article.keywords.contains { $0.lowercased().contains("ios") || $0.lowercased().contains("swift") }
        }
        XCTAssertGreaterThan(iosArticles.count, 0)
    }
    
    func testHybridSearch() async throws {
        // When - search term that matches both keyword and semantic
        let results = try await searchEngine.search("Swift")
        
        // Then
        XCTAssertGreaterThan(results.count, 0)
        
        // Articles with "Swift" in title should rank highest
        if let topResult = results.first {
            XCTAssertTrue(
                topResult.article.title.contains("Swift") ||
                topResult.article.keywords.contains("swift")
            )
            // Should have both match types for best matches
            XCTAssertTrue(topResult.matchTypes.contains(.keyword))
        }
    }
    
    // MARK: - Ranking Tests
    
    func testSearchResultsAreSortedByRelevance() async throws {
        // When
        let results = try await searchEngine.search("iOS")
        
        // Then
        guard results.count >= 2 else { return }
        
        for i in 1..<results.count {
            XCTAssertGreaterThanOrEqual(results[i-1].score, results[i].score)
        }
    }
    
    func testBoostForMultipleMatchTypes() async throws {
        // When
        let results = try await searchEngine.search("Swift")
        
        // Then
        if let topResult = results.first {
            // Articles matching both keyword and semantic should score higher
            if topResult.matchTypes.count == 2 {
                XCTAssertGreaterThan(topResult.score, topResult.keywordScore)
                XCTAssertGreaterThan(topResult.score, topResult.semanticScore)
            }
        }
    }
    
    // MARK: - Edge Cases
    
    func testEmptySearch() async throws {
        // When
        let results = try await searchEngine.search("")
        
        // Then
        XCTAssertTrue(results.isEmpty)
    }
    
    func testSearchWithNoMatches() async throws {
        // When
        let results = try await searchEngine.search("NonexistentTermXYZ123")
        
        // Then
        XCTAssertTrue(results.isEmpty)
    }
    
    func testCaseInsensitiveSearch() async throws {
        // When
        let upperResults = try await searchEngine.search("SWIFTUI")
        let lowerResults = try await searchEngine.search("swiftui")
        let mixedResults = try await searchEngine.search("SwiftUI")
        
        // Then - all should find the same articles
        XCTAssertEqual(upperResults.count, lowerResults.count)
        XCTAssertEqual(upperResults.count, mixedResults.count)
    }
    
    // MARK: - Suggestions Tests
    
    func testGetSuggestions() async throws {
        // When
        let suggestions = try await searchEngine.getSuggestions(for: "sw")
        
        // Then
        XCTAssertGreaterThan(suggestions.count, 0)
        XCTAssertTrue(suggestions.contains { $0.lowercased().hasPrefix("sw") })
        
        // Should include "swift" and "swiftui"
        let swiftSuggestions = suggestions.filter { $0.lowercased().contains("swift") }
        XCTAssertGreaterThan(swiftSuggestions.count, 0)
    }
    
    func testGetPopularKeywords() async throws {
        // When
        let popular = try await searchEngine.getPopularKeywords()
        
        // Then
        XCTAssertGreaterThan(popular.count, 0)
        XCTAssertLessThanOrEqual(popular.count, 20)
        
        // Should be sorted by count
        for i in 1..<popular.count {
            XCTAssertGreaterThanOrEqual(popular[i-1].count, popular[i].count)
        }
        
        // "ios" should be popular (appears in multiple articles)
        XCTAssertTrue(popular.contains { $0.keyword == "ios" })
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() async throws {
        // Add more test data
        for i in 0..<50 {
            let article = Article.testArticle(
                title: "Performance Test \(i)",
                keywords: ["performance", "test", "article\(i)"]
            )
            try await database.save(article)
        }
        
        // Measure search time
        let start = CFAbsoluteTimeGetCurrent()
        let results = try await searchEngine.search("performance")
        let elapsed = CFAbsoluteTimeGetCurrent() - start
        
        // Should complete quickly
        XCTAssertLessThan(elapsed, 1.0) // Less than 1 second
        XCTAssertGreaterThan(results.count, 0)
    }
    
    // MARK: - Helper Methods
    
    private func generateMockEmbedding(for text: String) -> [Float] {
        let hash = text.hashValue
        var embedding = Array(repeating: Float(0), count: 1536)
        
        for i in 0..<embedding.count {
            embedding[i] = Float(sin(Double(hash + i))) * 0.5 + 0.5
        }
        
        return embedding
    }
}