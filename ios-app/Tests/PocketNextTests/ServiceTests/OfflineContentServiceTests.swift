import XCTest
@testable import PocketNext

class OfflineContentServiceTests: XCTestCase {
    var sut: OfflineContentService!
    var mockFileManager: MockFileManager!
    var mockURLSession: MockURLSession!
    var testDirectory: URL!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create test directory
        let tempDir = FileManager.default.temporaryDirectory
        testDirectory = tempDir.appendingPathComponent("OfflineContentTests_\(UUID().uuidString)")
        try FileManager.default.createDirectory(at: testDirectory, withIntermediateDirectories: true)
        
        mockFileManager = MockFileManager(testDirectory: testDirectory)
        mockURLSession = MockURLSession()
        
        sut = OfflineContentService()
        // Inject mocks into sut
    }
    
    override func tearDown() async throws {
        // Clean up test directory
        if FileManager.default.fileExists(atPath: testDirectory.path) {
            try FileManager.default.removeItem(at: testDirectory)
        }
        
        sut = nil
        mockFileManager = nil
        mockURLSession = nil
        testDirectory = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Download Tests
    
    func testDownloadArticleForOfflineSuccess() async throws {
        // Given
        var article = Article(url: "https://test.com/article", title: "Test Article")
        article.content = "<html><body><p>Test content</p><img src='image.jpg'></body></html>"
        
        let mockData = article.content!.data(using: .utf8)!
        mockURLSession.dataToReturn = mockData
        
        // When
        let downloadedArticle = try await sut.downloadArticleForOffline(article)
        
        // Then
        XCTAssertTrue(downloadedArticle.isDownloadedForOffline)
        XCTAssertNotNil(downloadedArticle.downloadedAt)
        XCTAssertNotNil(downloadedArticle.htmlContent)
        XCTAssertTrue(mockFileManager.savedFiles.contains { $0.contains(article.id) })
    }
    
    func testDownloadArticleWithImagesSuccess() async throws {
        // Given
        var article = Article(url: "https://test.com/article", title: "Test Article")
        article.content = """
            <html>
            <body>
                <p>Test content</p>
                <img src="https://test.com/image1.jpg">
                <img src="https://test.com/image2.png">
            </body>
            </html>
            """
        
        mockURLSession.dataToReturn = article.content!.data(using: .utf8)!
        mockURLSession.imageDataToReturn = Data(repeating: 0xFF, count: 100) // Mock image data
        
        // When
        let downloadedArticle = try await sut.downloadArticleForOffline(article)
        
        // Then
        XCTAssertTrue(downloadedArticle.isDownloadedForOffline)
        XCTAssertEqual(mockURLSession.downloadedURLs.count, 3) // Article + 2 images
        
        // Verify images were saved locally
        let savedImageFiles = mockFileManager.savedFiles.filter { $0.contains("image") }
        XCTAssertEqual(savedImageFiles.count, 2)
    }
    
    func testDownloadArticleNetworkError() async {
        // Given
        let article = Article(url: "https://test.com/article", title: "Test Article")
        mockURLSession.shouldFail = true
        mockURLSession.errorToReturn = URLError(.notConnectedToInternet)
        
        // When & Then
        do {
            _ = try await sut.downloadArticleForOffline(article)
            XCTFail("Should throw error")
        } catch {
            XCTAssertTrue(error is URLError)
        }
    }
    
    // MARK: - Batch Download Tests
    
    func testDownloadMultipleArticles() async throws {
        // Given
        let articles = [
            Article(url: "https://test.com/1", title: "Article 1"),
            Article(url: "https://test.com/2", title: "Article 2"),
            Article(url: "https://test.com/3", title: "Article 3")
        ]
        
        mockURLSession.dataToReturn = "<html><body>Content</body></html>".data(using: .utf8)!
        
        // When
        let results = await sut.downloadArticlesForOffline(articles)
        
        // Then
        XCTAssertEqual(results.count, 3)
        
        var successCount = 0
        for result in results {
            if case .success = result {
                successCount += 1
            }
        }
        XCTAssertEqual(successCount, 3)
        XCTAssertEqual(sut.downloadProgress, 1.0)
    }
    
    func testDownloadProgressUpdates() async throws {
        // Given
        let articles = (0..<10).map { index in
            Article(url: "https://test.com/\(index)", title: "Article \(index)")
        }
        
        mockURLSession.dataToReturn = "<html><body>Content</body></html>".data(using: .utf8)!
        
        var progressUpdates: [Double] = []
        let expectation = expectation(description: "Progress updates")
        expectation.expectedFulfillmentCount = 10
        
        let cancellable = sut.$downloadProgress
            .dropFirst()
            .sink { progress in
                progressUpdates.append(progress)
                if progressUpdates.count <= 10 {
                    expectation.fulfill()
                }
            }
        
        // When
        _ = await sut.downloadArticlesForOffline(articles)
        
        // Then
        await fulfillment(of: [expectation], timeout: 5.0)
        XCTAssertEqual(progressUpdates.last, 1.0)
        XCTAssertTrue(progressUpdates.allSatisfy { $0 >= 0 && $0 <= 1 })
        
        cancellable.cancel()
    }
    
    // MARK: - Retrieval Tests
    
    func testGetOfflineContentSuccess() async throws {
        // Given
        var article = Article(url: "https://test.com/article", title: "Test Article")
        article.isDownloadedForOffline = true
        
        let htmlContent = "<html><body>Offline content</body></html>"
        let filePath = sut.getOfflineFilePath(for: article.id)
        try htmlContent.write(to: filePath, atomically: true, encoding: .utf8)
        
        // When
        let content = try await sut.getOfflineContent(for: article)
        
        // Then
        XCTAssertEqual(content, htmlContent)
    }
    
    func testGetOfflineContentNotDownloaded() async {
        // Given
        let article = Article(url: "https://test.com/article", title: "Test Article")
        
        // When & Then
        do {
            _ = try await sut.getOfflineContent(for: article)
            XCTFail("Should throw error")
        } catch OfflineContentError.notDownloaded {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    func testGetOfflineContentFileNotFound() async {
        // Given
        var article = Article(url: "https://test.com/article", title: "Test Article")
        article.isDownloadedForOffline = true
        // Don't create the file
        
        // When & Then
        do {
            _ = try await sut.getOfflineContent(for: article)
            XCTFail("Should throw error")
        } catch OfflineContentError.fileNotFound {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    // MARK: - Deletion Tests
    
    func testDeleteOfflineContent() async throws {
        // Given
        var article = Article(url: "https://test.com/article", title: "Test Article")
        article.isDownloadedForOffline = true
        
        let filePath = sut.getOfflineFilePath(for: article.id)
        let htmlContent = "<html><body>Content</body></html>"
        try htmlContent.write(to: filePath, atomically: true, encoding: .utf8)
        
        // When
        let updatedArticle = try await sut.deleteOfflineContent(for: article)
        
        // Then
        XCTAssertFalse(updatedArticle.isDownloadedForOffline)
        XCTAssertNil(updatedArticle.downloadedAt)
        XCTAssertFalse(FileManager.default.fileExists(atPath: filePath.path))
    }
    
    func testDeleteAllOfflineContent() async throws {
        // Given
        let articles = [
            Article(url: "https://test.com/1", title: "Article 1"),
            Article(url: "https://test.com/2", title: "Article 2")
        ]
        
        for article in articles {
            let filePath = sut.getOfflineFilePath(for: article.id)
            try "Content".write(to: filePath, atomically: true, encoding: .utf8)
        }
        
        // When
        try await sut.deleteAllOfflineContent()
        
        // Then
        for article in articles {
            let filePath = sut.getOfflineFilePath(for: article.id)
            XCTAssertFalse(FileManager.default.fileExists(atPath: filePath.path))
        }
    }
    
    // MARK: - Storage Management Tests
    
    func testGetOfflineStorageSize() async throws {
        // Given
        let articles = (0..<5).map { index in
            Article(url: "https://test.com/\(index)", title: "Article \(index)")
        }
        
        for article in articles {
            let filePath = sut.getOfflineFilePath(for: article.id)
            let content = String(repeating: "A", count: 1024) // 1KB of content
            try content.write(to: filePath, atomically: true, encoding: .utf8)
        }
        
        // When
        let size = try await sut.getOfflineStorageSize()
        
        // Then
        XCTAssertGreaterThan(size, 5000) // At least 5KB
        XCTAssertLessThan(size, 10000) // Less than 10KB
    }
    
    func testCleanupOldOfflineContent() async throws {
        // Given
        let oldDate = Date().addingTimeInterval(-31 * 24 * 60 * 60) // 31 days ago
        let recentDate = Date().addingTimeInterval(-5 * 24 * 60 * 60) // 5 days ago
        
        var oldArticle = Article(url: "https://test.com/old", title: "Old Article")
        oldArticle.downloadedAt = oldDate
        oldArticle.isDownloadedForOffline = true
        
        var recentArticle = Article(url: "https://test.com/recent", title: "Recent Article")
        recentArticle.downloadedAt = recentDate
        recentArticle.isDownloadedForOffline = true
        
        // Create files
        for article in [oldArticle, recentArticle] {
            let filePath = sut.getOfflineFilePath(for: article.id)
            try "Content".write(to: filePath, atomically: true, encoding: .utf8)
        }
        
        // When
        let deletedCount = try await sut.cleanupOldOfflineContent(olderThan: 30)
        
        // Then
        XCTAssertEqual(deletedCount, 1)
        XCTAssertFalse(FileManager.default.fileExists(atPath: sut.getOfflineFilePath(for: oldArticle.id).path))
        XCTAssertTrue(FileManager.default.fileExists(atPath: sut.getOfflineFilePath(for: recentArticle.id).path))
    }
    
    // MARK: - Image Caching Tests
    
    func testProcessAndCacheImages() async throws {
        // Given
        let html = """
            <html>
            <body>
                <img src="https://test.com/image1.jpg">
                <img src="https://test.com/image2.png">
                <img src="data:image/png;base64,iVBORw0KG..."> <!-- Data URL should be skipped -->
            </body>
            </html>
            """
        
        mockURLSession.imageDataToReturn = Data(repeating: 0xFF, count: 100)
        
        // When
        let processedHTML = try await sut.processAndCacheImages(html: html, articleId: "test-article")
        
        // Then
        XCTAssertTrue(processedHTML.contains("file://"))
        XCTAssertEqual(mockURLSession.downloadedURLs.count, 2) // Only remote images
        XCTAssertTrue(processedHTML.contains("data:image/png;base64")) // Data URL unchanged
    }
    
    // MARK: - Error Recovery Tests
    
    func testRecoverFromPartialDownload() async throws {
        // Given
        var article = Article(url: "https://test.com/article", title: "Test Article")
        article.content = "<html><body>Content</body></html>"
        
        // Simulate partial download by creating incomplete file
        let filePath = sut.getOfflineFilePath(for: article.id)
        try "".write(to: filePath, atomically: true, encoding: .utf8)
        
        mockURLSession.dataToReturn = article.content!.data(using: .utf8)!
        
        // When
        let downloadedArticle = try await sut.downloadArticleForOffline(article)
        
        // Then
        XCTAssertTrue(downloadedArticle.isDownloadedForOffline)
        let content = try String(contentsOf: filePath, encoding: .utf8)
        XCTAssertEqual(content, article.content)
    }
}

// MARK: - Mock Classes

class MockFileManager: FileManager {
    var savedFiles: [String] = []
    var deletedFiles: [String] = []
    let testDirectory: URL
    
    init(testDirectory: URL) {
        self.testDirectory = testDirectory
        super.init()
    }
    
    override func createDirectory(at url: URL, withIntermediateDirectories createIntermediates: Bool, attributes: [FileAttributeKey : Any]? = nil) throws {
        savedFiles.append(url.path)
    }
    
    override func removeItem(at URL: URL) throws {
        deletedFiles.append(URL.path)
    }
}

class MockURLSession: URLSession {
    var dataToReturn: Data?
    var imageDataToReturn: Data?
    var shouldFail = false
    var errorToReturn: Error?
    var downloadedURLs: [URL] = []
    
    override func data(from url: URL) async throws -> (Data, URLResponse) {
        downloadedURLs.append(url)
        
        if shouldFail {
            throw errorToReturn ?? URLError(.badServerResponse)
        }
        
        let data: Data
        if url.absoluteString.contains("image") {
            data = imageDataToReturn ?? Data()
        } else {
            data = dataToReturn ?? Data()
        }
        
        let response = HTTPURLResponse(
            url: url,
            statusCode: 200,
            httpVersion: nil,
            headerFields: nil
        )!
        
        return (data, response)
    }
}