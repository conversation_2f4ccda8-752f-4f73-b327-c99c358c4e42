import XCTest
import GRDB
@testable import PocketNext

@MainActor
class DatabaseManagerTests: XCTestCase {
    var sut: DatabaseManager!
    var testDbPath: String!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create a temporary test database
        let tempDir = FileManager.default.temporaryDirectory
        testDbPath = tempDir.appendingPathComponent("test_\(UUID().uuidString).db").path
        
        // Create a test instance with custom path
        sut = DatabaseManager()
        // Note: We'll need to modify DatabaseManager to accept a custom path for testing
        
        try await sut.initialize()
    }
    
    override func tearDown() async throws {
        // Clean up test database
        if FileManager.default.fileExists(atPath: testDbPath) {
            try FileManager.default.removeItem(atPath: testDbPath)
        }
        
        sut = nil
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testDatabaseInitialization() async throws {
        // Given & When - initialized in setUp
        
        // Then
        XCTAssertTrue(sut.isInitialized)
    }
    
    func testDatabaseTablesCreated() async throws {
        // Given - initialized database
        
        // When & Then - verify tables exist
        let dbQueue = try DatabaseQueue(path: testDbPath)
        try dbQueue.read { db in
            XCTAssertTrue(try db.tableExists("articles"))
            XCTAssertTrue(try db.tableExists("article_tags"))
            XCTAssertTrue(try db.tableExists("articles_fts"))
        }
    }
    
    // MARK: - Article Save Tests
    
    func testSaveArticle() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        
        // When
        try await sut.saveArticle(article)
        
        // Then
        let saved = try await sut.fetchArticle(id: article.id)
        XCTAssertNotNil(saved)
        XCTAssertEqual(saved?.url, article.url)
        XCTAssertEqual(saved?.title, article.title)
    }
    
    func testSaveArticleWithTags() async throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.tags = ["Swift", "Testing", "iOS"]
        
        // When
        try await sut.saveArticle(article)
        
        // Then
        let saved = try await sut.fetchArticle(id: article.id)
        XCTAssertNotNil(saved)
        XCTAssertEqual(saved?.tags.count, 3)
        XCTAssertTrue(saved?.tags.contains("Swift") ?? false)
        XCTAssertTrue(saved?.tags.contains("Testing") ?? false)
        XCTAssertTrue(saved?.tags.contains("iOS") ?? false)
    }
    
    func testSaveDuplicateArticle() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        try await sut.saveArticle(article)
        
        // When & Then
        do {
            try await sut.saveArticle(article)
            XCTFail("Should throw error for duplicate article")
        } catch {
            // Expected error
        }
    }
    
    // MARK: - Article Update Tests
    
    func testUpdateArticle() async throws {
        // Given
        var article = Article(url: "https://test.com", title: "Original Title")
        try await sut.saveArticle(article)
        
        // When
        article.title = "Updated Title"
        article.isRead = true
        article.readProgress = 0.5
        try await sut.updateArticle(article)
        
        // Then
        let updated = try await sut.fetchArticle(id: article.id)
        XCTAssertEqual(updated?.title, "Updated Title")
        XCTAssertTrue(updated?.isRead ?? false)
        XCTAssertEqual(updated?.readProgress, 0.5)
    }
    
    func testUpdateNonExistentArticle() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        
        // When & Then
        do {
            try await sut.updateArticle(article)
            XCTFail("Should throw error for non-existent article")
        } catch {
            // Expected error
        }
    }
    
    // MARK: - Article Delete Tests
    
    func testDeleteArticle() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        try await sut.saveArticle(article)
        
        // When
        try await sut.deleteArticle(article.id)
        
        // Then
        let deleted = try await sut.fetchArticle(id: article.id)
        XCTAssertNil(deleted)
    }
    
    func testDeleteNonExistentArticle() async throws {
        // Given
        let nonExistentId = "non-existent-id"
        
        // When & Then - should not throw
        try await sut.deleteArticle(nonExistentId)
    }
    
    // MARK: - Mark as Read Tests
    
    func testMarkAsRead() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        try await sut.saveArticle(article)
        
        // When
        try await sut.markAsRead(article.id)
        
        // Then
        let updated = try await sut.fetchArticle(id: article.id)
        XCTAssertTrue(updated?.isRead ?? false)
        XCTAssertEqual(updated?.readProgress, 1.0)
        XCTAssertEqual(updated?.syncStatus, .modified)
    }
    
    // MARK: - Fetch Operations Tests
    
    func testFetchRecentArticles() async throws {
        // Given
        var articles: [Article] = []
        for i in 0..<100 {
            var article = Article(url: "https://test\(i).com", title: "Article \(i)")
            article.savedAt = Date().addingTimeInterval(TimeInterval(-i * 3600)) // Each article 1 hour older
            articles.append(article)
        }
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When - fetch with default limit (50)
        let recent = try await sut.fetchRecentArticles()
        
        // Then
        XCTAssertEqual(recent.count, 50)
        // Verify they are ordered by savedAt descending
        for i in 0..<recent.count - 1 {
            XCTAssertGreaterThan(recent[i].savedAt, recent[i + 1].savedAt)
        }
    }
    
    func testFetchRecentArticlesWithCustomLimit() async throws {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2"),
            Article(url: "https://test3.com", title: "Article 3"),
            Article(url: "https://test4.com", title: "Article 4"),
            Article(url: "https://test5.com", title: "Article 5")
        ]
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let recent = try await sut.fetchRecentArticles(limit: 3)
        
        // Then
        XCTAssertEqual(recent.count, 3)
    }
    
    func testFetchRecentArticlesExcludesArchived() async throws {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2"),
            Article(url: "https://test3.com", title: "Article 3")
        ]
        
        // Archive the second article
        articles[1].isArchived = true
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let recent = try await sut.fetchRecentArticles()
        
        // Then
        XCTAssertEqual(recent.count, 2)
        XCTAssertFalse(recent.contains { $0.isArchived })
    }
    
    func testFetchAllArticles() async throws {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2"),
            Article(url: "https://test3.com", title: "Article 3")
        ]
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let fetched = try await sut.fetchAllArticles()
        
        // Then
        XCTAssertEqual(fetched.count, 3)
    }
    
    func testFetchArticleById() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        try await sut.saveArticle(article)
        
        // When
        let fetched = try await sut.fetchArticle(id: article.id)
        
        // Then
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.id, article.id)
    }
    
    func testFetchNonExistentArticle() async throws {
        // Given
        let nonExistentId = "non-existent-id"
        
        // When
        let fetched = try await sut.fetchArticle(id: nonExistentId)
        
        // Then
        XCTAssertNil(fetched)
    }
    
    // MARK: - Search Tests
    
    func testSearchArticles() async throws {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "SwiftUI Tutorial"),
            Article(url: "https://test2.com", title: "UIKit Basics"),
            Article(url: "https://test3.com", title: "Swift Concurrency")
        ]
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let results = try await sut.searchArticles(query: "Swift")
        
        // Then
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.contains { $0.title.contains("Swift") })
    }
    
    func testSearchWithEmptyQuery() async throws {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2")
        ]
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let results = try await sut.searchArticles(query: "")
        
        // Then
        XCTAssertEqual(results.count, 2)
    }
    
    // MARK: - Statistics Tests
    
    func testGetStatistics() async throws {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2"),
            Article(url: "https://test3.com", title: "Article 3")
        ]
        
        // Make one read
        articles[0].isRead = true
        articles[0].readingTime = 10
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let stats = try await sut.getStatistics()
        
        // Then
        XCTAssertEqual(stats.total, 3)
        XCTAssertEqual(stats.unread, 2)
        XCTAssertGreaterThanOrEqual(stats.todayRead, 1)
        XCTAssertGreaterThanOrEqual(stats.weeklyReadTime, 10)
    }
    
    // MARK: - Sync Support Tests
    
    func testFetchArticlesNeedingSync() async throws {
        // Given
        var articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2")
        ]
        
        articles[0].syncStatus = .pending
        articles[1].syncStatus = .synced
        
        for article in articles {
            try await sut.saveArticle(article)
        }
        
        // When
        let needingSync = try await sut.fetchArticlesNeedingSync()
        
        // Then
        XCTAssertEqual(needingSync.count, 1)
        XCTAssertEqual(needingSync[0].title, "Article 1")
    }
    
    func testMarkAsSynced() async throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.syncStatus = .pending
        try await sut.saveArticle(article)
        
        // When
        let cloudKitRecordID = "test-cloudkit-id"
        try await sut.markAsSynced(article.id, cloudKitRecordID: cloudKitRecordID)
        
        // Then
        let updated = try await sut.fetchArticle(id: article.id)
        XCTAssertEqual(updated?.syncStatus, .synced)
        XCTAssertEqual(updated?.cloudKitRecordID, cloudKitRecordID)
        XCTAssertNotNil(updated?.lastSyncedAt)
    }
    
    // MARK: - Error Handling Tests
    
    func testDatabaseNotInitializedError() async throws {
        // Given
        let uninitializedManager = DatabaseManager()
        
        // When & Then - Test all methods that use the db property
        
        // fetchAllArticles
        do {
            _ = try await uninitializedManager.fetchAllArticles()
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // fetchRecentArticles
        do {
            _ = try await uninitializedManager.fetchRecentArticles()
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // saveArticle
        do {
            let article = Article(url: "https://test.com", title: "Test")
            try await uninitializedManager.saveArticle(article)
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // updateArticle
        do {
            let article = Article(url: "https://test.com", title: "Test")
            try await uninitializedManager.updateArticle(article)
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // deleteArticle
        do {
            try await uninitializedManager.deleteArticle("test-id")
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // markAsRead
        do {
            try await uninitializedManager.markAsRead("test-id")
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // fetchArticle
        do {
            _ = try await uninitializedManager.fetchArticle(id: "test-id")
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // searchArticles
        do {
            _ = try await uninitializedManager.searchArticles(query: "test")
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // getStatistics
        do {
            _ = try await uninitializedManager.getStatistics()
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // fetchArticlesNeedingSync
        do {
            _ = try await uninitializedManager.fetchArticlesNeedingSync()
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
        
        // markAsSynced
        do {
            try await uninitializedManager.markAsSynced("test-id", cloudKitRecordID: "cloud-id")
            XCTFail("Should throw notInitialized error")
        } catch DatabaseError.notInitialized {
            // Expected error
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    func testDatabaseInitializationFailure() async throws {
        // Given - create a database manager with an invalid path
        // This test would require modifying DatabaseManager to accept a custom path
        // For now, we'll skip this test as it would require changing production code
    }
    
    // MARK: - Concurrency Tests
    
    func testConcurrentReads() async throws {
        // Given
        let articleCount = 100
        for i in 0..<articleCount {
            let article = Article(url: "https://test\(i).com", title: "Article \(i)")
            try await sut.saveArticle(article)
        }
        
        // When - perform multiple concurrent reads
        await withTaskGroup(of: [Article].self) { group in
            for _ in 0..<10 {
                group.addTask {
                    try! await self.sut.fetchAllArticles()
                }
            }
            
            // Then - all reads should succeed and return the same count
            var results: [[Article]] = []
            for await articles in group {
                results.append(articles)
            }
            
            XCTAssertEqual(results.count, 10)
            for articles in results {
                XCTAssertEqual(articles.count, articleCount)
            }
        }
    }
    
    func testConcurrentWrites() async throws {
        // Given
        let writeCount = 50
        
        // When - perform multiple concurrent writes
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<writeCount {
                group.addTask {
                    let article = Article(url: "https://concurrent\(i).com", title: "Concurrent \(i)")
                    try! await self.sut.saveArticle(article)
                }
            }
        }
        
        // Then - all articles should be saved
        let articles = try await sut.fetchAllArticles()
        let concurrentArticles = articles.filter { $0.url.contains("concurrent") }
        XCTAssertEqual(concurrentArticles.count, writeCount)
    }
    
    func testConcurrentReadWrite() async throws {
        // Given
        let initialCount = 10
        for i in 0..<initialCount {
            let article = Article(url: "https://initial\(i).com", title: "Initial \(i)")
            try await sut.saveArticle(article)
        }
        
        // When - perform reads and writes concurrently
        await withTaskGroup(of: Void.self) { group in
            // Readers
            for _ in 0..<5 {
                group.addTask {
                    _ = try! await self.sut.fetchAllArticles()
                }
            }
            
            // Writers
            for i in 0..<5 {
                group.addTask {
                    let article = Article(url: "https://concurrent\(i).com", title: "Concurrent \(i)")
                    try! await self.sut.saveArticle(article)
                }
            }
        }
        
        // Then - all operations should complete successfully
        let finalArticles = try await sut.fetchAllArticles()
        XCTAssertEqual(finalArticles.count, initialCount + 5)
    }
    
    // MARK: - Performance Tests
    
    func testBulkInsertPerformance() async throws {
        // Given
        let articleCount = 1000
        var articles: [Article] = []
        
        for i in 0..<articleCount {
            articles.append(Article(
                url: "https://test\(i).com",
                title: "Article \(i)"
            ))
        }
        
        // When & Then
        await measureAsync {
            for article in articles {
                try? await sut.saveArticle(article)
            }
        }
    }
    
    func testSearchPerformance() async throws {
        // Given - insert many articles
        for i in 0..<100 {
            let article = Article(
                url: "https://test\(i).com",
                title: "Article about Swift \(i)"
            )
            try await sut.saveArticle(article)
        }
        
        // When & Then
        await measureAsync {
            _ = try? await sut.searchArticles(query: "Swift")
        }
    }
}

// MARK: - Async Measurement Helper
extension XCTestCase {
    func measureAsync(block: () async throws -> Void) async {
        await withTaskGroup(of: Void.self) { group in
            for _ in 0..<10 {
                group.addTask {
                    try? await block()
                }
            }
        }
    }
}

// MARK: - Test-only DatabaseManager Extension
extension DatabaseManager {
    /// Creates a test instance with a custom database path for testing
    static func testInstance(path: String? = nil) -> DatabaseManager {
        let manager = DatabaseManager()
        // Note: This would require modifying DatabaseManager to support custom paths
        // For now, we use the shared instance
        return manager
    }
}