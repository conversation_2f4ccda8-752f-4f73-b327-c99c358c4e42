import XCTest
import CloudKit
import Combine
@testable import PocketNext

class CloudKitSyncServiceTests: XCTestCase {
    var sut: CloudKitSyncService!
    var mockContainer: MockCKContainer!
    var mockDatabase: MockCKDatabase!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockDatabase = MockCKDatabase()
        mockContainer = MockCKContainer(database: mockDatabase)
        sut = CloudKitSyncService()
        // We'll need to inject the mock container
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        sut = nil
        mockContainer = nil
        mockDatabase = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Account Status Tests
    
    func testCheckAccountStatusAvailable() async {
        // Given
        mockContainer.accountStatus = .available
        
        // When
        await sut.checkAccountStatus()
        
        // Then
        XCTAssertTrue(sut.isCloudKitAvailable)
        XCTAssertNil(sut.lastError)
    }
    
    func testCheckAccountStatusNoAccount() async {
        // Given
        mockContainer.accountStatus = .noAccount
        
        // When
        await sut.checkAccountStatus()
        
        // Then
        XCTAssertFalse(sut.isCloudKitAvailable)
        XCTAssertNotNil(sut.lastError)
    }
    
    func testCheckAccountStatusRestricted() async {
        // Given
        mockContainer.accountStatus = .restricted
        
        // When
        await sut.checkAccountStatus()
        
        // Then
        XCTAssertFalse(sut.isCloudKitAvailable)
        XCTAssertNotNil(sut.lastError)
    }
    
    // MARK: - Sync Tests
    
    func testStartSyncWhenAvailable() async {
        // Given
        sut.isCloudKitAvailable = true
        let expectation = expectation(description: "Sync started")
        
        sut.$syncState
            .dropFirst()
            .sink { state in
                if state == .syncing {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When
        await sut.startSync()
        
        // Then
        await fulfillment(of: [expectation], timeout: 1.0)
        XCTAssertEqual(sut.syncState, .syncing)
    }
    
    func testStartSyncWhenNotAvailable() async {
        // Given
        sut.isCloudKitAvailable = false
        
        // When
        await sut.startSync()
        
        // Then
        XCTAssertEqual(sut.syncState, .idle)
    }
    
    func testStopSync() async {
        // Given
        sut.syncState = .syncing
        
        // When
        await sut.stopSync()
        
        // Then
        XCTAssertEqual(sut.syncState, .idle)
    }
    
    // MARK: - Upload Tests
    
    func testUploadArticleSuccess() async throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        mockDatabase.shouldSucceed = true
        sut.isCloudKitAvailable = true
        
        // When
        let recordID = try await sut.uploadArticle(article)
        
        // Then
        XCTAssertNotNil(recordID)
        XCTAssertEqual(mockDatabase.savedRecords.count, 1)
        let savedRecord = mockDatabase.savedRecords.first
        XCTAssertEqual(savedRecord?["url"] as? String, article.url)
        XCTAssertEqual(savedRecord?["title"] as? String, article.title)
    }
    
    func testUploadArticleFailure() async {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        mockDatabase.shouldSucceed = false
        mockDatabase.errorToReturn = CKError(.networkFailure)
        sut.isCloudKitAvailable = true
        
        // When & Then
        do {
            _ = try await sut.uploadArticle(article)
            XCTFail("Should throw error")
        } catch {
            XCTAssertTrue(error is CKError)
        }
    }
    
    func testUploadMultipleArticles() async throws {
        // Given
        let articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2"),
            Article(url: "https://test3.com", title: "Article 3")
        ]
        mockDatabase.shouldSucceed = true
        sut.isCloudKitAvailable = true
        
        // When
        let results = await sut.uploadArticles(articles)
        
        // Then
        XCTAssertEqual(results.count, 3)
        XCTAssertEqual(mockDatabase.savedRecords.count, 3)
        
        for (article, result) in zip(articles, results) {
            switch result {
            case .success(let recordID):
                XCTAssertNotNil(recordID)
            case .failure:
                XCTFail("Upload should succeed")
            }
        }
    }
    
    // MARK: - Download Tests
    
    func testDownloadArticlesSuccess() async throws {
        // Given
        let mockRecords = createMockArticleRecords(count: 3)
        mockDatabase.recordsToReturn = mockRecords
        mockDatabase.shouldSucceed = true
        sut.isCloudKitAvailable = true
        
        // When
        let articles = try await sut.downloadArticles()
        
        // Then
        XCTAssertEqual(articles.count, 3)
        XCTAssertEqual(articles[0].title, "Mock Article 0")
        XCTAssertEqual(articles[1].title, "Mock Article 1")
        XCTAssertEqual(articles[2].title, "Mock Article 2")
    }
    
    func testDownloadArticlesFailure() async {
        // Given
        mockDatabase.shouldSucceed = false
        mockDatabase.errorToReturn = CKError(.networkUnavailable)
        sut.isCloudKitAvailable = true
        
        // When & Then
        do {
            _ = try await sut.downloadArticles()
            XCTFail("Should throw error")
        } catch {
            XCTAssertTrue(error is CKError)
        }
    }
    
    // MARK: - Update Tests
    
    func testUpdateArticleSuccess() async throws {
        // Given
        var article = Article(url: "https://test.com", title: "Original Title")
        article.cloudKitRecordID = "test-record-id"
        article.title = "Updated Title"
        
        let existingRecord = CKRecord(recordType: "Article", recordID: CKRecord.ID(recordName: "test-record-id"))
        mockDatabase.recordsToReturn = [existingRecord]
        mockDatabase.shouldSucceed = true
        sut.isCloudKitAvailable = true
        
        // When
        try await sut.updateArticle(article)
        
        // Then
        XCTAssertEqual(mockDatabase.savedRecords.count, 1)
        let savedRecord = mockDatabase.savedRecords.first
        XCTAssertEqual(savedRecord?["title"] as? String, "Updated Title")
    }
    
    // MARK: - Delete Tests
    
    func testDeleteArticleSuccess() async throws {
        // Given
        let recordID = "test-record-id"
        mockDatabase.shouldSucceed = true
        sut.isCloudKitAvailable = true
        
        // When
        try await sut.deleteArticle(cloudKitRecordID: recordID)
        
        // Then
        XCTAssertEqual(mockDatabase.deletedRecordIDs.count, 1)
        XCTAssertEqual(mockDatabase.deletedRecordIDs.first?.recordName, recordID)
    }
    
    // MARK: - Conflict Resolution Tests
    
    func testResolveConflictKeepLocal() async throws {
        // Given
        let localArticle = Article(url: "https://test.com", title: "Local Title")
        let remoteRecord = createMockArticleRecord(title: "Remote Title")
        
        // When
        let resolved = try await sut.resolveConflict(
            local: localArticle,
            remote: remoteRecord,
            resolution: .keepLocal
        )
        
        // Then
        XCTAssertEqual(resolved.title, "Local Title")
    }
    
    func testResolveConflictKeepRemote() async throws {
        // Given
        let localArticle = Article(url: "https://test.com", title: "Local Title")
        let remoteRecord = createMockArticleRecord(title: "Remote Title")
        
        // When
        let resolved = try await sut.resolveConflict(
            local: localArticle,
            remote: remoteRecord,
            resolution: .keepRemote
        )
        
        // Then
        XCTAssertEqual(resolved.title, "Remote Title")
    }
    
    func testResolveConflictMerge() async throws {
        // Given
        var localArticle = Article(url: "https://test.com", title: "Local Title")
        localArticle.isRead = true
        localArticle.savedAt = Date().addingTimeInterval(-3600) // 1 hour ago
        
        let remoteRecord = createMockArticleRecord(title: "Remote Title")
        remoteRecord["isRead"] = false
        remoteRecord["savedAt"] = Date() // Now
        
        // When
        let resolved = try await sut.resolveConflict(
            local: localArticle,
            remote: remoteRecord,
            resolution: .merge
        )
        
        // Then
        // Should keep the more recent savedAt and the true isRead value
        XCTAssertEqual(resolved.title, "Remote Title") // More recent
        XCTAssertTrue(resolved.isRead) // Keep true value
    }
    
    // MARK: - Subscription Tests
    
    func testSetupSubscriptions() async {
        // Given
        sut.isCloudKitAvailable = true
        mockDatabase.shouldSucceed = true
        
        // When
        await sut.setupSubscriptions()
        
        // Then
        XCTAssertEqual(mockDatabase.savedSubscriptions.count, 1)
        let subscription = mockDatabase.savedSubscriptions.first
        XCTAssertEqual(subscription?.recordType, "Article")
    }
    
    func testHandleRemoteNotification() async {
        // Given
        let userInfo: [String: Any] = [
            "ck": ["qry": ["rid": "test-subscription-id"]]
        ]
        
        // When
        let result = await sut.handleRemoteNotification(userInfo: userInfo)
        
        // Then
        XCTAssertTrue(result)
        // Verify sync was triggered
    }
    
    // MARK: - Error Handling Tests
    
    func testHandleQuotaExceeded() async {
        // Given
        mockDatabase.shouldSucceed = false
        mockDatabase.errorToReturn = CKError(.quotaExceeded)
        sut.isCloudKitAvailable = true
        
        // When
        do {
            _ = try await sut.uploadArticle(Article(url: "https://test.com", title: "Test"))
            XCTFail("Should throw error")
        } catch {
            // Then
            XCTAssertNotNil(sut.lastError)
            if case .quotaExceeded = sut.lastError {
                // Success
            } else {
                XCTFail("Wrong error type")
            }
        }
    }
    
    func testRetryOnNetworkError() async throws {
        // Given
        var attemptCount = 0
        mockDatabase.saveHandler = { _ in
            attemptCount += 1
            if attemptCount < 3 {
                throw CKError(.networkFailure)
            }
            return CKRecord(recordType: "Article")
        }
        sut.isCloudKitAvailable = true
        
        // When
        let article = Article(url: "https://test.com", title: "Test")
        let recordID = try await sut.uploadArticle(article)
        
        // Then
        XCTAssertNotNil(recordID)
        XCTAssertEqual(attemptCount, 3) // Should retry twice
    }
    
    // MARK: - Helper Methods
    
    private func createMockArticleRecord(title: String = "Mock Article") -> CKRecord {
        let record = CKRecord(recordType: "Article")
        record["url"] = "https://test.com"
        record["title"] = title
        record["savedAt"] = Date()
        record["isRead"] = false
        record["readProgress"] = 0.0
        return record
    }
    
    private func createMockArticleRecords(count: Int) -> [CKRecord] {
        return (0..<count).map { index in
            createMockArticleRecord(title: "Mock Article \(index)")
        }
    }
}

// MARK: - Mock Classes

class MockCKContainer: CKContainer {
    var accountStatus: CKAccountStatus = .available
    var database: MockCKDatabase
    
    init(database: MockCKDatabase) {
        self.database = database
        super.init(identifier: "test.container")
    }
    
    override func accountStatus(completionHandler: @escaping (CKAccountStatus, Error?) -> Void) {
        completionHandler(accountStatus, nil)
    }
    
    override var privateCloudDatabase: CKDatabase {
        return database
    }
}

class MockCKDatabase: CKDatabase {
    var shouldSucceed = true
    var errorToReturn: Error?
    var recordsToReturn: [CKRecord] = []
    var savedRecords: [CKRecord] = []
    var deletedRecordIDs: [CKRecord.ID] = []
    var savedSubscriptions: [CKSubscription] = []
    var saveHandler: ((CKRecord) throws -> CKRecord)?
    
    override func save(_ record: CKRecord, completionHandler: @escaping (CKRecord?, Error?) -> Void) {
        if let handler = saveHandler {
            do {
                let result = try handler(record)
                completionHandler(result, nil)
            } catch {
                completionHandler(nil, error)
            }
        } else if shouldSucceed {
            savedRecords.append(record)
            completionHandler(record, nil)
        } else {
            completionHandler(nil, errorToReturn ?? CKError(.internalError))
        }
    }
    
    override func fetch(withRecordID recordID: CKRecord.ID, completionHandler: @escaping (CKRecord?, Error?) -> Void) {
        if shouldSucceed {
            let record = recordsToReturn.first { $0.recordID == recordID } ?? CKRecord(recordType: "Article", recordID: recordID)
            completionHandler(record, nil)
        } else {
            completionHandler(nil, errorToReturn ?? CKError(.unknownItem))
        }
    }
    
    override func delete(withRecordID recordID: CKRecord.ID, completionHandler: @escaping (CKRecord.ID?, Error?) -> Void) {
        if shouldSucceed {
            deletedRecordIDs.append(recordID)
            completionHandler(recordID, nil)
        } else {
            completionHandler(nil, errorToReturn ?? CKError(.internalError))
        }
    }
    
    override func perform(_ query: CKQuery, inZoneWith zoneID: CKRecordZone.ID?, completionHandler: @escaping ([CKRecord]?, Error?) -> Void) {
        if shouldSucceed {
            completionHandler(recordsToReturn, nil)
        } else {
            completionHandler(nil, errorToReturn ?? CKError(.internalError))
        }
    }
    
    override func save(_ subscription: CKSubscription, completionHandler: @escaping (CKSubscription?, Error?) -> Void) {
        if shouldSucceed {
            savedSubscriptions.append(subscription)
            completionHandler(subscription, nil)
        } else {
            completionHandler(nil, errorToReturn ?? CKError(.internalError))
        }
    }
}