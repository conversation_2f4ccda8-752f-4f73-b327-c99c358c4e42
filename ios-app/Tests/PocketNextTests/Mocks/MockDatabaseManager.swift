import Foundation
import Combine
@testable import PocketNext

@MainActor
class MockDatabaseManager: ObservableObject {
    // MARK: - Published Properties
    @Published var isInitialized = false
    
    // MARK: - Configuration Properties
    var shouldFailInitialization = false
    var shouldFailOperations = false
    var operationDelay: TimeInterval = 0
    var customError: Error?
    
    // MARK: - Mock Data Storage
    private var articles: [Article] = []
    private var statistics = ArticleStatistics(total: 0, unread: 0, todayRead: 0, weeklyReadTime: 0)
    
    // MARK: - Tracking Properties
    var initializeCallCount = 0
    var saveArticleCallCount = 0
    var updateArticleCallCount = 0
    var deleteArticleCallCount = 0
    var markAsReadCallCount = 0
    var fetchAllArticlesCallCount = 0
    var fetchRecentArticlesCallCount = 0
    var fetchArticleCallCount = 0
    var searchArticlesCallCount = 0
    var getStatisticsCallCount = 0
    var fetchArticlesNeedingSyncCallCount = 0
    var markAsSyncedCallCount = 0
    
    // MARK: - Captured Parameters
    var lastSavedArticle: Article?
    var lastUpdatedArticle: Article?
    var lastDeletedArticleId: String?
    var lastMarkedAsReadId: String?
    var lastFetchedArticleId: String?
    var lastSearchQuery: String?
    var lastMarkAsSyncedId: String?
    var lastCloudKitRecordId: String?
    
    // MARK: - Initialization
    
    init(preloadedArticles: [Article] = [], isInitialized: Bool = true) {
        self.articles = preloadedArticles
        self.isInitialized = isInitialized
        updateStatistics()
    }
    
    // MARK: - Database Setup
    
    func initialize() async throws {
        initializeCallCount += 1
        
        if let customError = customError {
            throw customError
        }
        
        if shouldFailInitialization {
            throw DatabaseError.initializationFailed(MockError.intentionalFailure)
        }
        
        await simulateDelay()
        isInitialized = true
    }
    
    // MARK: - Article Operations
    
    func saveArticle(_ article: Article) async throws {
        saveArticleCallCount += 1
        lastSavedArticle = article
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        // Check for duplicate
        if articles.contains(where: { $0.id == article.id }) {
            throw MockError.duplicateArticle
        }
        
        articles.append(article)
        updateStatistics()
        
        // Notify observers
        NotificationCenter.default.post(name: .articlesUpdated, object: nil)
    }
    
    func updateArticle(_ article: Article) async throws {
        updateArticleCallCount += 1
        lastUpdatedArticle = article
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        guard let index = articles.firstIndex(where: { $0.id == article.id }) else {
            throw MockError.articleNotFound
        }
        
        articles[index] = article
        updateStatistics()
        
        NotificationCenter.default.post(name: .articlesUpdated, object: nil)
    }
    
    func deleteArticle(_ articleId: String) async throws {
        deleteArticleCallCount += 1
        lastDeletedArticleId = articleId
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        articles.removeAll { $0.id == articleId }
        updateStatistics()
        
        NotificationCenter.default.post(name: .articlesUpdated, object: nil)
    }
    
    func markAsRead(_ articleId: String) async throws {
        markAsReadCallCount += 1
        lastMarkedAsReadId = articleId
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        guard let index = articles.firstIndex(where: { $0.id == articleId }) else {
            return
        }
        
        articles[index].isRead = true
        articles[index].readProgress = 1.0
        articles[index].syncStatus = .modified
        updateStatistics()
        
        NotificationCenter.default.post(name: .articlesUpdated, object: nil)
    }
    
    // MARK: - Fetch Operations
    
    func fetchRecentArticles(limit: Int = 50) async throws -> [Article] {
        fetchRecentArticlesCallCount += 1
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        return Array(articles
            .filter { !$0.isArchived }
            .sorted { $0.savedAt > $1.savedAt }
            .prefix(limit))
    }
    
    func fetchAllArticles() async throws -> [Article] {
        fetchAllArticlesCallCount += 1
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        return articles
    }
    
    func fetchArticle(id: String) async throws -> Article? {
        fetchArticleCallCount += 1
        lastFetchedArticleId = id
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        return articles.first { $0.id == id }
    }
    
    func searchArticles(query: String) async throws -> [Article] {
        searchArticlesCallCount += 1
        lastSearchQuery = query
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        guard !query.isEmpty else { return articles }
        
        let lowercasedQuery = query.lowercased()
        return articles.filter { article in
            article.title.lowercased().contains(lowercasedQuery) ||
            (article.content?.lowercased().contains(lowercasedQuery) ?? false) ||
            (article.summary?.lowercased().contains(lowercasedQuery) ?? false)
        }
    }
    
    // MARK: - Statistics
    
    func getStatistics() async throws -> ArticleStatistics {
        getStatisticsCallCount += 1
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        return statistics
    }
    
    // MARK: - Sync Support
    
    func fetchArticlesNeedingSync() async throws -> [Article] {
        fetchArticlesNeedingSyncCallCount += 1
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        return articles.filter { $0.syncStatus != .synced }
    }
    
    func markAsSynced(_ articleId: String, cloudKitRecordID: String) async throws {
        markAsSyncedCallCount += 1
        lastMarkAsSyncedId = articleId
        lastCloudKitRecordId = cloudKitRecordID
        
        try checkInitialized()
        try checkShouldFail()
        await simulateDelay()
        
        guard let index = articles.firstIndex(where: { $0.id == articleId }) else {
            return
        }
        
        articles[index].cloudKitRecordID = cloudKitRecordID
        articles[index].lastSyncedAt = Date()
        articles[index].syncStatus = .synced
    }
    
    // MARK: - Helper Methods
    
    private func checkInitialized() throws {
        guard isInitialized else {
            throw DatabaseError.notInitialized
        }
    }
    
    private func checkShouldFail() throws {
        if let customError = customError {
            throw customError
        }
        
        if shouldFailOperations {
            throw MockError.intentionalFailure
        }
    }
    
    private func simulateDelay() async {
        if operationDelay > 0 {
            try? await Task.sleep(nanoseconds: UInt64(operationDelay * 1_000_000_000))
        }
    }
    
    private func updateStatistics() {
        let total = articles.count
        let unread = articles.filter { !$0.isRead }.count
        
        let startOfDay = Calendar.current.startOfDay(for: Date())
        let todayRead = articles.filter { article in
            article.isRead && article.savedAt >= startOfDay
        }.count
        
        let weekAgo = Date().addingTimeInterval(-7 * 24 * 60 * 60)
        let weeklyReadTime = articles
            .filter { $0.isRead && $0.savedAt >= weekAgo }
            .reduce(0) { sum, article in
                sum + (article.readingTime ?? 5)
            }
        
        statistics = ArticleStatistics(
            total: total,
            unread: unread,
            todayRead: todayRead,
            weeklyReadTime: weeklyReadTime
        )
    }
    
    // MARK: - Test Helpers
    
    func reset() {
        articles.removeAll()
        isInitialized = true
        shouldFailInitialization = false
        shouldFailOperations = false
        operationDelay = 0
        customError = nil
        
        // Reset call counts
        initializeCallCount = 0
        saveArticleCallCount = 0
        updateArticleCallCount = 0
        deleteArticleCallCount = 0
        markAsReadCallCount = 0
        fetchAllArticlesCallCount = 0
        fetchRecentArticlesCallCount = 0
        fetchArticleCallCount = 0
        searchArticlesCallCount = 0
        getStatisticsCallCount = 0
        fetchArticlesNeedingSyncCallCount = 0
        markAsSyncedCallCount = 0
        
        // Reset captured parameters
        lastSavedArticle = nil
        lastUpdatedArticle = nil
        lastDeletedArticleId = nil
        lastMarkedAsReadId = nil
        lastFetchedArticleId = nil
        lastSearchQuery = nil
        lastMarkAsSyncedId = nil
        lastCloudKitRecordId = nil
        
        updateStatistics()
    }
    
    func addMockArticles(_ articles: [Article]) {
        self.articles.append(contentsOf: articles)
        updateStatistics()
    }
    
    func setMockStatistics(_ stats: ArticleStatistics) {
        self.statistics = stats
    }
}

// MARK: - Mock Errors

enum MockError: LocalizedError {
    case intentionalFailure
    case duplicateArticle
    case articleNotFound
    
    var errorDescription: String? {
        switch self {
        case .intentionalFailure:
            return "Intentional test failure"
        case .duplicateArticle:
            return "Article with this ID already exists"
        case .articleNotFound:
            return "Article not found"
        }
    }
}

// MARK: - MockDatabaseManager as DatabaseManager Protocol

extension MockDatabaseManager {
    static let shared = MockDatabaseManager()
}