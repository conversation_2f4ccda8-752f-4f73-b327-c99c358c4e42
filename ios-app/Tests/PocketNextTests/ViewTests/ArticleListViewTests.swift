import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

class ArticleListViewTests: XCTestCase {
    var sut: ArticleListView!
    var viewModel: ArticleListViewModel!
    
    override func setUp() {
        super.setUp()
        viewModel = ArticleListViewModel()
        sut = ArticleListView(viewModel: viewModel)
    }
    
    override func tearDown() {
        sut = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - View Structure Tests
    
    func testViewHasNavigationStack() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.navigationStack())
    }
    
    func testViewHasToolbar() throws {
        // When
        let view = try sut.inspect()
        let navigationStack = try view.navigationStack()
        
        // Then
        XCTAssertNoThrow(try navigationStack.toolbar())
    }
    
    func testEmptyStateView() throws {
        // Given
        viewModel.articles = []
        
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(ContentUnavailableView.self))
    }
    
    func testArticleListContent() throws {
        // Given
        viewModel.articles = [
            Article(url: "https://test1.com", title: "Article 1"),
            Article(url: "https://test2.com", title: "Article 2")
        ]
        
        // When
        let view = try sut.inspect()
        
        // Then
        let scrollView = try view.find(ScrollView.self)
        let lazyVStack = try scrollView.lazyVStack()
        XCTAssertEqual(try lazyVStack.count, 2)
    }
    
    // MARK: - Filter Tests
    
    func testFilterSegmentedControl() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        let picker = try view.find(Picker.self)
        XCTAssertEqual(try picker.labelView().text().string(), "Filter")
        
        // Verify all filter options are present
        let pickerContent = try picker.pickerContent()
        XCTAssertEqual(try pickerContent.count, ArticleFilter.allCases.count)
    }
    
    func testFilterSelection() throws {
        // Given
        viewModel.articles = [
            Article(url: "https://test.com", title: "Test Article")
        ]
        
        // When
        viewModel.selectedFilter = .unread
        let view = try sut.inspect()
        
        // Then
        let picker = try view.find(Picker.self)
        // Verify selection binding works
    }
    
    // MARK: - Sort Tests
    
    func testSortMenu() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        let menu = try view.find(Menu.self)
        let menuLabel = try menu.labelView().hStack()
        XCTAssertNoThrow(try menuLabel.find(text: "Sort"))
    }
    
    func testSortOptions() throws {
        // When
        let view = try sut.inspect()
        let menu = try view.find(Menu.self)
        
        // Then
        let menuContent = try menu.content()
        XCTAssertEqual(try menuContent.count, SortOrder.allCases.count)
    }
    
    // MARK: - Search Bar Tests
    
    func testSearchBarPresence() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(ViewType.TextField.self))
    }
    
    func testSearchBarPlaceholder() throws {
        // When
        let view = try sut.inspect()
        let searchField = try view.find(ViewType.TextField.self)
        
        // Then
        XCTAssertEqual(try searchField.placeholder(), "Search articles...")
    }
    
    // MARK: - Article Row Tests
    
    func testArticleRowContent() throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.summary = "Test summary"
        article.author = "Test Author"
        article.readingTime = 5
        
        let articleRow = ArticleRow(article: article)
        
        // When
        let view = try articleRow.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(text: "Test Article"))
        XCTAssertNoThrow(try view.find(text: "Test summary"))
        XCTAssertNoThrow(try view.find(text: "Test Author"))
        XCTAssertNoThrow(try view.find(text: "5 min"))
    }
    
    func testArticleRowReadIndicator() throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.isRead = false
        
        let articleRow = ArticleRow(article: article)
        
        // When
        let view = try articleRow.inspect()
        
        // Then
        // Should show unread indicator
        let circle = try view.find(ViewType.Shape.self)
        XCTAssertNotNil(circle)
    }
    
    func testArticleRowFavoriteIcon() throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.isFavorite = true
        
        let articleRow = ArticleRow(article: article)
        
        // When
        let view = try articleRow.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(image: "star.fill"))
    }
    
    // MARK: - Loading State Tests
    
    func testLoadingIndicator() throws {
        // Given
        viewModel.isLoading = true
        
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(ProgressView.self))
    }
    
    // MARK: - Error State Tests
    
    func testErrorAlert() throws {
        // Given
        viewModel.errorMessage = "Test error message"
        
        // When
        let view = try sut.inspect()
        
        // Then
        let alert = try view.alert()
        XCTAssertEqual(try alert.title().string(), "Error")
        XCTAssertEqual(try alert.message().string(), "Test error message")
    }
    
    // MARK: - Pull to Refresh Tests
    
    func testRefreshable() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        // Verify refreshable modifier is applied
        XCTAssertNotNil(view.modifier(RefreshableModifier.self))
    }
    
    // MARK: - Context Menu Tests
    
    func testArticleContextMenu() throws {
        // Given
        viewModel.articles = [
            Article(url: "https://test.com", title: "Test Article")
        ]
        
        // When
        let view = try sut.inspect()
        let articleRow = try view.find(ArticleRow.self)
        
        // Then
        let contextMenu = try articleRow.contextMenu()
        XCTAssertNoThrow(try contextMenu.find(button: "Mark as Read"))
        XCTAssertNoThrow(try contextMenu.find(button: "Add to Favorites"))
        XCTAssertNoThrow(try contextMenu.find(button: "Archive"))
        XCTAssertNoThrow(try contextMenu.find(button: "Share"))
        XCTAssertNoThrow(try contextMenu.find(button: "Delete"))
    }
    
    // MARK: - Navigation Tests
    
    func testNavigationToArticleDetail() throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        viewModel.articles = [article]
        
        // When
        let view = try sut.inspect()
        let navigationLink = try view.find(NavigationLink.self)
        
        // Then
        // Verify navigation destination
        let destination = try navigationLink.destination()
        XCTAssertNoThrow(try destination.find(ArticleDetailView.self))
    }
    
    // MARK: - Statistics Footer Tests
    
    func testStatisticsFooter() throws {
        // Given
        viewModel.statistics = ArticleStatistics(
            total: 100,
            unread: 25,
            todayRead: 5,
            weeklyReadTime: 120
        )
        
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(text: "100 articles"))
        XCTAssertNoThrow(try view.find(text: "25 unread"))
        XCTAssertNoThrow(try view.find(text: "5 read today"))
    }
}

// MARK: - ArticleRow Tests

class ArticleRowTests: XCTestCase {
    
    func testSwipeActions() throws {
        // Given
        let article = Article(url: "https://test.com", title: "Test Article")
        let row = ArticleRow(article: article)
        
        // When
        let view = try row.inspect()
        
        // Then
        let swipeActions = try view.swipeActions(edge: .trailing)
        XCTAssertGreaterThan(try swipeActions.count, 0)
    }
    
    func testProgressIndicator() throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.readProgress = 0.5
        
        let row = ArticleRow(article: article)
        
        // When
        let view = try row.inspect()
        
        // Then
        let progressView = try view.find(ProgressView.self)
        XCTAssertNotNil(progressView)
    }
    
    func testThumbnailImage() throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.imageURL = "https://test.com/image.jpg"
        
        let row = ArticleRow(article: article, showThumbnail: true)
        
        // When
        let view = try row.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(AsyncImage.self))
    }
}

// MARK: - Helper Extensions

extension ArticleListView: Inspectable {}
extension ArticleRow: Inspectable {}
extension ContentUnavailableView: Inspectable {}

// Mock modifier for testing
struct RefreshableModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
    }
}