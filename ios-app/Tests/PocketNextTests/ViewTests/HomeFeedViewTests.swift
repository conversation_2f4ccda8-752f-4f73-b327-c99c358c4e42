import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class HomeFeedViewTests: XCTestCase {
    var sut: HomeFeedView!
    var mockDatabaseManager: MockDatabaseManager!
    var appState: AppState!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create mock dependencies
        mockDatabaseManager = MockDatabaseManager()
        appState = AppState()
        
        // Create view with mock dependencies
        sut = HomeFeedView()
    }
    
    override func tearDown() async throws {
        mockDatabaseManager.reset()
        sut = nil
        mockDatabaseManager = nil
        appState = nil
        try await super.tearDown()
    }
    
    // MARK: - Database Not Initialized Tests
    
    func testLoadArticlesWhenDatabaseNotInitialized() async throws {
        // Given
        mockDatabaseManager.isInitialized = false
        let expectation = expectation(description: "Error shown")
        
        // Setup error handler
        appState.errorHandler = { message in
            XCTAssertEqual(message, "Database not ready")
            expectation.fulfill()
        }
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        // Trigger the task
        await view.loadArticles()
        
        // Then
        await fulfillment(of: [expectation], timeout: 1)
        XCTAssertEqual(mockDatabaseManager.fetchAllArticlesCallCount, 0)
    }
    
    func testRetryLogicWhenDatabaseBecomesInitialized() async throws {
        // Given
        mockDatabaseManager.isInitialized = false
        let initExpectation = expectation(description: "Database initialized")
        
        // Simulate database becoming initialized after delay
        Task {
            try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
            await MainActor.run {
                mockDatabaseManager.isInitialized = true
                initExpectation.fulfill()
            }
        }
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // Then
        await fulfillment(of: [initExpectation], timeout: 1)
        
        // Give time for retry
        try await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds
        
        // Should have attempted to fetch after initialization
        XCTAssertGreaterThan(mockDatabaseManager.fetchAllArticlesCallCount, 0)
    }
    
    // MARK: - Error Handling Tests
    
    func testLoadArticlesFailure() async throws {
        // Given
        mockDatabaseManager.shouldFailOperations = true
        let expectation = expectation(description: "Error shown")
        
        appState.errorHandler = { message in
            XCTAssertEqual(message, "Failed to load articles")
            expectation.fulfill()
        }
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // Then
        await fulfillment(of: [expectation], timeout: 1)
        XCTAssertEqual(mockDatabaseManager.fetchAllArticlesCallCount, 1)
    }
    
    func testDeleteArticleFailure() async throws {
        // Given
        let article = TestHelpers.createTestArticle()
        mockDatabaseManager.addMockArticles([article])
        mockDatabaseManager.shouldFailOperations = true
        
        let expectation = expectation(description: "Error shown")
        appState.errorHandler = { message in
            XCTAssertEqual(message, "Failed to delete article")
            expectation.fulfill()
        }
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.deleteArticle(article)
        
        // Then
        await fulfillment(of: [expectation], timeout: 1)
        XCTAssertEqual(mockDatabaseManager.deleteArticleCallCount, 1)
        XCTAssertEqual(mockDatabaseManager.lastDeletedArticleId, article.id)
    }
    
    func testToggleFavoriteFailure() async throws {
        // Given
        let article = TestHelpers.createTestArticle()
        mockDatabaseManager.addMockArticles([article])
        mockDatabaseManager.shouldFailOperations = true
        
        let expectation = expectation(description: "Error shown")
        appState.errorHandler = { message in
            XCTAssertEqual(message, "Failed to update favorite status")
            expectation.fulfill()
        }
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.toggleFavorite(article)
        
        // Then
        await fulfillment(of: [expectation], timeout: 1)
        XCTAssertEqual(mockDatabaseManager.updateArticleCallCount, 1)
        XCTAssertEqual(mockDatabaseManager.lastUpdatedArticle?.id, article.id)
        XCTAssertTrue(mockDatabaseManager.lastUpdatedArticle?.isFavorite ?? false)
    }
    
    func testToggleArchiveFailure() async throws {
        // Given
        let article = TestHelpers.createTestArticle()
        mockDatabaseManager.addMockArticles([article])
        mockDatabaseManager.shouldFailOperations = true
        
        let expectation = expectation(description: "Error shown")
        appState.errorHandler = { message in
            XCTAssertEqual(message, "Failed to update archive status")
            expectation.fulfill()
        }
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.toggleArchive(article)
        
        // Then
        await fulfillment(of: [expectation], timeout: 1)
        XCTAssertEqual(mockDatabaseManager.updateArticleCallCount, 1)
        XCTAssertEqual(mockDatabaseManager.lastUpdatedArticle?.id, article.id)
        XCTAssertTrue(mockDatabaseManager.lastUpdatedArticle?.isArchived ?? false)
    }
    
    // MARK: - UI State Tests
    
    func testLoadingStateDisplay() async throws {
        // Given
        mockDatabaseManager.operationDelay = 0.5 // 500ms delay
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        // Start loading
        let loadTask = Task {
            await view.loadArticles()
        }
        
        // Check loading state immediately
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        XCTAssertTrue(view.isLoading)
        
        // Wait for completion
        await loadTask.value
        
        // Then
        XCTAssertFalse(view.isLoading)
    }
    
    func testEmptyStateDisplay() async throws {
        // Given - no articles
        mockDatabaseManager.articles = []
        
        // When
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // Then
        XCTAssertTrue(view.articles.isEmpty)
        XCTAssertFalse(view.isLoading)
    }
    
    // MARK: - Filtering Tests
    
    func testArticleFiltering() async throws {
        // Given
        let articles = [
            TestHelpers.createTestArticle(title: "Unread Article", isRead: false, isArchived: false),
            TestHelpers.createTestArticle(title: "Read Article", isRead: true, isArchived: false),
            TestHelpers.createTestArticle(title: "Archived Article", isRead: false, isArchived: true),
            TestHelpers.createTestArticle(title: "Favorite Article", isRead: false, isFavorite: true)
        ]
        mockDatabaseManager.addMockArticles(articles)
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // When & Then - test different filters
        
        // All filter
        appState.feedFilter = .all
        let allArticles = view.filteredArticles
        XCTAssertEqual(allArticles.count, 3) // Excludes archived
        
        // Unread filter
        appState.feedFilter = .unread
        let unreadArticles = view.filteredArticles
        XCTAssertEqual(unreadArticles.count, 2) // Unread and Favorite
        
        // Archived filter
        appState.feedFilter = .archived
        let archivedArticles = view.filteredArticles
        XCTAssertEqual(archivedArticles.count, 1)
        
        // Favorites filter
        appState.feedFilter = .favorites
        let favoriteArticles = view.filteredArticles
        XCTAssertEqual(favoriteArticles.count, 1)
    }
    
    func testSearchFiltering() async throws {
        // Given
        let articles = [
            TestHelpers.createTestArticle(title: "SwiftUI Tutorial"),
            TestHelpers.createTestArticle(title: "UIKit Basics"),
            TestHelpers.createTestArticle(title: "Swift Concurrency")
        ]
        mockDatabaseManager.addMockArticles(articles)
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // When
        view.searchText = "Swift"
        
        // Then
        let filtered = view.filteredArticles
        XCTAssertEqual(filtered.count, 2)
        XCTAssertTrue(filtered.allSatisfy { $0.title.contains("Swift") })
    }
    
    // MARK: - Sorting Tests
    
    func testArticleSorting() async throws {
        // Given
        let now = Date()
        let articles = [
            TestHelpers.createTestArticle(
                title: "Article 1",
                savedAt: now.addingTimeInterval(-3600), // 1 hour ago
                readingTime: 10
            ),
            TestHelpers.createTestArticle(
                title: "Article 2",
                savedAt: now.addingTimeInterval(-1800), // 30 minutes ago
                readingTime: 5,
                isFavorite: true
            ),
            TestHelpers.createTestArticle(
                title: "Article 3",
                savedAt: now.addingTimeInterval(-7200), // 2 hours ago
                readingTime: 15
            )
        ]
        mockDatabaseManager.addMockArticles(articles)
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // Test newest sort
        appState.sortOrder = .newest
        let newestSorted = view.filteredArticles
        XCTAssertEqual(newestSorted[0].title, "Article 2")
        XCTAssertEqual(newestSorted[1].title, "Article 1")
        XCTAssertEqual(newestSorted[2].title, "Article 3")
        
        // Test oldest sort
        appState.sortOrder = .oldest
        let oldestSorted = view.filteredArticles
        XCTAssertEqual(oldestSorted[0].title, "Article 3")
        XCTAssertEqual(oldestSorted[1].title, "Article 1")
        XCTAssertEqual(oldestSorted[2].title, "Article 2")
        
        // Test reading time sort
        appState.sortOrder = .readingTime
        let timeSorted = view.filteredArticles
        XCTAssertEqual(timeSorted[0].title, "Article 2") // 5 min
        XCTAssertEqual(timeSorted[1].title, "Article 1") // 10 min
        XCTAssertEqual(timeSorted[2].title, "Article 3") // 15 min
        
        // Test relevance sort
        appState.sortOrder = .relevance
        let relevanceSorted = view.filteredArticles
        XCTAssertEqual(relevanceSorted[0].title, "Article 2") // Favorite
    }
    
    // MARK: - Refresh Tests
    
    func testRefreshArticles() async throws {
        // Given
        let initialArticles = [TestHelpers.createTestArticle(title: "Initial")]
        mockDatabaseManager.addMockArticles(initialArticles)
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // Add new article during refresh
        let newArticle = TestHelpers.createTestArticle(title: "New Article")
        mockDatabaseManager.addMockArticles([newArticle])
        
        // When
        await view.refreshArticles()
        
        // Then
        XCTAssertTrue(appState.isRefreshing == false)
        XCTAssertEqual(view.articles.count, 2)
        XCTAssertEqual(mockDatabaseManager.fetchAllArticlesCallCount, 2) // Initial + refresh
    }
    
    // MARK: - Context Menu Actions Tests
    
    func testToggleFavoriteSuccess() async throws {
        // Given
        let article = TestHelpers.createTestArticle(isFavorite: false)
        mockDatabaseManager.addMockArticles([article])
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // When
        await view.toggleFavorite(article)
        
        // Then
        XCTAssertEqual(mockDatabaseManager.updateArticleCallCount, 1)
        XCTAssertEqual(mockDatabaseManager.lastUpdatedArticle?.id, article.id)
        XCTAssertTrue(mockDatabaseManager.lastUpdatedArticle?.isFavorite ?? false)
    }
    
    func testToggleArchiveSuccess() async throws {
        // Given
        let article = TestHelpers.createTestArticle(isArchived: false)
        mockDatabaseManager.addMockArticles([article])
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // When
        await view.toggleArchive(article)
        
        // Then
        XCTAssertEqual(mockDatabaseManager.updateArticleCallCount, 1)
        XCTAssertEqual(mockDatabaseManager.lastUpdatedArticle?.id, article.id)
        XCTAssertTrue(mockDatabaseManager.lastUpdatedArticle?.isArchived ?? false)
    }
    
    func testDeleteArticleSuccess() async throws {
        // Given
        let article = TestHelpers.createTestArticle()
        mockDatabaseManager.addMockArticles([article])
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        XCTAssertEqual(view.articles.count, 1)
        
        // When
        await view.deleteArticle(article)
        
        // Then
        XCTAssertEqual(mockDatabaseManager.deleteArticleCallCount, 1)
        XCTAssertEqual(mockDatabaseManager.lastDeletedArticleId, article.id)
        XCTAssertEqual(view.articles.count, 0)
    }
    
    // MARK: - Performance Tests
    
    func testLargeDatasetPerformance() async throws {
        // Given - 1000 articles
        let articles = (0..<1000).map { i in
            TestHelpers.createTestArticle(
                title: "Article \(i)",
                savedAt: Date().addingTimeInterval(TimeInterval(-i * 60))
            )
        }
        mockDatabaseManager.addMockArticles(articles)
        
        // When
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let view = sut
            .environmentObject(appState)
            .environmentObject(mockDatabaseManager)
        
        await view.loadArticles()
        
        // Apply filtering
        view.searchText = "Article 5"
        let _ = view.filteredArticles
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime
        
        // Then
        XCTAssertLessThan(duration, 1.0, "Filtering should complete in less than 1 second")
    }
}

// MARK: - View Loading Extension

extension HomeFeedView {
    @MainActor
    func loadArticles() async {
        await loadArticles()
    }
    
    @MainActor
    func refreshArticles() async {
        await refreshArticles()
    }
    
    @MainActor
    func toggleFavorite(_ article: Article) async {
        await toggleFavorite(article)
    }
    
    @MainActor
    func toggleArchive(_ article: Article) async {
        await toggleArchive(article)
    }
    
    @MainActor
    func deleteArticle(_ article: Article) async {
        await deleteArticle(article)
    }
}

// MARK: - AppState Test Extension

extension AppState {
    var errorHandler: ((String) -> Void)?
    
    func showError(_ message: String) {
        errorHandler?(message)
        // Call the original implementation if needed
    }
}