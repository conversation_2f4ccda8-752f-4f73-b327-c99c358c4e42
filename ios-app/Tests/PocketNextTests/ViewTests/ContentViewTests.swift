import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class ContentViewTests: XCTestCase {
    
    var appState: AppState!
    
    override func setUp() async throws {
        try await super.setUp()
        appState = AppState()
    }
    
    override func tearDown() async throws {
        appState = nil
        try await super.tearDown()
    }
    
    // MARK: - Tab Bar Tests
    
    func testTabBarExists() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should have TabView
        let tabView = try view.find(ViewType.TabView.self)
        XCTAssertNotNil(tabView)
    }
    
    func testAllTabsPresent() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should have all 5 tabs
        let expectedTabs = ["Home", "Search", "Chat", "Digest", "Profile"]
        
        for tabTitle in expectedTabs {
            let tab = try? view.find(ViewType.TabItem.self, where: { item in
                (try? item.find(text: tabTitle)) != nil
            })
            XCTAssertNotNil(tab, "Tab '\(tabTitle)' not found")
        }
    }
    
    func testDefaultSelectedTab() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Default should be home tab
        XCTAssertEqual(appState.selectedTab, .home)
    }
    
    func testTabIcons() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Check each tab has correct icon
        let tabIcons = [
            ("house.fill", AppState.Tab.home),
            ("magnifyingglass", AppState.Tab.search),
            ("bubble.left.and.bubble.right.fill", AppState.Tab.chat),
            ("newspaper.fill", AppState.Tab.digest),
            ("person.circle.fill", AppState.Tab.profile)
        ]
        
        for (iconName, tab) in tabIcons {
            let icon = try? view.find(ViewType.Image.self, where: { image in
                try image.actualImage().name() == iconName
            })
            XCTAssertNotNil(icon, "Icon '\(iconName)' for tab '\(tab)' not found")
        }
    }
    
    // MARK: - View Content Tests
    
    func testHomeTabContent() throws {
        appState.selectedTab = .home
        
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should display HomeFeedView
        let homeFeedView = try? view.find(HomeFeedView.self)
        XCTAssertNotNil(homeFeedView)
    }
    
    func testSearchTabContent() throws {
        appState.selectedTab = .search
        
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should display SearchView
        let searchView = try? view.find(SearchView.self)
        XCTAssertNotNil(searchView)
    }
    
    func testChatTabContent() throws {
        appState.selectedTab = .chat
        
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should display ChatView
        let chatView = try? view.find(ChatView.self)
        XCTAssertNotNil(chatView)
    }
    
    func testDigestTabContent() throws {
        appState.selectedTab = .digest
        
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should display DigestView
        let digestView = try? view.find(DigestView.self)
        XCTAssertNotNil(digestView)
    }
    
    func testProfileTabContent() throws {
        appState.selectedTab = .profile
        
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Should display ProfileView
        let profileView = try? view.find(ProfileView.self)
        XCTAssertNotNil(profileView)
    }
    
    // MARK: - Navigation Tests
    
    func testArticleSelectionNavigation() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Select an article
        let testArticle = Article.mock(title: "Test Article")
        appState.selectedArticle = testArticle
        
        let view = try contentView.inspect()
        
        // Should show reading view as sheet or navigation
        let readingView = try? view.find(ReadingView.self)
        XCTAssertNotNil(readingView)
    }
    
    // MARK: - Save Success Animation Tests
    
    func testSaveSuccessAnimation() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Trigger save success
        appState.showingSaveSuccess = true
        
        let view = try contentView.inspect()
        
        // Should show success overlay
        let successOverlay = try? view.find(ViewType.ZStack.self, where: { stack in
            (try? stack.find(text: "Saved!")) != nil ||
            (try? stack.find(ViewType.Image.self, where: { image in
                try image.actualImage().name() == "checkmark.circle.fill"
            })) != nil
        })
        
        XCTAssertNotNil(successOverlay)
    }
    
    // MARK: - Error State Tests
    
    func testErrorBanner() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Set error message
        appState.errorMessage = "Failed to load articles"
        
        let view = try contentView.inspect()
        
        // Should show error banner
        let errorBanner = try? view.find(text: "Failed to load articles")
        XCTAssertNotNil(errorBanner)
        
        // Should have dismiss button
        let dismissButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "xmark") ?? false
        })
        XCTAssertNotNil(dismissButton)
    }
    
    // MARK: - Offline Mode Tests
    
    func testOfflineModeIndicator() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Enable offline mode
        appState.isOfflineMode = true
        
        let view = try contentView.inspect()
        
        // Should show offline indicator
        let offlineIndicator = try? view.find(text: "Offline Mode")
        XCTAssertNotNil(offlineIndicator)
    }
    
    // MARK: - Badge Tests
    
    func testUnreadCountBadge() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Add unread articles
        appState.unreadCount = 5
        
        let view = try contentView.inspect()
        
        // Home tab should show badge with count
        let badge = try? view.find(ViewType.Badge.self)
        XCTAssertNotNil(badge)
        
        // Badge should show count
        let badgeText = try? view.find(text: "5")
        XCTAssertNotNil(badgeText)
    }
    
    // MARK: - Appearance Tests
    
    func testDarkModeSupport() throws {
        let contentView = ContentView()
            .environmentObject(appState)
            .preferredColorScheme(.dark)
        
        let view = try contentView.inspect()
        
        // Should adapt to dark mode
        let tabView = try view.find(ViewType.TabView.self)
        XCTAssertNotNil(tabView)
    }
    
    func testLightModeSupport() throws {
        let contentView = ContentView()
            .environmentObject(appState)
            .preferredColorScheme(.light)
        
        let view = try contentView.inspect()
        
        // Should adapt to light mode
        let tabView = try view.find(ViewType.TabView.self)
        XCTAssertNotNil(tabView)
    }
    
    // MARK: - Accessibility Tests
    
    func testTabAccessibility() throws {
        let contentView = ContentView()
            .environmentObject(appState)
        
        let view = try contentView.inspect()
        
        // Each tab should have accessibility label
        let tabs = [
            (AppState.Tab.home, "Home tab"),
            (AppState.Tab.search, "Search tab"),
            (AppState.Tab.chat, "Chat tab"),
            (AppState.Tab.digest, "Digest tab"),
            (AppState.Tab.profile, "Profile tab")
        ]
        
        for (tab, label) in tabs {
            // Find tab item and verify accessibility
            let tabItem = try? view.find(ViewType.TabItem.self, where: { item in
                (try? item.find(text: tab.title)) != nil
            })
            
            if let tabItem = tabItem {
                let accessibilityLabel = try? tabItem.accessibilityLabel()
                XCTAssertEqual(accessibilityLabel, Text(label))
            }
        }
    }
    
    // MARK: - State Persistence Tests
    
    func testTabSelectionPersistence() throws {
        // Select different tab
        appState.selectedTab = .digest
        
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Tab should remain selected
        XCTAssertEqual(appState.selectedTab, .digest)
        
        // Create new view with same state
        let newContentView = ContentView()
            .environmentObject(appState)
        
        // Tab selection should persist
        XCTAssertEqual(appState.selectedTab, .digest)
    }
    
    // MARK: - Performance Tests
    
    func testInitializationPerformance() {
        measure {
            _ = ContentView()
                .environmentObject(appState)
        }
    }
    
    func testTabSwitchingPerformance() {
        let contentView = ContentView()
            .environmentObject(appState)
        
        measure {
            // Switch through all tabs
            for tab in [AppState.Tab.home, .search, .chat, .digest, .profile] {
                appState.selectedTab = tab
            }
        }
    }
}