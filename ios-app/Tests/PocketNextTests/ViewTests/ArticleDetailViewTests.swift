import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

class ArticleDetailViewTests: XCTestCase {
    var sut: ArticleDetailView!
    var viewModel: ArticleDetailViewModel!
    var testArticle: Article!
    
    override func setUp() {
        super.setUp()
        testArticle = Article(url: "https://test.com", title: "Test Article")
        testArticle.content = "<p>This is test content</p>"
        viewModel = ArticleDetailViewModel(article: testArticle)
        sut = ArticleDetailView(viewModel: viewModel)
    }
    
    override func tearDown() {
        sut = nil
        viewModel = nil
        testArticle = nil
        super.tearDown()
    }
    
    // MARK: - View Structure Tests
    
    func testViewHasScrollView() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.scrollView())
    }
    
    func testViewHasToolbar() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.toolbar())
    }
    
    // MARK: - Content Display Tests
    
    func testArticleTitle() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        let title = try view.find(text: "Test Article")
        XCTAssertNotNil(title)
        XCTAssertEqual(try title.attributes().font(), .largeTitle)
    }
    
    func testArticleMetadata() throws {
        // Given
        testArticle.author = "John Doe"
        testArticle.publishDate = Date()
        testArticle.readingTime = 5
        
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(text: "John Doe"))
        XCTAssertNoThrow(try view.find(text: "5 min read"))
    }
    
    func testArticleContent() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        // WebView should be present for article content
        XCTAssertNoThrow(try view.find(WebView.self))
    }
    
    // MARK: - Toolbar Actions Tests
    
    func testToolbarFavoriteButton() throws {
        // When
        let view = try sut.inspect()
        let toolbar = try view.toolbar()
        
        // Then
        let favoriteButton = try toolbar.find(button: "favorite")
        XCTAssertNotNil(favoriteButton)
        
        // Test icon changes based on state
        if viewModel.article.isFavorite {
            XCTAssertNoThrow(try favoriteButton.find(image: "star.fill"))
        } else {
            XCTAssertNoThrow(try favoriteButton.find(image: "star"))
        }
    }
    
    func testToolbarShareButton() throws {
        // When
        let view = try sut.inspect()
        let toolbar = try view.toolbar()
        
        // Then
        let shareButton = try toolbar.find(button: "share")
        XCTAssertNotNil(shareButton)
        XCTAssertNoThrow(try shareButton.find(image: "square.and.arrow.up"))
    }
    
    func testToolbarArchiveButton() throws {
        // When
        let view = try sut.inspect()
        let toolbar = try view.toolbar()
        
        // Then
        let archiveButton = try toolbar.find(button: "archive")
        XCTAssertNotNil(archiveButton)
        
        if viewModel.article.isArchived {
            XCTAssertNoThrow(try archiveButton.find(image: "archivebox.fill"))
        } else {
            XCTAssertNoThrow(try archiveButton.find(image: "archivebox"))
        }
    }
    
    func testToolbarSettingsButton() throws {
        // When
        let view = try sut.inspect()
        let toolbar = try view.toolbar()
        
        // Then
        let settingsButton = try toolbar.find(button: "settings")
        XCTAssertNotNil(settingsButton)
        XCTAssertNoThrow(try settingsButton.find(image: "textformat"))
    }
    
    // MARK: - Reading Progress Tests
    
    func testReadingProgressBar() throws {
        // Given
        viewModel.readingProgress = 0.5
        
        // When
        let view = try sut.inspect()
        
        // Then
        let progressView = try view.find(ProgressView.self)
        XCTAssertNotNil(progressView)
    }
    
    func testReadingProgressLabel() throws {
        // Given
        viewModel.readingProgress = 0.75
        
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(text: "75%"))
    }
    
    // MARK: - Settings Sheet Tests
    
    func testSettingsSheet() throws {
        // Given
        viewModel.showingSettings = true
        
        // When
        let view = try sut.inspect()
        
        // Then
        let sheet = try view.sheet()
        XCTAssertNoThrow(try sheet.find(ReadingSettingsView.self))
    }
    
    func testFontSizeControls() throws {
        // Given
        viewModel.showingSettings = true
        let settingsView = ReadingSettingsView(viewModel: viewModel)
        
        // When
        let view = try settingsView.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(stepper: "Font Size"))
        XCTAssertNoThrow(try view.find(text: "\(viewModel.fontSize) pt"))
    }
    
    func testFontFamilyPicker() throws {
        // Given
        viewModel.showingSettings = true
        let settingsView = ReadingSettingsView(viewModel: viewModel)
        
        // When
        let view = try settingsView.inspect()
        
        // Then
        let picker = try view.find(picker: "Font")
        XCTAssertNotNil(picker)
    }
    
    func testDarkModeToggle() throws {
        // Given
        viewModel.showingSettings = true
        let settingsView = ReadingSettingsView(viewModel: viewModel)
        
        // When
        let view = try settingsView.inspect()
        
        // Then
        let toggle = try view.find(toggle: "Dark Mode")
        XCTAssertNotNil(toggle)
    }
    
    // MARK: - Offline Content Tests
    
    func testOfflineIndicator() throws {
        // Given
        viewModel.article.isDownloadedForOffline = true
        
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(image: "arrow.down.circle.fill"))
    }
    
    func testDownloadButton() throws {
        // Given
        viewModel.article.isDownloadedForOffline = false
        
        // When
        let view = try sut.inspect()
        let toolbar = try view.toolbar()
        
        // Then
        let downloadButton = try toolbar.find(button: "download")
        XCTAssertNotNil(downloadButton)
        XCTAssertNoThrow(try downloadButton.find(image: "arrow.down.circle"))
    }
    
    // MARK: - Share Sheet Tests
    
    func testShareSheet() throws {
        // Given
        viewModel.showingShareSheet = true
        
        // When
        let view = try sut.inspect()
        
        // Then
        let sheet = try view.sheet()
        XCTAssertNoThrow(try sheet.find(ShareSheet.self))
    }
    
    // MARK: - Notes and Highlights Tests
    
    func testNotesButton() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        let notesButton = try view.find(button: "notes")
        XCTAssertNotNil(notesButton)
        
        // Show badge if notes exist
        if !viewModel.notes.isEmpty {
            XCTAssertNoThrow(try notesButton.find(Badge.self))
        }
    }
    
    func testHighlightButton() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        let highlightButton = try view.find(button: "highlight")
        XCTAssertNotNil(highlightButton)
        XCTAssertNoThrow(try highlightButton.find(image: "highlighter"))
    }
    
    func testNotesSheet() throws {
        // Given
        viewModel.showingNotes = true
        viewModel.notes = [
            ArticleNote(text: "Test note", createdAt: Date())
        ]
        
        // When
        let view = try sut.inspect()
        
        // Then
        let sheet = try view.sheet()
        let notesView = try sheet.find(NotesView.self)
        XCTAssertNoThrow(try notesView.find(text: "Test note"))
    }
    
    // MARK: - WebView Tests
    
    func testWebViewInteraction() throws {
        // When
        let webView = WebView(
            htmlContent: viewModel.processedContent,
            onProgressUpdate: { _ in },
            onLinkTap: { _ in }
        )
        
        // Then
        // Verify WebView is configured correctly
        XCTAssertNotNil(webView)
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorAlert() throws {
        // Given
        viewModel.errorMessage = "Failed to load article"
        
        // When
        let view = try sut.inspect()
        
        // Then
        let alert = try view.alert()
        XCTAssertEqual(try alert.title().string(), "Error")
        XCTAssertEqual(try alert.message().string(), "Failed to load article")
    }
    
    // MARK: - Gesture Tests
    
    func testSwipeGestures() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        // Verify swipe gestures are attached
        XCTAssertNoThrow(try view.gesture(DragGesture.self))
    }
}

// MARK: - ReadingSettingsView Tests

class ReadingSettingsViewTests: XCTestCase {
    var sut: ReadingSettingsView!
    var viewModel: ArticleDetailViewModel!
    
    override func setUp() {
        super.setUp()
        let article = Article(url: "https://test.com", title: "Test")
        viewModel = ArticleDetailViewModel(article: article)
        sut = ReadingSettingsView(viewModel: viewModel)
    }
    
    func testFontSizeRange() throws {
        // When
        let view = try sut.inspect()
        let stepper = try view.find(ViewType.Stepper.self)
        
        // Then
        // Verify font size constraints (10-30)
        XCTAssertEqual(viewModel.fontSize, 16) // Default
    }
    
    func testFontPreview() throws {
        // When
        let view = try sut.inspect()
        
        // Then
        XCTAssertNoThrow(try view.find(text: "The quick brown fox jumps over the lazy dog"))
    }
    
    func testResetButton() throws {
        // Given
        viewModel.fontSize = 20
        viewModel.fontFamily = "Georgia"
        viewModel.isDarkMode = true
        
        // When
        let view = try sut.inspect()
        let resetButton = try view.find(button: "Reset to Defaults")
        
        // Then
        XCTAssertNotNil(resetButton)
    }
}

// MARK: - Helper Types

extension ArticleDetailView: Inspectable {}
extension ReadingSettingsView: Inspectable {}
extension NotesView: Inspectable {}
extension WebView: Inspectable {}
extension ShareSheet: Inspectable {}

struct Badge: View, Inspectable {
    var body: some View {
        EmptyView()
    }
}