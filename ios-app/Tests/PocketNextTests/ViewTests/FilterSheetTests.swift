import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class FilterSheetTests: XCTestCase {
    
    var appState: AppState!
    var isPresented: Binding<Bool>!
    var presentedValue: Bool = false
    
    override func setUp() async throws {
        try await super.setUp()
        
        appState = AppState()
        isPresented = Binding(
            get: { self.presentedValue },
            set: { self.presentedValue = $0 }
        )
        presentedValue = true
    }
    
    override func tearDown() async throws {
        appState = nil
        isPresented = nil
        try await super.tearDown()
    }
    
    // MARK: - Sheet Presentation Tests
    
    func testFilterSheetPresentation() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have navigation view/stack
        let navigationView = try? view.find(ViewType.NavigationView.self)
        XCTAssertNotNil(navigationView)
        
        // Should have title
        let title = try? view.find(text: "Filters")
        XCTAssertNotNil(title)
        
        // Should have Done button
        let doneButton = try? view.find(button: "Done")
        XCTAssertNotNil(doneButton)
    }
    
    func testDismissAction() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Find and tap Done button
        let doneButton = try view.find(button: "Done")
        try doneButton.tap()
        
        // Should dismiss sheet
        XCTAssertFalse(presentedValue)
    }
    
    // MARK: - Feed Filter Tests
    
    func testFeedFilterSection() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Feed Filter section
        let sectionHeader = try? view.find(text: "Feed Filter")
        XCTAssertNotNil(sectionHeader)
        
        // Should have filter options
        let filterOptions = [
            AppState.FeedFilter.all.rawValue,
            AppState.FeedFilter.unread.rawValue,
            AppState.FeedFilter.archived.rawValue,
            AppState.FeedFilter.favorites.rawValue
        ]
        
        for option in filterOptions {
            let filterOption = try? view.find(text: option)
            XCTAssertNotNil(filterOption, "Filter option '\(option)' not found")
        }
    }
    
    func testFeedFilterSelection() throws {
        // Set initial filter
        appState.feedFilter = .all
        
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Find and tap unread filter
        if let unreadButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string() == "Unread") ?? false
        }) {
            try unreadButton.tap()
            
            // Should update feed filter
            XCTAssertEqual(appState.feedFilter, .unread)
        }
    }
    
    // MARK: - Sort Order Tests
    
    func testSortOrderSection() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Sort Order section
        let sectionHeader = try? view.find(text: "Sort Order")
        XCTAssertNotNil(sectionHeader)
        
        // Should have sort options
        let sortOptions = [
            AppState.SortOrder.newest.rawValue,
            AppState.SortOrder.oldest.rawValue,
            AppState.SortOrder.readingTime.rawValue,
            AppState.SortOrder.relevance.rawValue
        ]
        
        for option in sortOptions {
            let sortOption = try? view.find(text: option)
            XCTAssertNotNil(sortOption, "Sort option '\(option)' not found")
        }
    }
    
    func testSortOrderIcons() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Check for sort order icons
        let expectedIcons = [
            "arrow.down",
            "arrow.up",
            "clock",
            "star"
        ]
        
        for icon in expectedIcons {
            let iconImage = try? view.find(ViewType.Image.self, where: { image in
                try image.actualImage().name() == icon
            })
            XCTAssertNotNil(iconImage, "Icon '\(icon)' not found")
        }
    }
    
    // MARK: - Content Type Filter Tests
    
    func testContentTypeFilters() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Content Type section
        let sectionHeader = try? view.find(text: "Content Type")
        XCTAssertNotNil(sectionHeader)
        
        // Should have toggles for each content type
        let contentTypes = ["Articles", "Twitter", "YouTube", "PDF", "GitHub"]
        
        for contentType in contentTypes {
            let toggle = try? view.find(ViewType.Toggle.self, where: { toggle in
                (try? toggle.labelView().text().string() == contentType) ?? false
            })
            XCTAssertNotNil(toggle, "Content type toggle '\(contentType)' not found")
        }
    }
    
    func testContentTypeToggle() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Find and toggle Articles filter
        if let articlesToggle = try? view.find(ViewType.Toggle.self, where: { toggle in
            (try? toggle.labelView().text().string() == "Articles") ?? false
        }) {
            // Check initial state
            let isOn = try articlesToggle.isOn()
            
            // Toggle it
            try articlesToggle.tap()
            
            // State should change
            // Note: Actual state verification depends on implementation
        }
    }
    
    // MARK: - Date Range Tests
    
    func testDateRangeFilter() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Date Range section
        let sectionHeader = try? view.find(text: "Date Range")
        XCTAssertNotNil(sectionHeader)
        
        // Should have date range options
        let dateRanges = ["Today", "This Week", "This Month", "All Time"]
        
        for range in dateRanges {
            let rangeOption = try? view.find(text: range)
            XCTAssertNotNil(rangeOption, "Date range '\(range)' not found")
        }
    }
    
    func testCustomDateRange() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have custom date range option
        let customRangeButton = try? view.find(button: "Custom Range")
        XCTAssertNotNil(customRangeButton)
        
        // Should have date pickers when custom is selected
        // Note: Testing date picker interaction requires specific implementation
    }
    
    // MARK: - Tag Filter Tests
    
    func testTagFilters() throws {
        // Add some tags to app state
        appState.allTags = ["swift", "ios", "swiftui", "combine", "coredata"]
        
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Tags section
        let sectionHeader = try? view.find(text: "Tags")
        XCTAssertNotNil(sectionHeader)
        
        // Should display available tags
        for tag in appState.allTags {
            let tagChip = try? view.find(text: tag)
            XCTAssertNotNil(tagChip, "Tag '\(tag)' not found")
        }
    }
    
    func testTagSelection() throws {
        appState.allTags = ["swift", "ios"]
        appState.selectedTags = []
        
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Find and tap a tag
        if let swiftTag = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string() == "swift") ?? false
        }) {
            try swiftTag.tap()
            
            // Should add to selected tags
            XCTAssertTrue(appState.selectedTags.contains("swift"))
        }
    }
    
    // MARK: - Reset Filters Tests
    
    func testResetButton() throws {
        // Set some filters
        appState.feedFilter = .unread
        appState.sortOrder = .oldest
        appState.selectedTags = ["swift", "ios"]
        
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Reset button
        let resetButton = try? view.find(button: "Reset Filters")
        XCTAssertNotNil(resetButton)
        
        // Tap reset
        if let button = resetButton {
            try button.tap()
            
            // Should reset all filters
            XCTAssertEqual(appState.feedFilter, .all)
            XCTAssertEqual(appState.sortOrder, .newest)
            XCTAssertTrue(appState.selectedTags.isEmpty)
        }
    }
    
    // MARK: - Apply Filters Tests
    
    func testApplyButton() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should have Apply button
        let applyButton = try? view.find(button: "Apply Filters")
        XCTAssertNotNil(applyButton)
        
        // Tap apply
        if let button = applyButton {
            try button.tap()
            
            // Should dismiss sheet
            XCTAssertFalse(presentedValue)
        }
    }
    
    // MARK: - Filter Count Tests
    
    func testActiveFilterCount() throws {
        // Set multiple filters
        appState.feedFilter = .unread
        appState.selectedTags = ["swift", "ios"]
        
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Should show active filter count
        let filterCount = try? view.find(text: "3 active filters")
        XCTAssertNotNil(filterCount)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibility() throws {
        let filterSheet = FilterSheet(isPresented: isPresented)
            .environmentObject(appState)
        
        let view = try filterSheet.inspect()
        
        // Section headers should have header trait
        if let feedFilterHeader = try? view.find(text: "Feed Filter") {
            let traits = try feedFilterHeader.accessibilityAddTraits()
            XCTAssertTrue(traits.contains(.isHeader))
        }
        
        // Buttons should have appropriate labels
        if let doneButton = try? view.find(button: "Done") {
            let label = try doneButton.accessibilityLabel()
            XCTAssertEqual(label, Text("Close filters"))
        }
    }
    
    // MARK: - Performance Tests
    
    func testRenderingPerformance() {
        // Add many tags
        appState.allTags = (1...100).map { "tag\($0)" }
        
        measure {
            _ = FilterSheet(isPresented: isPresented)
                .environmentObject(appState)
        }
    }
}

// MARK: - AppState Extensions for Testing

extension AppState {
    var allTags: [String] {
        get { [] } // Simplified for testing
        set { }
    }
    
    var selectedTags: Set<String> {
        get { [] }
        set { }
    }
}