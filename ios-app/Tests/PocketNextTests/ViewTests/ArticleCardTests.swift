import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

class ArticleCardTests: XCTestCase {
    
    var sampleArticle: Article!
    
    override func setUp() {
        super.setUp()
        sampleArticle = Article(
            url: "https://test.com/article",
            title: "Test Article Title",
            content: "Test content",
            summary: "This is a test article summary that should be displayed on the card",
            author: "Test Author",
            publishDate: Date(),
            readingTime: 5,
            imageURL: "https://test.com/image.jpg",
            tags: ["swift", "testing"],
            isRead: false,
            isFavorite: false,
            readProgress: 0.3
        )
    }
    
    override func tearDown() {
        sampleArticle = nil
        super.tearDown()
    }
    
    // MARK: - Component Tests
    
    func testArticleCardDisplaysTitle() throws {
        let card = ArticleCard(article: sampleArticle)
        
        let title = try card.inspect().find(text: "Test Article Title")
        XCTAssertNotNil(title)
    }
    
    func testArticleCardDisplaysSummary() throws {
        let card = ArticleCard(article: sampleArticle)
        
        let summary = try card.inspect().find(text: sampleArticle.summary!)
        XCTAssertNotNil(summary)
    }
    
    func testArticleCardDisplaysMetadata() throws {
        let card = ArticleCard(article: sampleArticle)
        
        // Should show author
        let author = try card.inspect().find(text: "Test Author")
        XCTAssertNotNil(author)
        
        // Should show reading time
        let readingTime = try card.inspect().find(text: "5 min")
        XCTAssertNotNil(readingTime)
    }
    
    func testArticleCardDisplaysTags() throws {
        let card = ArticleCard(article: sampleArticle)
        
        // Should display tags
        let swiftTag = try card.inspect().find(text: "swift")
        XCTAssertNotNil(swiftTag)
        
        let testingTag = try card.inspect().find(text: "testing")
        XCTAssertNotNil(testingTag)
    }
    
    // MARK: - State Display Tests
    
    func testReadArticleIndicator() throws {
        var readArticle = sampleArticle!
        readArticle.isRead = true
        
        let card = ArticleCard(article: readArticle)
        
        // Should show read indicator
        let readIndicator = try? card.inspect().find(ViewType.Image.self, where: { image in
            try image.actualImage().name() == "checkmark.circle.fill"
        })
        
        XCTAssertNotNil(readIndicator)
    }
    
    func testFavoriteArticleIndicator() throws {
        var favoriteArticle = sampleArticle!
        favoriteArticle.isFavorite = true
        
        let card = ArticleCard(article: favoriteArticle)
        
        // Should show favorite indicator
        let favoriteIndicator = try? card.inspect().find(ViewType.Image.self, where: { image in
            try image.actualImage().name() == "star.fill"
        })
        
        XCTAssertNotNil(favoriteIndicator)
    }
    
    func testReadingProgressIndicator() throws {
        let card = ArticleCard(article: sampleArticle)
        
        // Article has 30% progress, should show progress bar
        let progressView = try? card.inspect().find(ViewType.ProgressView.self)
        XCTAssertNotNil(progressView)
    }
    
    func testArchivedArticleAppearance() throws {
        var archivedArticle = sampleArticle!
        archivedArticle.isArchived = true
        
        let card = ArticleCard(article: archivedArticle)
        
        // Should have different opacity or styling
        let view = try card.inspect().view(ArticleCard.self)
        XCTAssertNotNil(view)
    }
    
    // MARK: - Image Tests
    
    func testArticleWithImage() throws {
        let card = ArticleCard(article: sampleArticle)
        
        // Should display image when imageURL exists
        let asyncImage = try? card.inspect().find(ViewType.AsyncImage.self)
        XCTAssertNotNil(asyncImage)
    }
    
    func testArticleWithoutImage() throws {
        var noImageArticle = sampleArticle!
        noImageArticle.imageURL = nil
        
        let card = ArticleCard(article: noImageArticle)
        
        // Should display placeholder or no image
        let asyncImage = try? card.inspect().find(ViewType.AsyncImage.self)
        
        // Verify placeholder or alternative layout
        if asyncImage == nil {
            // No image view should be present
            XCTAssertTrue(true)
        }
    }
    
    // MARK: - Content Type Tests
    
    func testDifferentContentTypes() throws {
        let contentTypes: [Article.ContentType] = [.article, .twitter, .youtube, .pdf]
        
        for contentType in contentTypes {
            var article = sampleArticle!
            article.contentType = contentType
            
            let card = ArticleCard(article: article)
            
            // Should display content type icon
            let icon = try? card.inspect().find(ViewType.Image.self, where: { image in
                let imageName = try image.actualImage().name()
                return imageName == contentType.icon
            })
            
            XCTAssertNotNil(icon, "Icon not found for content type: \(contentType)")
        }
    }
    
    // MARK: - Tap Action Tests
    
    func testCardTapAction() throws {
        var tapped = false
        let card = ArticleCard(article: sampleArticle) {
            tapped = true
        }
        
        // Find the tap gesture
        let tapGesture = try card.inspect().find(ViewType.TapGesture.self)
        try tapGesture.callOnTapGesture()
        
        XCTAssertTrue(tapped)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityLabel() throws {
        let card = ArticleCard(article: sampleArticle)
        
        let view = try card.inspect().view(ArticleCard.self)
        let accessibilityLabel = try view.accessibilityLabel().string()
        
        // Should contain article title and metadata
        XCTAssertTrue(accessibilityLabel.contains("Test Article Title"))
    }
    
    func testAccessibilityHint() throws {
        let card = ArticleCard(article: sampleArticle)
        
        let view = try card.inspect().view(ArticleCard.self)
        let hint = try view.accessibilityHint()
        
        XCTAssertEqual(hint, Text("Double tap to read article"))
    }
    
    // MARK: - Layout Tests
    
    func testCompactLayout() throws {
        let card = ArticleCard(article: sampleArticle, style: .compact)
        
        // Verify compact layout is used
        let view = try card.inspect().view(ArticleCard.self)
        XCTAssertNotNil(view)
    }
    
    func testExpandedLayout() throws {
        let card = ArticleCard(article: sampleArticle, style: .expanded)
        
        // Verify expanded layout shows more content
        let view = try card.inspect().view(ArticleCard.self)
        XCTAssertNotNil(view)
        
        // Expanded should show full summary
        let summary = try card.inspect().find(text: sampleArticle.summary!)
        XCTAssertNotNil(summary)
    }
    
    // MARK: - Edge Cases
    
    func testLongTitle() throws {
        var longTitleArticle = sampleArticle!
        longTitleArticle.title = String(repeating: "Very Long Title ", count: 20)
        
        let card = ArticleCard(article: longTitleArticle)
        
        // Should truncate or wrap appropriately
        let title = try card.inspect().find(ViewType.Text.self, where: { text in
            try text.string().contains("Very Long Title")
        })
        
        XCTAssertNotNil(title)
    }
    
    func testMissingMetadata() throws {
        var minimalArticle = sampleArticle!
        minimalArticle.author = nil
        minimalArticle.publishDate = nil
        minimalArticle.summary = nil
        minimalArticle.tags = []
        
        let card = ArticleCard(article: minimalArticle)
        
        // Should still render without crashing
        let view = try card.inspect().view(ArticleCard.self)
        XCTAssertNotNil(view)
        
        // Should show title at minimum
        let title = try card.inspect().find(text: minimalArticle.title)
        XCTAssertNotNil(title)
    }
    
    // MARK: - Performance Tests
    
    func testRenderingPerformance() throws {
        measure {
            _ = ArticleCard(article: sampleArticle)
                .frame(width: 350, height: 200)
        }
    }
}

// MARK: - ArticleCard Style Tests

extension ArticleCardTests {
    
    enum CardStyle {
        case compact
        case expanded
        case grid
        case list
    }
}

// MARK: - Mock Article Extension

extension Article {
    static func mock(
        title: String = "Mock Article",
        isRead: Bool = false,
        isFavorite: Bool = false,
        readProgress: Double = 0
    ) -> Article {
        Article(
            url: "https://mock.com/\(UUID().uuidString)",
            title: title,
            content: "Mock content",
            summary: "Mock summary",
            readingTime: 5,
            isRead: isRead,
            isFavorite: isFavorite,
            readProgress: readProgress
        )
    }
}