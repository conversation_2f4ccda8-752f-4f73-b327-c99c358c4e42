import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class ChatViewTests: XCTestCase {
    
    var appState: AppState!
    var mockDatabaseManager: MockDatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        mockDatabaseManager = MockDatabaseManager()
        appState = AppState()
        
        // Initialize with mock data
        appState.articles = [
            Article.mock(title: "SwiftUI Tutorial"),
            Article.mock(title: "iOS Development Guide"),
            Article.mock(title: "Swift Concurrency")
        ]
    }
    
    override func tearDown() async throws {
        appState = nil
        mockDatabaseManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testChatViewInitialState() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Should show empty chat state
        let emptyState = try? view.find(text: "Start a conversation")
        XCTAssertNotNil(emptyState)
        
        // Should have message input field
        let messageInput = try? view.find(ViewType.TextEditor.self)
        XCTAssertNotNil(messageInput)
        
        // Should have send button
        let sendButton = try? view.find(button: "Send")
        XCTAssertNotNil(sendButton)
    }
    
    // MARK: - Message Display Tests
    
    func testDisplayUserMessage() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Add a user message
        let userMessage = ChatMessage(content: "What articles do I have?", isUser: true)
        appState.chatMessages.append(userMessage)
        
        let view = try chatView.inspect()
        
        // Should display user message
        let messageText = try view.find(text: "What articles do I have?")
        XCTAssertNotNil(messageText)
        
        // User message should have specific styling
        let messageBubble = try view.find(ViewType.RoundedRectangle.self)
        XCTAssertNotNil(messageBubble)
    }
    
    func testDisplayAssistantMessage() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Add assistant message
        let assistantMessage = ChatMessage(
            content: "You have 3 articles in your library.",
            isUser: false,
            articleReferences: appState.articles
        )
        appState.chatMessages.append(assistantMessage)
        
        let view = try chatView.inspect()
        
        // Should display assistant message
        let messageText = try view.find(text: "You have 3 articles in your library.")
        XCTAssertNotNil(messageText)
    }
    
    func testMessageWithArticleReferences() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Add message with article references
        let message = ChatMessage(
            content: "Here are your SwiftUI articles:",
            isUser: false,
            articleReferences: [appState.articles[0]]
        )
        appState.chatMessages.append(message)
        
        let view = try chatView.inspect()
        
        // Should show article reference cards
        let articleCard = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string().contains("SwiftUI Tutorial")) ?? false
        })
        
        XCTAssertNotNil(articleCard)
    }
    
    // MARK: - Input Field Tests
    
    func testMessageInputField() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Find text editor
        let textEditor = try view.find(ViewType.TextEditor.self)
        
        // Should have placeholder
        let placeholder = try? view.find(text: "Ask about your articles...")
        XCTAssertNotNil(placeholder)
        
        // Should be enabled
        XCTAssertTrue(try textEditor.isDisabled() == false)
    }
    
    func testSendButtonState() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Send button should be disabled when message is empty
        let sendButton = try view.find(button: "Send")
        XCTAssertTrue(try sendButton.isDisabled())
        
        // TODO: Test enabled state when message has content
        // This requires ViewInspector updates for binding state
    }
    
    // MARK: - Loading State Tests
    
    func testLoadingIndicator() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Set loading state
        appState.isChatLoading = true
        
        let view = try chatView.inspect()
        
        // Should show loading indicator
        let progressView = try? view.find(ViewType.ProgressView.self)
        XCTAssertNotNil(progressView)
        
        // Input should be disabled during loading
        let textEditor = try view.find(ViewType.TextEditor.self)
        XCTAssertTrue(try textEditor.isDisabled())
    }
    
    // MARK: - Scroll Behavior Tests
    
    func testScrollToBottom() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Add multiple messages
        for i in 1...10 {
            appState.chatMessages.append(
                ChatMessage(content: "Message \(i)", isUser: i % 2 == 0)
            )
        }
        
        let view = try chatView.inspect()
        
        // Should have ScrollView
        let scrollView = try view.find(ViewType.ScrollView.self)
        XCTAssertNotNil(scrollView)
        
        // Should have ScrollViewReader for programmatic scrolling
        let scrollViewReader = try? view.find(ViewType.ScrollViewReader.self)
        XCTAssertNotNil(scrollViewReader)
    }
    
    // MARK: - Error State Tests
    
    func testErrorDisplay() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Set error state
        appState.chatError = "Failed to send message"
        
        let view = try chatView.inspect()
        
        // Should show error message
        let errorText = try? view.find(text: "Failed to send message")
        XCTAssertNotNil(errorText)
        
        // Should have retry button
        let retryButton = try? view.find(button: "Retry")
        XCTAssertNotNil(retryButton)
    }
    
    // MARK: - Suggestion Tests
    
    func testSuggestedQuestions() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Should show suggestions when chat is empty
        let suggestions = [
            "What articles have I saved recently?",
            "Summarize my SwiftUI articles",
            "Find articles about iOS development"
        ]
        
        for suggestion in suggestions {
            let suggestionButton = try? view.find(button: suggestion)
            XCTAssertNotNil(suggestionButton)
        }
    }
    
    func testSuggestionTap() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Tap a suggestion
        let suggestionButton = try view.find(button: "What articles have I saved recently?")
        try suggestionButton.tap()
        
        // Should populate message field
        // Note: This requires testing the binding update
    }
    
    // MARK: - Keyboard Tests
    
    func testKeyboardToolbar() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Should have keyboard toolbar
        let toolbar = try? view.find(ViewType.ToolbarItem.self)
        XCTAssertNotNil(toolbar)
        
        // Should have dismiss button
        let dismissButton = try? view.find(button: "Done")
        XCTAssertNotNil(dismissButton)
    }
    
    // MARK: - Chat Context Tests
    
    func testChatContext() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        // Set selected article context
        appState.selectedArticle = appState.articles[0]
        
        let view = try chatView.inspect()
        
        // Should show context indicator
        let contextBanner = try? view.find(text: "Discussing: SwiftUI Tutorial")
        XCTAssertNotNil(contextBanner)
        
        // Should have clear context button
        let clearButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "xmark.circle.fill") ?? false
        })
        XCTAssertNotNil(clearButton)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibility() throws {
        let chatView = ChatView()
            .environmentObject(appState)
        
        let view = try chatView.inspect()
        
        // Message input should have accessibility label
        let textEditor = try view.find(ViewType.TextEditor.self)
        let label = try textEditor.accessibilityLabel()
        XCTAssertEqual(label, Text("Message input"))
        
        // Send button should have hint
        let sendButton = try view.find(button: "Send")
        let hint = try sendButton.accessibilityHint()
        XCTAssertEqual(hint, Text("Send your message"))
    }
    
    // MARK: - Performance Tests
    
    func testRenderingPerformance() {
        // Add many messages
        for i in 1...100 {
            appState.chatMessages.append(
                ChatMessage(content: "Message \(i)", isUser: i % 2 == 0)
            )
        }
        
        measure {
            _ = ChatView()
                .environmentObject(appState)
        }
    }
}

// MARK: - ChatMessage Extensions for Testing

extension ChatMessage {
    static func mock(content: String, isUser: Bool) -> ChatMessage {
        ChatMessage(content: content, isUser: isUser)
    }
}

// MARK: - AppState Extensions for Testing

extension AppState {
    var chatMessages: [ChatMessage] {
        get { [] } // Simplified for testing
        set { } // Simplified for testing
    }
    
    var isChatLoading: Bool {
        get { false }
        set { }
    }
    
    var chatError: String? {
        get { nil }
        set { }
    }
}