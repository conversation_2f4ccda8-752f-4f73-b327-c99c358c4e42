import Foundation

/**
 View Testing Configuration
 
 The view tests in this directory use ViewInspector for SwiftUI view testing.
 To use these tests, add ViewInspector to your test dependencies:
 
 In Package.swift:
 ```swift
 dependencies: [
     .package(url: "https://github.com/nalexn/ViewInspector", from: "0.9.0")
 ]
 
 targets: [
     .testTarget(
         name: "PocketNextTests",
         dependencies: ["ViewInspector"]
     )
 ]
 ```
 
 Or in Xcode:
 1. Select the project in the navigator
 2. Select the test target
 3. Go to "Frameworks and Libraries"
 4. Add ViewInspector package
 
 If you don't want to use ViewInspector, you can:
 1. Comment out the view tests
 2. Use XCUITest for UI testing instead
 3. Mock the ViewInspector imports
 
 Note: ViewInspector allows unit testing of SwiftUI views without
 running them in a simulator, making tests faster and more reliable.
 */

// MARK: - Mock ViewInspector (if not using the real library)

#if !canImport(ViewInspector)

// Basic mock to allow compilation without ViewInspector
enum ViewType {
    enum Button {}
    enum Text {}
    enum Image {}
    enum List {}
    enum VStack {}
    enum HStack {}
    enum ZStack {}
    enum ScrollView {}
    enum TextField {}
    enum TextEditor {}
    enum Toggle {}
    enum Picker {}
    enum ProgressView {}
    enum AsyncImage {}
    enum NavigationView {}
    enum TabView {}
    enum TabItem {}
    enum TapGesture {}
    enum RoundedRectangle {}
    enum Chart {}
    enum GeometryReader {}
    enum ScrollViewReader {}
    enum ToolbarItem {}
    enum Badge {}
}

protocol InspectableView {
    func inspect() throws -> Self
    func find<T>(_ type: T.Type) throws -> T
    func find(text: String) throws -> Self
    func find(button: String) throws -> Self
    func findAll<T>(_ type: T.Type) throws -> [T]
}

extension View {
    func inspect() throws -> Self { self }
}

#endif