import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class DigestViewTests: XCTestCase {
    
    var appState: AppState!
    var mockArticles: [Article]!
    
    override func setUp() async throws {
        try await super.setUp()
        
        appState = AppState()
        
        // Create mock articles for digest
        mockArticles = [
            Article.mock(title: "SwiftUI Best Practices", readingTime: 10),
            Article.mock(title: "iOS 17 Features", readingTime: 8),
            Article.mock(title: "Swift Concurrency Guide", readingTime: 15),
            Article.mock(title: "Core Data Tutorial", readingTime: 12),
            Article.mock(title: "UIKit to SwiftUI Migration", readingTime: 20)
        ]
        
        appState.articles = mockArticles
    }
    
    override func tearDown() async throws {
        appState = nil
        mockArticles = nil
        try await super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testDigestViewInitialState() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should show digest header
        let header = try? view.find(text: "Your Daily Digest")
        XCTAssertNotNil(header)
        
        // Should show current date
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        let todayString = dateFormatter.string(from: Date())
        
        let dateText = try? view.find(text: todayString)
        XCTAssertNotNil(dateText)
    }
    
    // MARK: - Content Display Tests
    
    func testDigestSummary() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should show article count
        let articleCount = try? view.find(text: "5 articles")
        XCTAssertNotNil(articleCount)
        
        // Should show total reading time
        let totalTime = mockArticles.reduce(0) { $0 + $1.readingTime }
        let readingTime = try? view.find(text: "\(totalTime) min")
        XCTAssertNotNil(readingTime)
    }
    
    func testTopArticlesSection() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have "Top Articles" section
        let sectionHeader = try? view.find(text: "Top Articles")
        XCTAssertNotNil(sectionHeader)
        
        // Should display top articles (e.g., first 3)
        let topArticles = mockArticles.prefix(3)
        for article in topArticles {
            let articleTitle = try? view.find(text: article.title)
            XCTAssertNotNil(articleTitle, "Article '\(article.title)' not found in digest")
        }
    }
    
    func testReadingProgressSection() throws {
        // Mark some articles as read
        appState.articles[0].isRead = true
        appState.articles[1].isRead = true
        appState.articles[2].readProgress = 0.5
        
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should show reading progress
        let progressSection = try? view.find(text: "Reading Progress")
        XCTAssertNotNil(progressSection)
        
        // Should show read count
        let readCount = try? view.find(text: "2 articles read")
        XCTAssertNotNil(readCount)
        
        // Should show in-progress count
        let inProgressCount = try? view.find(text: "1 in progress")
        XCTAssertNotNil(inProgressCount)
    }
    
    // MARK: - Category Breakdown Tests
    
    func testCategoryBreakdown() throws {
        // Add tags to articles for categorization
        appState.articles[0].tags = ["SwiftUI", "iOS"]
        appState.articles[1].tags = ["iOS", "Features"]
        appState.articles[2].tags = ["Swift", "Concurrency"]
        appState.articles[3].tags = ["CoreData", "iOS"]
        appState.articles[4].tags = ["SwiftUI", "Migration"]
        
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should show category breakdown
        let categorySection = try? view.find(text: "By Category")
        XCTAssertNotNil(categorySection)
        
        // Should show tag counts
        let swiftUITag = try? view.find(text: "SwiftUI (2)")
        XCTAssertNotNil(swiftUITag)
        
        let iOSTag = try? view.find(text: "iOS (3)")
        XCTAssertNotNil(iOSTag)
    }
    
    // MARK: - Time Period Tests
    
    func testTimePeriodselector() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have time period selector
        let segmentedControl = try? view.find(ViewType.Picker.self)
        XCTAssertNotNil(segmentedControl)
        
        // Should have options for different periods
        let periods = ["Today", "This Week", "This Month"]
        for period in periods {
            let option = try? view.find(text: period)
            XCTAssertNotNil(option, "Period option '\(period)' not found")
        }
    }
    
    // MARK: - Chart/Visualization Tests
    
    func testReadingActivityChart() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should show reading activity visualization
        let chartSection = try? view.find(text: "Reading Activity")
        XCTAssertNotNil(chartSection)
        
        // Should have some form of chart/graph
        // Note: Actual chart testing depends on implementation
        let chart = try? view.find(ViewType.Chart.self)
        // If using SwiftUI Charts
        if chart != nil {
            XCTAssertNotNil(chart)
        } else {
            // Alternative: custom visualization
            let customChart = try? view.find(ViewType.GeometryReader.self)
            XCTAssertNotNil(customChart)
        }
    }
    
    // MARK: - Empty State Tests
    
    func testEmptyDigest() throws {
        // Clear all articles
        appState.articles = []
        
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should show empty state
        let emptyMessage = try? view.find(text: "No articles yet")
        XCTAssertNotNil(emptyMessage)
        
        // Should have call to action
        let ctaButton = try? view.find(button: "Start Reading")
        XCTAssertNotNil(ctaButton)
    }
    
    // MARK: - Refresh Tests
    
    func testPullToRefresh() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have refresh capability
        let scrollView = try? view.find(ViewType.ScrollView.self)
        XCTAssertNotNil(scrollView)
        
        // Should have refresh modifier
        // Note: Testing refresh gesture requires ViewInspector support
    }
    
    func testRefreshButton() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have manual refresh button
        let refreshButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "arrow.clockwise") ?? false
        })
        XCTAssertNotNil(refreshButton)
    }
    
    // MARK: - Navigation Tests
    
    func testArticleTapNavigation() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Find an article in the digest
        if let articleButton = try? view.find(button: mockArticles[0].title) {
            // Tap should select article
            try articleButton.tap()
            
            // Should set selected article
            XCTAssertEqual(appState.selectedArticle?.id, mockArticles[0].id)
        }
    }
    
    func testSeeAllNavigation() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have "See All" buttons for sections
        let seeAllButton = try? view.find(button: "See All")
        XCTAssertNotNil(seeAllButton)
        
        // Tap should navigate to appropriate view
        if let button = seeAllButton {
            try button.tap()
            // Should change tab or navigation state
            XCTAssertEqual(appState.selectedTab, .home)
        }
    }
    
    // MARK: - Share Tests
    
    func testShareDigest() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have share button
        let shareButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "square.and.arrow.up") ?? false
        })
        XCTAssertNotNil(shareButton)
    }
    
    // MARK: - Settings Tests
    
    func testDigestSettings() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Should have settings/customize button
        let settingsButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "gearshape") ?? false
        })
        XCTAssertNotNil(settingsButton)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityLabels() throws {
        let digestView = DigestView()
            .environmentObject(appState)
        
        let view = try digestView.inspect()
        
        // Header should have accessibility trait
        if let header = try? view.find(text: "Your Daily Digest") {
            let traits = try header.accessibilityAddTraits()
            XCTAssertTrue(traits.contains(.isHeader))
        }
        
        // Charts should have accessibility descriptions
        // Implementation-specific testing
    }
    
    // MARK: - Performance Tests
    
    func testDigestGenerationPerformance() {
        // Add many articles
        appState.articles = (1...100).map { index in
            Article.mock(
                title: "Article \(index)",
                readingTime: Int.random(in: 5...30),
                isRead: Bool.random()
            )
        }
        
        measure {
            _ = DigestView()
                .environmentObject(appState)
        }
    }
}

// MARK: - Mock Extensions

extension Article {
    static func mock(
        title: String,
        readingTime: Int = 10,
        isRead: Bool = false
    ) -> Article {
        var article = Article(
            url: "https://test.com/\(UUID())",
            title: title,
            readingTime: readingTime
        )
        article.isRead = isRead
        return article
    }
}