import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class ProfileViewTests: XCTestCase {
    
    var appState: AppState!
    
    override func setUp() async throws {
        try await super.setUp()
        
        appState = AppState()
        // Set up some statistics
        appState.totalArticles = 150
        appState.unreadCount = 45
        appState.todayReadCount = 5
        appState.weeklyReadTime = 120 // minutes
    }
    
    override func tearDown() async throws {
        appState = nil
        try await super.tearDown()
    }
    
    // MARK: - Header Tests
    
    func testProfileHeader() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have profile header
        let header = try? view.find(text: "Profile")
        XCTAssertNotNil(header)
        
        // Should have user avatar placeholder
        let avatar = try? view.find(ViewType.Image.self, where: { image in
            try image.actualImage().name() == "person.circle.fill"
        })
        XCTAssertNotNil(avatar)
    }
    
    func testUserInfo() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should show user email or identifier
        // This depends on authentication implementation
        let userInfo = try? view.find(text: "<EMAIL>")
        // If no auth, might show "Guest" or similar
        if userInfo == nil {
            let guestLabel = try? view.find(text: "Guest User")
            XCTAssertNotNil(guestLabel)
        }
    }
    
    // MARK: - Statistics Section Tests
    
    func testReadingStatistics() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have statistics section
        let statsSection = try? view.find(text: "Reading Statistics")
        XCTAssertNotNil(statsSection)
        
        // Should show total articles
        let totalArticles = try? view.find(text: "150")
        XCTAssertNotNil(totalArticles)
        let articlesLabel = try? view.find(text: "Total Articles")
        XCTAssertNotNil(articlesLabel)
        
        // Should show unread count
        let unreadCount = try? view.find(text: "45")
        XCTAssertNotNil(unreadCount)
        let unreadLabel = try? view.find(text: "Unread")
        XCTAssertNotNil(unreadLabel)
        
        // Should show today's reading
        let todayCount = try? view.find(text: "5")
        XCTAssertNotNil(todayCount)
        let todayLabel = try? view.find(text: "Read Today")
        XCTAssertNotNil(todayLabel)
        
        // Should show weekly reading time
        let weeklyTime = try? view.find(text: "2h")
        XCTAssertNotNil(weeklyTime)
        let weeklyLabel = try? view.find(text: "This Week")
        XCTAssertNotNil(weeklyLabel)
    }
    
    func testStatisticCards() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Statistics should be in card layout
        let cards = try view.findAll(ViewType.RoundedRectangle.self)
        
        // Should have at least 4 stat cards
        XCTAssertGreaterThanOrEqual(cards.count, 4)
    }
    
    // MARK: - Settings Section Tests
    
    func testSettingsSections() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have various settings sections
        let sections = [
            "Reading Preferences",
            "Notifications",
            "Sync & Backup",
            "Privacy",
            "About"
        ]
        
        for section in sections {
            let sectionRow = try? view.find(text: section)
            XCTAssertNotNil(sectionRow, "Settings section '\(section)' not found")
        }
    }
    
    func testSettingsNavigation() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Find Reading Preferences row
        if let readingPrefsButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string().contains("Reading Preferences")) ?? false
        }) {
            // Should have navigation indicator
            let chevron = try? button.labelView().find(ViewType.Image.self, where: { image in
                try image.actualImage().name() == "chevron.right"
            })
            XCTAssertNotNil(chevron)
        }
    }
    
    // MARK: - Account Actions Tests
    
    func testSignOutButton() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have sign out button
        let signOutButton = try? view.find(button: "Sign Out")
        XCTAssertNotNil(signOutButton)
        
        // Button should be styled appropriately (e.g., destructive)
        if let button = signOutButton {
            // Check for destructive styling
            let buttonStyle = try? button.buttonStyle()
            // Verify appropriate styling
        }
    }
    
    func testExportData() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have export option
        let exportButton = try? view.find(text: "Export Data")
        XCTAssertNotNil(exportButton)
    }
    
    func testDeleteAccount() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have delete account option (usually hidden or requires confirmation)
        let deleteOption = try? view.find(text: "Delete Account")
        
        if deleteOption != nil {
            // Should be in danger zone or similar section
            let dangerSection = try? view.find(text: "Danger Zone")
            XCTAssertNotNil(dangerSection)
        }
    }
    
    // MARK: - Theme Selection Tests
    
    func testThemeSelector() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have theme selection
        let themeSection = try? view.find(text: "Appearance")
        XCTAssertNotNil(themeSection)
        
        // Should have theme options
        let themes = ["System", "Light", "Dark"]
        for theme in themes {
            let themeOption = try? view.find(text: theme)
            XCTAssertNotNil(themeOption, "Theme option '\(theme)' not found")
        }
    }
    
    func testCurrentThemeIndicator() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should show checkmark on current theme
        let checkmark = try? view.find(ViewType.Image.self, where: { image in
            try image.actualImage().name() == "checkmark"
        })
        XCTAssertNotNil(checkmark)
    }
    
    // MARK: - Storage Management Tests
    
    func testStorageInfo() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should show storage usage
        let storageSection = try? view.find(text: "Storage")
        XCTAssertNotNil(storageSection)
        
        // Should show cache size
        let cacheInfo = try? view.find(text: "Clear Cache")
        XCTAssertNotNil(cacheInfo)
        
        // Should show offline content size
        let offlineInfo = try? view.find(text: "Offline Content")
        XCTAssertNotNil(offlineInfo)
    }
    
    func testClearCacheButton() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should have clear cache button
        let clearCacheButton = try? view.find(button: "Clear Cache")
        XCTAssertNotNil(clearCacheButton)
    }
    
    // MARK: - Version Info Tests
    
    func testAboutSection() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should show app version
        let versionText = try? view.find(ViewType.Text.self, where: { text in
            try text.string().contains("Version")
        })
        XCTAssertNotNil(versionText)
        
        // Should have links to privacy policy, terms
        let privacyLink = try? view.find(text: "Privacy Policy")
        XCTAssertNotNil(privacyLink)
        
        let termsLink = try? view.find(text: "Terms of Service")
        XCTAssertNotNil(termsLink)
    }
    
    // MARK: - Sync Status Tests
    
    func testSyncStatus() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Should show sync status
        let syncSection = try? view.find(text: "Sync & Backup")
        XCTAssertNotNil(syncSection)
        
        // Should show last sync time
        let lastSyncText = try? view.find(ViewType.Text.self, where: { text in
            try text.string().contains("Last synced")
        })
        XCTAssertNotNil(lastSyncText)
        
        // Should have sync now button
        let syncButton = try? view.find(button: "Sync Now")
        XCTAssertNotNil(syncButton)
    }
    
    // MARK: - Sheet Presentations Tests
    
    func testSettingsSheets() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Test that tapping settings opens appropriate sheets
        // This requires testing sheet presentation bindings
        
        // Find a settings row
        if let notificationRow = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string().contains("Notifications")) ?? false
        }) {
            try notificationRow.tap()
            
            // Should present notification settings sheet
            // Verify through state changes
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibility() throws {
        let profileView = ProfileView()
            .environmentObject(appState)
        
        let view = try profileView.inspect()
        
        // Section headers should have header trait
        if let statsHeader = try? view.find(text: "Reading Statistics") {
            let traits = try statsHeader.accessibilityAddTraits()
            XCTAssertTrue(traits.contains(.isHeader))
        }
        
        // Statistics should have appropriate labels
        if let totalArticlesCard = try? view.find(ViewType.VStack.self, where: { stack in
            (try? stack.find(text: "150")) != nil
        }) {
            let label = try totalArticlesCard.accessibilityLabel()
            XCTAssertTrue(label.string().contains("Total Articles"))
        }
    }
    
    // MARK: - Performance Tests
    
    func testRenderingPerformance() {
        measure {
            _ = ProfileView()
                .environmentObject(appState)
        }
    }
}