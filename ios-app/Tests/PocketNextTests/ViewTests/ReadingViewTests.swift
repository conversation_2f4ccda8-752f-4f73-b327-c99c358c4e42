import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class ReadingViewTests: XCTestCase {
    
    var appState: AppState!
    var mockArticle: Article!
    var onDismiss: (() -> Void)?
    var dismissCalled: Bool = false
    
    override func setUp() async throws {
        try await super.setUp()
        
        appState = AppState()
        
        mockArticle = Article(
            url: "https://test.com/article",
            title: "Understanding SwiftUI State Management",
            content: """
            SwiftUI's declarative syntax makes it easy to describe your user interface.
            
            State management is a crucial aspect of building SwiftUI applications.
            When you declare a piece of state in a view, SwiftUI manages the storage
            of that state and provides it back to your view for reading and writing.
            
            The @State property wrapper is used for simple properties that belong
            to a single view. For more complex scenarios, you might use @StateObject,
            @ObservedObject, or @EnvironmentObject.
            """,
            summary: "A comprehensive guide to SwiftUI state management",
            author: "John Developer",
            publishDate: Date(),
            readingTime: 8,
            imageURL: "https://test.com/header-image.jpg"
        )
        
        dismissCalled = false
        onDismiss = { self.dismissCalled = true }
    }
    
    override func tearDown() async throws {
        appState = nil
        mockArticle = nil
        onDismiss = nil
        try await super.tearDown()
    }
    
    // MARK: - Content Display Tests
    
    func testArticleContentDisplay() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should display article title
        let title = try? view.find(text: mockArticle.title)
        XCTAssertNotNil(title)
        
        // Should display author
        let author = try? view.find(text: "John Developer")
        XCTAssertNotNil(author)
        
        // Should display reading time
        let readingTime = try? view.find(text: "8 min read")
        XCTAssertNotNil(readingTime)
        
        // Should display article content
        let content = try? view.find(ViewType.Text.self, where: { text in
            try text.string().contains("SwiftUI's declarative syntax")
        })
        XCTAssertNotNil(content)
    }
    
    func testHeaderImage() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should display header image
        let asyncImage = try? view.find(ViewType.AsyncImage.self)
        XCTAssertNotNil(asyncImage)
    }
    
    func testPublishDate() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should display formatted publish date
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        let expectedDate = dateFormatter.string(from: mockArticle.publishDate!)
        
        let dateText = try? view.find(text: expectedDate)
        XCTAssertNotNil(dateText)
    }
    
    // MARK: - Navigation Tests
    
    func testDismissButton() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should have dismiss/close button
        let dismissButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "xmark") ?? false ||
            (try? button.labelView().text().string() == "Done") ?? false
        })
        XCTAssertNotNil(dismissButton)
        
        // Tap dismiss
        if let button = dismissButton {
            try button.tap()
            XCTAssertTrue(dismissCalled)
        }
    }
    
    // MARK: - Toolbar Tests
    
    func testToolbarButtons() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should have toolbar with actions
        let toolbar = try? view.find(ViewType.ToolbarItem.self)
        XCTAssertNotNil(toolbar)
        
        // Common toolbar buttons
        let expectedButtons = [
            "square.and.arrow.up", // Share
            "star",                // Favorite
            "archivebox",         // Archive
            "textformat.size"     // Text settings
        ]
        
        for buttonIcon in expectedButtons {
            let button = try? view.find(ViewType.Button.self, where: { button in
                (try? button.labelView().image().actualImage().name() == buttonIcon) ?? false
            })
            XCTAssertNotNil(button, "Toolbar button '\(buttonIcon)' not found")
        }
    }
    
    func testFavoriteToggle() throws {
        mockArticle.isFavorite = false
        
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Find favorite button
        let favoriteButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "star") ?? false
        })
        
        if let button = favoriteButton {
            try button.tap()
            
            // Should toggle favorite state
            // Note: Actual state update depends on implementation
        }
    }
    
    // MARK: - Reading Progress Tests
    
    func testReadingProgressBar() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should have progress indicator
        let progressView = try? view.find(ViewType.ProgressView.self)
        XCTAssertNotNil(progressView)
    }
    
    func testScrollProgressTracking() throws {
        mockArticle.readProgress = 0.5
        
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should show 50% progress
        let progressView = try? view.find(ViewType.ProgressView.self)
        XCTAssertNotNil(progressView)
        
        // Progress value should be 0.5
        if let progress = progressView {
            let value = try progress.fractionCompleted()
            XCTAssertEqual(value, 0.5, accuracy: 0.01)
        }
    }
    
    // MARK: - Text Settings Tests
    
    func testTextSizeAdjustment() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should have text size controls
        let increaseSizeButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "textformat.size.larger") ?? false
        })
        
        let decreaseSizeButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "textformat.size.smaller") ?? false
        })
        
        // At least one text control should exist
        XCTAssertTrue(increaseSizeButton != nil || decreaseSizeButton != nil)
    }
    
    func testFontSelection() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should have font selection option
        let fontButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string().contains("Font")) ?? false
        })
        
        // Font selection might be in settings
        if fontButton == nil {
            let settingsButton = try? view.find(ViewType.Button.self, where: { button in
                (try? button.labelView().image().actualImage().name() == "textformat") ?? false
            })
            XCTAssertNotNil(settingsButton)
        }
    }
    
    // MARK: - Share Tests
    
    func testShareButton() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Find share button
        let shareButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "square.and.arrow.up") ?? false
        })
        XCTAssertNotNil(shareButton)
        
        // Tap share
        if let button = shareButton {
            try button.tap()
            
            // Should present share sheet
            // Note: Testing sheet presentation requires specific implementation
        }
    }
    
    // MARK: - Highlighting Tests
    
    func testTextSelection() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Content should be selectable
        let contentText = try? view.find(ViewType.Text.self, where: { text in
            try text.string().contains("SwiftUI's declarative syntax")
        })
        
        if let text = contentText {
            // Text should allow selection
            // Note: Testing text selection requires gesture testing
        }
    }
    
    // MARK: - Empty State Tests
    
    func testEmptyContent() throws {
        var emptyArticle = mockArticle!
        emptyArticle.content = nil
        
        let readingView = ReadingView(article: emptyArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should show summary if no content
        let summary = try? view.find(text: emptyArticle.summary!)
        XCTAssertNotNil(summary)
    }
    
    // MARK: - Loading State Tests
    
    func testLoadingState() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        // Set loading state
        appState.isLoading = true
        
        let view = try readingView.inspect()
        
        // Should show loading indicator
        let progressView = try? view.find(ViewType.ProgressView.self, where: { progress in
            // Distinguish from reading progress
            (try? progress.progressViewStyle()) != nil
        })
        XCTAssertNotNil(progressView)
    }
    
    // MARK: - Offline Mode Tests
    
    func testOfflineReading() throws {
        appState.isOfflineMode = true
        mockArticle.isDownloadedForOffline = true
        
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Should show offline indicator
        let offlineIndicator = try? view.find(ViewType.Image.self, where: { image in
            try image.actualImage().name() == "arrow.down.circle.fill"
        })
        XCTAssertNotNil(offlineIndicator)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibility() throws {
        let readingView = ReadingView(article: mockArticle, onDismiss: onDismiss)
            .environmentObject(appState)
        
        let view = try readingView.inspect()
        
        // Title should have header trait
        if let title = try? view.find(text: mockArticle.title) {
            let traits = try title.accessibilityAddTraits()
            XCTAssertTrue(traits.contains(.isHeader))
        }
        
        // Content should be accessible
        let content = try? view.find(ViewType.Text.self, where: { text in
            try text.string().contains("SwiftUI's declarative syntax")
        })
        XCTAssertNotNil(content)
    }
    
    // MARK: - Performance Tests
    
    func testRenderingPerformance() {
        // Create article with long content
        var longArticle = mockArticle!
        longArticle.content = String(repeating: "This is a long article. ", count: 1000)
        
        measure {
            _ = ReadingView(article: longArticle, onDismiss: onDismiss)
                .environmentObject(appState)
        }
    }
}