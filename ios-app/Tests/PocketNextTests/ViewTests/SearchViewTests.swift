import XCTest
import SwiftUI
import ViewInspector
@testable import PocketNext

@MainActor
class SearchViewTests: XCTestCase {
    
    var appState: AppState!
    var mockArticles: [Article]!
    
    override func setUp() async throws {
        try await super.setUp()
        
        appState = AppState()
        
        // Create mock articles with searchable content
        mockArticles = [
            Article(
                url: "https://test.com/1",
                title: "SwiftUI Tutorial for Beginners",
                content: "Learn the basics of SwiftUI development",
                summary: "A comprehensive guide to getting started with SwiftUI",
                tags: ["swiftui", "tutorial", "ios"]
            ),
            Article(
                url: "https://test.com/2",
                title: "Advanced iOS Development",
                content: "Deep dive into advanced iOS concepts",
                summary: "Master advanced techniques in iOS development",
                tags: ["ios", "advanced", "swift"]
            ),
            Article(
                url: "https://test.com/3",
                title: "Understanding Swift Concurrency",
                content: "Async/await and actors in Swift",
                summary: "Modern concurrency features in Swift",
                tags: ["swift", "concurrency", "async"]
            ),
            Article(
                url: "https://test.com/4",
                title: "UIKit to SwiftUI Migration Guide",
                content: "How to migrate your UIKit app to SwiftUI",
                summary: "Step-by-step migration from UIKit to SwiftUI",
                tags: ["uikit", "swiftui", "migration"]
            )
        ]
        
        appState.articles = mockArticles
    }
    
    override func tearDown() async throws {
        appState = nil
        mockArticles = nil
        try await super.tearDown()
    }
    
    // MARK: - Search Bar Tests
    
    func testSearchBarPresence() throws {
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should have search field
        let searchField = try? view.find(ViewType.TextField.self)
        XCTAssertNotNil(searchField)
        
        // Should have placeholder text
        let placeholder = try? searchField?.placeholderText()
        XCTAssertEqual(placeholder, "Search articles...")
    }
    
    func testSearchBarFocus() throws {
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Search field should auto-focus
        let searchField = try view.find(ViewType.TextField.self)
        
        // Check if field has focus modifier
        // Note: Testing focus state requires specific implementation
    }
    
    func testClearButton() throws {
        appState.searchQuery = "test"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show clear button when query exists
        let clearButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "xmark.circle.fill") ?? false
        })
        XCTAssertNotNil(clearButton)
        
        // Tap clear
        if let button = clearButton {
            try button.tap()
            XCTAssertEqual(appState.searchQuery, "")
        }
    }
    
    // MARK: - Search Results Tests
    
    func testSearchResults() throws {
        appState.searchQuery = "SwiftUI"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show filtered results
        let resultsList = try? view.find(ViewType.List.self)
        XCTAssertNotNil(resultsList)
        
        // Should show articles containing "SwiftUI"
        let swiftUITutorial = try? view.find(text: "SwiftUI Tutorial for Beginners")
        XCTAssertNotNil(swiftUITutorial)
        
        let migrationGuide = try? view.find(text: "UIKit to SwiftUI Migration Guide")
        XCTAssertNotNil(migrationGuide)
    }
    
    func testNoResults() throws {
        appState.searchQuery = "nonexistent"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show no results message
        let noResultsMessage = try? view.find(text: "No results found")
        XCTAssertNotNil(noResultsMessage)
        
        // Should show search suggestions
        let suggestionText = try? view.find(text: "Try different keywords")
        XCTAssertNotNil(suggestionText)
    }
    
    func testResultCount() throws {
        appState.searchQuery = "iOS"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show result count
        let resultCount = try? view.find(text: "2 results")
        XCTAssertNotNil(resultCount)
    }
    
    // MARK: - Search Filters Tests
    
    func testFilterButton() throws {
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should have filter button
        let filterButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().image().actualImage().name() == "line.horizontal.3.decrease.circle") ?? false
        })
        XCTAssertNotNil(filterButton)
        
        // Tap filter button
        if let button = filterButton {
            try button.tap()
            
            // Should present filter sheet
            // Note: Sheet presentation testing requires state verification
        }
    }
    
    func testQuickFilters() throws {
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should have quick filter chips
        let unreadChip = try? view.find(button: "Unread")
        XCTAssertNotNil(unreadChip)
        
        let favoritesChip = try? view.find(button: "Favorites")
        XCTAssertNotNil(favoritesChip)
        
        let recentChip = try? view.find(button: "Recent")
        XCTAssertNotNil(recentChip)
    }
    
    // MARK: - Search Suggestions Tests
    
    func testRecentSearches() throws {
        // Set recent searches
        appState.recentSearches = ["Swift", "iOS", "Tutorial"]
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show recent searches when search is empty
        if appState.searchQuery.isEmpty {
            let recentSection = try? view.find(text: "Recent Searches")
            XCTAssertNotNil(recentSection)
            
            // Should show recent search items
            for search in appState.recentSearches {
                let searchItem = try? view.find(text: search)
                XCTAssertNotNil(searchItem, "Recent search '\(search)' not found")
            }
        }
    }
    
    func testSearchSuggestions() throws {
        appState.searchQuery = "Sw"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show search suggestions
        let suggestions = ["Swift", "SwiftUI", "Swift Concurrency"]
        
        for suggestion in suggestions {
            let suggestionItem = try? view.find(text: suggestion)
            // At least some suggestions should appear
        }
    }
    
    // MARK: - Search History Tests
    
    func testClearHistory() throws {
        appState.recentSearches = ["Swift", "iOS"]
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should have clear history button
        let clearHistoryButton = try? view.find(button: "Clear History")
        XCTAssertNotNil(clearHistoryButton)
        
        // Tap clear
        if let button = clearHistoryButton {
            try button.tap()
            XCTAssertTrue(appState.recentSearches.isEmpty)
        }
    }
    
    // MARK: - Result Interaction Tests
    
    func testResultTap() throws {
        appState.searchQuery = "SwiftUI"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Find a result
        if let resultButton = try? view.find(ViewType.Button.self, where: { button in
            (try? button.labelView().text().string().contains("SwiftUI Tutorial")) ?? false
        }) {
            try resultButton.tap()
            
            // Should select article
            XCTAssertNotNil(appState.selectedArticle)
            XCTAssertEqual(appState.selectedArticle?.title, "SwiftUI Tutorial for Beginners")
        }
    }
    
    func testResultSwipeActions() throws {
        appState.searchQuery = "iOS"
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Find result list
        if let list = try? view.find(ViewType.List.self) {
            // Results should have swipe actions
            // Note: Testing swipe actions requires gesture testing
        }
    }
    
    // MARK: - Search Scope Tests
    
    func testSearchScope() throws {
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should have scope selector
        let scopeSelector = try? view.find(ViewType.Picker.self)
        
        if scopeSelector != nil {
            // Should have scope options
            let scopes = ["All", "Title", "Content", "Tags"]
            
            for scope in scopes {
                let scopeOption = try? view.find(text: scope)
                XCTAssertNotNil(scopeOption, "Scope option '\(scope)' not found")
            }
        }
    }
    
    // MARK: - Loading State Tests
    
    func testSearchLoading() throws {
        appState.searchQuery = "test"
        appState.isSearching = true
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show loading indicator
        let progressView = try? view.find(ViewType.ProgressView.self)
        XCTAssertNotNil(progressView)
        
        // Should show searching message
        let searchingText = try? view.find(text: "Searching...")
        XCTAssertNotNil(searchingText)
    }
    
    // MARK: - Empty State Tests
    
    func testEmptySearchState() throws {
        appState.searchQuery = ""
        appState.recentSearches = []
        
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Should show empty state
        let emptyMessage = try? view.find(text: "Start typing to search")
        XCTAssertNotNil(emptyMessage)
        
        // Should show search tips
        let tipText = try? view.find(ViewType.Text.self, where: { text in
            try text.string().contains("Tip:")
        })
        XCTAssertNotNil(tipText)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibility() throws {
        let searchView = SearchView()
            .environmentObject(appState)
        
        let view = try searchView.inspect()
        
        // Search field should have accessibility label
        let searchField = try view.find(ViewType.TextField.self)
        let label = try searchField.accessibilityLabel()
        XCTAssertEqual(label, Text("Search articles"))
        
        // Results should be in accessibility container
        if let resultsList = try? view.find(ViewType.List.self) {
            // List should be accessible
        }
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() {
        // Add many articles
        appState.articles = (1...1000).map { index in
            Article(
                url: "https://test.com/\(index)",
                title: "Article \(index)",
                content: "Content with keywords: Swift iOS SwiftUI",
                tags: ["tag\(index % 10)"]
            )
        }
        
        appState.searchQuery = "Swift"
        
        measure {
            _ = SearchView()
                .environmentObject(appState)
        }
    }
}

// MARK: - AppState Extensions for Testing

extension AppState {
    var recentSearches: [String] {
        get { [] } // Simplified for testing
        set { }
    }
    
    var isSearching: Bool {
        get { false }
        set { }
    }
}