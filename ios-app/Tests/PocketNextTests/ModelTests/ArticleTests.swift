import XCTest
import GRDB
@testable import PocketNext

class ArticleTests: XCTestCase {
    
    // MARK: - Initialization Tests
    
    func testArticleInitialization() {
        // Given
        let url = "https://example.com/article"
        let title = "Test Article"
        
        // When
        let article = Article(url: url, title: title)
        
        // Then
        XCTAssertFalse(article.id.isEmpty)
        XCTAssertEqual(article.url, url)
        XCTAssertEqual(article.title, title)
        XCTAssertNil(article.content)
        XCTAssertNil(article.summary)
        XCTAssertNil(article.author)
        XCTAssertNil(article.publishDate)
        XCTAssertNotNil(article.savedAt)
        XCTAssertEqual(article.tags, [])
        XCTAssertNil(article.readingTime)
        XCTAssertFalse(article.isRead)
        XCTAssertFalse(article.isFavorite)
        XCTAssertFalse(article.isArchived)
        XCTAssertEqual(article.readProgress, 0.0)
        XCTAssertNil(article.lastReadPosition)
        XCTAssertNil(article.imageURL)
        XCTAssertNil(article.faviconURL)
        XCTAssertEqual(article.domain, "example.com")
        XCTAssertNil(article.cloudKitRecordID)
        XCTAssertNil(article.lastSyncedAt)
        XCTAssertEqual(article.syncStatus, .pending)
        XCTAssertNil(article.htmlContent)
        XCTAssertFalse(article.isDownloadedForOffline)
        XCTAssertNil(article.downloadedAt)
        XCTAssertEqual(article.extractedKeywords, [])
        XCTAssertNil(article.aiSummary)
        XCTAssertNil(article.embeddings)
    }
    
    func testArticleInitializationExtractsDomain() {
        // Test various URL formats
        let testCases = [
            ("https://www.example.com/path", "www.example.com"),
            ("http://subdomain.example.com", "subdomain.example.com"),
            ("https://example.com", "example.com"),
            ("https://example.com:8080/path", "example.com"),
            ("invalid-url", nil)
        ]
        
        for (url, expectedDomain) in testCases {
            let article = Article(url: url, title: "Test")
            XCTAssertEqual(article.domain, expectedDomain, "Failed for URL: \(url)")
        }
    }
    
    // MARK: - Computed Properties Tests
    
    func testEstimatedReadingTime() {
        var article = Article(url: "https://test.com", title: "Test")
        
        // Test nil reading time
        XCTAssertEqual(article.estimatedReadingTime, "Unknown")
        
        // Test zero reading time
        article.readingTime = 0
        XCTAssertEqual(article.estimatedReadingTime, "< 1 min")
        
        // Test regular reading time
        article.readingTime = 5
        XCTAssertEqual(article.estimatedReadingTime, "5 min")
        
        article.readingTime = 30
        XCTAssertEqual(article.estimatedReadingTime, "30 min")
    }
    
    func testFormattedPublishDate() {
        var article = Article(url: "https://test.com", title: "Test")
        
        // Test nil publish date
        XCTAssertEqual(article.formattedPublishDate, "")
        
        // Test with date
        let date = Date(timeIntervalSince1970: 1609459200) // 2021-01-01
        article.publishDate = date
        XCTAssertFalse(article.formattedPublishDate.isEmpty)
        // Note: Exact format depends on locale
    }
    
    func testFormattedSavedDate() {
        let article = Article(url: "https://test.com", title: "Test")
        
        // Should return relative time like "just now" or "1 minute ago"
        XCTAssertFalse(article.formattedSavedDate.isEmpty)
    }
    
    func testReadingProgressPercentage() {
        var article = Article(url: "https://test.com", title: "Test")
        
        article.readProgress = 0.0
        XCTAssertEqual(article.readingProgressPercentage, 0)
        
        article.readProgress = 0.5
        XCTAssertEqual(article.readingProgressPercentage, 50)
        
        article.readProgress = 1.0
        XCTAssertEqual(article.readingProgressPercentage, 100)
    }
    
    func testIsFullyRead() {
        var article = Article(url: "https://test.com", title: "Test")
        
        article.readProgress = 0.0
        XCTAssertFalse(article.isFullyRead)
        
        article.readProgress = 0.94
        XCTAssertFalse(article.isFullyRead)
        
        article.readProgress = 0.95
        XCTAssertTrue(article.isFullyRead)
        
        article.readProgress = 1.0
        XCTAssertTrue(article.isFullyRead)
    }
    
    func testNeedsSync() {
        var article = Article(url: "https://test.com", title: "Test")
        
        article.syncStatus = .pending
        XCTAssertTrue(article.needsSync)
        
        article.syncStatus = .modified
        XCTAssertTrue(article.needsSync)
        
        article.syncStatus = .synced
        XCTAssertFalse(article.needsSync)
        
        article.syncStatus = .conflict
        XCTAssertFalse(article.needsSync)
    }
    
    // MARK: - Search Matching Tests
    
    func testMatchesSearchQuery() {
        var article = Article(url: "https://test.com", title: "SwiftUI Tutorial")
        article.content = "Learn about SwiftUI and iOS development"
        article.summary = "A comprehensive guide to SwiftUI"
        article.tags = ["swift", "ios", "tutorial"]
        
        // Test empty query
        XCTAssertTrue(article.matches(searchQuery: ""))
        
        // Test title matching
        XCTAssertTrue(article.matches(searchQuery: "SwiftUI"))
        XCTAssertTrue(article.matches(searchQuery: "tutorial"))
        XCTAssertTrue(article.matches(searchQuery: "SWIFTUI")) // Case insensitive
        
        // Test content matching
        XCTAssertTrue(article.matches(searchQuery: "iOS"))
        XCTAssertTrue(article.matches(searchQuery: "development"))
        
        // Test summary matching
        XCTAssertTrue(article.matches(searchQuery: "comprehensive"))
        XCTAssertTrue(article.matches(searchQuery: "guide"))
        
        // Test tag matching
        XCTAssertTrue(article.matches(searchQuery: "swift"))
        XCTAssertTrue(article.matches(searchQuery: "ios"))
        
        // Test non-matching
        XCTAssertFalse(article.matches(searchQuery: "Android"))
        XCTAssertFalse(article.matches(searchQuery: "Kotlin"))
    }
    
    func testMatchesSearchQueryWithNilFields() {
        let article = Article(url: "https://test.com", title: "Test Article")
        // content, summary are nil by default
        
        // Should still match on title
        XCTAssertTrue(article.matches(searchQuery: "Test"))
        XCTAssertTrue(article.matches(searchQuery: "Article"))
        
        // Should not crash on nil fields
        XCTAssertFalse(article.matches(searchQuery: "content"))
    }
    
    // MARK: - GRDB Tests
    
    func testGRDBEncoding() throws {
        // Given
        var article = Article(url: "https://test.com", title: "Test Article")
        article.content = "Test content"
        article.summary = "Test summary"
        article.author = "Test Author"
        article.publishDate = Date()
        article.tags = ["tag1", "tag2"]
        article.readingTime = 10
        article.isRead = true
        article.isFavorite = true
        article.isArchived = false
        article.readProgress = 0.75
        article.lastReadPosition = 500
        article.imageURL = "https://test.com/image.jpg"
        article.faviconURL = "https://test.com/favicon.ico"
        article.cloudKitRecordID = "test-record-id"
        article.lastSyncedAt = Date()
        article.syncStatus = .synced
        article.htmlContent = "<p>HTML content</p>"
        article.isDownloadedForOffline = true
        article.downloadedAt = Date()
        article.aiSummary = "AI generated summary"
        article.embeddings = Data([1, 2, 3, 4])
        
        // When
        var container = PersistenceContainer()
        article.encode(to: &container)
        
        // Then
        XCTAssertEqual(container[Article.Columns.id], article.id)
        XCTAssertEqual(container[Article.Columns.url], article.url)
        XCTAssertEqual(container[Article.Columns.title], article.title)
        XCTAssertEqual(container[Article.Columns.content], article.content)
        XCTAssertEqual(container[Article.Columns.summary], article.summary)
        XCTAssertEqual(container[Article.Columns.author], article.author)
        XCTAssertNotNil(container[Article.Columns.publishDate])
        XCTAssertNotNil(container[Article.Columns.savedAt])
        XCTAssertEqual(container[Article.Columns.readingTime], article.readingTime)
        XCTAssertEqual(container[Article.Columns.isRead], article.isRead)
        XCTAssertEqual(container[Article.Columns.isFavorite], article.isFavorite)
        XCTAssertEqual(container[Article.Columns.isArchived], article.isArchived)
        XCTAssertEqual(container[Article.Columns.readProgress], article.readProgress)
        XCTAssertEqual(container[Article.Columns.lastReadPosition], article.lastReadPosition)
        XCTAssertEqual(container[Article.Columns.imageURL], article.imageURL)
        XCTAssertEqual(container[Article.Columns.faviconURL], article.faviconURL)
        XCTAssertEqual(container[Article.Columns.domain], article.domain)
        XCTAssertEqual(container[Article.Columns.cloudKitRecordID], article.cloudKitRecordID)
        XCTAssertNotNil(container[Article.Columns.lastSyncedAt])
        XCTAssertEqual(container[Article.Columns.syncStatus], article.syncStatus.rawValue)
        XCTAssertEqual(container[Article.Columns.htmlContent], article.htmlContent)
        XCTAssertEqual(container[Article.Columns.isDownloadedForOffline], article.isDownloadedForOffline)
        XCTAssertNotNil(container[Article.Columns.downloadedAt])
        XCTAssertEqual(container[Article.Columns.aiSummary], article.aiSummary)
        XCTAssertEqual(container[Article.Columns.embeddings], article.embeddings)
    }
    
    func testGRDBDecoding() throws {
        // Given
        let id = UUID().uuidString
        let url = "https://test.com"
        let title = "Test Article"
        let content = "Test content"
        let publishDate = Date()
        let savedAt = Date()
        let embeddings = Data([1, 2, 3, 4])
        
        let row = Row([
            Article.Columns.id.rawValue: id,
            Article.Columns.url.rawValue: url,
            Article.Columns.title.rawValue: title,
            Article.Columns.content.rawValue: content,
            Article.Columns.summary.rawValue: "Test summary",
            Article.Columns.author.rawValue: "Test Author",
            Article.Columns.publishDate.rawValue: publishDate,
            Article.Columns.savedAt.rawValue: savedAt,
            Article.Columns.readingTime.rawValue: 10,
            Article.Columns.isRead.rawValue: true,
            Article.Columns.isFavorite.rawValue: true,
            Article.Columns.isArchived.rawValue: false,
            Article.Columns.readProgress.rawValue: 0.75,
            Article.Columns.lastReadPosition.rawValue: 500,
            Article.Columns.imageURL.rawValue: "https://test.com/image.jpg",
            Article.Columns.faviconURL.rawValue: "https://test.com/favicon.ico",
            Article.Columns.domain.rawValue: "test.com",
            Article.Columns.cloudKitRecordID.rawValue: "test-record-id",
            Article.Columns.lastSyncedAt.rawValue: Date(),
            Article.Columns.syncStatus.rawValue: "synced",
            Article.Columns.htmlContent.rawValue: "<p>HTML content</p>",
            Article.Columns.isDownloadedForOffline.rawValue: true,
            Article.Columns.downloadedAt.rawValue: Date(),
            Article.Columns.aiSummary.rawValue: "AI generated summary",
            Article.Columns.embeddings.rawValue: embeddings
        ])
        
        // When
        let article = Article(row: row)
        
        // Then
        XCTAssertEqual(article.id, id)
        XCTAssertEqual(article.url, url)
        XCTAssertEqual(article.title, title)
        XCTAssertEqual(article.content, content)
        XCTAssertEqual(article.summary, "Test summary")
        XCTAssertEqual(article.author, "Test Author")
        XCTAssertNotNil(article.publishDate)
        XCTAssertNotNil(article.savedAt)
        XCTAssertEqual(article.readingTime, 10)
        XCTAssertTrue(article.isRead)
        XCTAssertTrue(article.isFavorite)
        XCTAssertFalse(article.isArchived)
        XCTAssertEqual(article.readProgress, 0.75)
        XCTAssertEqual(article.lastReadPosition, 500)
        XCTAssertEqual(article.imageURL, "https://test.com/image.jpg")
        XCTAssertEqual(article.faviconURL, "https://test.com/favicon.ico")
        XCTAssertEqual(article.domain, "test.com")
        XCTAssertEqual(article.cloudKitRecordID, "test-record-id")
        XCTAssertNotNil(article.lastSyncedAt)
        XCTAssertEqual(article.syncStatus, .synced)
        XCTAssertEqual(article.htmlContent, "<p>HTML content</p>")
        XCTAssertTrue(article.isDownloadedForOffline)
        XCTAssertNotNil(article.downloadedAt)
        XCTAssertEqual(article.aiSummary, "AI generated summary")
        XCTAssertEqual(article.embeddings, embeddings)
    }
    
    // MARK: - Edge Cases
    
    func testSyncStatusRawValueHandling() {
        // Test invalid sync status defaults to pending
        let row = Row([
            Article.Columns.id.rawValue: "test-id",
            Article.Columns.url.rawValue: "https://test.com",
            Article.Columns.title.rawValue: "Test",
            Article.Columns.savedAt.rawValue: Date(),
            Article.Columns.syncStatus.rawValue: "invalid-status"
        ])
        
        let article = Article(row: row)
        XCTAssertEqual(article.syncStatus, .pending)
    }
    
    func testArticleEquality() {
        let article1 = Article(url: "https://test.com", title: "Test")
        var article2 = article1
        
        // Same ID should be equal
        XCTAssertEqual(article1, article2)
        
        // Different ID should not be equal
        article2.id = UUID().uuidString
        XCTAssertNotEqual(article1, article2)
    }
    
    func testArticleHashable() {
        var articles = Set<Article>()
        
        let article1 = Article(url: "https://test1.com", title: "Test 1")
        let article2 = Article(url: "https://test2.com", title: "Test 2")
        var article3 = article1 // Same ID as article1
        
        articles.insert(article1)
        articles.insert(article2)
        articles.insert(article3)
        
        // Should only contain 2 unique articles
        XCTAssertEqual(articles.count, 2)
    }
}

// MARK: - Article Tag Tests

extension ArticleTests {
    
    func testArticleTagInitialization() {
        let articleTag = ArticleTag(articleId: "test-id", tag: "Swift")
        
        XCTAssertEqual(articleTag.articleId, "test-id")
        XCTAssertEqual(articleTag.tag, "Swift")
    }
    
    func testArticleTagCodable() throws {
        let originalTag = ArticleTag(articleId: "test-id", tag: "Swift")
        
        // Encode
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalTag)
        
        // Decode
        let decoder = JSONDecoder()
        let decodedTag = try decoder.decode(ArticleTag.self, from: data)
        
        XCTAssertEqual(originalTag.articleId, decodedTag.articleId)
        XCTAssertEqual(originalTag.tag, decodedTag.tag)
    }
}

// MARK: - Sample Data Tests

#if DEBUG
extension ArticleTests {
    
    func testSampleArticles() {
        let samples = Article.sampleArticles
        
        XCTAssertEqual(samples.count, 3)
        
        for article in samples {
            XCTAssertFalse(article.url.isEmpty)
            XCTAssertFalse(article.title.isEmpty)
            XCTAssertNotNil(article.summary)
            XCTAssertNotNil(article.author)
            XCTAssertNotNil(article.publishDate)
            XCTAssertNotNil(article.readingTime)
            XCTAssertFalse(article.tags.isEmpty)
            XCTAssertNotNil(article.imageURL)
        }
    }
}
#endif