import XCTest
import SwiftUI
@testable import PocketNext

@MainActor
class AppStateTests: XCTestCase {
    
    var sut: AppState!
    
    override func setUp() async throws {
        try await super.setUp()
        sut = AppState()
    }
    
    override func tearDown() async throws {
        sut = nil
        try await super.tearDown()
    }
    
    // MARK: - Tab Management Tests
    
    func testDefaultSelectedTab() {
        XCTAssertEqual(sut.selectedTab, .home)
    }
    
    func testTabEnum() {
        // Test all tab cases exist and have correct properties
        XCTAssertEqual(AppState.Tab.home.rawValue, "house.fill")
        XCTAssertEqual(AppState.Tab.home.title, "Home")
        
        XCTAssertEqual(AppState.Tab.search.rawValue, "magnifyingglass")
        XCTAssertEqual(AppState.Tab.search.title, "Search")
        
        XCTAssertEqual(AppState.Tab.chat.rawValue, "bubble.left.and.bubble.right.fill")
        XCTAssertEqual(AppState.Tab.chat.title, "Chat")
        
        XCTAssertEqual(AppState.Tab.digest.rawValue, "newspaper.fill")
        XCTAssertEqual(AppState.Tab.digest.title, "Digest")
        
        XCTAssertEqual(AppState.Tab.profile.rawValue, "person.circle.fill")
        XCTAssertEqual(AppState.Tab.profile.title, "Profile")
    }
    
    func testTabSelection() {
        // Test changing tabs
        sut.selectedTab = .search
        XCTAssertEqual(sut.selectedTab, .search)
        
        sut.selectedTab = .chat
        XCTAssertEqual(sut.selectedTab, .chat)
        
        sut.selectedTab = .digest
        XCTAssertEqual(sut.selectedTab, .digest)
        
        sut.selectedTab = .profile
        XCTAssertEqual(sut.selectedTab, .profile)
        
        sut.selectedTab = .home
        XCTAssertEqual(sut.selectedTab, .home)
    }
    
    // MARK: - Article Selection Tests
    
    func testSelectedArticleDefault() {
        XCTAssertNil(sut.selectedArticle)
    }
    
    func testSelectedArticleBinding() {
        let article = Article(url: "https://test.com", title: "Test Article")
        
        sut.selectedArticle = article
        XCTAssertNotNil(sut.selectedArticle)
        XCTAssertEqual(sut.selectedArticle?.id, article.id)
        
        sut.selectedArticle = nil
        XCTAssertNil(sut.selectedArticle)
    }
    
    // MARK: - Feed Filter Tests
    
    func testDefaultFeedFilter() {
        XCTAssertEqual(sut.feedFilter, .all)
    }
    
    func testFeedFilterEnum() {
        // Test all filter cases
        XCTAssertEqual(AppState.FeedFilter.all.rawValue, "All")
        XCTAssertEqual(AppState.FeedFilter.unread.rawValue, "Unread")
        XCTAssertEqual(AppState.FeedFilter.archived.rawValue, "Archived")
        XCTAssertEqual(AppState.FeedFilter.favorites.rawValue, "Favorites")
    }
    
    func testFeedFilterChange() {
        sut.feedFilter = .unread
        XCTAssertEqual(sut.feedFilter, .unread)
        
        sut.feedFilter = .archived
        XCTAssertEqual(sut.feedFilter, .archived)
        
        sut.feedFilter = .favorites
        XCTAssertEqual(sut.feedFilter, .favorites)
        
        sut.feedFilter = .all
        XCTAssertEqual(sut.feedFilter, .all)
    }
    
    // MARK: - Sort Order Tests
    
    func testDefaultSortOrder() {
        XCTAssertEqual(sut.sortOrder, .newest)
    }
    
    func testSortOrderEnum() {
        XCTAssertEqual(AppState.SortOrder.newest.rawValue, "Newest First")
        XCTAssertEqual(AppState.SortOrder.newest.systemImage, "arrow.down")
        
        XCTAssertEqual(AppState.SortOrder.oldest.rawValue, "Oldest First")
        XCTAssertEqual(AppState.SortOrder.oldest.systemImage, "arrow.up")
        
        XCTAssertEqual(AppState.SortOrder.readingTime.rawValue, "Reading Time")
        XCTAssertEqual(AppState.SortOrder.readingTime.systemImage, "clock")
        
        XCTAssertEqual(AppState.SortOrder.relevance.rawValue, "Relevance")
        XCTAssertEqual(AppState.SortOrder.relevance.systemImage, "star")
    }
    
    func testSortOrderChange() {
        sut.sortOrder = .oldest
        XCTAssertEqual(sut.sortOrder, .oldest)
        
        sut.sortOrder = .readingTime
        XCTAssertEqual(sut.sortOrder, .readingTime)
        
        sut.sortOrder = .relevance
        XCTAssertEqual(sut.sortOrder, .relevance)
        
        sut.sortOrder = .newest
        XCTAssertEqual(sut.sortOrder, .newest)
    }
    
    // MARK: - UI State Tests
    
    func testUIStateDefaults() {
        XCTAssertFalse(sut.isLoading)
        XCTAssertFalse(sut.isRefreshing)
        XCTAssertFalse(sut.showingSaveSuccess)
        XCTAssertNil(sut.errorMessage)
    }
    
    func testLoadingState() {
        sut.isLoading = true
        XCTAssertTrue(sut.isLoading)
        
        sut.isLoading = false
        XCTAssertFalse(sut.isLoading)
    }
    
    func testRefreshingState() {
        sut.isRefreshing = true
        XCTAssertTrue(sut.isRefreshing)
        
        sut.isRefreshing = false
        XCTAssertFalse(sut.isRefreshing)
    }
    
    func testSaveSuccessState() {
        sut.showingSaveSuccess = true
        XCTAssertTrue(sut.showingSaveSuccess)
        
        sut.showingSaveSuccess = false
        XCTAssertFalse(sut.showingSaveSuccess)
    }
    
    // MARK: - Statistics Tests
    
    func testStatisticsDefaults() {
        XCTAssertEqual(sut.totalArticles, 0)
        XCTAssertEqual(sut.unreadCount, 0)
        XCTAssertEqual(sut.todayReadCount, 0)
        XCTAssertEqual(sut.weeklyReadTime, 0)
    }
    
    func testStatisticsUpdate() {
        sut.totalArticles = 100
        XCTAssertEqual(sut.totalArticles, 100)
        
        sut.unreadCount = 25
        XCTAssertEqual(sut.unreadCount, 25)
        
        sut.todayReadCount = 5
        XCTAssertEqual(sut.todayReadCount, 5)
        
        sut.weeklyReadTime = 120
        XCTAssertEqual(sut.weeklyReadTime, 120)
    }
    
    // MARK: - Search State Tests
    
    func testSearchStateDefaults() {
        XCTAssertEqual(sut.searchQuery, "")
        XCTAssertFalse(sut.isSearching)
    }
    
    func testSearchQueryUpdate() {
        sut.searchQuery = "SwiftUI"
        XCTAssertEqual(sut.searchQuery, "SwiftUI")
        
        sut.searchQuery = ""
        XCTAssertEqual(sut.searchQuery, "")
    }
    
    func testSearchingState() {
        sut.isSearching = true
        XCTAssertTrue(sut.isSearching)
        
        sut.isSearching = false
        XCTAssertFalse(sut.isSearching)
    }
    
    // MARK: - Offline Mode Tests
    
    func testOfflineModeDefault() {
        XCTAssertFalse(sut.isOfflineMode)
    }
    
    func testOfflineModeToggle() {
        sut.isOfflineMode = true
        XCTAssertTrue(sut.isOfflineMode)
        
        sut.isOfflineMode = false
        XCTAssertFalse(sut.isOfflineMode)
    }
    
    // MARK: - Error Handling Tests
    
    func testShowError() {
        let errorMessage = "Test error message"
        
        sut.showError(errorMessage)
        
        XCTAssertEqual(sut.errorMessage, errorMessage)
    }
    
    func testClearError() {
        sut.errorMessage = "Some error"
        XCTAssertNotNil(sut.errorMessage)
        
        sut.errorMessage = nil
        XCTAssertNil(sut.errorMessage)
    }
    
    // MARK: - Update Statistics Tests
    
    func testUpdateStatisticsAsync() async {
        // Given - mock database manager would be injected here
        // For now, just test the method exists and can be called
        
        // When
        await sut.updateStatistics()
        
        // Then - would verify database calls with mock
        // Currently just verify no crash
        XCTAssertTrue(true)
    }
    
    // MARK: - User Preferences Tests
    
    func testLoadUserPreferencesAsync() async {
        // When
        await sut.loadUserPreferences()
        
        // Then - would verify UserDefaults calls
        // Currently just verify no crash
        XCTAssertTrue(true)
    }
    
    // MARK: - Haptic Feedback Tests
    
    func testTriggerHapticFeedback() {
        // Test all haptic types
        sut.triggerHapticFeedback(.light)
        sut.triggerHapticFeedback(.medium)
        sut.triggerHapticFeedback(.heavy)
        sut.triggerHapticFeedback(.error)
        sut.triggerHapticFeedback(.success)
        sut.triggerHapticFeedback(.warning)
        
        // Just verify no crashes
        XCTAssertTrue(true)
    }
    
    // MARK: - Chat Message Tests
    
    func testChatMessageInitialization() {
        let message = ChatMessage(content: "Test message", isUser: true)
        
        XCTAssertFalse(message.id.isEmpty)
        XCTAssertEqual(message.content, "Test message")
        XCTAssertTrue(message.isUser)
        XCTAssertNil(message.articleReferences)
        XCTAssertNotNil(message.timestamp)
    }
    
    func testChatMessageWithReferences() {
        let article = Article(url: "https://test.com", title: "Test")
        let message = ChatMessage(
            content: "Test message",
            isUser: false,
            articleReferences: [article]
        )
        
        XCTAssertEqual(message.content, "Test message")
        XCTAssertFalse(message.isUser)
        XCTAssertNotNil(message.articleReferences)
        XCTAssertEqual(message.articleReferences?.count, 1)
        XCTAssertEqual(message.articleReferences?.first?.id, article.id)
    }
    
    func testChatMessageEquality() {
        let message1 = ChatMessage(content: "Test", isUser: true)
        let message2 = ChatMessage(content: "Test", isUser: true)
        
        // Different IDs mean different messages
        XCTAssertNotEqual(message1, message2)
        
        // Same message instance
        XCTAssertEqual(message1, message1)
    }
    
    // MARK: - Theme Tests
    
    func testDefaultTheme() {
        XCTAssertEqual(sut.currentTheme, .system)
    }
    
    func testThemeChange() {
        sut.currentTheme = .light
        XCTAssertEqual(sut.currentTheme, .light)
        
        sut.currentTheme = .dark
        XCTAssertEqual(sut.currentTheme, .dark)
        
        sut.currentTheme = .system
        XCTAssertEqual(sut.currentTheme, .system)
    }
    
    // MARK: - ObservableObject Tests
    
    func testPublishedProperties() {
        // Verify AppState conforms to ObservableObject
        XCTAssertTrue(sut is ObservableObject)
        
        // Test that changing published properties triggers updates
        let expectation = expectation(description: "Published property changed")
        
        let cancellable = sut.objectWillChange.sink { _ in
            expectation.fulfill()
        }
        
        sut.selectedTab = .search
        
        wait(for: [expectation], timeout: 1.0)
        cancellable.cancel()
    }
    
    // MARK: - Thread Safety Tests
    
    func testConcurrentAccess() async {
        // Test concurrent reads and writes
        await withTaskGroup(of: Void.self) { group in
            // Multiple concurrent reads
            for i in 0..<10 {
                group.addTask { [weak self] in
                    _ = self?.sut.totalArticles
                    _ = self?.sut.unreadCount
                    _ = self?.sut.selectedTab
                }
            }
            
            // Concurrent writes
            for i in 0..<10 {
                group.addTask { [weak self] in
                    await MainActor.run {
                        self?.sut.totalArticles = i
                        self?.sut.unreadCount = i * 2
                    }
                }
            }
        }
        
        // Verify no crashes
        XCTAssertTrue(true)
    }
}

// MARK: - Article Filters Extension

extension AppStateTests {
    
    func testArticleFilterExtension() {
        // Create test articles
        var articles: [Article] = []
        
        var unreadArticle = Article(url: "https://test1.com", title: "Unread")
        unreadArticle.isRead = false
        unreadArticle.isArchived = false
        articles.append(unreadArticle)
        
        var readArticle = Article(url: "https://test2.com", title: "Read")
        readArticle.isRead = true
        readArticle.isArchived = false
        articles.append(readArticle)
        
        var archivedArticle = Article(url: "https://test3.com", title: "Archived")
        archivedArticle.isArchived = true
        articles.append(archivedArticle)
        
        var favoriteArticle = Article(url: "https://test4.com", title: "Favorite")
        favoriteArticle.isFavorite = true
        favoriteArticle.isArchived = false
        articles.append(favoriteArticle)
        
        // Test "All" filter (excludes archived)
        sut.feedFilter = .all
        let allFiltered = articles.filter { article in
            switch sut.feedFilter {
            case .all:
                return !article.isArchived
            case .unread:
                return !article.isRead && !article.isArchived
            case .archived:
                return article.isArchived
            case .favorites:
                return article.isFavorite
            }
        }
        XCTAssertEqual(allFiltered.count, 3) // Excludes archived
        
        // Test "Unread" filter
        sut.feedFilter = .unread
        let unreadFiltered = articles.filter { article in
            switch sut.feedFilter {
            case .all:
                return !article.isArchived
            case .unread:
                return !article.isRead && !article.isArchived
            case .archived:
                return article.isArchived
            case .favorites:
                return article.isFavorite
            }
        }
        XCTAssertEqual(unreadFiltered.count, 1)
        
        // Test "Archived" filter
        sut.feedFilter = .archived
        let archivedFiltered = articles.filter { article in
            switch sut.feedFilter {
            case .all:
                return !article.isArchived
            case .unread:
                return !article.isRead && !article.isArchived
            case .archived:
                return article.isArchived
            case .favorites:
                return article.isFavorite
            }
        }
        XCTAssertEqual(archivedFiltered.count, 1)
        
        // Test "Favorites" filter
        sut.feedFilter = .favorites
        let favoritesFiltered = articles.filter { article in
            switch sut.feedFilter {
            case .all:
                return !article.isArchived
            case .unread:
                return !article.isRead && !article.isArchived
            case .archived:
                return article.isArchived
            case .favorites:
                return article.isFavorite
            }
        }
        XCTAssertEqual(favoritesFiltered.count, 1)
    }
}