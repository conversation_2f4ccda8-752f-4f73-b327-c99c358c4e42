import XCTest
import GRDB
@testable import PocketNext

class DatabaseIntegrationTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var testDbPath: String!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create real database for integration testing
        let tempDir = FileManager.default.temporaryDirectory
        testDbPath = tempDir.appendingPathComponent("integration_test_\(UUID().uuidString).db").path
        
        databaseManager = DatabaseManager()
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        // Clean up
        if FileManager.default.fileExists(atPath: testDbPath) {
            try FileManager.default.removeItem(atPath: testDbPath)
        }
        
        databaseManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Full Article Lifecycle Tests
    
    func testCompleteArticleLifecycle() async throws {
        // 1. Create and save article
        var article = Article(url: "https://test.com", title: "Integration Test Article")
        article.content = "This is test content"
        article.author = "Test Author"
        article.tags = ["integration", "testing"]
        article.readingTime = 5
        
        try await databaseManager.saveArticle(article)
        
        // 2. Fetch and verify
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.title, article.title)
        XCTAssertEqual(fetched?.tags.count, 2)
        
        // 3. Update article
        article.isRead = true
        article.readProgress = 1.0
        article.tags.append("completed")
        try await databaseManager.updateArticle(article)
        
        // 4. Verify update
        let updated = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertTrue(updated?.isRead ?? false)
        XCTAssertEqual(updated?.readProgress, 1.0)
        XCTAssertEqual(updated?.tags.count, 3)
        
        // 5. Search for article
        let searchResults = try await databaseManager.searchArticles(query: "integration")
        XCTAssertFalse(searchResults.isEmpty)
        XCTAssertTrue(searchResults.contains { $0.id == article.id })
        
        // 6. Delete article
        try await databaseManager.deleteArticle(article.id)
        
        // 7. Verify deletion
        let deleted = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNil(deleted)
    }
    
    // MARK: - Batch Operations Tests
    
    func testBatchArticleOperations() async throws {
        // Create multiple articles
        let articles = (1...100).map { index in
            var article = Article(
                url: "https://test.com/\(index)",
                title: "Article \(index)"
            )
            article.isRead = index % 3 == 0
            article.isFavorite = index % 5 == 0
            article.tags = index % 2 == 0 ? ["even", "number"] : ["odd", "number"]
            return article
        }
        
        // Save all articles
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // Test various queries
        let allArticles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(allArticles.count, 100)
        
        // Test filtering
        let unreadArticles = allArticles.filter { !$0.isRead }
        XCTAssertEqual(unreadArticles.count, 67) // ~2/3 should be unread
        
        // Test search with tags
        let evenArticles = try await databaseManager.searchArticles(query: "even")
        XCTAssertEqual(evenArticles.count, 50)
        
        // Test statistics
        let stats = try await databaseManager.getStatistics()
        XCTAssertEqual(stats.total, 100)
        XCTAssertEqual(stats.unread, 67)
    }
    
    // MARK: - Concurrent Access Tests
    
    func testConcurrentDatabaseAccess() async throws {
        // Create initial articles
        let articles = (1...10).map { index in
            Article(url: "https://test.com/\(index)", title: "Article \(index)")
        }
        
        // Save articles concurrently
        await withTaskGroup(of: Void.self) { group in
            for article in articles {
                group.addTask { [weak self] in
                    try? await self?.databaseManager.saveArticle(article)
                }
            }
        }
        
        // Verify all saved
        let saved = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(saved.count, 10)
        
        // Update articles concurrently
        await withTaskGroup(of: Void.self) { group in
            for article in saved {
                group.addTask { [weak self] in
                    try? await self?.databaseManager.markAsRead(article.id)
                }
            }
        }
        
        // Verify all updated
        let updated = try await databaseManager.fetchAllArticles()
        XCTAssertTrue(updated.allSatisfy { $0.isRead })
    }
    
    // MARK: - Search Performance Tests
    
    func testSearchPerformance() async throws {
        // Populate database with realistic data
        let words = ["Swift", "iOS", "Development", "Tutorial", "Guide", "Programming", "Mobile", "App"]
        
        for i in 1...1000 {
            let title = "\(words.randomElement()!) \(words.randomElement()!) Article \(i)"
            let content = (1...100).map { _ in words.randomElement()! }.joined(separator: " ")
            
            var article = Article(url: "https://test.com/\(i)", title: title)
            article.content = content
            article.summary = String(content.prefix(200))
            
            try await databaseManager.saveArticle(article)
        }
        
        // Measure search performance
        let startTime = Date()
        let results = try await databaseManager.searchArticles(query: "Swift Development")
        let endTime = Date()
        
        let searchTime = endTime.timeIntervalSince(startTime)
        
        XCTAssertLessThan(searchTime, 1.0) // Should complete in under 1 second
        XCTAssertFalse(results.isEmpty)
        
        print("Search completed in \(searchTime) seconds with \(results.count) results")
    }
    
    // MARK: - Migration Tests
    
    func testDatabaseMigration() async throws {
        // This would test database schema migrations
        // For now, just verify the current schema
        
        let dbQueue = try DatabaseQueue(path: testDbPath)
        
        try dbQueue.read { db in
            // Verify tables exist
            XCTAssertTrue(try db.tableExists("articles"))
            XCTAssertTrue(try db.tableExists("article_tags"))
            XCTAssertTrue(try db.tableExists("articles_fts"))
            
            // Verify columns
            let columns = try db.columns(in: "articles")
            let columnNames = columns.map { $0.name }
            
            XCTAssertTrue(columnNames.contains("id"))
            XCTAssertTrue(columnNames.contains("url"))
            XCTAssertTrue(columnNames.contains("title"))
            XCTAssertTrue(columnNames.contains("cloudKitRecordID"))
            XCTAssertTrue(columnNames.contains("syncStatus"))
        }
    }
    
    // MARK: - Error Recovery Tests
    
    func testErrorRecovery() async throws {
        // Test handling of various error conditions
        
        // 1. Duplicate article
        let article = Article(url: "https://test.com", title: "Duplicate Test")
        try await databaseManager.saveArticle(article)
        
        // Attempt to save again
        do {
            try await databaseManager.saveArticle(article)
            XCTFail("Should throw duplicate error")
        } catch {
            // Expected error
        }
        
        // 2. Invalid data
        var invalidArticle = Article(url: "", title: "") // Empty URL
        do {
            try await databaseManager.saveArticle(invalidArticle)
            XCTFail("Should throw validation error")
        } catch {
            // Expected error
        }
        
        // 3. Non-existent article update
        let nonExistent = Article(url: "https://fake.com", title: "Fake")
        do {
            try await databaseManager.updateArticle(nonExistent)
            XCTFail("Should throw not found error")
        } catch {
            // Expected error
        }
    }
    
    // MARK: - Tag Management Tests
    
    func testTagOperations() async throws {
        // Create articles with various tags
        var article1 = Article(url: "https://test1.com", title: "Article 1")
        article1.tags = ["Swift", "iOS", "Tutorial"]
        
        var article2 = Article(url: "https://test2.com", title: "Article 2")
        article2.tags = ["Swift", "macOS", "Guide"]
        
        var article3 = Article(url: "https://test3.com", title: "Article 3")
        article3.tags = ["JavaScript", "Web", "Tutorial"]
        
        for article in [article1, article2, article3] {
            try await databaseManager.saveArticle(article)
        }
        
        // Get all unique tags
        let allTags = try await databaseManager.getAllTags()
        XCTAssertEqual(allTags.count, 7) // All unique tags
        XCTAssertTrue(allTags.contains("Swift"))
        XCTAssertTrue(allTags.contains("Tutorial"))
        
        // Get articles by tag
        let swiftArticles = try await databaseManager.fetchArticlesByTag("Swift")
        XCTAssertEqual(swiftArticles.count, 2)
        
        let tutorialArticles = try await databaseManager.fetchArticlesByTag("Tutorial")
        XCTAssertEqual(tutorialArticles.count, 2)
    }
}

// MARK: - Extensions for Testing

extension DatabaseManager {
    func getAllTags() async throws -> Set<String> {
        guard let dbQueue = dbQueue else { throw DatabaseError.notInitialized }
        
        return try await dbQueue.read { db in
            let tags = try ArticleTag
                .select(Column("tag"), as: String.self)
                .distinct()
                .fetchAll(db)
            return Set(tags)
        }
    }
    
    func fetchArticlesByTag(_ tag: String) async throws -> [Article] {
        guard let dbQueue = dbQueue else { throw DatabaseError.notInitialized }
        
        return try await dbQueue.read { db in
            let articleIds = try ArticleTag
                .filter(Column("tag") == tag)
                .fetchAll(db)
                .map(\.articleId)
            
            var articles: [Article] = []
            for id in articleIds {
                if var article = try Article.fetchOne(db, id: id) {
                    let tags = try ArticleTag
                        .filter(Column("articleId") == article.id)
                        .fetchAll(db)
                        .map(\.tag)
                    article.tags = tags
                    articles.append(article)
                }
            }
            
            return articles
        }
    }
}