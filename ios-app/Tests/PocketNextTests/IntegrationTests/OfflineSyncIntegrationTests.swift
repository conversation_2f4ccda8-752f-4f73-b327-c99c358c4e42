import XCTest
@testable import PocketNext

class OfflineSyncIntegrationTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var offlineService: OfflineContentService!
    var syncService: CloudKitSyncService!
    
    override func setUp() async throws {
        try await super.setUp()
        
        databaseManager = DatabaseManager()
        offlineService = OfflineContentService()
        syncService = CloudKitSyncService()
        
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        // Clean up
        try await offlineService.deleteAllOfflineContent()
        
        databaseManager = nil
        offlineService = nil
        syncService = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Offline Content Sync Tests
    
    func testOfflineContentWithSync() async throws {
        // 1. Create and save article
        var article = Article(url: "https://test.com/article", title: "Offline Sync Test")
        article.content = """
            <html>
            <body>
                <h1>Test Article</h1>
                <p>This is test content with an image.</p>
                <img src="https://test.com/image.jpg">
            </body>
            </html>
            """
        
        try await databaseManager.saveArticle(article)
        
        // 2. Download for offline
        let downloadedArticle = try await offlineService.downloadArticleForOffline(article)
        
        // 3. Update in database
        try await databaseManager.updateArticle(downloadedArticle)
        
        // 4. Sync to CloudKit
        if syncService.isCloudKitAvailable {
            let recordID = try await syncService.uploadArticle(downloadedArticle)
            try await databaseManager.markAsSynced(downloadedArticle.id, cloudKitRecordID: recordID)
        }
        
        // 5. Verify offline content persists
        let offlineContent = try await offlineService.getOfflineContent(for: downloadedArticle)
        XCTAssertNotNil(offlineContent)
        XCTAssertTrue(offlineContent.contains("Test Article"))
        
        // 6. Simulate offline mode and verify access
        let fetchedArticle = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertTrue(fetchedArticle?.isDownloadedForOffline ?? false)
        XCTAssertNotNil(fetchedArticle?.downloadedAt)
    }
    
    // MARK: - Sync Conflict Resolution Tests
    
    func testSyncConflictResolution() async throws {
        // 1. Create article locally
        var localArticle = Article(url: "https://test.com", title: "Local Version")
        localArticle.isRead = true
        localArticle.readProgress = 0.5
        localArticle.savedAt = Date().addingTimeInterval(-3600) // 1 hour ago
        
        try await databaseManager.saveArticle(localArticle)
        
        // 2. Simulate remote version
        let remoteRecord = CKRecord(recordType: "Article")
        remoteRecord["id"] = localArticle.id
        remoteRecord["url"] = localArticle.url
        remoteRecord["title"] = "Remote Version"
        remoteRecord["isRead"] = false
        remoteRecord["readProgress"] = 0.8
        remoteRecord["savedAt"] = Date() // More recent
        
        // 3. Resolve conflict with merge strategy
        let resolved = try await syncService.resolveConflict(
            local: localArticle,
            remote: remoteRecord,
            resolution: .merge
        )
        
        // 4. Update local database
        try await databaseManager.updateArticle(resolved)
        
        // 5. Verify merged result
        let finalArticle = try await databaseManager.fetchArticle(id: localArticle.id)
        XCTAssertEqual(finalArticle?.title, "Remote Version") // More recent
        XCTAssertTrue(finalArticle?.isRead ?? false) // Keep true value
        XCTAssertEqual(finalArticle?.readProgress, 0.8) // Higher progress
    }
    
    // MARK: - Batch Sync Tests
    
    func testBatchOfflineSync() async throws {
        // 1. Create multiple articles
        let articles = (1...10).map { index in
            var article = Article(
                url: "https://test.com/\(index)",
                title: "Batch Article \(index)"
            )
            article.content = "<p>Content for article \(index)</p>"
            return article
        }
        
        // 2. Save all articles
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 3. Download first 5 for offline
        let articlesToDownload = Array(articles.prefix(5))
        let downloadResults = await offlineService.downloadArticlesForOffline(articlesToDownload)
        
        // 4. Verify download results
        var successCount = 0
        for result in downloadResults {
            if case .success = result {
                successCount += 1
            }
        }
        XCTAssertEqual(successCount, 5)
        
        // 5. Sync all articles
        let articlesNeedingSync = try await databaseManager.fetchArticlesNeedingSync()
        XCTAssertGreaterThanOrEqual(articlesNeedingSync.count, 10)
        
        if syncService.isCloudKitAvailable {
            let uploadResults = await syncService.uploadArticles(articlesNeedingSync)
            
            // 6. Mark synced articles
            for (article, result) in zip(articlesNeedingSync, uploadResults) {
                if case .success(let recordID) = result {
                    try await databaseManager.markAsSynced(article.id, cloudKitRecordID: recordID)
                }
            }
        }
        
        // 7. Verify offline content still accessible
        for article in articlesToDownload {
            let content = try? await offlineService.getOfflineContent(for: article)
            XCTAssertNotNil(content)
        }
    }
    
    // MARK: - Network Failure Recovery Tests
    
    func testSyncWithNetworkFailures() async throws {
        // 1. Create articles
        let articles = (1...5).map { index in
            Article(url: "https://test.com/\(index)", title: "Network Test \(index)")
        }
        
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Simulate partial sync failure
        var syncedCount = 0
        var failedArticles: [Article] = []
        
        for (index, article) in articles.enumerated() {
            if index < 3 {
                // Simulate successful sync
                if syncService.isCloudKitAvailable {
                    do {
                        let recordID = try await syncService.uploadArticle(article)
                        try await databaseManager.markAsSynced(article.id, cloudKitRecordID: recordID)
                        syncedCount += 1
                    } catch {
                        failedArticles.append(article)
                    }
                }
            } else {
                // Simulate network failure
                failedArticles.append(article)
            }
        }
        
        // 3. Verify partial sync state
        let needingSync = try await databaseManager.fetchArticlesNeedingSync()
        XCTAssertEqual(needingSync.count, failedArticles.count)
        
        // 4. Retry failed articles
        for article in failedArticles {
            article.syncStatus = .pending
            try await databaseManager.updateArticle(article)
        }
        
        // 5. Verify retry queue
        let retryQueue = try await databaseManager.fetchArticlesNeedingSync()
        XCTAssertEqual(retryQueue.count, failedArticles.count)
    }
    
    // MARK: - Storage Management Tests
    
    func testOfflineStorageManagement() async throws {
        // 1. Create and download multiple articles
        let articles = (1...20).map { index in
            var article = Article(
                url: "https://test.com/\(index)",
                title: "Storage Test \(index)"
            )
            article.content = String(repeating: "Content ", count: 1000) // ~8KB each
            return article
        }
        
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Download all for offline
        _ = await offlineService.downloadArticlesForOffline(articles)
        
        // 3. Check storage size
        let storageSize = try await offlineService.getOfflineStorageSize()
        XCTAssertGreaterThan(storageSize, 100_000) // At least 100KB
        
        // 4. Clean up old content (simulate old downloads)
        let oldArticles = Array(articles.prefix(10))
        for var article in oldArticles {
            article.downloadedAt = Date().addingTimeInterval(-40 * 24 * 60 * 60) // 40 days ago
            try await databaseManager.updateArticle(article)
        }
        
        // 5. Run cleanup
        let deletedCount = try await offlineService.cleanupOldOfflineContent(olderThan: 30)
        XCTAssertGreaterThanOrEqual(deletedCount, 10)
        
        // 6. Verify storage reduced
        let newStorageSize = try await offlineService.getOfflineStorageSize()
        XCTAssertLessThan(newStorageSize, storageSize)
        
        // 7. Verify articles updated
        for article in oldArticles {
            let updated = try await databaseManager.fetchArticle(id: article.id)
            XCTAssertFalse(updated?.isDownloadedForOffline ?? true)
        }
    }
    
    // MARK: - Widget Data Sync Tests
    
    func testWidgetDataSync() async throws {
        // 1. Create articles with various states
        var articles: [Article] = []
        
        for i in 1...10 {
            var article = Article(
                url: "https://test.com/\(i)",
                title: "Widget Article \(i)"
            )
            article.isRead = i <= 5
            article.isFavorite = i % 2 == 0
            article.savedAt = Date().addingTimeInterval(Double(-i * 3600))
            articles.append(article)
        }
        
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Get widget data
        let widgetData = try await databaseManager.getWidgetData()
        
        XCTAssertEqual(widgetData.totalCount, 10)
        XCTAssertEqual(widgetData.unreadCount, 5)
        XCTAssertEqual(widgetData.recentArticles.count, min(5, articles.count))
        
        // 3. Verify widget data is most recent unread
        let recentUnread = widgetData.recentArticles
        XCTAssertTrue(recentUnread.allSatisfy { !$0.isRead })
        
        // 4. Update widget cache
        try await databaseManager.updateWidgetCache(widgetData)
        
        // 5. Verify cache exists
        let cachedData = try await databaseManager.getCachedWidgetData()
        XCTAssertNotNil(cachedData)
        XCTAssertEqual(cachedData?.unreadCount, widgetData.unreadCount)
    }
}

// MARK: - Widget Data Extension

extension DatabaseManager {
    struct WidgetData {
        let totalCount: Int
        let unreadCount: Int
        let recentArticles: [Article]
        let lastUpdated: Date
    }
    
    func getWidgetData() async throws -> WidgetData {
        let allArticles = try await fetchAllArticles()
        let unreadArticles = allArticles.filter { !$0.isRead }
        let recentUnread = Array(unreadArticles.sorted { $0.savedAt > $1.savedAt }.prefix(5))
        
        return WidgetData(
            totalCount: allArticles.count,
            unreadCount: unreadArticles.count,
            recentArticles: recentUnread,
            lastUpdated: Date()
        )
    }
    
    func updateWidgetCache(_ data: WidgetData) async throws {
        // Implementation would save to shared container
    }
    
    func getCachedWidgetData() async throws -> WidgetData? {
        // Implementation would read from shared container
        return nil
    }
}