import XCTest
import GRDB
@testable import PocketNext

/// Integration tests that use real database without mocks
/// These tests verify actual database operations, concurrency, and data persistence
class RealDatabaseIntegrationTests: XCTestCase {
    
    var databaseManager: DatabaseManager!
    var testDatabasePath: String!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create a temporary database for testing
        let tempDir = NSTemporaryDirectory()
        testDatabasePath = "\(tempDir)/test_database_\(UUID().uuidString).sqlite"
        
        // Initialize real database manager
        databaseManager = DatabaseManager()
        
        // Override the database path for testing
        // Note: We'll need to modify DatabaseManager to accept custom path for testing
        try await initializeTestDatabase()
    }
    
    override func tearDown() async throws {
        // Clean up test database
        try? FileManager.default.removeItem(atPath: testDatabasePath)
        databaseManager = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Database Initialization Tests
    
    func testDatabaseInitialization() async throws {
        // Database should be initialized successfully
        XCTAssertTrue(databaseManager.isInitialized)
    }
    
    func testDatabaseSchema() async throws {
        // Verify database tables exist
        let tableNames = try await databaseManager.withDatabase { db in
            try String.fetchAll(db, sql: """
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
        }
        
        XCTAssertTrue(tableNames.contains("articles"))
        XCTAssertTrue(tableNames.contains("tags"))
        XCTAssertTrue(tableNames.contains("articleTags"))
    }
    
    // MARK: - CRUD Operations Tests
    
    func testCreateAndFetchArticle() async throws {
        // Create article
        let article = Article(
            url: "https://test.com/article",
            title: "Test Article",
            content: "Test content",
            summary: "Test summary",
            author: "Test Author",
            publishDate: Date(),
            readingTime: 5,
            tags: ["swift", "testing"]
        )
        
        // Save to database
        try await databaseManager.save(article)
        
        // Fetch back
        let fetchedArticle = try await databaseManager.fetchArticle(byId: article.id)
        
        // Verify
        XCTAssertNotNil(fetchedArticle)
        XCTAssertEqual(fetchedArticle?.id, article.id)
        XCTAssertEqual(fetchedArticle?.url, article.url)
        XCTAssertEqual(fetchedArticle?.title, article.title)
        XCTAssertEqual(fetchedArticle?.content, article.content)
        XCTAssertEqual(fetchedArticle?.tags, article.tags)
    }
    
    func testUpdateArticle() async throws {
        // Create and save article
        var article = Article(url: "https://test.com", title: "Original Title")
        try await databaseManager.save(article)
        
        // Update article
        article.title = "Updated Title"
        article.isRead = true
        article.readProgress = 0.5
        
        try await databaseManager.update(article)
        
        // Fetch and verify
        let updated = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertEqual(updated?.title, "Updated Title")
        XCTAssertTrue(updated?.isRead ?? false)
        XCTAssertEqual(updated?.readProgress, 0.5)
    }
    
    func testDeleteArticle() async throws {
        // Create and save article
        let article = Article(url: "https://test.com", title: "To Delete")
        try await databaseManager.save(article)
        
        // Verify it exists
        let exists = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertNotNil(exists)
        
        // Delete
        try await databaseManager.delete(article.id)
        
        // Verify it's gone
        let deleted = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertNil(deleted)
    }
    
    // MARK: - Bulk Operations Tests
    
    func testBulkInsert() async throws {
        // Create multiple articles
        let articles = (1...100).map { index in
            Article(
                url: "https://test.com/article\(index)",
                title: "Article \(index)",
                readingTime: index
            )
        }
        
        // Measure bulk insert performance
        let startTime = Date()
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let duration = Date().timeIntervalSince(startTime)
        
        // Verify all inserted
        let count = try await databaseManager.withDatabase { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertEqual(count, 100)
        XCTAssertLessThan(duration, 5.0, "Bulk insert took too long: \(duration)s")
    }
    
    // MARK: - Search and Query Tests
    
    func testSearchArticles() async throws {
        // Create articles with different content
        let articles = [
            Article(url: "https://test1.com", title: "SwiftUI Tutorial", content: "Learn SwiftUI basics"),
            Article(url: "https://test2.com", title: "UIKit Guide", content: "Master UIKit development"),
            Article(url: "https://test3.com", title: "Swift Concurrency", content: "Understanding async/await")
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Search for "Swift"
        let results = try await databaseManager.searchArticles(query: "Swift")
        
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.contains { $0.title.contains("Swift") })
    }
    
    func testFetchRecentArticles() async throws {
        // Create articles with different saved dates
        let now = Date()
        let articles = [
            Article(url: "https://test1.com", title: "Today", savedAt: now),
            Article(url: "https://test2.com", title: "Yesterday", savedAt: now.addingTimeInterval(-86400)),
            Article(url: "https://test3.com", title: "Last Week", savedAt: now.addingTimeInterval(-604800))
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Fetch recent (limit 2)
        let recent = try await databaseManager.fetchRecentArticles(limit: 2)
        
        XCTAssertEqual(recent.count, 2)
        XCTAssertEqual(recent[0].title, "Today")
        XCTAssertEqual(recent[1].title, "Yesterday")
    }
    
    // MARK: - Concurrency Tests
    
    func testConcurrentReads() async throws {
        // Create test data
        let article = Article(url: "https://test.com", title: "Concurrent Test")
        try await databaseManager.save(article)
        
        // Perform concurrent reads
        await withTaskGroup(of: Article?.self) { group in
            for _ in 0..<10 {
                group.addTask {
                    try? await self.databaseManager.fetchArticle(byId: article.id)
                }
            }
            
            var results: [Article?] = []
            for await result in group {
                results.append(result)
            }
            
            // All reads should succeed
            XCTAssertEqual(results.count, 10)
            XCTAssertTrue(results.allSatisfy { $0 != nil })
        }
    }
    
    func testConcurrentWrites() async throws {
        // Perform concurrent inserts
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<10 {
                group.addTask {
                    let article = Article(
                        url: "https://test.com/concurrent\(i)",
                        title: "Concurrent Article \(i)"
                    )
                    try? await self.databaseManager.save(article)
                }
            }
        }
        
        // Verify all were inserted
        let count = try await databaseManager.withDatabase { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertEqual(count, 10)
    }
    
    // MARK: - Transaction Tests
    
    func testTransactionRollback() async throws {
        // Start with one article
        let article1 = Article(url: "https://test1.com", title: "Article 1")
        try await databaseManager.save(article1)
        
        // Try to save multiple articles in a transaction that fails
        do {
            try await databaseManager.withDatabase { db in
                try db.inTransaction { db in
                    // Save article 2
                    let article2 = Article(url: "https://test2.com", title: "Article 2")
                    try article2.insert(db)
                    
                    // Force an error
                    throw DatabaseError.notInitialized
                }
            }
            
            XCTFail("Transaction should have failed")
        } catch {
            // Expected error
        }
        
        // Verify only original article exists
        let count = try await databaseManager.withDatabase { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertEqual(count, 1)
    }
    
    // MARK: - Tag Management Tests
    
    func testTagOperations() async throws {
        // Create article with tags
        let article = Article(
            url: "https://test.com",
            title: "Tagged Article",
            tags: ["swift", "ios", "testing"]
        )
        
        try await databaseManager.save(article)
        
        // Fetch and verify tags
        let fetched = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertEqual(fetched?.tags.sorted(), ["ios", "swift", "testing"])
        
        // Update tags
        var updated = fetched!
        updated.tags = ["swift", "macos"] // Remove ios and testing, add macos
        try await databaseManager.update(updated)
        
        // Verify tag update
        let reFetched = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertEqual(reFetched?.tags.sorted(), ["macos", "swift"])
    }
    
    // MARK: - Archive Operations Tests
    
    func testArchiveUnarchive() async throws {
        // Create article
        let article = Article(url: "https://test.com", title: "Archive Test")
        try await databaseManager.save(article)
        
        // Archive it
        try await databaseManager.archive(article.id)
        
        // Verify archived
        let archived = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertTrue(archived?.isArchived ?? false)
        
        // Unarchive it
        try await databaseManager.unarchive(article.id)
        
        // Verify unarchived
        let unarchived = try await databaseManager.fetchArticle(byId: article.id)
        XCTAssertFalse(unarchived?.isArchived ?? true)
    }
    
    // MARK: - Statistics Tests
    
    func testGetStatistics() async throws {
        // Create test data
        let articles = [
            Article(url: "https://test1.com", title: "Read", isRead: true, readingTime: 5),
            Article(url: "https://test2.com", title: "Unread 1", isRead: false, readingTime: 10),
            Article(url: "https://test3.com", title: "Unread 2", isRead: false, readingTime: 15),
            Article(url: "https://test4.com", title: "Archived", isArchived: true, readingTime: 20)
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Get statistics
        let stats = try await databaseManager.getStatistics()
        
        XCTAssertEqual(stats.totalArticles, 4)
        XCTAssertEqual(stats.unreadCount, 2)
        XCTAssertEqual(stats.totalReadingTime, 50)
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() async throws {
        // Create 1000 articles
        let articles = (1...1000).map { index in
            Article(
                url: "https://test.com/article\(index)",
                title: "Article \(index)",
                content: "This is the content for article number \(index). Some have Swift, some have iOS."
            )
        }
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Measure search performance
        let startTime = Date()
        let results = try await databaseManager.searchArticles(query: "article")
        let duration = Date().timeIntervalSince(startTime)
        
        XCTAssertEqual(results.count, 1000)
        XCTAssertLessThan(duration, 1.0, "Search took too long: \(duration)s")
    }
    
    // MARK: - Error Handling Tests
    
    func testInvalidOperations() async throws {
        // Try to fetch non-existent article
        let nonExistent = try await databaseManager.fetchArticle(byId: "invalid-id")
        XCTAssertNil(nonExistent)
        
        // Try to update non-existent article
        let fakeArticle = Article(url: "https://fake.com", title: "Fake")
        
        do {
            try await databaseManager.update(fakeArticle)
            XCTFail("Update should fail for non-existent article")
        } catch {
            // Expected error
        }
    }
    
    // MARK: - Helper Methods
    
    private func initializeTestDatabase() async throws {
        // Initialize database with test path
        // Note: This requires modifying DatabaseManager to accept custom path
        // For now, we'll use the default initialization
        try await databaseManager.initialize()
    }
}

// MARK: - Database Statistics Extension

extension RealDatabaseIntegrationTests {
    struct DatabaseStatistics {
        let totalArticles: Int
        let unreadCount: Int
        let totalReadingTime: Int
    }
}

// MARK: - DatabaseManager Test Extensions

extension DatabaseManager {
    /// Get database statistics for testing
    func getStatistics() async throws -> RealDatabaseIntegrationTests.DatabaseStatistics {
        try await withDatabase { db in
            let total = try Article.fetchCount(db)
            let unread = try Article.filter(!Article.Columns.isRead && !Article.Columns.isArchived).fetchCount(db)
            let totalTime = try Article.select(sum(Article.Columns.readingTime)).fetchOne(db) ?? 0
            
            return RealDatabaseIntegrationTests.DatabaseStatistics(
                totalArticles: total,
                unreadCount: unread,
                totalReadingTime: totalTime
            )
        }
    }
}