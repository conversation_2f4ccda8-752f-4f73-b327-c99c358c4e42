import XCTest
import WidgetKit
@testable import PocketNext
@testable import PocketNextWidget

class WidgetIntegrationTests: XCTestCase {
    var widgetProvider: ArticleWidgetProvider!
    var databaseManager: DatabaseManager!
    var sharedContainer: URL!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Set up shared container
        sharedContainer = FileManager.default.containerURL(
            forSecurityApplicationGroupIdentifier: "group.com.pocketnext"
        )
        
        databaseManager = DatabaseManager()
        widgetProvider = ArticleWidgetProvider()
        
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        widgetProvider = nil
        databaseManager = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Widget Timeline Tests
    
    func testWidgetTimelineGeneration() async throws {
        // 1. Create test articles
        let articles = createTestArticles(count: 10)
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Generate timeline
        let context = TimelineProviderContext()
        let timeline = await widgetProvider.getTimeline(in: context)
        
        // 3. Verify timeline entries
        XCTAssertFalse(timeline.entries.isEmpty)
        XCTAssertGreaterThanOrEqual(timeline.entries.count, 1)
        
        // 4. Check entry content
        if let firstEntry = timeline.entries.first {
            XCTAssertNotNil(firstEntry.date)
            XCTAssertFalse(firstEntry.articles.isEmpty)
            XCTAssertLessThanOrEqual(firstEntry.articles.count, 5) // Widget shows max 5
        }
        
        // 5. Verify refresh policy
        switch timeline.policy {
        case .atEnd:
            // Expected for normal operation
            break
        case .after(let date):
            XCTAssertGreaterThan(date, Date())
        case .never:
            XCTFail("Widget should refresh")
        @unknown default:
            break
        }
    }
    
    // MARK: - Widget Data Sharing Tests
    
    func testWidgetDataSharing() async throws {
        // 1. Save articles in main app
        var unreadArticles: [Article] = []
        for i in 1...5 {
            var article = Article(
                url: "https://test.com/\(i)",
                title: "Unread Article \(i)"
            )
            article.isRead = false
            article.savedAt = Date().addingTimeInterval(Double(-i * 3600))
            unreadArticles.append(article)
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Export data for widget
        let widgetData = WidgetData(
            unreadCount: 5,
            recentArticles: unreadArticles.map { article in
                WidgetArticle(
                    id: article.id,
                    title: article.title,
                    url: article.url,
                    author: article.author,
                    savedAt: article.savedAt,
                    readingTime: article.readingTime
                )
            },
            lastUpdated: Date()
        )
        
        try await saveWidgetData(widgetData)
        
        // 3. Read from widget side
        let loadedData = try await loadWidgetData()
        
        XCTAssertNotNil(loadedData)
        XCTAssertEqual(loadedData?.unreadCount, 5)
        XCTAssertEqual(loadedData?.recentArticles.count, 5)
    }
    
    // MARK: - Widget Configuration Tests
    
    func testWidgetConfigurations() async throws {
        // Test different widget families
        let families: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]
        
        for family in families {
            // 1. Create configuration
            let entry = ArticleWidgetEntry(
                date: Date(),
                articles: createWidgetArticles(count: 5),
                configuration: ConfigurationIntent()
            )
            
            // 2. Verify view renders correctly for each size
            let view = ArticleWidgetEntryView(entry: entry)
                .widgetFamily(family)
            
            // 3. Test layout constraints
            switch family {
            case .systemSmall:
                // Small widget shows only count
                XCTAssertNotNil(view)
            case .systemMedium:
                // Medium shows 2-3 articles
                XCTAssertNotNil(view)
            case .systemLarge:
                // Large shows 4-5 articles
                XCTAssertNotNil(view)
            default:
                break
            }
        }
    }
    
    // MARK: - Widget Interaction Tests
    
    func testWidgetDeepLinks() async throws {
        // 1. Create articles with deep links
        let articles = createTestArticles(count: 3)
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Generate widget URLs
        let widgetArticles = articles.map { article in
            WidgetArticle(
                id: article.id,
                title: article.title,
                url: article.url,
                author: article.author,
                savedAt: article.savedAt,
                readingTime: article.readingTime
            )
        }
        
        // 3. Test URL generation
        for widgetArticle in widgetArticles {
            let url = widgetArticle.deepLinkURL
            XCTAssertNotNil(url)
            XCTAssertTrue(url.absoluteString.contains("pocketnext://"))
            XCTAssertTrue(url.absoluteString.contains(widgetArticle.id))
        }
        
        // 4. Test URL handling
        if let testURL = URL(string: "pocketnext://article/\(articles[0].id)") {
            let handled = await handleWidgetURL(testURL)
            XCTAssertTrue(handled)
        }
    }
    
    // MARK: - Widget Update Tests
    
    func testWidgetReloadTriggers() async throws {
        // 1. Initial state
        let initialArticles = createTestArticles(count: 3)
        for article in initialArticles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Trigger widget reload
        WidgetCenter.shared.reloadAllTimelines()
        
        // 3. Add new article
        let newArticle = Article(
            url: "https://test.com/new",
            title: "Breaking News Article"
        )
        try await databaseManager.saveArticle(newArticle)
        
        // 4. Verify widget receives update notification
        NotificationCenter.default.post(
            name: .articlesUpdated,
            object: nil,
            userInfo: ["reason": "newArticle"]
        )
        
        // Widget should reload automatically
        // In real app, this would trigger WidgetCenter.shared.reloadAllTimelines()
    }
    
    // MARK: - Widget Performance Tests
    
    func testWidgetDataLoadPerformance() async throws {
        // 1. Create large dataset
        let articles = createTestArticles(count: 100)
        for article in articles {
            try await databaseManager.saveArticle(article)
        }
        
        // 2. Measure widget data preparation
        let startTime = Date()
        
        let widgetData = try await prepareWidgetData()
        
        let endTime = Date()
        let loadTime = endTime.timeIntervalSince(startTime)
        
        // 3. Verify performance
        XCTAssertLessThan(loadTime, 1.0) // Should load in under 1 second
        XCTAssertEqual(widgetData.recentArticles.count, 5) // Limited to 5 for widget
        
        print("Widget data loaded in \(loadTime) seconds")
    }
    
    // MARK: - Helper Methods
    
    private func createTestArticles(count: Int) -> [Article] {
        return (1...count).map { index in
            var article = Article(
                url: "https://test.com/article\(index)",
                title: "Test Article \(index)"
            )
            article.author = "Author \(index)"
            article.savedAt = Date().addingTimeInterval(Double(-index * 3600))
            article.readingTime = Int.random(in: 3...15)
            article.isRead = index > count / 2
            return article
        }
    }
    
    private func createWidgetArticles(count: Int) -> [WidgetArticle] {
        return (1...count).map { index in
            WidgetArticle(
                id: UUID().uuidString,
                title: "Widget Article \(index)",
                url: "https://test.com/\(index)",
                author: "Author \(index)",
                savedAt: Date().addingTimeInterval(Double(-index * 3600)),
                readingTime: Int.random(in: 3...10)
            )
        }
    }
    
    private func saveWidgetData(_ data: WidgetData) async throws {
        let encoder = JSONEncoder()
        let encoded = try encoder.encode(data)
        
        let url = sharedContainer.appendingPathComponent("widget_data.json")
        try encoded.write(to: url)
    }
    
    private func loadWidgetData() async throws -> WidgetData? {
        let url = sharedContainer.appendingPathComponent("widget_data.json")
        
        guard FileManager.default.fileExists(atPath: url.path) else {
            return nil
        }
        
        let data = try Data(contentsOf: url)
        let decoder = JSONDecoder()
        return try decoder.decode(WidgetData.self, from: data)
    }
    
    private func prepareWidgetData() async throws -> WidgetData {
        let articles = try await databaseManager.fetchAllArticles()
        let unread = articles.filter { !$0.isRead }
        let recent = Array(unread.sorted { $0.savedAt > $1.savedAt }.prefix(5))
        
        return WidgetData(
            unreadCount: unread.count,
            recentArticles: recent.map { article in
                WidgetArticle(
                    id: article.id,
                    title: article.title,
                    url: article.url,
                    author: article.author,
                    savedAt: article.savedAt,
                    readingTime: article.readingTime
                )
            },
            lastUpdated: Date()
        )
    }
    
    private func handleWidgetURL(_ url: URL) async -> Bool {
        guard url.scheme == "pocketnext" else { return false }
        
        if url.host == "article",
           let articleId = url.pathComponents.last {
            // Handle article navigation
            NotificationCenter.default.post(
                name: .openArticle,
                object: nil,
                userInfo: ["articleId": articleId]
            )
            return true
        }
        
        return false
    }
}

// MARK: - Widget Support Types

struct WidgetData: Codable {
    let unreadCount: Int
    let recentArticles: [WidgetArticle]
    let lastUpdated: Date
}

struct WidgetArticle: Codable, Identifiable {
    let id: String
    let title: String
    let url: String
    let author: String?
    let savedAt: Date
    let readingTime: Int?
    
    var deepLinkURL: URL {
        URL(string: "pocketnext://article/\(id)")!
    }
}

// MARK: - Mock Widget Provider

class ArticleWidgetProvider: TimelineProvider {
    typealias Entry = ArticleWidgetEntry
    
    func placeholder(in context: Context) -> ArticleWidgetEntry {
        ArticleWidgetEntry(
            date: Date(),
            articles: [],
            configuration: ConfigurationIntent()
        )
    }
    
    func getSnapshot(
        for configuration: ConfigurationIntent,
        in context: Context,
        completion: @escaping (ArticleWidgetEntry) -> Void
    ) {
        let entry = ArticleWidgetEntry(
            date: Date(),
            articles: [],
            configuration: configuration
        )
        completion(entry)
    }
    
    func getTimeline(in context: TimelineProviderContext) async -> Timeline<ArticleWidgetEntry> {
        // Mock implementation
        let entry = ArticleWidgetEntry(
            date: Date(),
            articles: [],
            configuration: ConfigurationIntent()
        )
        
        let timeline = Timeline(
            entries: [entry],
            policy: .after(Date().addingTimeInterval(3600))
        )
        
        return timeline
    }
}

struct ArticleWidgetEntry: TimelineEntry {
    let date: Date
    let articles: [WidgetArticle]
    let configuration: ConfigurationIntent
}

struct ArticleWidgetEntryView: View {
    let entry: ArticleWidgetEntry
    
    var body: some View {
        Text("Widget View")
    }
}

// Mock types
struct ConfigurationIntent {}
struct TimelineProviderContext {}