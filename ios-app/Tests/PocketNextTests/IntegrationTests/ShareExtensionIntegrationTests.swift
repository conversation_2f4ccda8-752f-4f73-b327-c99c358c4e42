import XCTest
import MobileCoreServices
import UniformTypeIdentifiers
@testable import PocketNext
@testable import ShareExtension

class ShareExtensionIntegrationTests: XCTestCase {
    var shareViewController: ShareViewController!
    var databaseManager: DatabaseManager!
    var extensionContext: MockExtensionContext!
    
    override func setUp() async throws {
        try await super.setUp()
        
        databaseManager = DatabaseManager()
        extensionContext = MockExtensionContext()
        shareViewController = ShareViewController()
        shareViewController.extensionContext = extensionContext
        
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        shareViewController = nil
        databaseManager = nil
        extensionContext = nil
        
        try await super.tearDown()
    }
    
    // MARK: - URL Sharing Tests
    
    func testShareURLFromSafari() async throws {
        // 1. Create URL item
        let testURL = URL(string: "https://example.com/article")!
        let urlItem = MockNSExtensionItem(
            attachments: [
                MockItemProvider(url: testURL)
            ]
        )
        
        extensionContext.inputItems = [urlItem]
        
        // 2. Process share
        await shareViewController.handleShare()
        
        // 3. Verify article created
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 1)
        
        let article = articles.first
        XCTAssertEqual(article?.url, testURL.absoluteString)
        XCTAssertNotNil(article?.title)
    }
    
    func testShareMultipleURLs() async throws {
        // 1. Create multiple URL items
        let urls = [
            "https://example.com/article1",
            "https://example.com/article2",
            "https://example.com/article3"
        ].compactMap { URL(string: $0) }
        
        let items = urls.map { url in
            MockNSExtensionItem(attachments: [MockItemProvider(url: url)])
        }
        
        extensionContext.inputItems = items
        
        // 2. Process shares
        await shareViewController.handleShare()
        
        // 3. Verify all articles created
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 3)
        
        for (index, article) in articles.enumerated() {
            XCTAssertEqual(article.url, urls[index].absoluteString)
        }
    }
    
    // MARK: - Text Sharing Tests
    
    func testSharePlainText() async throws {
        // 1. Create text item with URL
        let text = "Check out this article: https://example.com/great-article"
        let textItem = MockNSExtensionItem(
            attachments: [
                MockItemProvider(text: text)
            ]
        )
        
        extensionContext.inputItems = [textItem]
        
        // 2. Process share
        await shareViewController.handleShare()
        
        // 3. Verify URL extracted and article created
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 1)
        XCTAssertEqual(articles.first?.url, "https://example.com/great-article")
    }
    
    func testShareTextWithMultipleURLs() async throws {
        // 1. Create text with multiple URLs
        let text = """
        Here are some interesting articles:
        - https://example.com/article1
        - https://example.com/article2
        Also check out https://example.com/article3
        """
        
        let textItem = MockNSExtensionItem(
            attachments: [MockItemProvider(text: text)]
        )
        
        extensionContext.inputItems = [textItem]
        
        // 2. Process share
        await shareViewController.handleShare()
        
        // 3. Verify all URLs extracted
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 3)
    }
    
    // MARK: - Web Archive Tests
    
    func testShareWebArchive() async throws {
        // 1. Create web archive data
        let htmlContent = """
        <html>
        <head><title>Test Article</title></head>
        <body>
            <h1>Test Article Title</h1>
            <p>This is the article content.</p>
        </body>
        </html>
        """
        
        let webArchiveData = createMockWebArchive(html: htmlContent, url: "https://example.com/article")
        let webArchiveItem = MockNSExtensionItem(
            attachments: [
                MockItemProvider(data: webArchiveData, typeIdentifier: "com.apple.webarchive")
            ]
        )
        
        extensionContext.inputItems = [webArchiveItem]
        
        // 2. Process share
        await shareViewController.handleShare()
        
        // 3. Verify article created with content
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 1)
        
        let article = articles.first
        XCTAssertEqual(article?.title, "Test Article Title")
        XCTAssertNotNil(article?.content)
        XCTAssertTrue(article?.content?.contains("article content") ?? false)
    }
    
    // MARK: - Metadata Extraction Tests
    
    func testMetadataExtraction() async throws {
        // 1. Create item with metadata
        let url = URL(string: "https://example.com/article")!
        let item = MockNSExtensionItem(
            attachments: [MockItemProvider(url: url)]
        )
        
        // Add metadata
        item.attributedContentText = NSAttributedString(string: "Article Title - Example.com")
        item.userInfo = [
            "author": "John Doe",
            "publishDate": Date()
        ]
        
        extensionContext.inputItems = [item]
        
        // 2. Process share
        await shareViewController.handleShare()
        
        // 3. Verify metadata extracted
        let articles = try await databaseManager.fetchAllArticles()
        let article = articles.first
        
        XCTAssertNotNil(article)
        XCTAssertTrue(article?.title.contains("Article Title") ?? false)
        XCTAssertEqual(article?.author, "John Doe")
        XCTAssertNotNil(article?.publishDate)
    }
    
    // MARK: - Error Handling Tests
    
    func testInvalidURLHandling() async throws {
        // 1. Create item with invalid URL
        let text = "This text has no valid URL"
        let textItem = MockNSExtensionItem(
            attachments: [MockItemProvider(text: text)]
        )
        
        extensionContext.inputItems = [textItem]
        
        // 2. Process share
        await shareViewController.handleShare()
        
        // 3. Verify no article created
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 0)
        
        // 4. Verify error shown
        XCTAssertTrue(shareViewController.didShowError)
    }
    
    func testDuplicateURLHandling() async throws {
        // 1. Save initial article
        let url = "https://example.com/article"
        let existingArticle = Article(url: url, title: "Existing Article")
        try await databaseManager.saveArticle(existingArticle)
        
        // 2. Try to share same URL
        let urlItem = MockNSExtensionItem(
            attachments: [
                MockItemProvider(url: URL(string: url)!)
            ]
        )
        
        extensionContext.inputItems = [urlItem]
        
        // 3. Process share
        await shareViewController.handleShare()
        
        // 4. Verify only one article exists
        let articles = try await databaseManager.fetchAllArticles()
        XCTAssertEqual(articles.count, 1)
        
        // 5. Verify duplicate message shown
        XCTAssertTrue(shareViewController.didShowDuplicateMessage)
    }
    
    // MARK: - Performance Tests
    
    func testSharePerformance() async throws {
        // Measure time to share and save an article
        let url = URL(string: "https://example.com/article")!
        let urlItem = MockNSExtensionItem(
            attachments: [MockItemProvider(url: url)]
        )
        
        extensionContext.inputItems = [urlItem]
        
        let startTime = Date()
        await shareViewController.handleShare()
        let endTime = Date()
        
        let processingTime = endTime.timeIntervalSince(startTime)
        
        XCTAssertLessThan(processingTime, 2.0) // Should complete in under 2 seconds
        print("Share processed in \(processingTime) seconds")
    }
    
    // MARK: - UI State Tests
    
    func testLoadingStateTransitions() async throws {
        // 1. Initial state
        XCTAssertFalse(shareViewController.isLoading)
        
        // 2. Create share item
        let url = URL(string: "https://example.com/article")!
        let urlItem = MockNSExtensionItem(
            attachments: [MockItemProvider(url: url)]
        )
        
        extensionContext.inputItems = [urlItem]
        
        // 3. Track loading states
        var loadingStates: [Bool] = []
        let observation = shareViewController.observe(\.isLoading) { _, _ in
            loadingStates.append(self.shareViewController.isLoading)
        }
        
        // 4. Process share
        await shareViewController.handleShare()
        
        // 5. Verify loading state transitions
        XCTAssertTrue(loadingStates.contains(true)) // Was loading
        XCTAssertFalse(shareViewController.isLoading) // Not loading anymore
        
        observation.invalidate()
    }
    
    // MARK: - Helper Methods
    
    private func createMockWebArchive(html: String, url: String) -> Data {
        // Create a simple web archive structure
        let archive: [String: Any] = [
            "WebMainResource": [
                "WebResourceURL": url,
                "WebResourceMIMEType": "text/html",
                "WebResourceTextEncodingName": "UTF-8",
                "WebResourceData": html.data(using: .utf8)!
            ]
        ]
        
        return try! PropertyListSerialization.data(
            fromPropertyList: archive,
            format: .binary,
            options: 0
        )
    }
}

// MARK: - Mock Classes

class MockExtensionContext: NSExtensionContext {
    var inputItems: [NSExtensionItem] = []
    var didCompleteRequest = false
    var didCancelRequest = false
    
    override var inputItems: [Any]? {
        return self.inputItems
    }
    
    override func completeRequest(returningItems items: [Any]?, completionHandler: ((Bool) -> Void)? = nil) {
        didCompleteRequest = true
        completionHandler?(true)
    }
    
    override func cancelRequest(withError error: Error) {
        didCancelRequest = true
    }
}

class MockNSExtensionItem: NSExtensionItem {
    private var _attachments: [NSItemProvider]?
    
    init(attachments: [NSItemProvider]) {
        self._attachments = attachments
        super.init()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    override var attachments: [NSItemProvider]? {
        get { _attachments }
        set { _attachments = newValue }
    }
}

class MockItemProvider: NSItemProvider {
    private let url: URL?
    private let text: String?
    private let data: Data?
    private let typeId: String?
    
    init(url: URL) {
        self.url = url
        self.text = nil
        self.data = nil
        self.typeId = nil
        super.init()
    }
    
    init(text: String) {
        self.text = text
        self.url = nil
        self.data = nil
        self.typeId = nil
        super.init()
    }
    
    init(data: Data, typeIdentifier: String) {
        self.data = data
        self.typeId = typeIdentifier
        self.url = nil
        self.text = nil
        super.init()
    }
    
    override func loadItem(
        forTypeIdentifier typeIdentifier: String,
        options: [AnyHashable : Any]? = nil,
        completionHandler: @escaping (NSSecureCoding?, Error?) -> Void
    ) {
        if typeIdentifier == UTType.url.identifier, let url = url {
            completionHandler(url as NSURL, nil)
        } else if typeIdentifier == UTType.plainText.identifier, let text = text {
            completionHandler(text as NSString, nil)
        } else if typeIdentifier == typeId, let data = data {
            completionHandler(data as NSData, nil)
        } else {
            completionHandler(nil, NSError(domain: "MockError", code: 0))
        }
    }
    
    override func hasItemConformingToTypeIdentifier(_ typeIdentifier: String) -> Bool {
        switch typeIdentifier {
        case UTType.url.identifier:
            return url != nil
        case UTType.plainText.identifier:
            return text != nil
        case typeId:
            return data != nil
        default:
            return false
        }
    }
}

// Mock ShareViewController for testing
class ShareViewController: UIViewController {
    var extensionContext: NSExtensionContext?
    @objc dynamic var isLoading = false
    var didShowError = false
    var didShowDuplicateMessage = false
    
    func handleShare() async {
        isLoading = true
        defer { isLoading = false }
        
        // Process items from extension context
        guard let items = extensionContext?.inputItems as? [NSExtensionItem] else {
            didShowError = true
            return
        }
        
        for item in items {
            await processItem(item)
        }
        
        extensionContext?.completeRequest(returningItems: nil)
    }
    
    private func processItem(_ item: NSExtensionItem) async {
        // Mock processing logic
        guard let attachments = item.attachments else { return }
        
        for provider in attachments {
            if provider.hasItemConformingToTypeIdentifier(UTType.url.identifier) {
                // Process URL
            } else if provider.hasItemConformingToTypeIdentifier(UTType.plainText.identifier) {
                // Process text
            }
        }
    }
}