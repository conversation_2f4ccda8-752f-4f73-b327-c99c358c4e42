import Foundation
import GRDB
@testable import PocketNext

// MARK: - Test Extensions

// MARK: - Test Factories

extension Article {
    static func testArticle(
        id: UUID = UUID(),
        url: String = "https://example.com/test",
        title: String = "Test Article",
        content: String = "Test content",
        summary: String = "Test summary",
        keywords: [String] = ["test"],
        author: String? = "Test Author",
        publishDate: Date? = Date(),
        readingTime: Int = 5,
        contentType: ContentType = .article,
        capturedAt: Date = Date(),
        lastAccessedAt: Date? = nil,
        isArchived: Bool = false,
        syncStatus: SyncStatus = .pending,
        embeddingData: Data? = nil,
        embeddingModelVersion: String? = nil,
        hasLocalEmbedding: Bool = false
    ) -> Article {
        Article(
            id: id,
            url: url,
            title: title,
            content: content,
            summary: summary,
            keywords: keywords,
            author: author,
            publishDate: publishDate,
            readingTime: readingTime,
            contentType: contentType,
            capturedAt: capturedAt,
            lastAccessedAt: lastAccessedAt,
            isArchived: isArchived,
            syncStatus: syncStatus,
            embeddingData: embeddingData,
            embeddingModelVersion: embeddingModelVersion,
            hasLocalEmbedding: hasLocalEmbedding
        )
    }
    
    static func testArticleWithEmbedding(
        title: String = "Test Article",
        keywords: [String] = ["test"],
        embeddingDimensions: Int = 1536
    ) -> Article {
        let embedding = MockDataGenerator.generateMockEmbeddings(dimensions: embeddingDimensions)
        let compressedData = VectorOperations.compressEmbedding(embedding)
        
        return testArticle(
            title: title,
            keywords: keywords,
            embeddingData: compressedData,
            embeddingModelVersion: "test-model-v1",
            hasLocalEmbedding: true
        )
    }
}

// MARK: - Database Test Helpers

struct DatabaseTestHelpers {
    /// Creates a temporary in-memory database for testing
    static func createTestDatabase() async throws -> DatabaseManager {
        return try await DatabaseManager(inMemory: true)
    }
    
    /// Populates database with test articles
    static func populateWithTestArticles(_ database: DatabaseManager, count: Int = 10) async throws {
        for i in 0..<count {
            let article = Article.testArticle(
                title: "Article \(i)",
                capturedAt: Date().addingTimeInterval(TimeInterval(-i * 3600))
            )
            try await database.save(article)
        }
    }
    
    /// Creates articles with specific sync states
    static func createSyncTestArticles() -> [Article] {
        return [
            Article.testArticle(title: "Pending Sync", syncStatus: .pending),
            Article.testArticle(title: "Synced", syncStatus: .synced),
            Article.testArticle(title: "Error Sync", syncStatus: .error),
            Article.testArticle(title: "Another Pending", syncStatus: .pending)
        ]
    }
}

// MARK: - Async Test Utilities

struct AsyncTestUtilities {
    /// Waits for a condition to become true
    static func waitFor(
        _ condition: @escaping () async -> Bool,
        timeout: TimeInterval = 5.0,
        pollingInterval: TimeInterval = 0.1
    ) async throws {
        let deadline = Date().addingTimeInterval(timeout)
        
        while Date() < deadline {
            if await condition() {
                return
            }
            try await Task.sleep(nanoseconds: UInt64(pollingInterval * 1_000_000_000))
        }
        
        throw TestError.timeout
    }
    
    /// Measures the execution time of an async operation
    static func measure<T>(_ operation: () async throws -> T) async throws -> (result: T, duration: TimeInterval) {
        let start = CFAbsoluteTimeGetCurrent()
        let result = try await operation()
        let duration = CFAbsoluteTimeGetCurrent() - start
        return (result, duration)
    }
}

// MARK: - Test Errors

enum TestError: LocalizedError {
    case timeout
    case conditionNotMet
    case unexpectedState
    
    var errorDescription: String? {
        switch self {
        case .timeout:
            return "Operation timed out"
        case .conditionNotMet:
            return "Test condition not met"
        case .unexpectedState:
            return "Unexpected state encountered"
        }
    }
}

// MARK: - Mock Data Generators

struct MockDataGenerator {
    /// Generates mock article content
    static func generateMockContent(wordCount: Int = 500) -> String {
        let words = ["Lorem", "ipsum", "dolor", "sit", "amet", "consectetur", "adipiscing", "elit"]
        return (0..<wordCount).map { _ in words.randomElement()! }.joined(separator: " ")
    }
    
    /// Generates mock embeddings
    static func generateMockEmbeddings(dimensions: Int = 1536) -> [Float] {
        return (0..<dimensions).map { _ in Float.random(in: -1...1) }
    }
    
    /// Generates a batch of test articles
    static func generateTestArticles(count: Int, baseDate: Date = Date()) -> [Article] {
        return (0..<count).map { i in
            Article.testArticle(
                title: "Generated Article \(i)",
                content: generateMockContent(wordCount: 100 + i * 50),
                readingTime: 5 + (i % 10),
                capturedAt: baseDate.addingTimeInterval(TimeInterval(-i * 3600))
            )
        }
    }
}