# PocketNext iOS Test Suite

Comprehensive test suite for the PocketNext iOS application following Test-Driven Development (TDD) principles.

## Test Structure

```
Tests/
├── PocketNextTests/
│   ├── ServiceTests/          # Unit tests for services
│   ├── ViewModelTests/        # Unit tests for view models
│   ├── ViewTests/            # UI component tests
│   ├── IntegrationTests/     # Integration tests
│   ├── Mocks/                # Mock objects and test doubles
│   └── TestHelpers.swift     # Shared test utilities
```

## Test Categories

### Unit Tests (70%)

#### Service Tests
- **DatabaseManagerTests**: Core database operations, CRUD, search, sync
- **NotificationServiceTests**: Push notifications, local notifications, scheduling
- **CloudKitSyncServiceTests**: iCloud sync, conflict resolution, offline handling
- **OfflineContentServiceTests**: Offline content download, storage, retrieval

#### View Model Tests
- **ArticleListViewModelTests**: List management, filtering, sorting, search
- **ArticleDetailViewModelTests**: Article reading, progress tracking, actions
- **SearchViewModelTests**: Search functionality, suggestions, filters

#### View Tests
- **ArticleListViewTests**: List UI, swipe actions, context menus
- **ArticleDetailViewTests**: Reading view, settings, annotations

### Integration Tests (20%)

- **DatabaseIntegrationTests**: Full database lifecycle, concurrent access
- **OfflineSyncIntegrationTests**: Offline/online sync, conflict handling
- **WidgetIntegrationTests**: Widget data sharing, timeline generation
- **ShareExtensionIntegrationTests**: Share extension, URL extraction

### End-to-End Tests (10%)
- Full user flows (implemented separately in UI test target)

## Running Tests

### Command Line
```bash
# Run all tests
xcodebuild test -scheme PocketNext -destination 'platform=iOS Simulator,name=iPhone 15'

# Run specific test class
xcodebuild test -scheme PocketNext -only-testing:PocketNextTests/DatabaseManagerTests

# Run with coverage
xcodebuild test -scheme PocketNext -enableCodeCoverage YES
```

### Xcode
1. Open `PocketNext.xcodeproj`
2. Select the test navigator (⌘6)
3. Run all tests with ⌘U or individual tests by clicking the diamond

## Test Coverage Goals

- Overall: >80%
- Critical paths: >90%
- Services: >85%
- View Models: >80%
- Views: >70%

## Mocking Strategy

### Mock Objects
- `MockDatabaseManager`: In-memory database for testing with configurable errors
- `MockCloudKitContainer`: Simulated CloudKit operations
- `MockURLSession`: Network request simulation
- `MockNotificationCenter`: Notification testing

### New Test Utilities
- `ArticleFactory`: Create test articles with custom properties
- `waitFor`: Async helper for testing state changes
- `measureAsync`: Performance testing for async operations
- `runConcurrently`: Test concurrent access patterns

### Test Data
- Use factory methods for consistent test data
- Avoid hardcoded values where possible
- Clean up after each test

## Performance Testing

Performance tests measure:
- Database query performance
- Search operations
- Bulk operations
- Widget data preparation

Targets:
- Search: <1s for 1000 articles
- Widget refresh: <1s
- Bulk save: <5s for 100 articles

## Continuous Integration

Tests run automatically on:
- Pull requests
- Main branch commits
- Nightly builds

Failed tests block merging.

## Best Practices

1. **Test Naming**: Use descriptive names following `test<Method>_<Condition>_<ExpectedResult>`
2. **Arrange-Act-Assert**: Structure tests clearly
3. **One Assertion**: Focus each test on a single behavior
4. **Fast Tests**: Keep unit tests under 100ms
5. **Isolated Tests**: No dependencies between tests
6. **Mock External**: Mock all external dependencies

## Adding New Tests

1. Create test file in appropriate directory
2. Import XCTest and @testable import PocketNext
3. Inherit from XCTestCase
4. Add setUp() and tearDown() methods
5. Write focused test methods
6. Run tests locally before committing

## Debugging Failed Tests

1. Check test output for specific failure
2. Use breakpoints in test and implementation
3. Verify mock setup is correct
4. Check for timing/async issues
5. Ensure proper cleanup in tearDown

## Test Utilities

Common test helpers in `TestHelpers/`:
- Article factory methods
- Async test extensions
- Mock data generators
- Performance measurement helpers