// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "PocketNext-iOS",
    platforms: [
        .iOS(.v16),
        .macOS(.v11)
    ],
    products: [
        .library(
            name: "PocketNext",
            targets: ["PocketNext"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/groue/GRDB.swift.git", from: "6.24.0"),
        .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
        .package(url: "https://github.com/onevcat/Kingfisher.git", from: "7.10.0"),
        .package(url: "https://github.com/SwiftyJSON/SwiftyJSON.git", from: "5.0.0"),
        .package(url: "https://github.com/apple/swift-markdown.git", branch: "main"),
        .package(url: "https://github.com/apple/swift-algorithms.git", from: "1.2.0"),
    ],
    targets: [
        .target(
            name: "PocketNext",
            dependencies: [
                .product(name: "GRDB", package: "GRDB.swift"),
                .product(name: "Alamofire", package: "Alamofire"),
                .product(name: "Kingfisher", package: "Kingfisher"),
                .product(name: "SwiftyJSON", package: "SwiftyJSON"),
                .product(name: "Markdown", package: "swift-markdown"),
                .product(name: "Algorithms", package: "swift-algorithms"),
            ],
            path: "PocketNext",
            exclude: ["Info.plist", "PocketNext.entitlements", "Assets.xcassets", "Preview Content"]
        ),
        .testTarget(
            name: "PocketNextTests",
            dependencies: ["PocketNext"],
            path: "Tests/PocketNextTests"
        ),
    ]
)
