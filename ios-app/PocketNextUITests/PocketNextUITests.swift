import XCTest

/// End-to-End UI Tests for PocketNext iOS app
/// These tests verify complete user flows and interactions
class PocketNextUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launchEnvironment = ["TESTING": "1"]
        
        // Reset app state for consistent testing
        app.launchArguments.append("--reset-state")
        
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - App Launch Tests
    
    func testAppLaunchesSuccessfully() throws {
        // Verify tab bar exists
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.exists)
        
        // Verify all tabs are present
        XCTAssertTrue(tabBar.buttons["Home"].exists)
        XCTAssertTrue(tabBar.buttons["Search"].exists)
        XCTAssertTrue(tabBar.buttons["Chat"].exists)
        XCTAssertTrue(tabBar.buttons["Digest"].exists)
        XCTAssertTrue(tabBar.buttons["Profile"].exists)
    }
    
    // MARK: - Home Feed Tests
    
    func testHomeFeedDisplaysArticles() throws {
        // Wait for home feed to load
        let articlesList = app.collectionViews.firstMatch
        XCTAssertTrue(articlesList.waitForExistence(timeout: 5))
        
        // Check if articles are displayed
        let firstArticle = articlesList.cells.firstMatch
        
        if firstArticle.exists {
            // Verify article has required elements
            XCTAssertTrue(firstArticle.staticTexts.count >= 1) // Title
            XCTAssertTrue(firstArticle.images.count >= 0) // May have image
        } else {
            // Empty state should be shown
            XCTAssertTrue(app.staticTexts["No Articles"].exists)
        }
    }
    
    func testArticleSwipeActions() throws {
        // Navigate to home feed
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list found")
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTSkip("No articles to test")
        }
        
        // Swipe left on article
        firstArticle.swipeLeft()
        
        // Verify swipe actions appear
        XCTAssertTrue(app.buttons["Archive"].exists || app.buttons["Delete"].exists)
        
        // Tap outside to dismiss
        app.tap()
    }
    
    func testArticleDetailNavigation() throws {
        // Find and tap first article
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list found")
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTSkip("No articles to test")
        }
        
        firstArticle.tap()
        
        // Verify reading view appears
        let readingView = app.scrollViews["ReadingView"]
        XCTAssertTrue(readingView.waitForExistence(timeout: 3))
        
        // Verify navigation bar exists
        XCTAssertTrue(app.navigationBars.firstMatch.exists)
        
        // Go back
        app.navigationBars.buttons.firstMatch.tap()
        
        // Verify we're back at home feed
        XCTAssertTrue(articlesList.exists)
    }
    
    // MARK: - Search Tests
    
    func testSearchFunctionality() throws {
        // Navigate to Search tab
        app.tabBars.buttons["Search"].tap()
        
        // Find search field
        let searchField = app.searchFields.firstMatch
        XCTAssertTrue(searchField.waitForExistence(timeout: 3))
        
        // Enter search query
        searchField.tap()
        searchField.typeText("SwiftUI")
        
        // Wait for results
        let searchResults = app.collectionViews["SearchResults"]
        _ = searchResults.waitForExistence(timeout: 3)
        
        // Verify search is working (results or no results message)
        let hasResults = searchResults.cells.count > 0
        let noResultsMessage = app.staticTexts["No results found"].exists
        
        XCTAssertTrue(hasResults || noResultsMessage)
        
        // Clear search
        if searchField.buttons["Clear text"].exists {
            searchField.buttons["Clear text"].tap()
        }
    }
    
    // MARK: - Chat Tests
    
    func testChatInterface() throws {
        // Navigate to Chat tab
        app.tabBars.buttons["Chat"].tap()
        
        // Verify chat view loads
        let chatView = app.otherElements["ChatView"]
        XCTAssertTrue(chatView.waitForExistence(timeout: 3))
        
        // Find message input field
        let messageField = app.textViews["MessageInput"] 
        if !messageField.exists {
            // Try text field if text view not found
            let textField = app.textFields["MessageInput"]
            XCTAssertTrue(textField.exists)
        }
        
        // Verify send button exists
        XCTAssertTrue(app.buttons["Send"].exists)
    }
    
    func testSendChatMessage() throws {
        // Navigate to Chat
        app.tabBars.buttons["Chat"].tap()
        
        // Find and tap message input
        let messageInput = app.textViews["MessageInput"].exists ? 
            app.textViews["MessageInput"] : app.textFields["MessageInput"]
        
        messageInput.tap()
        messageInput.typeText("What articles do I have about SwiftUI?")
        
        // Send message
        app.buttons["Send"].tap()
        
        // Wait for response
        let chatMessages = app.collectionViews["ChatMessages"]
        if chatMessages.exists {
            // Verify message was added
            let messageCount = chatMessages.cells.count
            XCTAssertGreaterThan(messageCount, 0)
        }
    }
    
    // MARK: - Digest Tests
    
    func testDigestView() throws {
        // Navigate to Digest tab
        app.tabBars.buttons["Digest"].tap()
        
        // Verify digest view loads
        let digestView = app.scrollViews["DigestView"]
        XCTAssertTrue(digestView.waitForExistence(timeout: 3))
        
        // Check for digest content or empty state
        let hasDigestContent = app.staticTexts["Daily Digest"].exists || 
                             app.staticTexts["Your Reading Digest"].exists
        let emptyDigest = app.staticTexts["No digest available"].exists
        
        XCTAssertTrue(hasDigestContent || emptyDigest)
    }
    
    // MARK: - Profile Tests
    
    func testProfileSettings() throws {
        // Navigate to Profile tab
        app.tabBars.buttons["Profile"].tap()
        
        // Verify profile view loads
        let profileView = app.tables["ProfileView"]
        XCTAssertTrue(profileView.waitForExistence(timeout: 3))
        
        // Check for settings options
        XCTAssertTrue(profileView.cells.count > 0)
        
        // Test tapping a settings option
        if profileView.cells["Reading Preferences"].exists {
            profileView.cells["Reading Preferences"].tap()
            
            // Verify navigation occurred
            XCTAssertTrue(app.navigationBars.count > 0)
            
            // Go back
            app.navigationBars.buttons.firstMatch.tap()
        }
    }
    
    // MARK: - Offline Mode Tests
    
    func testOfflineMode() throws {
        // Enable airplane mode programmatically if possible
        // Note: This requires special entitlements in real testing
        
        // For now, test offline UI indicators
        let offlineIndicator = app.staticTexts["Offline Mode"]
        
        // If offline indicator exists, verify offline functionality
        if offlineIndicator.exists {
            // Verify cached articles are still accessible
            let articlesList = app.collectionViews.firstMatch
            XCTAssertTrue(articlesList.exists)
        }
    }
    
    // MARK: - Performance Tests
    
    func testScrollPerformance() throws {
        // Navigate to home feed
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list found")
        }
        
        // Measure scrolling performance
        let metrics: [XCTMetric] = [
            XCTOSSignpostMetric.scrollDecelerationMetric,
            XCTOSSignpostMetric.scrollDraggingMetric
        ]
        
        measure(metrics: metrics) {
            // Scroll down
            articlesList.swipeUp(velocity: .fast)
            
            // Scroll up
            articlesList.swipeDown(velocity: .fast)
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityLabels() throws {
        // Verify important UI elements have accessibility labels
        XCTAssertTrue(app.tabBars.buttons["Home"].isHittable)
        XCTAssertNotNil(app.tabBars.buttons["Home"].label)
        
        // Test VoiceOver navigation
        let articlesList = app.collectionViews.firstMatch
        if articlesList.waitForExistence(timeout: 5) {
            let firstArticle = articlesList.cells.firstMatch
            if firstArticle.exists {
                XCTAssertNotNil(firstArticle.label)
                XCTAssertTrue(firstArticle.isHittable)
            }
        }
    }
    
    // MARK: - State Restoration Tests
    
    func testStateRestoration() throws {
        // Navigate to a specific state
        app.tabBars.buttons["Search"].tap()
        
        let searchField = app.searchFields.firstMatch
        searchField.tap()
        searchField.typeText("Test Query")
        
        // Simulate app backgrounding
        XCUIDevice.shared.press(.home)
        
        // Re-launch app
        app.activate()
        
        // Verify state was preserved
        XCTAssertTrue(app.tabBars.buttons["Search"].isSelected)
        
        // Note: Full state restoration testing requires additional setup
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling() throws {
        // Test network error handling
        // This would require mocking network conditions
        
        // For now, test that error UI can be displayed
        let errorBanner = app.otherElements["ErrorBanner"]
        let errorAlert = app.alerts.firstMatch
        
        // If any errors occur during testing, verify they're handled gracefully
        if errorBanner.exists || errorAlert.exists {
            // Verify error can be dismissed
            if errorAlert.exists {
                errorAlert.buttons["OK"].tap()
            }
        }
    }
}

// MARK: - Helper Extensions

extension XCUIElement {
    /// Wait for element to not exist
    func waitForNonExistence(timeout: TimeInterval) -> Bool {
        let predicate = NSPredicate(format: "exists == false")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: self)
        let result = XCTWaiter().wait(for: [expectation], timeout: timeout)
        return result == .completed
    }
}

// MARK: - Test Launch Arguments

extension PocketNextUITests {
    /// Common launch arguments for different test scenarios
    enum TestLaunchArgument {
        static let resetState = "--reset-state"
        static let skipOnboarding = "--skip-onboarding"
        static let mockData = "--use-mock-data"
        static let offlineMode = "--offline-mode"
    }
}