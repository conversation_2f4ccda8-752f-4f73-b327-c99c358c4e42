import XCTest

/// End-to-End tests for the Save Article user flow
class SaveArticleFlowTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting", "--reset-state"]
        app.launch()
    }
    
    // MARK: - Save Article Flow Tests
    
    func testSaveArticleFromShareExtension() throws {
        // Note: Testing share extensions requires special setup
        // This test documents the expected flow
        
        /*
        Expected flow:
        1. User browses to article in Safari
        2. User taps Share button
        3. User selects PocketNext from share sheet
        4. Article is saved
        5. User opens PocketNext app
        6. Article appears in feed
        */
        
        // For now, test that articles can be displayed
        let articlesList = app.collectionViews.firstMatch
        XCTAssertTrue(articlesList.waitForExistence(timeout: 5))
    }
    
    func testSaveArticleWithTags() throws {
        // This would test adding tags during save
        // Requires UI for tag input which may be in a modal
        
        // Navigate to a saved article
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles to test")
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTSkip("No articles available")
        }
        
        // Long press for context menu
        firstArticle.press(forDuration: 1.0)
        
        // Look for tag option
        if app.buttons["Add Tags"].exists {
            app.buttons["Add Tags"].tap()
            
            // Enter tags
            let tagField = app.textFields["TagInput"]
            if tagField.exists {
                tagField.tap()
                tagField.typeText("swift, ios, tutorial")
                
                // Save tags
                app.buttons["Save"].tap()
            }
        }
    }
    
    func testQuickSaveFromClipboard() throws {
        // Test saving URL from clipboard
        
        // Simulate clipboard containing URL
        // Note: Actual clipboard access requires special permissions
        
        // Check if app detects clipboard URL
        let clipboardBanner = app.banners["ClipboardURL"]
        
        if clipboardBanner.waitForExistence(timeout: 2) {
            // Tap to save
            clipboardBanner.buttons["Save"].tap()
            
            // Verify save confirmation
            let savedBanner = app.banners["ArticleSaved"]
            XCTAssertTrue(savedBanner.waitForExistence(timeout: 3))
        }
    }
    
    // MARK: - Article Management Flow
    
    func testCompleteArticleLifecycle() throws {
        // Test complete flow: Save -> Read -> Archive -> Delete
        
        // 1. Verify article exists
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        let testArticle = articlesList.cells.firstMatch
        guard testArticle.exists else {
            XCTSkip("No articles to test")
        }
        
        let articleTitle = testArticle.staticTexts.firstMatch.label
        
        // 2. Read article
        testArticle.tap()
        
        let readingView = app.scrollViews["ReadingView"]
        XCTAssertTrue(readingView.waitForExistence(timeout: 3))
        
        // Scroll to simulate reading
        readingView.swipeUp()
        Thread.sleep(forTimeInterval: 1)
        readingView.swipeUp()
        
        // 3. Go back
        app.navigationBars.buttons.firstMatch.tap()
        
        // 4. Archive article
        testArticle.swipeLeft()
        if app.buttons["Archive"].exists {
            app.buttons["Archive"].tap()
        }
        
        // 5. Navigate to archived
        app.tabBars.buttons["Profile"].tap()
        
        let profileTable = app.tables["ProfileView"]
        if profileTable.cells["Archived Articles"].exists {
            profileTable.cells["Archived Articles"].tap()
            
            // Verify article is in archive
            let archivedList = app.collectionViews["ArchivedArticles"]
            let archivedArticle = archivedList.cells.containing(.staticText, identifier: articleTitle).firstMatch
            
            if archivedArticle.exists {
                // 6. Delete from archive
                archivedArticle.swipeLeft()
                if app.buttons["Delete"].exists {
                    app.buttons["Delete"].tap()
                    
                    // Confirm deletion
                    if app.alerts.firstMatch.exists {
                        app.alerts.firstMatch.buttons["Delete"].tap()
                    }
                }
            }
        }
    }
    
    // MARK: - Batch Operations
    
    func testBatchArchive() throws {
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        // Enter selection mode
        if app.buttons["Select"].exists {
            app.buttons["Select"].tap()
            
            // Select multiple articles
            let cells = articlesList.cells
            let count = min(3, cells.count)
            
            for i in 0..<count {
                cells.element(boundBy: i).tap()
            }
            
            // Archive selected
            if app.buttons["Archive Selected"].exists {
                app.buttons["Archive Selected"].tap()
                
                // Verify confirmation
                let successMessage = app.staticTexts["Archived \(count) articles"]
                XCTAssertTrue(successMessage.waitForExistence(timeout: 2))
            }
            
            // Exit selection mode
            app.buttons["Done"].tap()
        }
    }
    
    // MARK: - Offline Save
    
    func testOfflineSaveQueue() throws {
        // Test that articles can be queued for saving when offline
        
        // Check for offline indicator
        let offlineMode = app.staticTexts["Offline Mode"]
        
        if offlineMode.exists {
            // Attempt to save (would be from share extension or clipboard)
            // Verify article is queued
            
            let queuedBanner = app.banners["ArticleQueued"]
            if queuedBanner.exists {
                XCTAssertTrue(queuedBanner.staticTexts["Will save when online"].exists)
            }
        }
    }
    
    // MARK: - Import/Export
    
    func testExportArticles() throws {
        // Navigate to settings
        app.tabBars.buttons["Profile"].tap()
        
        let profileTable = app.tables["ProfileView"]
        if profileTable.cells["Export Articles"].exists {
            profileTable.cells["Export Articles"].tap()
            
            // Select export format
            let formatPicker = app.pickers["ExportFormat"]
            if formatPicker.exists {
                formatPicker.pickerWheels.firstMatch.adjust(toPickerWheelValue: "JSON")
                
                // Export
                app.buttons["Export"].tap()
                
                // Verify share sheet appears
                let shareSheet = app.otherElements["ActivityListView"]
                XCTAssertTrue(shareSheet.waitForExistence(timeout: 3))
                
                // Cancel
                if app.buttons["Close"].exists {
                    app.buttons["Close"].tap()
                } else if app.buttons["Cancel"].exists {
                    app.buttons["Cancel"].tap()
                }
            }
        }
    }
}