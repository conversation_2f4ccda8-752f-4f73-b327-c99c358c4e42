import XCTest

/// End-to-End tests for the Reading Experience
class ReadingExperienceTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting", "--mock-articles"]
        app.launch()
    }
    
    // MARK: - Reading View Tests
    
    func testOpenArticleInReadingView() throws {
        // Navigate to first article
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTSkip("No articles available")
        }
        
        firstArticle.tap()
        
        // Verify reading view components
        let readingView = app.scrollViews["ReadingView"]
        XCTAssertTrue(readingView.waitForExistence(timeout: 3))
        
        // Check for article content
        XCTAssertTrue(app.staticTexts["ArticleTitle"].exists || 
                     readingView.staticTexts.count > 0)
        
        // Verify toolbar exists
        XCTAssertTrue(app.toolbars.firstMatch.exists)
    }
    
    func testReadingProgress() throws {
        // Open article
        navigateToReadingView()
        
        let readingView = app.scrollViews["ReadingView"]
        
        // Get initial progress
        let progressBar = app.progressIndicators["ReadingProgress"]
        let initialProgress = progressBar.exists ? progressBar.value as? String : "0%"
        
        // Scroll through article
        for _ in 0..<5 {
            readingView.swipeUp()
            Thread.sleep(forTimeInterval: 0.5)
        }
        
        // Check progress updated
        if progressBar.exists {
            let currentProgress = progressBar.value as? String ?? "0%"
            XCTAssertNotEqual(currentProgress, initialProgress)
        }
        
        // Go back
        app.navigationBars.buttons.firstMatch.tap()
        
        // Verify progress was saved
        let articlesList = app.collectionViews.firstMatch
        let firstArticle = articlesList.cells.firstMatch
        
        // Check for progress indicator on article card
        let cardProgress = firstArticle.progressIndicators.firstMatch
        if cardProgress.exists {
            XCTAssertNotEqual(cardProgress.value as? String, "0%")
        }
    }
    
    // MARK: - Reading Settings Tests
    
    func testAdjustTextSize() throws {
        navigateToReadingView()
        
        // Open settings
        if app.buttons["TextSettings"].exists {
            app.buttons["TextSettings"].tap()
            
            // Find text size slider
            let textSizeSlider = app.sliders["TextSize"]
            if textSizeSlider.exists {
                // Increase text size
                textSizeSlider.adjust(toNormalizedSliderPosition: 0.75)
                
                // Close settings
                app.buttons["Done"].tap()
                
                // Verify text size changed (hard to test directly)
                XCTAssertTrue(app.scrollViews["ReadingView"].exists)
            }
        }
    }
    
    func testChangeTheme() throws {
        navigateToReadingView()
        
        // Open theme picker
        if app.buttons["Theme"].exists {
            app.buttons["Theme"].tap()
            
            // Select dark theme
            if app.buttons["Dark"].exists {
                app.buttons["Dark"].tap()
                
                // Verify theme changed (check background color indirectly)
                let readingView = app.scrollViews["ReadingView"]
                XCTAssertTrue(readingView.exists)
            }
        }
    }
    
    func testChangeFontFamily() throws {
        navigateToReadingView()
        
        // Open font settings
        if app.buttons["FontSettings"].exists {
            app.buttons["FontSettings"].tap()
            
            // Select different font
            let fontPicker = app.pickers["FontFamily"]
            if fontPicker.exists {
                fontPicker.pickerWheels.firstMatch.adjust(toPickerWheelValue: "Georgia")
                app.buttons["Done"].tap()
            }
        }
    }
    
    // MARK: - Article Actions Tests
    
    func testHighlightText() throws {
        navigateToReadingView()
        
        let readingView = app.scrollViews["ReadingView"]
        let textView = readingView.textViews.firstMatch
        
        if textView.exists {
            // Long press to select text
            textView.press(forDuration: 1.0)
            
            // Look for highlight option
            if app.menuItems["Highlight"].exists {
                app.menuItems["Highlight"].tap()
                
                // Verify highlight was created
                let highlights = app.otherElements["Highlights"]
                if highlights.exists {
                    XCTAssertGreaterThan(highlights.buttons.count, 0)
                }
            }
        }
    }
    
    func testAddNote() throws {
        navigateToReadingView()
        
        // Tap note button
        if app.buttons["AddNote"].exists {
            app.buttons["AddNote"].tap()
            
            // Enter note
            let noteField = app.textViews["NoteInput"]
            if noteField.exists {
                noteField.tap()
                noteField.typeText("This is an important point to remember")
                
                // Save note
                app.buttons["SaveNote"].tap()
                
                // Verify note indicator appears
                XCTAssertTrue(app.images["NoteIndicator"].exists ||
                            app.buttons["ViewNotes"].exists)
            }
        }
    }
    
    func testShareArticle() throws {
        navigateToReadingView()
        
        // Tap share button
        app.buttons["Share"].tap()
        
        // Verify share sheet appears
        let shareSheet = app.otherElements["ActivityListView"]
        XCTAssertTrue(shareSheet.waitForExistence(timeout: 3))
        
        // Cancel share
        if app.buttons["Close"].exists {
            app.buttons["Close"].tap()
        } else if app.buttons["Cancel"].exists {
            app.buttons["Cancel"].tap()
        }
    }
    
    // MARK: - Offline Reading Tests
    
    func testDownloadForOffline() throws {
        navigateToReadingView()
        
        // Tap download button
        if app.buttons["Download"].exists {
            app.buttons["Download"].tap()
            
            // Wait for download to complete
            let downloadProgress = app.progressIndicators["DownloadProgress"]
            if downloadProgress.exists {
                // Wait for completion
                _ = downloadProgress.waitForNonExistence(timeout: 10)
            }
            
            // Verify downloaded indicator
            XCTAssertTrue(app.images["Downloaded"].exists ||
                         app.buttons["Downloaded"].exists)
        }
    }
    
    // MARK: - Navigation Tests
    
    func testTableOfContents() throws {
        navigateToReadingView()
        
        // Open table of contents
        if app.buttons["TableOfContents"].exists {
            app.buttons["TableOfContents"].tap()
            
            let tocView = app.tables["TableOfContents"]
            if tocView.waitForExistence(timeout: 2) {
                // Tap a section
                let firstSection = tocView.cells.firstMatch
                if firstSection.exists {
                    firstSection.tap()
                    
                    // Verify scrolled to section
                    let readingView = app.scrollViews["ReadingView"]
                    XCTAssertTrue(readingView.exists)
                }
            }
        }
    }
    
    func testSwipeNavigation() throws {
        // Open first article
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5),
              articlesList.cells.count >= 2 else {
            XCTSkip("Need at least 2 articles")
        }
        
        articlesList.cells.element(boundBy: 0).tap()
        
        let readingView = app.scrollViews["ReadingView"]
        XCTAssertTrue(readingView.waitForExistence(timeout: 3))
        
        // Swipe to next article
        app.swipeLeft()
        
        // Verify navigated to next article
        Thread.sleep(forTimeInterval: 1)
        XCTAssertTrue(readingView.exists)
        
        // Swipe back to previous
        app.swipeRight()
        
        Thread.sleep(forTimeInterval: 1)
        XCTAssertTrue(readingView.exists)
    }
    
    // MARK: - Reader Mode Tests
    
    func testEnableReaderMode() throws {
        navigateToReadingView()
        
        // Toggle reader mode
        if app.buttons["ReaderMode"].exists {
            app.buttons["ReaderMode"].tap()
            
            // Verify reader mode activated
            let readerView = app.otherElements["ReaderModeView"]
            XCTAssertTrue(readerView.waitForExistence(timeout: 2))
            
            // Toggle off
            app.buttons["ReaderMode"].tap()
            XCTAssertFalse(readerView.exists)
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testVoiceOverReading() throws {
        navigateToReadingView()
        
        // This would test VoiceOver functionality
        // Requires special setup for accessibility testing
        
        // For now, verify accessibility elements exist
        let readingView = app.scrollViews["ReadingView"]
        XCTAssertTrue(readingView.isAccessibilityElement ||
                     readingView.staticTexts.count > 0)
    }
    
    func testTextToSpeech() throws {
        navigateToReadingView()
        
        // Start text-to-speech
        if app.buttons["Listen"].exists {
            app.buttons["Listen"].tap()
            
            // Verify playback controls appear
            XCTAssertTrue(app.buttons["Pause"].waitForExistence(timeout: 2) ||
                         app.buttons["Stop"].exists)
            
            // Stop playback
            if app.buttons["Stop"].exists {
                app.buttons["Stop"].tap()
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func navigateToReadingView() {
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTFail("No articles list found")
            return
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTFail("No articles available")
            return
        }
        
        firstArticle.tap()
        
        let readingView = app.scrollViews["ReadingView"]
        XCTAssertTrue(readingView.waitForExistence(timeout: 3))
    }
}

// MARK: - Reading Statistics Tests

extension ReadingExperienceTests {
    
    func testReadingTimeTracking() throws {
        navigateToReadingView()
        
        // Read for a specific duration
        Thread.sleep(forTimeInterval: 5)
        
        // Go back
        app.navigationBars.buttons.firstMatch.tap()
        
        // Check reading stats updated
        app.tabBars.buttons["Profile"].tap()
        
        let statsCell = app.tables["ProfileView"].cells["Reading Statistics"]
        if statsCell.exists {
            statsCell.tap()
            
            // Verify reading time is tracked
            let todayReading = app.staticTexts["TodayReadingTime"]
            XCTAssertTrue(todayReading.exists)
        }
    }
}