{"configurations": [{"id": "6D8E6C5E-4D6B-4F5E-8C5C-9C5E5D8E6C5E", "name": "All Tests", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 60, "maximumTestRepetitions": 3, "testRepetitionMode": "retryOnFailure", "codeCoverageEnabled": true}}, {"id": "UNIT-TEST-CONFIG", "name": "Unit Tests Only", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 30, "codeCoverageEnabled": true}}, {"id": "INTEGRATION-TEST-CONFIG", "name": "Integration Tests", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 120, "codeCoverageEnabled": true}}, {"id": "E2E-TEST-CONFIG", "name": "E2E Tests", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 300, "uiTestingScreenshotsEnabled": true, "uiTestingLifecycleScreenshotsEnabled": true}}], "defaultOptions": {"codeCoverage": {"targets": [{"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNext", "name": "PocketNext"}]}, "commandLineArgumentEntries": [{"argument": "-com.apple.CoreData.SQLDebug 0"}, {"argument": "--u<PERSON>ting", "enabled": false}], "environmentVariableEntries": [{"key": "GRDB_SQLITE_SUPPRESS_WAL_JOURNAL_MODE_ERRORS", "value": "1"}, {"key": "TESTING", "value": "1"}], "testExecutionOrdering": "random", "testTargetExecutionOrderingMode": "dependencyOrder"}, "testTargets": [{"parallelizable": true, "target": {"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNextTests", "name": "PocketNextTests"}, "selectedTests": ["ArticleTests", "AppStateTests", "DatabaseManagerTests", "NotificationServiceTests", "CloudKitSyncServiceTests", "OfflineContentServiceTests", "BackgroundTaskServiceTests", "ArticleCardTests", "ChatViewTests", "ContentViewTests", "DigestViewTests", "FilterSheetTests", "HomeFeedViewTests", "ProfileViewTests", "ReadingViewTests", "SearchViewTests", "RealDatabaseIntegrationTests", "DatabaseIntegrationTests", "OfflineSyncIntegrationTests", "ShareExtensionIntegrationTests", "WidgetIntegrationTests"]}, {"parallelizable": false, "target": {"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNextUITests", "name": "PocketNextUITests"}, "selectedTests": ["PocketNextUITests", "PocketNextUITestsLaunchTests", "SaveArticleFlowTests", "ReadingExperienceTests"]}], "version": 1}