import Foundation
import GRDB

// MARK: - Article Model
struct Article: Identifiable, Codable {
    let id: UUID
    let url: String
    let title: String
    let content: String
    let summary: String
    let keywords: [String]
    let author: String?
    let publishDate: Date?
    let readingTime: Int
    let contentType: ContentType
    let capturedAt: Date
    var lastAccessedAt: Date?
    var isArchived: Bool
    var syncStatus: SyncStatus

    // UI-specific fields
    var faviconURL: String?
    var imageURL: String?
    var isFavorite: Bool
    var isDownloadedForOffline: Bool
    var readProgress: Double
    var savedAt: Date

    // Embedding fields for hybrid architecture
    var embeddingData: Data?  // Compressed Int8 embedding for CloudKit
    var embeddingModelVersion: String?  // Track model version for migrations
    var hasLocalEmbedding: Bool  // Flag to track if local vector index is built

    init(
        id: UUID = UUID(),
        url: String,
        title: String,
        content: String,
        summary: String,
        keywords: [String],
        author: String? = nil,
        publishDate: Date? = nil,
        readingTime: Int,
        contentType: ContentType,
        capturedAt: Date = Date(),
        lastAccessedAt: Date? = nil,
        isArchived: Bool = false,
        syncStatus: SyncStatus = .pending,
        faviconURL: String? = nil,
        imageURL: String? = nil,
        isFavorite: Bool = false,
        isDownloadedForOffline: Bool = false,
        readProgress: Double = 0.0,
        savedAt: Date = Date(),
        embeddingData: Data? = nil,
        embeddingModelVersion: String? = nil,
        hasLocalEmbedding: Bool = false
    ) {
        self.id = id
        self.url = url
        self.title = title
        self.content = content
        self.summary = summary
        self.keywords = keywords
        self.author = author
        self.publishDate = publishDate
        self.readingTime = readingTime
        self.contentType = contentType
        self.capturedAt = capturedAt
        self.lastAccessedAt = lastAccessedAt
        self.isArchived = isArchived
        self.syncStatus = syncStatus
        self.faviconURL = faviconURL
        self.imageURL = imageURL
        self.isFavorite = isFavorite
        self.isDownloadedForOffline = isDownloadedForOffline
        self.readProgress = readProgress
        self.savedAt = savedAt
        self.embeddingData = embeddingData
        self.embeddingModelVersion = embeddingModelVersion
        self.hasLocalEmbedding = hasLocalEmbedding
    }

    // Computed properties
    var domain: String {
        URL(string: url)?.host ?? ""
    }

    var isRead: Bool {
        lastAccessedAt != nil
    }

    var tags: [String] {
        keywords // Use keywords as tags for UI compatibility
    }

    var isFullyRead: Bool {
        readProgress >= 1.0
    }

    var estimatedReadingTime: String {
        "\(readingTime) min"
    }

    var formattedSavedDate: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: savedAt, relativeTo: Date())
    }

    enum ContentType: String, Codable, CaseIterable {
        case article = "article"
        case twitter = "twitter"
        case youtube = "youtube"
        case github = "github"
        case reddit = "reddit"
        case pdf = "pdf"
        case generic = "generic"

        var icon: String {
            switch self {
            case .article: return "doc.text"
            case .twitter: return "bubble.left"
            case .youtube: return "play.rectangle"
            case .github: return "chevron.left.forwardslash.chevron.right"
            case .reddit: return "bubble.left.and.bubble.right"
            case .pdf: return "doc.richtext"
            case .generic: return "globe"
            }
        }
    }

    enum SyncStatus: Int, Codable {
        case pending = 0
        case synced = 1
        case error = 2
    }
}

// MARK: - GRDB Support
extension Article: FetchableRecord, PersistableRecord {
    static let databaseTableName = "articles"

    enum Columns: String, ColumnExpression {
        case id, url, title, content, summary, keywords
        case author, publishDate, readingTime, contentType
        case capturedAt, lastAccessedAt, isArchived, syncStatus
        case faviconURL, imageURL, isFavorite, isDownloadedForOffline
        case readProgress, savedAt
        case embeddingData, embeddingModelVersion, hasLocalEmbedding
    }

    init(row: Row) {
        id = row[Columns.id]
        url = row[Columns.url]
        title = row[Columns.title]
        content = row[Columns.content]
        summary = row[Columns.summary]
        keywords = (try? JSONDecoder().decode([String].self, from: row[Columns.keywords])) ?? []
        author = row[Columns.author]
        publishDate = row[Columns.publishDate]
        readingTime = row[Columns.readingTime]
        contentType = ContentType(rawValue: row[Columns.contentType]) ?? .generic
        capturedAt = row[Columns.capturedAt]
        lastAccessedAt = row[Columns.lastAccessedAt]
        isArchived = row[Columns.isArchived]
        syncStatus = SyncStatus(rawValue: row[Columns.syncStatus]) ?? .pending
        faviconURL = row[Columns.faviconURL]
        imageURL = row[Columns.imageURL]
        isFavorite = row[Columns.isFavorite]
        isDownloadedForOffline = row[Columns.isDownloadedForOffline]
        readProgress = row[Columns.readProgress]
        savedAt = row[Columns.savedAt]
        embeddingData = row[Columns.embeddingData]
        embeddingModelVersion = row[Columns.embeddingModelVersion]
        hasLocalEmbedding = row[Columns.hasLocalEmbedding]
    }

    func encode(to container: inout PersistenceContainer) {
        container[Columns.id] = id
        container[Columns.url] = url
        container[Columns.title] = title
        container[Columns.content] = content
        container[Columns.summary] = summary
        container[Columns.keywords] = try? JSONEncoder().encode(keywords)
        container[Columns.author] = author
        container[Columns.publishDate] = publishDate
        container[Columns.readingTime] = readingTime
        container[Columns.contentType] = contentType.rawValue
        container[Columns.capturedAt] = capturedAt
        container[Columns.lastAccessedAt] = lastAccessedAt
        container[Columns.isArchived] = isArchived
        container[Columns.syncStatus] = syncStatus.rawValue
        container[Columns.faviconURL] = faviconURL
        container[Columns.imageURL] = imageURL
        container[Columns.isFavorite] = isFavorite
        container[Columns.isDownloadedForOffline] = isDownloadedForOffline
        container[Columns.readProgress] = readProgress
        container[Columns.savedAt] = savedAt
        container[Columns.embeddingData] = embeddingData
        container[Columns.embeddingModelVersion] = embeddingModelVersion
        container[Columns.hasLocalEmbedding] = hasLocalEmbedding
    }
}

// MARK: - Local Vector Index Model
struct LocalVectorIndex: Codable {
    let articleId: UUID
    let embedding: [Float]  // Normalized Float32 for fast local search
    let magnitude: Float    // Pre-computed for fast cosine similarity
    let modelVersion: String
    let createdAt: Date
}

extension LocalVectorIndex: FetchableRecord, PersistableRecord {
    static let databaseTableName = "local_vector_index"

    enum Columns: String, ColumnExpression {
        case articleId, embedding, magnitude, modelVersion, createdAt
    }

    init(row: Row) {
        articleId = row[Columns.articleId]
        embedding = (try? JSONDecoder().decode([Float].self, from: row[Columns.embedding])) ?? []
        magnitude = row[Columns.magnitude]
        modelVersion = row[Columns.modelVersion]
        createdAt = row[Columns.createdAt]
    }

    func encode(to container: inout PersistenceContainer) {
        container[Columns.articleId] = articleId
        container[Columns.embedding] = try? JSONEncoder().encode(embedding)
        container[Columns.magnitude] = magnitude
        container[Columns.modelVersion] = modelVersion
        container[Columns.createdAt] = createdAt
    }
}

// MARK: - Captured Content (from browser)
struct CapturedContent: Codable {
    let url: String
    let title: String
    let htmlContent: String
    let timestamp: Date
    let metadata: Metadata

    struct Metadata: Codable {
        let description: String?
        let author: String?
        let publishDate: String?
        let type: String
        let userId: String?
    }
}

// MARK: - Parsed Article (from parse server)
struct ParsedArticle: Codable {
    let title: String
    let content: String
    let summary: String
    let keywords: [String]
    let author: String?
    let publishDate: String?
    let readingTime: Int
    let contentType: String

    enum CodingKeys: String, CodingKey {
        case title, content, summary, keywords, author
        case publishDate = "publish_date"
        case readingTime = "reading_time"
        case contentType = "content_type"
    }
}

// MARK: - Sample Data for Previews
extension Article {
    static let sampleArticles: [Article] = [
        Article(
            url: "https://example.com/article1",
            title: "The Future of AI in Mobile Development",
            content: "Artificial Intelligence is revolutionizing how we build mobile applications. From intelligent user interfaces to predictive analytics, AI is becoming an integral part of the mobile development ecosystem...",
            summary: "Exploring how AI is transforming mobile app development with practical examples and future predictions.",
            keywords: ["AI", "Mobile", "Development", "iOS", "Machine Learning"],
            author: "Jane Smith",
            publishDate: Date().addingTimeInterval(-86400 * 2), // 2 days ago
            readingTime: 8,
            contentType: .article,
            faviconURL: "https://example.com/favicon.ico",
            imageURL: "https://example.com/ai-mobile.jpg",
            isFavorite: false,
            isDownloadedForOffline: true,
            readProgress: 0.0,
            savedAt: Date().addingTimeInterval(-86400 * 2)
        ),
        Article(
            url: "https://techblog.com/swiftui-tips",
            title: "Advanced SwiftUI Techniques for Better Performance",
            content: "SwiftUI has evolved significantly since its introduction. In this comprehensive guide, we'll explore advanced techniques that can help you build more performant and maintainable SwiftUI applications...",
            summary: "A deep dive into SwiftUI performance optimization techniques and best practices for building efficient user interfaces.",
            keywords: ["SwiftUI", "iOS", "Performance", "UI", "Apple"],
            author: "John Doe",
            publishDate: Date().addingTimeInterval(-86400), // 1 day ago
            readingTime: 12,
            contentType: .article,
            faviconURL: "https://techblog.com/favicon.ico",
            imageURL: "https://techblog.com/swiftui-hero.jpg",
            isFavorite: true,
            isDownloadedForOffline: false,
            readProgress: 0.65,
            savedAt: Date().addingTimeInterval(-86400)
        ),
        Article(
            url: "https://github.com/apple/swift-evolution",
            title: "Swift Evolution: What's Coming in Swift 6.0",
            content: "The Swift programming language continues to evolve with exciting new features and improvements. Swift 6.0 brings significant enhancements to concurrency, performance, and developer experience...",
            summary: "An overview of the new features and improvements coming in Swift 6.0, including enhanced concurrency support and performance optimizations.",
            keywords: ["Swift", "Programming", "Apple", "Concurrency", "Performance"],
            author: "Swift Team",
            publishDate: Date().addingTimeInterval(-86400 * 3), // 3 days ago
            readingTime: 15,
            contentType: .github,
            faviconURL: "https://github.com/favicon.ico",
            isFavorite: true,
            isDownloadedForOffline: true,
            readProgress: 1.0,
            savedAt: Date().addingTimeInterval(-86400 * 3)
        )
    ]
}
