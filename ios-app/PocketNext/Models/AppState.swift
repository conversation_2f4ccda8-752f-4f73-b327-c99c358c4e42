import SwiftUI
import Combine

@MainActor
class AppState: ObservableObject {
    // Navigation
    @Published var selectedTab: Tab = .home
    @Published var isShowingSearch = false
    @Published var isShowingSettings = false
    @Published var selectedArticle: Article?
    
    // Feed State
    @Published var feedFilter: FeedFilter = .all
    @Published var sortOrder: SortOrder = .newest
    @Published var isRefreshing = false
    @Published var searchQuery = ""
    
    // Reading State
    @Published var readingProgress: [String: Double] = [:]
    @Published var isReaderMode = false
    @Published var readerTheme: ReaderTheme = .automatic
    @Published var fontSize: CGFloat = 17
    
    // Chat State
    @Published var chatMessages: [ChatMessage] = []
    @Published var isProcessingChat = false
    
    // UI State
    @Published var showingSaveSuccess = false
    @Published var errorMessage: String?
    @Published var isLoading = false
    
    // User Preferences
    @AppStorage("enableNotifications") var enableNotifications = true
    @AppStorage("enableDigest") var enableDigest = true
    @AppStorage("digestFrequency") var digestFrequency: DigestFrequency = .daily
    @AppStorage("preferredReaderFont") var preferredReaderFont = "System"
    @AppStorage("enableHaptics") var enableHaptics = true
    @AppStorage("autoDownloadForOffline") var autoDownloadForOffline = true
    @AppStorage("wifiOnlyDownloads") var wifiOnlyDownloads = true
    
    // Statistics
    @Published var totalArticles = 0
    @Published var unreadCount = 0
    @Published var todayReadCount = 0
    @Published var weeklyReadTime = 0
    
    private var cancellables = Set<AnyCancellable>()
    
    enum Tab: String, CaseIterable {
        case home = "house.fill"
        case search = "magnifyingglass"
        case chat = "bubble.left.fill"
        case digest = "newspaper.fill"
        case profile = "person.fill"
        
        var title: String {
            switch self {
            case .home: return "Home"
            case .search: return "Search"
            case .chat: return "Chat"
            case .digest: return "Digest"
            case .profile: return "Profile"
            }
        }
    }
    
    enum FeedFilter: String, CaseIterable {
        case all = "All"
        case unread = "Unread"
        case archived = "Archived"
        case favorites = "Favorites"
        
        var systemImage: String {
            switch self {
            case .all: return "tray.2.fill"
            case .unread: return "envelope.fill"
            case .archived: return "archivebox.fill"
            case .favorites: return "star.fill"
            }
        }
    }
    
    enum SortOrder: String, CaseIterable {
        case newest = "Newest"
        case oldest = "Oldest"
        case readingTime = "Reading Time"
        case relevance = "Relevance"
        
        var systemImage: String {
            switch self {
            case .newest: return "arrow.down.circle.fill"
            case .oldest: return "arrow.up.circle.fill"
            case .readingTime: return "clock.fill"
            case .relevance: return "sparkles"
            }
        }
    }
    
    enum ReaderTheme: String, CaseIterable {
        case automatic = "Automatic"
        case light = "Light"
        case dark = "Dark"
        case sepia = "Sepia"
        
        var backgroundColor: Color {
            switch self {
            case .automatic:
                return Color(UIColor.systemBackground)
            case .light:
                return .white
            case .dark:
                return Color(red: 0.1, green: 0.1, blue: 0.1)
            case .sepia:
                return Color(red: 0.98, green: 0.96, blue: 0.89)
            }
        }
        
        var textColor: Color {
            switch self {
            case .automatic:
                return Color(UIColor.label)
            case .light:
                return .black
            case .dark:
                return .white
            case .sepia:
                return Color(red: 0.3, green: 0.25, blue: 0.2)
            }
        }
    }
    
    enum DigestFrequency: String, CaseIterable {
        case daily = "Daily"
        case weekly = "Weekly"
        case never = "Never"
    }
    
    init() {
        setupSubscriptions()
    }
    
    private func setupSubscriptions() {
        // Listen for article updates
        NotificationCenter.default.publisher(for: .articlesUpdated)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                Task {
                    await self?.updateStatistics()
                }
            }
            .store(in: &cancellables)
        
        // Listen for sync updates
        NotificationCenter.default.publisher(for: .syncCompleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.isRefreshing = false
            }
            .store(in: &cancellables)
    }
    
    func loadUserPreferences() async {
        // Load saved reading progress
        if let data = UserDefaults.standard.data(forKey: "readingProgress"),
           let progress = try? JSONDecoder().decode([String: Double].self, from: data) {
            readingProgress = progress
        }
        
        // Update statistics
        await updateStatistics()
    }
    
    func updateStatistics() async {
        do {
            let stats = try await DatabaseManager.shared.getStatistics()
            totalArticles = stats.total
            unreadCount = stats.unread
            todayReadCount = stats.todayRead
            weeklyReadTime = stats.weeklyReadTime
        } catch {
            print("Failed to update statistics: \(error)")
        }
    }
    
    func updateReadingProgress(for articleId: String, progress: Double) {
        readingProgress[articleId] = progress
        
        // Save to UserDefaults
        if let data = try? JSONEncoder().encode(readingProgress) {
            UserDefaults.standard.set(data, forKey: "readingProgress")
        }
    }
    
    func markArticleAsRead(_ article: Article) async {
        do {
            try await DatabaseManager.shared.markAsRead(article.id)
            unreadCount = max(0, unreadCount - 1)
            todayReadCount += 1
        } catch {
            errorMessage = "Failed to mark article as read"
        }
    }
    
    func showError(_ message: String) {
        errorMessage = message
        
        // Auto-dismiss after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            if self?.errorMessage == message {
                self?.errorMessage = nil
            }
        }
    }
    
    func triggerHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .light) {
        guard enableHaptics else { return }
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.prepare()
        generator.impactOccurred()
    }
}

// MARK: - Notifications
extension Notification.Name {
    static let articlesUpdated = Notification.Name("articlesUpdated")
    static let syncCompleted = Notification.Name("syncCompleted")
    static let articleSaved = Notification.Name("articleSaved")
}

