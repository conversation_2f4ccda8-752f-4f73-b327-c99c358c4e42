import SwiftUI

struct FilterSheet: View {
    @Binding var filters: ArticleFilters
    @Environment(\.dismiss) private var dismiss
    @State private var tempFilters: ArticleFilters
    
    init(filters: Binding<ArticleFilters>) {
        self._filters = filters
        self._tempFilters = State(initialValue: filters.wrappedValue)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Filter Articles")
                    .font(.title2)
                    .bold()
                
                Spacer()
                
                Button("Reset") {
                    tempFilters = ArticleFilters()
                }
                .buttonStyle(.plain)
                .foregroundColor(.accentColor)
            }
            .padding()
            
            Divider()
            
            // Filter options
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Reading status
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Reading Status", systemImage: "book")
                            .font(.headline)
                        
                        Picker("Status", selection: $tempFilters.readingStatus) {
                            Text("All").tag(ReadingStatus.all)
                            Text("Unread").tag(ReadingStatus.unread)
                            Text("In Progress").tag(ReadingStatus.inProgress)
                            Text("Read").tag(ReadingStatus.read)
                        }
                        .pickerStyle(.segmented)
                    }
                    
                    // Date range
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Date Added", systemImage: "calendar")
                            .font(.headline)
                        
                        Picker("Date Range", selection: $tempFilters.dateRange) {
                            Text("Any Time").tag(DateRange.allTime)
                            Text("Today").tag(DateRange.today)
                            Text("This Week").tag(DateRange.thisWeek)
                            Text("This Month").tag(DateRange.thisMonth)
                            Text("Custom").tag(DateRange.custom)
                        }
                        .pickerStyle(.menu)
                        
                        if tempFilters.dateRange == .custom {
                            HStack {
                                DatePicker("From:", selection: $tempFilters.customStartDate, displayedComponents: .date)
                                DatePicker("To:", selection: $tempFilters.customEndDate, displayedComponents: .date)
                            }
                            .padding(.leading)
                        }
                    }
                    
                    // Content type
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Content Type", systemImage: "doc.richtext")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Toggle("Articles", isOn: $tempFilters.includeArticles)
                            Toggle("Videos", isOn: $tempFilters.includeVideos)
                            Toggle("PDFs", isOn: $tempFilters.includePDFs)
                            Toggle("Tweets", isOn: $tempFilters.includeTweets)
                        }
                    }
                    
                    // Special filters
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Special Filters", systemImage: "star")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Toggle("Favorites Only", isOn: $tempFilters.favoritesOnly)
                            Toggle("Archived", isOn: $tempFilters.includeArchived)
                            Toggle("Has Notes", isOn: $tempFilters.hasNotes)
                            Toggle("Has Highlights", isOn: $tempFilters.hasHighlights)
                        }
                    }
                    
                    // Reading time
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Estimated Reading Time", systemImage: "clock")
                            .font(.headline)
                        
                        HStack {
                            Text("Up to")
                            Picker("", selection: $tempFilters.maxReadingTime) {
                                Text("Any").tag(nil as Int?)
                                Text("5 min").tag(5 as Int?)
                                Text("10 min").tag(10 as Int?)
                                Text("15 min").tag(15 as Int?)
                                Text("30 min").tag(30 as Int?)
                                Text("1 hour").tag(60 as Int?)
                            }
                            .labelsHidden()
                        }
                    }
                    
                    // Tags
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Tags", systemImage: "tag")
                            .font(.headline)
                        
                        TagSelectionView(selectedTags: $tempFilters.tags)
                    }
                    
                    // Sort options
                    VStack(alignment: .leading, spacing: 12) {
                        Label("Sort By", systemImage: "arrow.up.arrow.down")
                            .font(.headline)
                        
                        Picker("Sort By", selection: $tempFilters.sortBy) {
                            Text("Date Added (Newest)").tag(SortOption.dateAddedNewest)
                            Text("Date Added (Oldest)").tag(SortOption.dateAddedOldest)
                            Text("Title (A-Z)").tag(SortOption.titleAZ)
                            Text("Title (Z-A)").tag(SortOption.titleZA)
                            Text("Reading Time (Shortest)").tag(SortOption.readingTimeShortest)
                            Text("Reading Time (Longest)").tag(SortOption.readingTimeLongest)
                        }
                        .pickerStyle(.menu)
                    }
                }
                .padding()
            }
            
            Divider()
            
            // Action buttons
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                .keyboardShortcut(.escape)
                
                Spacer()
                
                Button("Apply Filters") {
                    filters = tempFilters
                    dismiss()
                }
                .keyboardShortcut(.defaultAction)
            }
            .padding()
        }
        .frame(width: 500, height: 700)
    }
}

// MARK: - Tag Selection View
struct TagSelectionView: View {
    @Binding var selectedTags: Set<String>
    @State private var availableTags = ["Technology", "Science", "Business", "Health", "Politics", "Entertainment", "Sports", "Education"]
    @State private var newTag = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Selected tags
            if !selectedTags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack {
                        ForEach(Array(selectedTags), id: \.self) { tag in
                            TagChip(
                                tag: tag,
                                isSelected: true,
                                onTap: {
                                    selectedTags.remove(tag)
                                }
                            )
                        }
                    }
                }
                
                Divider()
            }
            
            // Available tags
            LazyVGrid(columns: [GridItem(.adaptive(minimum: 80))], spacing: 8) {
                ForEach(availableTags.filter { !selectedTags.contains($0) }, id: \.self) { tag in
                    TagChip(
                        tag: tag,
                        isSelected: false,
                        onTap: {
                            selectedTags.insert(tag)
                        }
                    )
                }
            }
            
            // Add custom tag
            HStack {
                TextField("Add custom tag...", text: $newTag)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        if !newTag.isEmpty && !availableTags.contains(newTag) {
                            availableTags.append(newTag)
                            selectedTags.insert(newTag)
                            newTag = ""
                        }
                    }
                
                Button("Add") {
                    if !newTag.isEmpty && !availableTags.contains(newTag) {
                        availableTags.append(newTag)
                        selectedTags.insert(newTag)
                        newTag = ""
                    }
                }
                .disabled(newTag.isEmpty)
            }
        }
    }
}

// MARK: - Tag Chip
struct TagChip: View {
    let tag: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 4) {
                Text(tag)
                    .font(.caption)
                
                if isSelected {
                    Image(systemName: "xmark.circle.fill")
                        .font(.caption2)
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(isSelected ? Color.accentColor : Color(UIColor.systemGray6))
            .foregroundColor(isSelected ? .white : .primary)
            .cornerRadius(12)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Supporting Types
struct ArticleFilters: Equatable {
    var readingStatus: ReadingStatus = .all
    var dateRange: DateRange = .allTime
    var customStartDate = Date()
    var customEndDate = Date()
    var includeArticles = true
    var includeVideos = true
    var includePDFs = true
    var includeTweets = true
    var favoritesOnly = false
    var includeArchived = false
    var hasNotes = false
    var hasHighlights = false
    var maxReadingTime: Int?
    var tags: Set<String> = []
    var sortBy: SortOption = .dateAddedNewest
}

enum ReadingStatus: String, CaseIterable {
    case all = "All"
    case unread = "Unread"
    case inProgress = "In Progress"
    case read = "Read"
}

enum DateRange: String, CaseIterable {
    case allTime = "All Time"
    case today = "Today"
    case thisWeek = "This Week"
    case thisMonth = "This Month"
    case custom = "Custom"
}

enum SortOption: String, CaseIterable {
    case dateAddedNewest = "Date Added (Newest)"
    case dateAddedOldest = "Date Added (Oldest)"
    case titleAZ = "Title (A-Z)"
    case titleZA = "Title (Z-A)"
    case readingTimeShortest = "Reading Time (Shortest)"
    case readingTimeLongest = "Reading Time (Longest)"
}

#Preview {
    FilterSheet(filters: .constant(ArticleFilters()))
}