import SwiftUI

struct ContentView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var databaseManager: DatabaseManager
    @State private var showingSplash = true
    
    var body: some View {
        ZStack {
            if showingSplash {
                SplashView()
                    .transition(.opacity)
                    .zIndex(1)
            } else {
                mainContent
                    .transition(.opacity)
            }
        }
        .onAppear {
            // Hide splash after a short delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                withAnimation(.easeOut(duration: 0.5)) {
                    showingSplash = false
                }
            }
        }
    }
    
    @ViewBuilder
    private var mainContent: some View {
        TabView(selection: $appState.selectedTab) {
            // Home Feed
            NavigationStack {
                HomeFeedView()
            }
            .tabItem {
                Label(AppState.Tab.home.title, systemImage: AppState.Tab.home.rawValue)
            }
            .tag(AppState.Tab.home)
            
            // Search
            NavigationStack {
                SearchView()
            }
            .tabItem {
                Label(AppState.Tab.search.title, systemImage: AppState.Tab.search.rawValue)
            }
            .tag(AppState.Tab.search)
            
            // Chat
            NavigationStack {
                ChatView()
            }
            .tabItem {
                Label(AppState.Tab.chat.title, systemImage: AppState.Tab.chat.rawValue)
            }
            .tag(AppState.Tab.chat)
            
            // Digest
            NavigationStack {
                DigestView()
            }
            .tabItem {
                Label(AppState.Tab.digest.title, systemImage: AppState.Tab.digest.rawValue)
            }
            .tag(AppState.Tab.digest)
            .badge(appState.unreadCount > 0 ? "\(appState.unreadCount)" : nil)
            
            // Profile & Settings
            NavigationStack {
                ProfileView()
            }
            .tabItem {
                Label(AppState.Tab.profile.title, systemImage: AppState.Tab.profile.rawValue)
            }
            .tag(AppState.Tab.profile)
        }
        .tint(.accentColor)
        .sheet(item: $appState.selectedArticle) { article in
            NavigationStack {
                ReadingView(article: article)
            }
            .presentationDragIndicator(.visible)
            .presentationCornerRadius(20)
        }
        .overlay(alignment: .top) {
            // Success/Error banner
            if appState.showingSaveSuccess {
                SuccessBanner(message: "Article Saved!")
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .onAppear {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            withAnimation {
                                appState.showingSaveSuccess = false
                            }
                        }
                    }
            }
            
            if let error = appState.errorMessage {
                ErrorBanner(message: error)
                    .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
    }
}

// MARK: - Splash View
struct SplashView: View {
    @State private var animating = false
    
    var body: some View {
        ZStack {
            LinearGradient(
                colors: [.blue, .purple],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Image(systemName: "bookmark.square.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.white)
                    .scaleEffect(animating ? 1.1 : 1.0)
                    .animation(
                        .easeInOut(duration: 1)
                        .repeatForever(autoreverses: true),
                        value: animating
                    )
                
                Text("Pocket-next")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text("Your Personal Knowledge Hub")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .onAppear {
            animating = true
        }
    }
}

// MARK: - Success Banner
struct SuccessBanner: View {
    let message: String
    
    var body: some View {
        HStack {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
            
            Text(message)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(12)
        .shadow(radius: 4)
        .padding(.horizontal)
        .padding(.top, 50)
    }
}

// MARK: - Error Banner
struct ErrorBanner: View {
    let message: String
    
    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
            
            Text(message)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
        }
        .padding()
        .background(.regularMaterial)
        .cornerRadius(12)
        .shadow(radius: 4)
        .padding(.horizontal)
        .padding(.top, 50)
    }
}

// MARK: - Preview
#Preview {
    ContentView()
        .environmentObject(AppState())
        .environmentObject(DatabaseManager.shared)
        .environmentObject(CloudKitSyncService.shared)
}