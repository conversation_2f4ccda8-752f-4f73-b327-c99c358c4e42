import SwiftUI

struct SearchView: View {
    @StateObject private var viewModel = SearchViewModel()
    @State private var searchText = ""
    @State private var selectedFilter: SearchFilter = .all
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search articles...", text: $searchText)
                        .textFieldStyle(.plain)
                        .focused($isSearchFocused)
                        .onSubmit {
                            viewModel.search(query: searchText, filter: selectedFilter)
                        }
                    
                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                            viewModel.clearSearch()
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(10)
                .padding()
                
                // Filter chips
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack {
                        ForEach(SearchFilter.allCases, id: \.self) { filter in
                            FilterChip(
                                title: filter.displayName,
                                isSelected: selectedFilter == filter
                            ) {
                                selectedFilter = filter
                                if !searchText.isEmpty {
                                    viewModel.search(query: searchText, filter: filter)
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                
                Divider()
                
                // Search results
                if viewModel.isSearching {
                    ProgressView("Searching...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.searchResults.isEmpty && !searchText.isEmpty {
                    ContentUnavailableView(
                        "No Results",
                        systemImage: "magnifyingglass",
                        description: Text("Try different keywords or filters")
                    )
                } else if !viewModel.searchResults.isEmpty {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(viewModel.searchResults) { article in
                                ArticleCard(article: article)
                                    .onTapGesture {
                                        // TODO: Navigate to reading view
                                    }
                            }
                        }
                        .padding()
                    }
                } else {
                    // Recent searches
                    if !viewModel.recentSearches.isEmpty {
                        VStack(alignment: .leading) {
                            Text("Recent Searches")
                                .font(.headline)
                                .padding(.horizontal)
                                .padding(.top)
                            
                            ForEach(viewModel.recentSearches, id: \.self) { search in
                                Button(action: {
                                    searchText = search
                                    viewModel.search(query: search, filter: selectedFilter)
                                }) {
                                    HStack {
                                        Image(systemName: "clock.arrow.circlepath")
                                            .foregroundColor(.secondary)
                                        Text(search)
                                            .foregroundColor(.primary)
                                        Spacer()
                                    }
                                    .padding(.horizontal)
                                    .padding(.vertical, 8)
                                }
                                .buttonStyle(.plain)
                                .background(Color(UIColor.systemGray6).opacity(0.5))
                            }
                        }
                    }
                    
                    Spacer()
                }
            }
            .navigationTitle("Search")
            .onAppear {
                isSearchFocused = true
            }
        }
    }
}

// MARK: - Supporting Types
enum SearchFilter: String, CaseIterable {
    case all = "All"
    case unread = "Unread"
    case archived = "Archived"
    case favorites = "Favorites"
    case tags = "Tags"
    
    var displayName: String { rawValue }
}

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? Color.accentColor : Color(UIColor.systemGray6))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(15)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - View Model
@MainActor
class SearchViewModel: ObservableObject {
    @Published var searchResults: [Article] = []
    @Published var isSearching = false
    @Published var recentSearches: [String] = []
    
    init() {
        loadRecentSearches()
    }
    
    func search(query: String, filter: SearchFilter) {
        guard !query.isEmpty else { return }
        
        isSearching = true
        
        // TODO: Implement actual search using SearchEngine service
        Task {
            try? await Task.sleep(nanoseconds: 500_000_000) // Simulate search
            
            // Mock results for now
            var article = Article(url: "https://example.com/1", title: "Search Result: \(query)")
            article.id = UUID().uuidString
            article.content = "Content matching your search..."
            article.author = "Author"
            article.publishDate = Date()
            article.savedAt = Date()
            article.isRead = false
            article.isFavorite = false
            article.readProgress = 0
            article.readingTime = 5
            
            searchResults = [article]
            
            // Add to recent searches
            if !recentSearches.contains(query) {
                recentSearches.insert(query, at: 0)
                if recentSearches.count > 10 {
                    recentSearches.removeLast()
                }
                saveRecentSearches()
            }
            
            isSearching = false
        }
    }
    
    func clearSearch() {
        searchResults = []
    }
    
    private func loadRecentSearches() {
        // TODO: Load from UserDefaults or persistent storage
        recentSearches = ["SwiftUI tutorials", "AI development", "macOS apps"]
    }
    
    private func saveRecentSearches() {
        // TODO: Save to UserDefaults or persistent storage
    }
}

#Preview {
    SearchView()
        .frame(width: 800, height: 600)
}