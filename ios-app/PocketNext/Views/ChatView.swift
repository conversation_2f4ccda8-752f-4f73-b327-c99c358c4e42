import SwiftUI

struct ChatView: View {
    @StateObject private var viewModel = ChatViewModel()
    @State private var messageText = ""
    @FocusState private var isMessageFieldFocused: Bool
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Chat messages
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(viewModel.messages) { message in
                                MessageBubble(message: message)
                                    .id(message.id)
                            }
                            
                            if viewModel.isTyping {
                                TypingIndicator()
                                    .id("typing")
                            }
                        }
                        .padding()
                    }
                    .onChange(of: viewModel.messages.count) { _, _ in
                        withAnimation {
                            if let lastMessage = viewModel.messages.last {
                                proxy.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }
                
                Divider()
                
                // Message input
                HStack(alignment: .bottom, spacing: 12) {
                    // Attachment button
                    Button(action: viewModel.attachCurrentArticle) {
                        Image(systemName: "paperclip")
                            .font(.title3)
                    }
                    .buttonStyle(.plain)
                    .disabled(viewModel.currentArticle == nil)
                    .help("Attach current article")
                    
                    // Message field
                    VStack(alignment: .leading, spacing: 4) {
                        if let article = viewModel.attachedArticle {
                            AttachedArticleView(article: article) {
                                viewModel.removeAttachment()
                            }
                        }
                        
                        TextField("Ask about your articles...", text: $messageText, axis: .vertical)
                            .textFieldStyle(.plain)
                            .lineLimit(1...5)
                            .focused($isMessageFieldFocused)
                            .onSubmit {
                                if !messageText.isEmpty {
                                    sendMessage()
                                }
                            }
                    }
                    .padding(8)
                    .background(Color(UIColor.systemGray6))
                    .cornerRadius(8)
                    
                    // Send button
                    Button(action: sendMessage) {
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.title2)
                            .foregroundColor(.accentColor)
                    }
                    .buttonStyle(.plain)
                    .disabled(messageText.isEmpty || viewModel.isTyping)
                    .keyboardShortcut(.return, modifiers: .command)
                }
                .padding()
            }
            .navigationTitle("AI Assistant")
            .toolbar {
                ToolbarItem(placement: .navigation) {
                    Button(action: viewModel.clearChat) {
                        Label("Clear Chat", systemImage: "trash")
                    }
                    .disabled(viewModel.messages.isEmpty)
                }
                
                ToolbarItem(placement: .automatic) {
                    Menu {
                        ForEach(ChatContext.allCases, id: \.self) { context in
                            Button(action: { viewModel.currentContext = context }) {
                                Label(context.displayName, systemImage: context.icon)
                            }
                        }
                    } label: {
                        Label(viewModel.currentContext.displayName, systemImage: "slider.horizontal.3")
                    }
                }
            }
            .onAppear {
                viewModel.loadInitialContext()
                isMessageFieldFocused = true
            }
        }
    }
    
    private func sendMessage() {
        let text = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }
        
        viewModel.sendMessage(text)
        messageText = ""
    }
}

// MARK: - Message Bubble
struct MessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isUser {
                Spacer()
            }
            
            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 4) {
                if let article = message.attachedArticle {
                    AttachedArticleView(article: article, onRemove: nil)
                        .allowsHitTesting(false)
                }
                
                Text(message.content)
                    .padding(12)
                    .background(message.isUser ? Color.accentColor : Color(UIColor.systemGray6))
                    .foregroundColor(message.isUser ? .white : .primary)
                    .cornerRadius(16)
                    .textSelection(.enabled)
                
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: 400, alignment: message.isUser ? .trailing : .leading)
            
            if !message.isUser {
                Spacer()
            }
        }
    }
}

// MARK: - Attached Article View
struct AttachedArticleView: View {
    let article: Article
    let onRemove: (() -> Void)?
    
    var body: some View {
        HStack {
            Image(systemName: "doc.text")
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(article.title)
                    .font(.caption)
                    .lineLimit(1)
                Text(article.domain ?? "Unknown source")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if let onRemove = onRemove {
                Button(action: onRemove) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
        }
        .padding(8)
        .background(Color(UIColor.systemGray6).opacity(0.5))
        .cornerRadius(8)
    }
}

// MARK: - Typing Indicator
struct TypingIndicator: View {
    @State private var animationPhase = 0.0
    
    var body: some View {
        HStack {
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.secondary)
                        .frame(width: 8, height: 8)
                        .scaleEffect(animationPhase == Double(index) ? 1.2 : 0.8)
                        .animation(.easeInOut(duration: 0.6).repeatForever().delay(Double(index) * 0.2), value: animationPhase)
                }
            }
            .padding(12)
            .background(Color(UIColor.systemGray6))
            .cornerRadius(16)
            .onAppear {
                animationPhase = 2.0
            }
            
            Spacer()
        }
    }
}

// MARK: - Supporting Types
struct ChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isUser: Bool
    let timestamp: Date
    let attachedArticle: Article?
}

enum ChatContext: String, CaseIterable {
    case general = "General"
    case currentArticle = "Current Article"
    case savedArticles = "Saved Articles"
    case summaries = "Summaries"
    
    var displayName: String { rawValue }
    
    var icon: String {
        switch self {
        case .general: return "bubble.left.and.bubble.right"
        case .currentArticle: return "doc.text"
        case .savedArticles: return "folder"
        case .summaries: return "doc.plaintext"
        }
    }
}

// MARK: - View Model
@MainActor
class ChatViewModel: ObservableObject {
    @Published var messages: [ChatMessage] = []
    @Published var isTyping = false
    @Published var currentContext = ChatContext.general
    @Published var attachedArticle: Article?
    @Published var currentArticle: Article?
    
    func loadInitialContext() {
        // TODO: Load chat history from persistent storage
        messages = [
            ChatMessage(
                content: "Hi! I'm your AI assistant. I can help you understand your saved articles, find connections between them, and answer questions about your reading list.",
                isUser: false,
                timestamp: Date(),
                attachedArticle: nil
            )
        ]
    }
    
    func sendMessage(_ text: String) {
        // Add user message
        let userMessage = ChatMessage(
            content: text,
            isUser: true,
            timestamp: Date(),
            attachedArticle: attachedArticle
        )
        messages.append(userMessage)
        
        // Clear attachment after sending
        let attachment = attachedArticle
        attachedArticle = nil
        
        // Simulate AI response
        isTyping = true
        
        Task {
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            
            // TODO: Implement actual AI chat using backend service
            let response = ChatMessage(
                content: "I understand you're asking about: \"\(text)\". This feature will connect to the AI backend to provide intelligent responses about your articles.",
                isUser: false,
                timestamp: Date(),
                attachedArticle: nil
            )
            
            messages.append(response)
            isTyping = false
        }
    }
    
    func clearChat() {
        messages.removeAll()
        loadInitialContext()
    }
    
    func attachCurrentArticle() {
        if let article = currentArticle {
            attachedArticle = article
        }
    }
    
    func removeAttachment() {
        attachedArticle = nil
    }
}

#Preview {
    ChatView()
        .frame(width: 600, height: 800)
}