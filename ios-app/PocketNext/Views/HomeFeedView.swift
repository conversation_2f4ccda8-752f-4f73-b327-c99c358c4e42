import SwiftUI

struct HomeFeedView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var databaseManager: DatabaseManager
    
    @State private var articles: [Article] = []
    @State private var isLoading = false
    @State private var showingFilterSheet = false
    @State private var searchText = ""
    @State private var filters = ArticleFilters()
    
    private var filteredArticles: [Article] {
        articles
            .filter { article in
                switch appState.feedFilter {
                case .all:
                    return !article.isArchived
                case .unread:
                    return !article.isRead && !article.isArchived
                case .archived:
                    return article.isArchived
                case .favorites:
                    return article.isFavorite
                }
            }
            .filter { $0.matches(searchQuery: searchText) }
            .sorted { lhs, rhs in
                switch appState.sortOrder {
                case .newest:
                    return lhs.savedAt > rhs.savedAt
                case .oldest:
                    return lhs.savedAt < rhs.savedAt
                case .readingTime:
                    return (lhs.readingTime ?? 0) < (rhs.readingTime ?? 0)
                case .relevance:
                    // Simple relevance based on favorites and reading progress
                    let lhsScore = (lhs.isFavorite ? 10 : 0) + Int(lhs.readProgress * 5)
                    let rhsScore = (rhs.isFavorite ? 10 : 0) + Int(rhs.readProgress * 5)
                    return lhsScore > rhsScore
                }
            }
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                if articles.isEmpty && !isLoading {
                    EmptyStateView()
                } else {
                    articleList
                }
                
                if isLoading {
                    ProgressView()
                        .scaleEffect(1.5)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.3))
                }
            }
            .navigationTitle("Library")
            .navigationBarTitleDisplayMode(.large)
            .searchable(text: $searchText, prompt: "Search articles")
            .refreshable {
                await refreshArticles()
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    filterButton
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    sortButton
                }
            }
            .sheet(isPresented: $showingFilterSheet) {
                FilterSheet(filters: $filters)
                    .presentationDetents([.medium])
                    .presentationDragIndicator(.visible)
            }
        }
        .task {
            await loadArticles()
        }
    }
    
    // MARK: - Article List
    private var articleList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                // Stats Card
                if appState.feedFilter == .all {
                    StatsCard()
                        .padding(.horizontal)
                        .padding(.top, 8)
                }
                
                // Filter Chips
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(AppState.FeedFilter.allCases, id: \.self) { filter in
                            HomeFeedFilterChip(
                                title: filter.rawValue,
                                isSelected: appState.feedFilter == filter,
                                count: countArticles(for: filter)
                            ) {
                                withAnimation(.spring(response: 0.3)) {
                                    appState.feedFilter = filter
                                    appState.triggerHapticFeedback(.light)
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                }
                
                // Articles
                ForEach(filteredArticles) { article in
                    ArticleCard(article: article)
                        .padding(.horizontal)
                        .onTapGesture {
                            appState.selectedArticle = article
                            appState.triggerHapticFeedback(.light)
                        }
                        .contextMenu {
                            articleContextMenu(for: article)
                        }
                        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                            swipeActions(for: article)
                        }
                }
                
                // Load more indicator
                if filteredArticles.count > 20 {
                    ProgressView()
                        .padding()
                }
            }
            .padding(.bottom, 100)
        }
    }
    
    // MARK: - Toolbar Items
    private var filterButton: some View {
        Button(action: {
            showingFilterSheet = true
            appState.triggerHapticFeedback(.light)
        }) {
            Image(systemName: "line.3.horizontal.decrease.circle")
                .symbolRenderingMode(.hierarchical)
        }
    }
    
    private var sortButton: some View {
        Menu {
            ForEach(AppState.SortOrder.allCases, id: \.self) { order in
                Button(action: {
                    appState.sortOrder = order
                    appState.triggerHapticFeedback(.light)
                }) {
                    Label(order.rawValue, systemImage: order.systemImage)
                }
            }
        } label: {
            Image(systemName: "arrow.up.arrow.down.circle")
                .symbolRenderingMode(.hierarchical)
        }
    }
    
    // MARK: - Context Menu
    @ViewBuilder
    private func articleContextMenu(for article: Article) -> some View {
        Button(action: {
            Task {
                await toggleFavorite(article)
            }
        }) {
            Label(
                article.isFavorite ? "Remove from Favorites" : "Add to Favorites",
                systemImage: article.isFavorite ? "star.slash" : "star"
            )
        }
        
        Button(action: {
            Task {
                await toggleArchive(article)
            }
        }) {
            Label(
                article.isArchived ? "Unarchive" : "Archive",
                systemImage: article.isArchived ? "archivebox.slash" : "archivebox"
            )
        }
        
        Button(action: {
            shareArticle(article)
        }) {
            Label("Share", systemImage: "square.and.arrow.up")
        }
        
        Divider()
        
        Button(role: .destructive, action: {
            Task {
                await deleteArticle(article)
            }
        }) {
            Label("Delete", systemImage: "trash")
        }
    }
    
    // MARK: - Swipe Actions
    @ViewBuilder
    private func swipeActions(for article: Article) -> some View {
        Button(action: {
            Task {
                await deleteArticle(article)
            }
        }) {
            Label("Delete", systemImage: "trash")
        }
        .tint(.red)
        
        Button(action: {
            Task {
                await toggleArchive(article)
            }
        }) {
            Label(
                article.isArchived ? "Unarchive" : "Archive",
                systemImage: article.isArchived ? "archivebox.slash" : "archivebox"
            )
        }
        .tint(.orange)
        
        Button(action: {
            Task {
                await toggleFavorite(article)
            }
        }) {
            Label(
                article.isFavorite ? "Unfavorite" : "Favorite",
                systemImage: article.isFavorite ? "star.slash" : "star"
            )
        }
        .tint(.yellow)
    }
    
    // MARK: - Data Methods
    private func loadArticles() async {
        isLoading = true
        
        // Wait for database to be initialized
        guard databaseManager.isInitialized else {
            // Try again after a short delay
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            if databaseManager.isInitialized {
                await loadArticles()
            } else {
                isLoading = false
                appState.showError("Database not ready")
            }
            return
        }
        
        do {
            articles = try await databaseManager.fetchAllArticles()
            await appState.updateStatistics()
        } catch {
            appState.showError("Failed to load articles")
        }
        isLoading = false
    }
    
    private func refreshArticles() async {
        appState.isRefreshing = true
        
        // Trigger sync
        CloudKitSyncService.shared.startSync()
        
        // Reload data
        await loadArticles()
        
        appState.isRefreshing = false
    }
    
    private func countArticles(for filter: AppState.FeedFilter) -> Int {
        articles.filter { article in
            switch filter {
            case .all:
                return !article.isArchived
            case .unread:
                return !article.isRead && !article.isArchived
            case .archived:
                return article.isArchived
            case .favorites:
                return article.isFavorite
            }
        }.count
    }
    
    private func toggleFavorite(_ article: Article) async {
        var updatedArticle = article
        updatedArticle.isFavorite.toggle()
        
        do {
            try await databaseManager.updateArticle(updatedArticle)
            if let index = articles.firstIndex(where: { $0.id == article.id }) {
                articles[index] = updatedArticle
            }
            appState.triggerHapticFeedback(.medium)
        } catch {
            appState.showError("Failed to update favorite status")
        }
    }
    
    private func toggleArchive(_ article: Article) async {
        var updatedArticle = article
        updatedArticle.isArchived.toggle()
        
        do {
            try await databaseManager.updateArticle(updatedArticle)
            if let index = articles.firstIndex(where: { $0.id == article.id }) {
                articles[index] = updatedArticle
            }
            appState.triggerHapticFeedback(.medium)
        } catch {
            appState.showError("Failed to update archive status")
        }
    }
    
    private func deleteArticle(_ article: Article) async {
        do {
            try await databaseManager.deleteArticle(article.id)
            articles.removeAll { $0.id == article.id }
            await appState.updateStatistics()
            appState.triggerHapticFeedback(.medium)
        } catch {
            appState.showError("Failed to delete article")
        }
    }
    
    private func shareArticle(_ article: Article) {
        let activityController = UIActivityViewController(
            activityItems: [article.url, article.title],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityController, animated: true)
        }
    }
}

// MARK: - Supporting Views

struct EmptyStateView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "bookmark.slash")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("No Articles Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Save articles from Safari or any app\nusing the share button")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: {
                // Show onboarding or instructions
            }) {
                Label("Learn How", systemImage: "questionmark.circle")
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.accentColor)
                    .foregroundColor(.white)
                    .cornerRadius(20)
            }
        }
        .padding()
    }
}

struct StatsCard: View {
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        HStack(spacing: 0) {
            StatItem(
                value: "\(appState.totalArticles)",
                label: "Total",
                color: .blue
            )
            
            Divider()
                .frame(height: 40)
            
            StatItem(
                value: "\(appState.unreadCount)",
                label: "Unread",
                color: .orange
            )
            
            Divider()
                .frame(height: 40)
            
            StatItem(
                value: "\(appState.todayReadCount)",
                label: "Today",
                color: .green
            )
            
            Divider()
                .frame(height: 40)
            
            StatItem(
                value: "\(appState.weeklyReadTime)m",
                label: "This Week",
                color: .purple
            )
        }
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct StatItem: View {
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(.title3, design: .rounded))
                .fontWeight(.semibold)
                .foregroundColor(color)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct HomeFeedFilterChip: View {
    let title: String
    let isSelected: Bool
    let count: Int
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(isSelected ? .semibold : .medium)
                
                if count > 0 {
                    Text("\(count)")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(isSelected ? Color.white : Color.accentColor)
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? Color.accentColor : Color(.systemGray5))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
    }
}

// MARK: - Preview
#Preview {
    NavigationStack {
        HomeFeedView()
    }
    .environmentObject(AppState())
    .environmentObject(DatabaseManager.shared)
}