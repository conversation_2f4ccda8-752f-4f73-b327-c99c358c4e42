import SwiftUI
import Charts

struct ProfileView: View {
    @StateObject private var viewModel = ProfileViewModel()
    @State private var selectedTimeRange = TimeRange.week
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Profile header
                    ProfileHeaderView(profile: viewModel.profile)
                    
                    // Reading stats overview
                    ReadingStatsGrid(stats: viewModel.readingStats)
                    
                    // Reading activity chart
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Reading Activity")
                                .font(.headline)
                            
                            Spacer()
                            
                            Picker("Time Range", selection: $selectedTimeRange) {
                                ForEach(TimeRange.allCases, id: \.self) { range in
                                    Text(range.displayName).tag(range)
                                }
                            }
                            .pickerStyle(.segmented)
                            .frame(width: 200)
                        }
                        
                        ReadingActivityChart(
                            data: viewModel.readingActivity,
                            timeRange: selectedTimeRange
                        )
                        .frame(height: 200)
                    }
                    .padding()
                    .background(Color(UIColor.systemGray6).opacity(0.5))
                    .cornerRadius(12)
                    
                    // Reading goals
                    ReadingGoalsView(goals: viewModel.readingGoals)
                    
                    // Recent achievements
                    if !viewModel.achievements.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Recent Achievements")
                                .font(.headline)
                            
                            LazyVGrid(columns: [GridItem(.adaptive(minimum: 100))], spacing: 12) {
                                ForEach(viewModel.achievements) { achievement in
                                    AchievementBadge(achievement: achievement)
                                }
                            }
                        }
                        .padding()
                        .background(Color(UIColor.systemGray6).opacity(0.5))
                        .cornerRadius(12)
                    }
                    
                    // Export options
                    HStack {
                        Button(action: viewModel.exportReadingData) {
                            Label("Export Data", systemImage: "arrow.down.doc")
                        }
                        
                        Button(action: viewModel.shareProfile) {
                            Label("Share Profile", systemImage: "square.and.arrow.up")
                        }
                    }
                    .padding(.top)
                }
                .padding()
            }
            .navigationTitle("Profile")
            .toolbar {
                ToolbarItem(placement: .automatic) {
                    Button(action: viewModel.refreshStats) {
                        Label("Refresh", systemImage: "arrow.clockwise")
                    }
                }
            }
            .onAppear {
                viewModel.loadProfile()
            }
            .onChange(of: selectedTimeRange) { _, newRange in
                viewModel.loadReadingActivity(for: newRange)
            }
        }
    }
}

// MARK: - Profile Header
struct ProfileHeaderView: View {
    let profile: UserProfile?
    
    var body: some View {
        HStack(spacing: 20) {
            // Avatar
            Image(systemName: "person.circle.fill")
                .resizable()
                .frame(width: 80, height: 80)
                .foregroundColor(.accentColor)
            
            VStack(alignment: .leading, spacing: 8) {
                Text(profile?.name ?? "Reader")
                    .font(.title)
                    .bold()
                
                Text("Member since \(profile?.memberSince ?? "Unknown")")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 16) {
                    Label("\(profile?.totalArticles ?? 0) articles", systemImage: "doc.text")
                    Label("\(profile?.totalReadTime ?? 0) hours", systemImage: "clock")
                }
                .font(.caption)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(UIColor.systemGray6).opacity(0.5))
        .cornerRadius(12)
    }
}

// MARK: - Reading Stats Grid
struct ReadingStatsGrid: View {
    let stats: ReadingStats?
    
    var body: some View {
        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
            StatCard(
                title: "Articles Read",
                value: "\(stats?.articlesRead ?? 0)",
                icon: "book",
                color: .blue
            )
            
            StatCard(
                title: "Reading Streak",
                value: "\(stats?.currentStreak ?? 0) days",
                icon: "flame",
                color: .orange
            )
            
            StatCard(
                title: "Avg. Reading Time",
                value: "\(stats?.avgReadingTime ?? 0) min",
                icon: "timer",
                color: .green
            )
            
            StatCard(
                title: "Completion Rate",
                value: "\(stats?.completionRate ?? 0)%",
                icon: "checkmark.circle",
                color: .purple
            )
        }
    }
}

// MARK: - Stat Card
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(value)
                .font(.title2)
                .bold()
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(UIColor.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Reading Activity Chart
struct ReadingActivityChart: View {
    let data: [ReadingActivityData]
    let timeRange: TimeRange
    
    var body: some View {
        Chart(data) { item in
            BarMark(
                x: .value("Date", item.date, unit: timeRange.chartUnit),
                y: .value("Articles", item.articlesRead)
            )
            .foregroundStyle(Color.accentColor.gradient)
        }
        .chartXAxis {
            AxisMarks(values: .stride(by: timeRange.chartStride)) { _ in
                AxisValueLabel(format: timeRange.dateFormat)
                AxisGridLine()
            }
        }
        .chartYAxis {
            AxisMarks { _ in
                AxisValueLabel()
                AxisGridLine()
            }
        }
    }
}

// MARK: - Reading Goals
struct ReadingGoalsView: View {
    let goals: [ReadingGoal]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Reading Goals")
                .font(.headline)
            
            ForEach(goals) { goal in
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(goal.title)
                            .font(.subheadline)
                        Spacer()
                        Text("\(goal.current)/\(goal.target)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    ProgressView(value: Double(goal.current), total: Double(goal.target))
                        .tint(goal.progress >= 1.0 ? .green : .accentColor)
                }
                .padding()
                .background(Color(UIColor.systemGray6))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(UIColor.systemGray6).opacity(0.5))
        .cornerRadius(12)
    }
}

// MARK: - Achievement Badge
struct AchievementBadge: View {
    let achievement: Achievement
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: achievement.icon)
                .font(.title)
                .foregroundColor(achievement.isUnlocked ? .accentColor : .secondary)
            
            Text(achievement.title)
                .font(.caption)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(width: 100, height: 100)
        .background(Color(UIColor.systemGray6))
        .cornerRadius(12)
        .opacity(achievement.isUnlocked ? 1.0 : 0.6)
    }
}

// MARK: - Supporting Types
enum TimeRange: String, CaseIterable {
    case week = "Week"
    case month = "Month"
    case year = "Year"
    
    var displayName: String { rawValue }
    
    var chartUnit: Calendar.Component {
        switch self {
        case .week: return .day
        case .month: return .day
        case .year: return .month
        }
    }
    
    var chartStride: Calendar.Component {
        switch self {
        case .week: return .day
        case .month: return .weekOfMonth
        case .year: return .month
        }
    }
    
    var dateFormat: Date.FormatStyle {
        switch self {
        case .week: return .dateTime.weekday(.abbreviated)
        case .month: return .dateTime.day()
        case .year: return .dateTime.month(.abbreviated)
        }
    }
}

struct UserProfile {
    let name: String
    let memberSince: String
    let totalArticles: Int
    let totalReadTime: Int
}

struct ReadingStats {
    let articlesRead: Int
    let currentStreak: Int
    let avgReadingTime: Int
    let completionRate: Int
}

struct ReadingActivityData: Identifiable {
    let id = UUID()
    let date: Date
    let articlesRead: Int
}

struct ReadingGoal: Identifiable {
    let id = UUID()
    let title: String
    let current: Int
    let target: Int
    
    var progress: Double {
        Double(current) / Double(target)
    }
}

struct Achievement: Identifiable {
    let id = UUID()
    let title: String
    let icon: String
    let isUnlocked: Bool
}

// MARK: - View Model
@MainActor
class ProfileViewModel: ObservableObject {
    @Published var profile: UserProfile?
    @Published var readingStats: ReadingStats?
    @Published var readingActivity: [ReadingActivityData] = []
    @Published var readingGoals: [ReadingGoal] = []
    @Published var achievements: [Achievement] = []
    
    func loadProfile() {
        // TODO: Load actual profile data from backend
        profile = UserProfile(
            name: "John Doe",
            memberSince: "January 2024",
            totalArticles: 342,
            totalReadTime: 156
        )
        
        readingStats = ReadingStats(
            articlesRead: 342,
            currentStreak: 7,
            avgReadingTime: 12,
            completionRate: 78
        )
        
        loadReadingActivity(for: .week)
        loadReadingGoals()
        loadAchievements()
    }
    
    func loadReadingActivity(for timeRange: TimeRange) {
        // TODO: Load actual reading activity data
        let calendar = Calendar.current
        let today = Date()
        var activities: [ReadingActivityData] = []
        
        let days = timeRange == .week ? 7 : (timeRange == .month ? 30 : 365)
        
        for i in 0..<days {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                activities.append(ReadingActivityData(
                    date: date,
                    articlesRead: Int.random(in: 0...10)
                ))
            }
        }
        
        readingActivity = activities.reversed()
    }
    
    func loadReadingGoals() {
        readingGoals = [
            ReadingGoal(title: "Daily Reading", current: 3, target: 5),
            ReadingGoal(title: "Weekly Articles", current: 18, target: 25),
            ReadingGoal(title: "Monthly Hours", current: 12, target: 20)
        ]
    }
    
    func loadAchievements() {
        achievements = [
            Achievement(title: "Speed Reader", icon: "hare", isUnlocked: true),
            Achievement(title: "Week Warrior", icon: "calendar", isUnlocked: true),
            Achievement(title: "Knowledge Seeker", icon: "magnifyingglass", isUnlocked: false),
            Achievement(title: "Completionist", icon: "checkmark.seal", isUnlocked: false)
        ]
    }
    
    func refreshStats() {
        loadProfile()
    }
    
    func exportReadingData() {
        // TODO: Implement data export
    }
    
    func shareProfile() {
        // TODO: Implement profile sharing
    }
}

#Preview {
    ProfileView()
        .frame(width: 800, height: 900)
}