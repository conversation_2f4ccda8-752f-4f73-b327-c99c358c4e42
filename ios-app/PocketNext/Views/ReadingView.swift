import SwiftUI
import WebKit

struct ReadingView: View {
    let article: Article
    @StateObject private var viewModel = ReadingViewModel()
    @State private var showingOriginal = false
    @State private var fontSize: CGFloat = 16
    @State private var lineSpacing: CGFloat = 1.5
    @State private var fontFamily = "System"
    @State private var theme = ReadingTheme.light
    @State private var showingAnnotationPopover = false
    @State private var selectedText = ""
    @State private var selectedRange: NSRange?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            // Main reading area
            ScrollViewReader { proxy in
                ScrollView {
                    readingContent
                    .background(theme.backgroundColor)
                    .onAppear {
                        viewModel.startReading(article: article)
                    }
                    .onDisappear {
                        viewModel.saveProgress()
                    }
                }
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItemGroup(placement: .navigation) {
                    Button(action: { dismiss() }) {
                        Label("Back", systemImage: "chevron.left")
                    }
                    
                    Text(article.title)
                        .font(.headline)
                        .lineLimit(1)
                }
                
                ToolbarItemGroup {
                    // View options
                    Menu {
                        Button(action: { showingOriginal.toggle() }) {
                            Label(showingOriginal ? "Reader View" : "Original", 
                                  systemImage: showingOriginal ? "doc.text" : "safari")
                        }
                        
                        Divider()
                        
                        Menu("Font Size") {
                            ForEach([14, 16, 18, 20, 22], id: \.self) { size in
                                Button(action: { fontSize = CGFloat(size) }) {
                                    Text("\(size)pt")
                                        .tag(CGFloat(size))
                                }
                            }
                        }
                        
                        Menu("Line Spacing") {
                            ForEach([1.0, 1.5, 2.0, 2.5], id: \.self) { spacing in
                                Button(action: { lineSpacing = spacing }) {
                                    Text("\(spacing, specifier: "%.1f")x")
                                }
                            }
                        }
                        
                        Menu("Theme") {
                            ForEach(ReadingTheme.allCases, id: \.self) { readingTheme in
                                Button(action: { theme = readingTheme }) {
                                    Label(readingTheme.displayName, 
                                          systemImage: readingTheme == theme ? "checkmark" : "")
                                }
                            }
                        }
                    } label: {
                        Label("View Options", systemImage: "textformat")
                    }
                    
                    // Annotations toggle
                    Button(action: { viewModel.showAnnotations.toggle() }) {
                        Label("Annotations", systemImage: "note.text")
                    }
                    
                    // Actions
                    Menu {
                        Button(action: viewModel.toggleFavorite) {
                            Label(viewModel.isFavorite ? "Unfavorite" : "Favorite",
                                  systemImage: viewModel.isFavorite ? "star.fill" : "star")
                        }
                        
                        Button(action: viewModel.toggleArchive) {
                            Label(viewModel.isArchived ? "Unarchive" : "Archive",
                                  systemImage: "archivebox")
                        }
                        
                        Divider()
                        
                        Button(action: viewModel.shareArticle) {
                            Label("Share", systemImage: "square.and.arrow.up")
                        }
                        
                        Button(action: viewModel.exportArticle) {
                            Label("Export", systemImage: "arrow.down.doc")
                        }
                        
                        Divider()
                        
                        Button(action: viewModel.deleteArticle) {
                            Label("Delete", systemImage: "trash")
                        }
                        .foregroundColor(.red)
                    } label: {
                        Label("Actions", systemImage: "ellipsis.circle")
                    }
                }
            }
        }
    }
    
    // Extracted complex view to simplify body
    private var readingContent: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Article header
            VStack(alignment: .leading, spacing: 12) {
                Text(article.title)
                    .font(.largeTitle)
                    .bold()
                    .textSelection(.enabled)
                
                HStack {
                    if let author = article.author {
                        Label(author, systemImage: "person")
                    }
                    
                    if let domain = article.domain {
                        Label(domain, systemImage: "globe")
                    }
                    
                    if let date = article.publishDate {
                        Label(date.formatted(date: .abbreviated, time: .omitted), systemImage: "calendar")
                    }
                    
                    Spacer()
                    
                    Text("\(article.readingTime) min read")
                        .foregroundColor(.secondary)
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding(.bottom)
            
            // Article content
            if showingOriginal {
                WebContentView(url: article.url)
                    .frame(minHeight: 600)
            } else {
                Text(article.content ?? "")
                    .font(.custom(fontFamily, size: fontSize))
                    .lineSpacing(fontSize * (lineSpacing - 1))
                    .textSelection(.enabled)
                    .onOpenURL { url in
                        UIApplication.shared.open(url)
                    }
            }
            
            // Reading progress indicator
            ReadingProgressBar(progress: viewModel.readingProgress)
                .padding(.top)
        }
        .padding(40)
        .frame(maxWidth: 800)
        .frame(maxWidth: .infinity)
        .background(theme.backgroundColor)
        .foregroundColor(theme.textColor)
    }
}

// MARK: - Web Content View
struct WebContentView: UIViewRepresentable {
    let url: String
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        if let url = URL(string: url) {
            let request = URLRequest(url: url)
            webView.load(request)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        // TODO: Implement navigation delegate methods
    }
}

// MARK: - Reading Progress Bar
struct ReadingProgressBar: View {
    let progress: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Reading Progress")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: progress)
                .tint(.accentColor)
        }
    }
}

// MARK: - Annotations Sidebar
struct AnnotationsSidebar: View {
    let annotations: [Annotation]
    let onAddNote: (String) -> Void
    @State private var newNote = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Notes & Highlights")
                .font(.headline)
                .padding(.horizontal)
                .padding(.top)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 12) {
                    // Add new note
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Add Note")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        TextEditor(text: $newNote)
                            .font(.body)
                            .frame(minHeight: 60)
                            .padding(8)
                            .background(Color(UIColor.systemBackground))
                            .cornerRadius(8)
                        
                        Button(action: {
                            if !newNote.isEmpty {
                                onAddNote(newNote)
                                newNote = ""
                            }
                        }) {
                            Text("Add Note")
                                .frame(maxWidth: .infinity)
                        }
                        .controlSize(.small)
                        .disabled(newNote.isEmpty)
                    }
                    .padding(.horizontal)
                    
                    Divider()
                    
                    // Existing annotations
                    ForEach(annotations) { annotation in
                        AnnotationCard(annotation: annotation)
                            .padding(.horizontal)
                    }
                }
            }
        }
        .background(Color(UIColor.systemGray6))
    }
}

// MARK: - Annotation Card
struct AnnotationCard: View {
    let annotation: Annotation
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            if let highlight = annotation.highlightedText {
                Text(highlight)
                    .font(.caption)
                    .italic()
                    .padding(8)
                    .background(Color.yellow.opacity(0.2))
                    .cornerRadius(4)
            }
            
            if let note = annotation.note {
                Text(note)
                    .font(.body)
            }
            
            Text(annotation.timestamp, style: .relative)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(UIColor.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Supporting Types
enum ReadingTheme: String, CaseIterable {
    case light = "Light"
    case dark = "Dark"
    case sepia = "Sepia"
    case auto = "Auto"
    
    var displayName: String { rawValue }
    
    var backgroundColor: Color {
        switch self {
        case .light: return Color.white
        case .dark: return Color.black
        case .sepia: return Color(red: 0.96, green: 0.94, blue: 0.86)
        case .auto: return Color(UIColor.systemBackground)
        }
    }
    
    var textColor: Color {
        switch self {
        case .light: return Color.black
        case .dark: return Color.white
        case .sepia: return Color(red: 0.3, green: 0.2, blue: 0.1)
        case .auto: return Color(UIColor.label)
        }
    }
}

struct Annotation: Identifiable {
    let id = UUID()
    let articleId: String
    let highlightedText: String?
    let note: String?
    let position: Int
    let timestamp: Date
}

// MARK: - View Model
@MainActor
class ReadingViewModel: ObservableObject {
    @Published var readingProgress: Double = 0
    @Published var annotations: [Annotation] = []
    @Published var showAnnotations = false
    @Published var isFavorite = false
    @Published var isArchived = false
    
    private var article: Article?
    private var readingTimer: Timer?
    
    func startReading(article: Article) {
        self.article = article
        self.isFavorite = article.isFavorite
        self.isArchived = article.isArchived
        self.readingProgress = article.readProgress
        
        loadAnnotations()
        startReadingTimer()
    }
    
    func saveProgress() {
        // TODO: Save reading progress to database
        readingTimer?.invalidate()
    }
    
    func addNote(_ note: String) {
        guard let article = article else { return }
        
        let annotation = Annotation(
            articleId: article.id,
            highlightedText: nil,
            note: note,
            position: 0,
            timestamp: Date()
        )
        
        annotations.append(annotation)
        // TODO: Save annotation to database
    }
    
    func toggleFavorite() {
        isFavorite.toggle()
        // TODO: Update article favorite status in database
    }
    
    func toggleArchive() {
        isArchived.toggle()
        // TODO: Update article archive status in database
    }
    
    func shareArticle() {
        // TODO: Implement share functionality
    }
    
    func exportArticle() {
        // TODO: Implement export functionality
    }
    
    func deleteArticle() {
        // TODO: Implement delete functionality
    }
    
    private func loadAnnotations() {
        // TODO: Load annotations from database
    }
    
    private func startReadingTimer() {
        readingTimer = Timer.scheduledTimer(withTimeInterval: 10, repeats: true) { _ in
            Task { @MainActor in
                self.updateReadingProgress()
            }
        }
    }
    
    private func updateReadingProgress() {
        // TODO: Calculate actual reading progress based on scroll position
        readingProgress = min(readingProgress + 0.1, 1.0)
    }
}

#Preview {
    ReadingView(article: {
        var article = Article(url: "https://example.com", title: "Sample Article")
        article.id = "1"
        article.content = "This is a sample article content for preview..."
        article.author = "John Doe"
        article.publishDate = Date()
        article.savedAt = Date()
        article.isRead = false
        article.isFavorite = false
        article.readProgress = 0.3
        article.readingTime = 10
        return article
    }())
    .frame(width: 1200, height: 800)
}