import SwiftUI
import <PERSON><PERSON><PERSON>

struct ArticleCard: View {
    let article: Article
    @EnvironmentObject private var appState: AppState
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with favicon and domain
            HStack {
                if let faviconURL = article.faviconURL {
                    KFImage(URL(string: faviconURL))
                        .placeholder {
                            Image(systemName: "globe")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .resizable()
                        .frame(width: 16, height: 16)
                        .cornerRadius(3)
                }
                
                Text(article.domain ?? "Unknown Source")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack(spacing: 8) {
                    if article.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                    }
                    
                    if article.isDownloadedForOffline {
                        Image(systemName: "arrow.down.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                    
                    Text(article.formattedSavedDate)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Title
            Text(article.title)
                .font(.headline)
                .lineLimit(2)
                .fixedSize(horizontal: false, vertical: true)
            
            // Summary or content preview
            if let summary = article.summary ?? article.content {
                Text(summary)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            // Image preview (if available)
            if let imageURL = article.imageURL {
                KFImage(URL(string: imageURL))
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 180)
                    .clipped()
                    .cornerRadius(8)
            }
            
            // Footer with tags and reading info
            HStack {
                // Tags
                if !article.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(article.tags.prefix(3), id: \.self) { tag in
                                TagView(tag: tag)
                            }
                            if article.tags.count > 3 {
                                Text("+\(article.tags.count - 3)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                Spacer()
                
                // Reading progress or time
                if article.readProgress > 0 && !article.isFullyRead {
                    CircularProgressView(progress: article.readProgress)
                        .frame(width: 20, height: 20)
                } else if !article.isRead {
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption)
                        Text(article.estimatedReadingTime)
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(
                    color: .black.opacity(0.1),
                    radius: isPressed ? 2 : 5,
                    x: 0,
                    y: isPressed ? 1 : 2
                )
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .onTapGesture {
            // Handled by parent view
        }
        ._onButtonGesture { pressing in
            isPressed = pressing
        }
    }
}

// MARK: - Supporting Views

struct TagView: View {
    let tag: String
    
    var body: some View {
        Text(tag)
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(Color.accentColor.opacity(0.1))
            )
            .foregroundColor(.accentColor)
    }
}

struct CircularProgressView: View {
    let progress: Double
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.2), lineWidth: 2)
            
            Circle()
                .trim(from: 0, to: progress)
                .stroke(Color.accentColor, lineWidth: 2)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut, value: progress)
            
            Text("\(Int(progress * 100))%")
                .font(.system(size: 8))
                .fontWeight(.semibold)
        }
    }
}

// MARK: - Button Gesture Extension
extension View {
    func _onButtonGesture(
        pressing: @escaping (Bool) -> Void
    ) -> some View {
        self.onLongPressGesture(
            minimumDuration: .infinity,
            maximumDistance: .infinity,
            pressing: pressing,
            perform: {}
        )
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        ArticleCard(article: Article.sampleArticles[0])
        ArticleCard(article: {
            var article = Article.sampleArticles[1]
            article.readProgress = 0.65
            article.tags = ["SwiftUI", "iOS", "Development", "Tutorial"]
            return article
        }())
        ArticleCard(article: {
            var article = Article.sampleArticles[2]
            article.isRead = true
            article.isFavorite = true
            article.isDownloadedForOffline = true
            return article
        }())
    }
    .padding()
    .environmentObject(AppState())
}