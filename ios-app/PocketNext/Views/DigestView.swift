import SwiftUI

struct DigestView: View {
    @StateObject private var viewModel = DigestViewModel()
    @State private var selectedDigestType = DigestType.daily
    @State private var showingCustomizeSheet = false

    var body: some View {
        NavigationStack {
            content
        }
    }

    @ViewBuilder
    private var content: some View {
        VStack(spacing: 0) {
            digestTypePicker

            if viewModel.isLoading {
                loadingView
            } else if let digest = viewModel.currentDigest {
                digestContent(digest)
            } else {
                emptyStateView
            }
        }
        .navigationTitle("Reading Digest")
        .toolbar {
            ToolbarItem(placement: .automatic) {
                Button(action: { showingCustomizeSheet = true }) {
                    Label("Customize", systemImage: "slider.horizontal.3")
                }
            }
        }
        .sheet(isPresented: $showingCustomizeSheet) {
            DigestCustomizationSheet(viewModel: viewModel)
        }
        .onAppear {
            viewModel.loadDigest(type: selectedDigestType)
        }
    }

    private var digestTypePicker: some View {
        Picker("Digest Type", selection: $selectedDigestType) {
            ForEach(DigestType.allCases, id: \.self) { type in
                Text(type.displayName).tag(type)
            }
        }
        .pickerStyle(.segmented)
        .padding()
        .onChange(of: selectedDigestType) { _, newType in
            viewModel.loadDigest(type: newType)
        }
    }

    private var loadingView: some View {
        ProgressView("Generating digest...")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptyStateView: some View {
        ContentUnavailableView(
            "No Digest Available",
            systemImage: "newspaper",
            description: Text("Your \(selectedDigestType.displayName.lowercased()) digest will appear here")
        )
    }

    private func digestContent(_ digest: Digest) -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Digest header
                DigestHeaderView(digest: digest)

                // Key insights
                if !digest.keyInsights.isEmpty {
                    keyInsightsSection(digest: digest)
                }

                // Article summaries
                articleSummariesSection(digest: digest)

                // Reading recommendations
                if !digest.recommendations.isEmpty {
                    recommendationsSection(digest: digest)
                }

                // Share and export options
                actionButtons
            }
            .padding()
        }
    }

    private func keyInsightsSection(digest: Digest) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Key Insights", systemImage: "lightbulb")
                .font(.headline)

            ForEach(digest.keyInsights, id: \.self) { insight in
                HStack(alignment: .top, spacing: 8) {
                    Circle()
                        .fill(Color.accentColor)
                        .frame(width: 6, height: 6)
                        .offset(y: 6)
                    Text(insight)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    private func articleSummariesSection(digest: Digest) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Label("Article Summaries", systemImage: "doc.text")
                .font(.headline)

            ForEach(digest.articleSummaries) { summary in
                ArticleSummaryCard(summary: summary)
            }
        }
    }

    private func recommendationsSection(digest: Digest) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Recommended Reading", systemImage: "star")
                .font(.headline)

            ForEach(digest.recommendations) { article in
                ArticleCard(article: article)
                    .onTapGesture {
                        // TODO: Navigate to reading view
                    }
            }
        }
    }

    private var actionButtons: some View {
        HStack {
            Button(action: viewModel.shareDigest) {
                Label("Share", systemImage: "square.and.arrow.up")
            }

            Button(action: viewModel.exportDigest) {
                Label("Export", systemImage: "arrow.down.doc")
            }

            Spacer()

            Button(action: { viewModel.refreshDigest(type: selectedDigestType) }) {
                Label("Refresh", systemImage: "arrow.clockwise")
            }
        }
        .padding(.top)
    }
}

// MARK: - Digest Header
struct DigestHeaderView: View {
    let digest: Digest

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(digest.title)
                .font(.title)
                .bold()

            HStack {
                Label("\(digest.articleCount) articles", systemImage: "doc.text")
                Label("\(digest.estimatedReadTime) min", systemImage: "clock")
                Label(digest.dateRange, systemImage: "calendar")
            }
            .font(.caption)
            .foregroundColor(.secondary)

            if let summary = digest.overallSummary {
                Text(summary)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
        }
    }
}

// MARK: - Article Summary Card
struct ArticleSummaryCard: View {
    let summary: ArticleSummary
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(summary.title)
                        .font(.headline)
                        .lineLimit(2)

                    HStack {
                        if let source = summary.source {
                            Text(source)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Text("• \(summary.readTime) min read")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                Button(action: { withAnimation { isExpanded.toggle() } }) {
                    Image(systemName: "chevron.down")
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                }
                .buttonStyle(.plain)
            }

            Text(summary.summary)
                .font(.body)
                .lineLimit(isExpanded ? nil : 3)
                .animation(.easeInOut, value: isExpanded)

            if isExpanded {
                HStack {
                    ForEach(summary.tags, id: \.self) { tag in
                        Text(tag)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.accentColor.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                .padding(.top, 4)
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }
}

// MARK: - Customization Sheet
struct DigestCustomizationSheet: View {
    @ObservedObject var viewModel: DigestViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("Customize Digest")
                .font(.title2)
                .bold()

            VStack(alignment: .leading, spacing: 12) {
                Text("Include in digest:")
                    .font(.headline)

                Toggle("Unread articles", isOn: $viewModel.preferences.includeUnread)
                Toggle("Recently read", isOn: $viewModel.preferences.includeRecentlyRead)
                Toggle("Favorites", isOn: $viewModel.preferences.includeFavorites)
                Toggle("AI-generated insights", isOn: $viewModel.preferences.includeInsights)
            }

            VStack(alignment: .leading, spacing: 12) {
                Text("Digest frequency:")
                    .font(.headline)

                Picker("Frequency", selection: $viewModel.preferences.frequency) {
                    Text("Daily").tag(DigestFrequency.daily)
                    Text("Weekly").tag(DigestFrequency.weekly)
                    Text("Monthly").tag(DigestFrequency.monthly)
                }
                .pickerStyle(.menu)
            }

            Spacer()

            HStack {
                Button("Cancel") {
                    dismiss()
                }
                .keyboardShortcut(.escape)

                Spacer()

                Button("Save") {
                    viewModel.savePreferences()
                    dismiss()
                }
                .keyboardShortcut(.defaultAction)
            }
        }
        .padding()
        .frame(width: 400, height: 400)
    }
}

// MARK: - Supporting Types
enum DigestType: String, CaseIterable {
    case daily = "Daily"
    case weekly = "Weekly"
    case monthly = "Monthly"
    case custom = "Custom"

    var displayName: String { rawValue }
}

enum DigestFrequency: String {
    case daily, weekly, monthly
}

struct Digest: Identifiable {
    let id = UUID()
    let title: String
    let dateRange: String
    let articleCount: Int
    let estimatedReadTime: Int
    let overallSummary: String?
    let keyInsights: [String]
    let articleSummaries: [ArticleSummary]
    let recommendations: [Article]
}

struct ArticleSummary: Identifiable {
    let id = UUID()
    let articleId: String
    let title: String
    let source: String?
    let summary: String
    let readTime: Int
    let tags: [String]
}

struct DigestPreferences {
    var includeUnread = true
    var includeRecentlyRead = true
    var includeFavorites = true
    var includeInsights = true
    var frequency = DigestFrequency.daily
}

// MARK: - View Model
@MainActor
class DigestViewModel: ObservableObject {
    @Published var currentDigest: Digest?
    @Published var isLoading = false
    @Published var preferences = DigestPreferences()

    func loadDigest(type: DigestType) {
        isLoading = true

        // TODO: Implement actual digest generation using backend service
        Task {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            // Mock digest for now
            currentDigest = Digest(
                title: "\(type.displayName) Digest",
                dateRange: "Jan 6, 2025",
                articleCount: 15,
                estimatedReadTime: 45,
                overallSummary: "Your reading this \(type.displayName.lowercased()) focused on AI development, SwiftUI best practices, and productivity tools.",
                keyInsights: [
                    "AI-powered tools are becoming essential for modern development workflows",
                    "SwiftUI continues to evolve with better performance optimizations",
                    "Privacy-focused apps are gaining more user trust"
                ],
                articleSummaries: [
                    ArticleSummary(
                        articleId: "1",
                        title: "Building Better SwiftUI Apps",
                        source: "Swift Blog",
                        summary: "This article explores advanced SwiftUI techniques for building responsive and performant macOS applications...",
                        readTime: 8,
                        tags: ["SwiftUI", "macOS", "Development"]
                    )
                ],
                recommendations: []
            )

            isLoading = false
        }
    }

    func refreshDigest(type: DigestType) {
        loadDigest(type: type)
    }

    func shareDigest() {
        // TODO: Implement share functionality
    }

    func exportDigest() {
        // TODO: Implement export functionality
    }

    func savePreferences() {
        // TODO: Save preferences to persistent storage
    }
}

#Preview {
    DigestView()
        .frame(width: 800, height: 600)
}
