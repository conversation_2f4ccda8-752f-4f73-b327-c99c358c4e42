import SwiftUI
import GRDB

@main
struct PocketNextApp: App {
    @StateObject private var appState = AppState()
    @StateObject private var databaseManager = DatabaseManager.shared
    @StateObject private var syncService = CloudKitSyncService.shared
    
    @Environment(\.scenePhase) private var scenePhase
    
    init() {
        setupAppearance()
        registerBackgroundTasks()
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
                .environmentObject(databaseManager)
                .environmentObject(syncService)
                .onAppear {
                    setupInitialData()
                }
                .onChange(of: scenePhase) { oldPhase, newPhase in
                    handleScenePhaseChange(from: oldPhase, to: newPhase)
                }
        }
    }
    
    private func setupAppearance() {
        // Configure global appearance
        UINavigationBar.appearance().largeTitleTextAttributes = [
            .font: UIFont.systemFont(ofSize: 34, weight: .bold)
        ]
        
        // Set up tint colors
        UIView.appearance(whenContainedInInstancesOf: [UIAlertController.self]).tintColor = UIColor.systemBlue
        
        // Configure tab bar
        UITabBar.appearance().backgroundColor = UIColor.systemBackground
        UITabBar.appearance().tintColor = UIColor.systemBlue
    }
    
    private func registerBackgroundTasks() {
        BackgroundTaskService.shared.registerTasks()
    }
    
    private func setupInitialData() {
        Task {
            do {
                try await databaseManager.initialize()
                syncService.startSync()
                await appState.loadUserPreferences()
            } catch {
                print("Failed to initialize app: \(error)")
            }
        }
    }
    
    private func handleScenePhaseChange(from oldPhase: ScenePhase, to newPhase: ScenePhase) {
        switch newPhase {
        case .active:
            // App became active
            Task {
                await syncService.resumeSync()
                await NotificationService.shared.updateBadgeCount()
            }
            
        case .inactive:
            // App is inactive
            break
            
        case .background:
            // App moved to background
            Task {
                await syncService.pauseSync()
                await BackgroundTaskService.shared.scheduleAppRefresh()
                await OfflineContentService.shared.pauseDownloads()
            }
            
        @unknown default:
            break
        }
    }
}

// MARK: - App Configuration
extension PocketNextApp {
    static let appGroup = "group.com.pocketnext.ios"
    static let iCloudContainer = "iCloud.com.pocketnext.ios"
    static let urlScheme = "pocketnext"
    
    static var documentsDirectory: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    static var sharedContainerURL: URL? {
        FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroup)
    }
}