import Foundation

/// Shared vector operations for embeddings
public struct VectorOperations {
    
    /// Compress Float32 embeddings to Int8 for CloudKit storage
    /// Reduces size by 4x while maintaining reasonable accuracy
    public static func compressEmbedding(_ embedding: [Float]) -> Data {
        // Find min/max for normalization
        let minVal = embedding.min() ?? -1.0
        let maxVal = embedding.max() ?? 1.0
        let range = maxVal - minVal
        
        // Store normalization parameters (8 bytes) + compressed data
        var data = Data()
        data.append(minVal.data)
        data.append(maxVal.data)
        
        // Quantize to Int8 (-128 to 127)
        let compressed = embedding.map { value -> Int8 in
            let normalized = (value - minVal) / range
            let scaled = normalized * 255.0 - 128.0
            return Int8(max(-128, min(127, Int(scaled.rounded()))))
        }
        
        data.append(contentsOf: compressed)
        return data
    }
    
    /// Decompress Int8 data back to Float32 embeddings
    public static func decompressEmbedding(_ data: Data) -> [Float]? {
        guard data.count > 8 else { return nil }
        
        // Extract normalization parameters
        let minVal = Float(data: data[0..<4]) ?? -1.0
        let maxVal = Float(data: data[4..<8]) ?? 1.0
        let range = maxVal - minVal
        
        // Decompress Int8 values
        let compressedData = data[8...]
        let decompressed = compressedData.map { byte -> Float in
            let scaled = Float(Int8(bitPattern: byte)) + 128.0
            let normalized = scaled / 255.0
            return normalized * range + minVal
        }
        
        return decompressed
    }
    
    /// Calculate cosine similarity between two vectors
    public static func cosineSimilarity(_ a: [Float], _ b: [Float]) -> Float {
        guard a.count == b.count else { return 0 }
        
        var dotProduct: Float = 0
        var magnitudeA: Float = 0
        var magnitudeB: Float = 0
        
        for i in 0..<a.count {
            dotProduct += a[i] * b[i]
            magnitudeA += a[i] * a[i]
            magnitudeB += b[i] * b[i]
        }
        
        let magnitude = sqrt(magnitudeA) * sqrt(magnitudeB)
        return magnitude > 0 ? dotProduct / magnitude : 0
    }
    
    /// Calculate magnitude of a vector (for pre-computing)
    public static func magnitude(_ vector: [Float]) -> Float {
        sqrt(vector.reduce(0) { $0 + $1 * $1 })
    }
    
    /// Normalize a vector to unit length
    public static func normalize(_ vector: [Float]) -> [Float] {
        let mag = magnitude(vector)
        guard mag > 0 else { return vector }
        return vector.map { $0 / mag }
    }
    
    /// Fast cosine similarity using pre-computed magnitudes
    public static func cosineSimilarityFast(_ a: [Float], magnitudeA: Float, _ b: [Float], magnitudeB: Float) -> Float {
        guard a.count == b.count else { return 0 }
        
        let dotProduct = zip(a, b).reduce(Float(0)) { $0 + $1.0 * $1.1 }
        let magnitude = magnitudeA * magnitudeB
        
        return magnitude > 0 ? dotProduct / magnitude : 0
    }
}

// MARK: - Data Extensions
private extension Float {
    var data: Data {
        withUnsafeBytes(of: self) { Data($0) }
    }
    
    init?(data: Data) {
        guard data.count == 4 else { return nil }
        self = data.withUnsafeBytes { $0.load(as: Float.self) }
    }
}