import Foundation
import Combine
import CryptoKit

/// Service responsible for managing offline content
class OfflineContentService: ObservableObject {
    static let shared = OfflineContentService()
    
    @Published var offlineArticles: Set<String> = []
    @Published var downloadProgress: [String: Double] = [:]
    @Published var storageUsed: Int64 = 0
    @Published var storageLimit: Int64 = 1_073_741_824 // 1GB default
    
    private let fileManager = FileManager.default
    private let offlineContentDirectory: URL
    private let metadataDirectory: URL
    private let imageCache: URL
    
    private var downloadTasks: [String: URLSessionDownloadTask] = [:]
    private let downloadQueue = DispatchQueue(label: "com.readlaterai.offline.download", attributes: .concurrent)
    private let session: URLSession
    
    private init() {
        // Setup directories
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        offlineContentDirectory = documentsPath.appendingPathComponent("OfflineContent")
        metadataDirectory = documentsPath.appendingPathComponent("OfflineMetadata")
        imageCache = documentsPath.appendingPathComponent("ImageCache")
        
        // Create directories if needed
        try? fileManager.createDirectory(at: offlineContentDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: metadataDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: imageCache, withIntermediateDirectories: true)
        
        // Setup URLSession
        let configuration = URLSessionConfiguration.background(withIdentifier: "com.readlaterai.offline")
        configuration.allowsCellularAccess = false
        configuration.isDiscretionary = true
        session = URLSession(configuration: configuration, delegate: nil, delegateQueue: nil)
        
        // Load existing offline articles
        loadOfflineArticles()
        calculateStorageUsed()
    }
    
    // MARK: - Public Methods
    
    /// Download an article for offline reading
    func downloadArticle(_ article: Article) async throws {
        guard !offlineArticles.contains(article.id) else {
            print("Article already downloaded: \(article.id)")
            return
        }
        
        // Check storage space
        guard hasEnoughStorage(for: article) else {
            throw OfflineError.insufficientStorage
        }
        
        await MainActor.run {
            downloadProgress[article.id] = 0
        }
        
        do {
            // Download article content
            let content = try await downloadContent(for: article)
            
            // Extract and download images
            let processedContent = try await downloadImages(from: content, articleId: article.id)
            
            // Save article data
            try await saveArticle(article, content: processedContent)
            
            // Update state
            await MainActor.run {
                offlineArticles.insert(article.id)
                downloadProgress.removeValue(forKey: article.id)
            }
            
            // Update storage
            calculateStorageUsed()
            
            print("Article downloaded successfully: \(article.id)")
            
        } catch {
            await MainActor.run {
                downloadProgress.removeValue(forKey: article.id)
            }
            throw error
        }
    }
    
    /// Remove an article from offline storage
    func removeOfflineArticle(_ articleId: String) throws {
        let articlePath = offlineContentDirectory.appendingPathComponent("\(articleId).json")
        let imagePath = imageCache.appendingPathComponent(articleId)
        
        // Remove article file
        if fileManager.fileExists(atPath: articlePath.path) {
            try fileManager.removeItem(at: articlePath)
        }
        
        // Remove associated images
        if fileManager.fileExists(atPath: imagePath.path) {
            try fileManager.removeItem(at: imagePath)
        }
        
        // Update state
        offlineArticles.remove(articleId)
        calculateStorageUsed()
        
        print("Offline article removed: \(articleId)")
    }
    
    /// Get offline content for an article
    func getOfflineContent(for articleId: String) -> OfflineArticle? {
        let articlePath = offlineContentDirectory.appendingPathComponent("\(articleId).json")
        
        guard fileManager.fileExists(atPath: articlePath.path),
              let data = try? Data(contentsOf: articlePath),
              let article = try? JSONDecoder().decode(OfflineArticle.self, from: data) else {
            return nil
        }
        
        return article
    }
    
    /// Check if an article is available offline
    func isAvailableOffline(_ articleId: String) -> Bool {
        return offlineArticles.contains(articleId)
    }
    
    /// Clear all offline content
    func clearAllOfflineContent() throws {
        // Remove all content
        try fileManager.removeItem(at: offlineContentDirectory)
        try fileManager.removeItem(at: imageCache)
        
        // Recreate directories
        try fileManager.createDirectory(at: offlineContentDirectory, withIntermediateDirectories: true)
        try fileManager.createDirectory(at: imageCache, withIntermediateDirectories: true)
        
        // Reset state
        offlineArticles.removeAll()
        downloadProgress.removeAll()
        storageUsed = 0
        
        print("All offline content cleared")
    }
    
    /// Update storage limit
    func updateStorageLimit(_ limit: Int64) {
        storageLimit = limit
        
        // TODO: If current usage exceeds new limit, remove oldest articles
        if storageUsed > storageLimit {
            Task {
                await pruneOldestArticles()
            }
        }
    }
    
    /// Pause all active downloads
    func pauseDownloads() async {
        await withCheckedContinuation { continuation in
            downloadQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // Pause all active download tasks
                for (articleId, task) in self.downloadTasks {
                    task.suspend()
                    print("Paused download for article: \(articleId)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Resume all paused downloads
    func resumeDownloads() async {
        await withCheckedContinuation { continuation in
            downloadQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // Resume all suspended download tasks
                for (articleId, task) in self.downloadTasks {
                    task.resume()
                    print("Resumed download for article: \(articleId)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Cancel all active downloads
    func cancelAllDownloads() async {
        await withCheckedContinuation { continuation in
            downloadQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // Cancel all download tasks
                for (articleId, task) in self.downloadTasks {
                    task.cancel()
                }
                
                self.downloadTasks.removeAll()
                
                Task {
                    await MainActor.run {
                        self.downloadProgress.removeAll()
                    }
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func downloadContent(for article: Article) async throws -> String {
        // TODO: Implement actual content download
        // For now, return the existing content
        await updateProgress(for: article.id, progress: 0.5)
        return article.content ?? ""
    }
    
    private func downloadImages(from content: String, articleId: String) async throws -> String {
        // TODO: Parse content for image URLs and download them
        // Replace remote URLs with local paths
        
        var processedContent = content
        
        // Simple regex to find image URLs (this is a simplified example)
        let imagePattern = #"https?://[^\s<>"{}|\\^\[\]`]+\.(?:jpg|jpeg|png|gif|webp)"#
        
        if let regex = try? NSRegularExpression(pattern: imagePattern, options: .caseInsensitive) {
            let matches = regex.matches(in: content, range: NSRange(content.startIndex..., in: content))
            
            for match in matches.reversed() {
                if let range = Range(match.range, in: content) {
                    let imageURL = String(content[range])
                    
                    // Download and cache image
                    if let localPath = try? await downloadImage(imageURL, articleId: articleId) {
                        processedContent = processedContent.replacingOccurrences(of: imageURL, with: localPath)
                    }
                }
            }
        }
        
        await updateProgress(for: articleId, progress: 0.8)
        
        return processedContent
    }
    
    private func downloadImage(_ urlString: String, articleId: String) async throws -> String {
        guard let url = URL(string: urlString) else {
            throw OfflineError.invalidURL
        }
        
        let (data, _) = try await URLSession.shared.data(from: url)
        
        // Generate unique filename based on URL hash
        let hash = SHA256.hash(data: Data(urlString.utf8))
        let filename = hash.compactMap { String(format: "%02x", $0) }.joined() + ".jpg"
        
        // Create article-specific image directory
        let articleImageDir = imageCache.appendingPathComponent(articleId)
        try? fileManager.createDirectory(at: articleImageDir, withIntermediateDirectories: true)
        
        // Save image
        let imagePath = articleImageDir.appendingPathComponent(filename)
        try data.write(to: imagePath)
        
        return imagePath.path
    }
    
    private func saveArticle(_ article: Article, content: String) async throws {
        let offlineArticle = OfflineArticle(
            id: article.id,
            url: article.url,
            title: article.title,
            content: content,
            author: article.author,
            source: article.domain,
            publishedDate: article.publishDate,
            downloadedDate: Date(),
            originalContent: article.content ?? ""
        )
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try encoder.encode(offlineArticle)
        
        let articlePath = offlineContentDirectory.appendingPathComponent("\(article.id).json")
        try data.write(to: articlePath)
        
        await updateProgress(for: article.id, progress: 1.0)
    }
    
    private func loadOfflineArticles() {
        guard let files = try? fileManager.contentsOfDirectory(at: offlineContentDirectory, includingPropertiesForKeys: nil) else {
            return
        }
        
        let articleIds = files
            .filter { $0.pathExtension == "json" }
            .compactMap { $0.deletingPathExtension().lastPathComponent }
        
        offlineArticles = Set(articleIds)
        print("Loaded \(offlineArticles.count) offline articles")
    }
    
    private func calculateStorageUsed() {
        downloadQueue.async {
            var totalSize: Int64 = 0
            
            // Calculate offline content size
            if let enumerator = self.fileManager.enumerator(at: self.offlineContentDirectory, includingPropertiesForKeys: [.fileSizeKey]) {
                for case let fileURL as URL in enumerator {
                    if let fileSize = try? fileURL.resourceValues(forKeys: [.fileSizeKey]).fileSize {
                        totalSize += Int64(fileSize)
                    }
                }
            }
            
            // Calculate image cache size
            if let enumerator = self.fileManager.enumerator(at: self.imageCache, includingPropertiesForKeys: [.fileSizeKey]) {
                for case let fileURL as URL in enumerator {
                    if let fileSize = try? fileURL.resourceValues(forKeys: [.fileSizeKey]).fileSize {
                        totalSize += Int64(fileSize)
                    }
                }
            }
            
            Task { @MainActor in
                self.storageUsed = totalSize
            }
        }
    }
    
    private func hasEnoughStorage(for article: Article) -> Bool {
        // Estimate article size (rough calculation)
        let estimatedSize = Int64((article.content?.count ?? 0) + 1_000_000) // Content + estimated images
        return (storageUsed + estimatedSize) <= storageLimit
    }
    
    private func updateProgress(for articleId: String, progress: Double) async {
        await MainActor.run {
            downloadProgress[articleId] = progress
        }
    }
    
    private func pruneOldestArticles() async {
        // TODO: Remove oldest articles until under storage limit
        print("Pruning oldest articles to meet storage limit")
    }
}

// MARK: - Supporting Types

struct OfflineArticle: Codable {
    let id: String
    let url: String
    let title: String
    let content: String // Processed content with local image paths
    let author: String?
    let source: String?
    let publishedDate: Date?
    let downloadedDate: Date
    let originalContent: String // Original content for reference
}

enum OfflineError: LocalizedError {
    case insufficientStorage
    case downloadFailed
    case invalidURL
    case contentProcessingFailed
    
    var errorDescription: String? {
        switch self {
        case .insufficientStorage:
            return "Not enough storage space for offline content"
        case .downloadFailed:
            return "Failed to download content"
        case .invalidURL:
            return "Invalid URL"
        case .contentProcessingFailed:
            return "Failed to process content for offline viewing"
        }
    }
}