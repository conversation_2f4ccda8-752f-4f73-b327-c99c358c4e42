import Foundation
import GRDB

actor DatabaseManager {
    static let shared = DatabaseManager()
    
    private var dbQueue: DatabaseQueue?
    var isInitialized = false
    var isInitializing = false
    
    enum DatabaseError: Error {
        case notInitialized
        case initializationFailed(String)
        case alreadyInitializing
    }
    
    private var databaseURL: URL {
        let appSupport = FileManager.default.urls(for: .applicationSupportDirectory,
                                                  in: .userDomainMask).first!
        let appFolder = appSupport.appendingPathComponent("PocketNext", isDirectory: true)
        
        // Create directory if needed
        try? FileManager.default.createDirectory(at: appFolder,
                                               withIntermediateDirectories: true)
        
        return appFolder.appendingPathComponent("pocketnext.db")
    }
    
    init() {
        // Default initializer
    }
    
    init(inMemory: Bool = false) async throws {
        if inMemory {
            self.dbQueue = try DatabaseQueue()
        }
    }
    
    func initialize() async throws {
        // Return early if already initialized
        guard !isInitialized else { return }
        
        // Prevent concurrent initialization
        guard !isInitializing else {
            // Wait for the current initialization to complete
            while isInitializing {
                try await Task.sleep(nanoseconds: 10_000_000) // 10ms
            }
            if isInitialized {
                return
            } else {
                throw DatabaseError.alreadyInitializing
            }
        }
        
        isInitializing = true
        
        do {
            // Open database with proper configuration to handle locks
            var config = Configuration()
            config.defaultTransactionKind = .immediate
            config.busyMode = .timeout(5.0)
            
            dbQueue = try DatabaseQueue(path: databaseURL.path, configuration: config)
            
            // Run migrations
            guard let queue = dbQueue else {
                throw DatabaseError.initializationFailed("Failed to create database queue")
            }
            
            try await queue.write { [self] db in
                try self.createTables(db)
                try self.createIndexes(db)
            }
            
            isInitialized = true
            isInitializing = false
        } catch {
            dbQueue = nil
            isInitialized = false
            isInitializing = false
            throw DatabaseError.initializationFailed(error.localizedDescription)
        }
    }
    
    private func getQueue() throws -> DatabaseQueue {
        guard let queue = dbQueue, isInitialized else {
            throw DatabaseError.notInitialized
        }
        return queue
    }
    
    // Public methods for direct database access
    func read<T>(_ block: @escaping (Database) throws -> T) async throws -> T {
        let queue = try getQueue()
        return try await queue.read(block)
    }
    
    func write<T>(_ block: @escaping (Database) throws -> T) async throws -> T {
        let queue = try getQueue()
        return try await queue.write(block)
    }
    
    nonisolated private func createTables(_ db: Database) throws {
        // Articles table
        try db.create(table: "articles", ifNotExists: true) { t in
            t.column("id", .text).primaryKey()
            t.column("url", .text).notNull().unique()
            t.column("title", .text).notNull()
            t.column("content", .text).notNull()
            t.column("summary", .text)
            t.column("keywords", .blob)  // JSON array
            t.column("author", .text)
            t.column("publishDate", .datetime)
            t.column("readingTime", .integer).notNull()
            t.column("contentType", .text).notNull()
            t.column("capturedAt", .datetime).notNull()
            t.column("lastAccessedAt", .datetime)
            t.column("isArchived", .boolean).notNull().defaults(to: false)
            t.column("syncStatus", .integer).notNull().defaults(to: 0)
            t.column("embeddingData", .blob)  // Compressed embedding for CloudKit
            t.column("embeddingModelVersion", .text)
            t.column("hasLocalEmbedding", .boolean).notNull().defaults(to: false)
        }
        
        // Add columns if they don't exist (for migration)
        let tableInfo = try db.columns(in: "articles")
        let columnNames = Set(tableInfo.map { $0.name })
        
        if !columnNames.contains("embeddingData") {
            try db.alter(table: "articles") { t in
                t.add(column: "embeddingData", .blob)
                t.add(column: "embeddingModelVersion", .text)
                t.add(column: "hasLocalEmbedding", .boolean).notNull().defaults(to: false)
            }
        }
        
        // Digests table
        try db.create(table: "digests", ifNotExists: true) { t in
            t.column("id", .text).primaryKey()
            t.column("type", .text).notNull()
            t.column("generatedAt", .datetime).notNull()
            t.column("data", .blob).notNull()
        }
        
        // Local vector index table (replaces old embeddings table)
        try db.create(table: "local_vector_index", ifNotExists: true) { t in
            t.column("articleId", .text).primaryKey()
                .references("articles", onDelete: .cascade)
            t.column("embedding", .blob).notNull()  // Normalized Float32 array as JSON
            t.column("magnitude", .double).notNull()  // Pre-computed for fast similarity
            t.column("modelVersion", .text).notNull()
            t.column("createdAt", .datetime).notNull()
        }
        
        // Drop old embeddings table if it exists
        if try db.tableExists("embeddings") {
            try db.drop(table: "embeddings")
        }
        
        // Full-text search virtual table
        try db.create(virtualTable: "articles_fts", ifNotExists: true, using: FTS5()) { t in
            t.content = nil  // External content
            t.column("title")
            t.column("content")
            t.column("summary")
            t.column("keywords")
        }
        
        // Triggers to keep FTS in sync
        try db.execute(sql: """
            CREATE TRIGGER IF NOT EXISTS articles_ai
            AFTER INSERT ON articles
            BEGIN
                INSERT INTO articles_fts(rowid, title, content, summary, keywords)
                VALUES (new.rowid, new.title, new.content, new.summary, new.keywords);
            END
        """)
        
        try db.execute(sql: """
            CREATE TRIGGER IF NOT EXISTS articles_ad
            AFTER DELETE ON articles
            BEGIN
                DELETE FROM articles_fts WHERE rowid = old.rowid;
            END
        """)
        
        try db.execute(sql: """
            CREATE TRIGGER IF NOT EXISTS articles_au
            AFTER UPDATE ON articles
            BEGIN
                UPDATE articles_fts
                SET title = new.title,
                    content = new.content,
                    summary = new.summary,
                    keywords = new.keywords
                WHERE rowid = new.rowid;
            END
        """)
    }
    
    nonisolated private func createIndexes(_ db: Database) throws {
        try db.create(index: "idx_articles_captured",
                     on: "articles",
                     columns: ["capturedAt"],
                     ifNotExists: true)
        
        try db.create(index: "idx_articles_archived",
                     on: "articles",
                     columns: ["isArchived", "capturedAt"],
                     ifNotExists: true)
        
        try db.create(index: "idx_articles_url",
                     on: "articles",
                     columns: ["url"],
                     ifNotExists: true)
        
        try db.create(index: "idx_articles_embedding",
                     on: "articles",
                     columns: ["hasLocalEmbedding"],
                     ifNotExists: true)
        
        try db.create(index: "idx_vector_index_created",
                     on: "local_vector_index",
                     columns: ["createdAt"],
                     ifNotExists: true)
    }
    
    // MARK: - Article Operations
    
    func save(_ article: Article) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try article.save(db)
        }
    }
    
    func fetchRecentArticles(limit: Int = 50) async throws -> [Article] {
        let queue = try getQueue()
        return try await queue.read { db in
            try Article
                .filter(Article.Columns.isArchived == false)
                .order(Article.Columns.capturedAt.desc)
                .limit(limit)
                .fetchAll(db)
        }
    }
    
    func fetchArticle(id: UUID) async throws -> Article? {
        let queue = try getQueue()
        return try await queue.read { db in
            try Article.fetchOne(db, key: id)
        }
    }
    
    func fetchArticle(url: String) async throws -> Article? {
        let queue = try getQueue()
        return try await queue.read { db in
            try Article
                .filter(Article.Columns.url == url)
                .fetchOne(db)
        }
    }
    
    func markAsRead(_ articleId: UUID) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try db.execute(
                sql: "UPDATE articles SET lastAccessedAt = ? WHERE id = ?",
                arguments: [Date(), articleId]
            )
        }
    }
    
    func archive(_ articleId: UUID) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try db.execute(
                sql: "UPDATE articles SET isArchived = ? WHERE id = ?",
                arguments: [true, articleId]
            )
        }
    }
    
    func unarchive(_ articleId: UUID) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try db.execute(
                sql: "UPDATE articles SET isArchived = ? WHERE id = ?",
                arguments: [false, articleId]
            )
        }
    }
    
    func delete(_ articleId: UUID) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try Article.deleteOne(db, key: articleId)
        }
    }
    
    // MARK: - Embedding Operations
    
    func saveEmbedding(_ embedding: ArticleEmbedding) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try embedding.save(db)
        }
    }
    
    func fetchEmbedding(for articleId: UUID) async throws -> ArticleEmbedding? {
        let queue = try getQueue()
        return try await queue.read { db in
            try ArticleEmbedding.fetchOne(db, key: articleId)
        }
    }
    
    func fetchAllEmbeddings() async throws -> [ArticleEmbedding] {
        let queue = try getQueue()
        return try await queue.read { db in
            try ArticleEmbedding.fetchAll(db)
        }
    }
    
    // MARK: - Search Operations
    
    func searchArticles(query: String, limit: Int = 20) async throws -> [Article] {
        let queue = try getQueue()
        return try await queue.read { db in
            let pattern = FTS5Pattern(matchingAllTokensIn: query)
            
            let articles = try Article
                .matching(pattern)
                .order(Article.Columns.capturedAt.desc)
                .limit(limit)
                .fetchAll(db)
            
            return articles
        }
    }
    
    // MARK: - Sync Operations
    
    func fetchUnsyncedArticles() async throws -> [Article] {
        let queue = try getQueue()
        return try await queue.read { db in
            try Article
                .filter(Article.Columns.syncStatus != Article.SyncStatus.synced.rawValue)
                .fetchAll(db)
        }
    }
    
    func markAsSynced(_ articleId: UUID) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try db.execute(
                sql: "UPDATE articles SET syncStatus = ? WHERE id = ?",
                arguments: [Article.SyncStatus.synced.rawValue, articleId]
            )
        }
    }
    
    // MARK: - Statistics
    
    func fetchStatistics() async throws -> Statistics {
        let queue = try getQueue()
        return try await queue.read { db in
            let totalCount = try Article.fetchCount(db)
            let unreadCount = try Article
                .filter(Article.Columns.lastAccessedAt == nil)
                .fetchCount(db)
            let archivedCount = try Article
                .filter(Article.Columns.isArchived == true)
                .fetchCount(db)
            
            return Statistics(
                totalArticles: totalCount,
                unreadArticles: unreadCount,
                archivedArticles: archivedCount
            )
        }
    }
    
    struct Statistics {
        let totalArticles: Int
        let unreadArticles: Int
        let archivedArticles: Int
    }
    
    // MARK: - Chat Operations (Extensions)
    
    func fetchAllConversations() async throws -> [ChatConversation] {
        let queue = try getQueue()
        return try await queue.read { db in
            try ChatConversation.fetchAll(db)
        }
    }
    
    func save(_ conversation: ChatConversation) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try conversation.save(db)
        }
    }
    
    func save(_ message: DBChatMessage) async throws {
        let queue = try getQueue()
        try await queue.write { db in
            try message.save(db)
        }
    }
    
    func fetchMessages(for conversationId: UUID) async throws -> [DBChatMessage] {
        let queue = try getQueue()
        return try await queue.read { db in
            try DBChatMessage
                .filter(sql: "conversationId = ?", arguments: [conversationId])
                .order(sql: "timestamp ASC")
                .fetchAll(db)
        }
    }
    
    // MARK: - Digest Operations
    
    func saveDigest(_ digest: ArticleDigest) async throws {
        let queue = try getQueue()
        let data = try JSONEncoder().encode(digest)
        
        try await queue.write { db in
            try db.execute(
                sql: """
                INSERT OR REPLACE INTO digests (id, type, generatedAt, data)
                VALUES (?, ?, ?, ?)
                """,
                arguments: [digest.id, digest.type.rawValue, digest.generatedAt, data]
            )
        }
    }
    
    func fetchDigests(limit: Int) async throws -> [ArticleDigest] {
        let queue = try getQueue()
        return try await queue.read { db in
            let rows = try Row.fetchAll(db, sql: """
                SELECT data FROM digests
                ORDER BY generatedAt DESC
                LIMIT ?
                """, arguments: [limit])
            
            return rows.compactMap { row in
                guard let data = row["data"] as? Data else { return nil }
                return try? JSONDecoder().decode(ArticleDigest.self, from: data)
            }
        }
    }
    
    func fetchLatestDigest() async throws -> ArticleDigest? {
        let queue = try getQueue()
        return try await queue.read { db in
            let row = try Row.fetchOne(db, sql: """
                SELECT data FROM digests
                ORDER BY generatedAt DESC
                LIMIT 1
                """)
            
            guard let data = row?["data"] as? Data else { return nil }
            return try JSONDecoder().decode(ArticleDigest.self, from: data)
        }
    }
    
    func fetchArticles(from startDate: Date, to endDate: Date, limit: Int) async throws -> [Article] {
        let queue = try getQueue()
        return try await queue.read { db in
            try Article
                .filter(sql: "capturedAt >= ? AND capturedAt <= ?", arguments: [startDate, endDate])
                .order(Article.Columns.capturedAt.desc)
                .limit(limit)
                .fetchAll(db)
        }
    }
}