import Foundation
import UserNotifications
import Combine
#if canImport(UIKit)
import UIKit
#endif

/// Service responsible for managing notifications
class NotificationService: ObservableObject {
    static let shared = NotificationService()

    @Published var notificationSettings: NotificationSettings = NotificationSettings()
    @Published var isAuthorized = false
    @Published var pendingNotifications: [PendingNotification] = []

    private let notificationCenter = UNUserNotificationCenter.current()
    private var cancellables = Set<AnyCancellable>()

    private init() {
        setupNotificationCategories()
        checkAuthorizationStatus()
    }

    // MARK: - Authorization

    /// Request notification permissions
    func requestAuthorization() async throws {
        do {
            let granted = try await notificationCenter.requestAuthorization(
                options: [.alert, .badge, .sound]
            )

            await MainActor.run {
                self.isAuthorized = granted
            }

            if granted {
                print("Notification authorization granted")
                await registerForRemoteNotifications()
            } else {
                print("Notification authorization denied")
            }
        } catch {
            print("Failed to request notification authorization: \(error)")
            throw error
        }
    }

    /// Check current authorization status
    func checkAuthorizationStatus() {
        Task {
            let settings = await notificationCenter.notificationSettings()

            await MainActor.run {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }

    // MARK: - Notification Scheduling

    /// Send a digest notification
    func sendDigestNotification() async {
        guard isAuthorized else { return }

        let content = UNMutableNotificationContent()
        content.title = "Your Daily Digest is Ready"
        content.body = "Check out today's reading highlights and AI-generated insights"
        content.sound = .default
        content.categoryIdentifier = "DIGEST"
        content.userInfo = ["type": "digest"]

        // Category identifier already set above

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "digest-\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )

        do {
            try await notificationCenter.add(request)
            print("Digest notification scheduled")
        } catch {
            print("Failed to schedule digest notification: \(error)")
        }
    }

    /// Send a reading reminder
    func scheduleReadingReminder(at date: Date) async {
        guard isAuthorized else { return }
        guard notificationSettings.readingReminders else { return }

        let content = UNMutableNotificationContent()
        content.title = "Time to Read"
        content.body = "You have \(getUnreadCount()) unread articles waiting for you"
        content.sound = .default
        content.categoryIdentifier = "REMINDER"
        content.userInfo = ["type": "reminder"]

        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)

        let request = UNNotificationRequest(
            identifier: "reading-reminder",
            content: content,
            trigger: trigger
        )

        do {
            try await notificationCenter.add(request)
            print("Reading reminder scheduled")
        } catch {
            print("Failed to schedule reading reminder: \(error)")
        }
    }

    /// Send article saved confirmation
    func sendArticleSavedNotification(title: String) async {
        guard isAuthorized else { return }
        guard notificationSettings.saveConfirmations else { return }

        let content = UNMutableNotificationContent()
        content.title = "Article Saved"
        content.body = title
        content.sound = .default

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "saved-\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )

        do {
            try await notificationCenter.add(request)
        } catch {
            print("Failed to send saved notification: \(error)")
        }
    }

    /// Send sync completed notification
    func sendSyncCompletedNotification(itemCount: Int) async {
        guard isAuthorized else { return }
        guard notificationSettings.syncNotifications else { return }

        let content = UNMutableNotificationContent()
        content.title = "Sync Completed"
        content.body = "\(itemCount) items synced across your devices"
        content.sound = .default

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "sync-\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )

        do {
            try await notificationCenter.add(request)
        } catch {
            print("Failed to send sync notification: \(error)")
        }
    }

    /// Update app badge count
    func updateBadgeCount() async {
        let unreadCount = getUnreadCount()

        await MainActor.run {
            #if canImport(UIKit)
            UIApplication.shared.applicationIconBadgeNumber = unreadCount
            #endif
        }
    }


    // MARK: - Notification Management

    /// Get all pending notifications
    func getPendingNotifications() async {
        let requests = await notificationCenter.pendingNotificationRequests()

        let pending = requests.map { request in
            PendingNotification(
                identifier: request.identifier,
                title: request.content.title,
                body: request.content.body,
                trigger: request.trigger
            )
        }

        await MainActor.run {
            self.pendingNotifications = pending
        }
    }

    /// Cancel a specific notification
    func cancelNotification(identifier: String) {
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [identifier])
        print("Cancelled notification: \(identifier)")
    }

    /// Cancel all notifications
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
        pendingNotifications.removeAll()
        print("All notifications cancelled")
    }

    /// Update notification settings
    func updateSettings(_ settings: NotificationSettings) {
        notificationSettings = settings
        saveSettings()

        // Reschedule notifications based on new settings
        Task {
            await rescheduleNotifications()
        }
    }

    // MARK: - Private Methods

    private func setupNotificationCategories() {
        // Digest category with actions
        let viewAction = UNNotificationAction(
            identifier: "VIEW_DIGEST",
            title: "View Digest",
            options: .foreground
        )

        let dismissAction = UNNotificationAction(
            identifier: "DISMISS",
            title: "Dismiss",
            options: .destructive
        )

        let digestCategory = UNNotificationCategory(
            identifier: "DIGEST",
            actions: [viewAction, dismissAction],
            intentIdentifiers: []
        )

        // Reminder category
        let readNowAction = UNNotificationAction(
            identifier: "READ_NOW",
            title: "Read Now",
            options: .foreground
        )

        let snoozeAction = UNNotificationAction(
            identifier: "SNOOZE",
            title: "Snooze",
            options: []
        )

        let reminderCategory = UNNotificationCategory(
            identifier: "REMINDER",
            actions: [readNowAction, snoozeAction],
            intentIdentifiers: []
        )

        notificationCenter.setNotificationCategories([digestCategory, reminderCategory])
    }

    private func registerForRemoteNotifications() async {
        await MainActor.run {
            #if canImport(UIKit)
            UIApplication.shared.registerForRemoteNotifications()
            #endif
        }
    }

    private func rescheduleNotifications() async {
        // Cancel existing scheduled notifications
        cancelAllNotifications()

        // Reschedule based on settings
        if notificationSettings.readingReminders {
            await scheduleReadingReminder(at: notificationSettings.reminderTime)
        }

        // TODO: Reschedule other notification types
    }

    private func getUnreadCount() -> Int {
        // TODO: Get actual unread count from database
        return 5
    }

    private func saveSettings() {
        // TODO: Save notification settings to persistent storage
    }

    private func loadSettings() {
        // TODO: Load notification settings from persistent storage
    }
}

// MARK: - Supporting Types

struct NotificationSettings: Codable {
    var digestNotifications = true
    var readingReminders = true
    var saveConfirmations = false
    var syncNotifications = true
    var reminderTime = Date()
    var digestFrequency: DigestFrequency = .daily

    enum DigestFrequency: String, Codable, CaseIterable {
        case daily = "Daily"
        case weekly = "Weekly"
        case never = "Never"
    }
}

struct PendingNotification: Identifiable {
    let id = UUID()
    let identifier: String
    let title: String
    let body: String
    let trigger: UNNotificationTrigger?
}

// MARK: - Notification Delegate

class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {

    // Handle notification when app is in foreground
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }

    // Handle notification actions
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let actionIdentifier = response.actionIdentifier
        let notification = response.notification

        switch actionIdentifier {
        case "VIEW_DIGEST":
            // TODO: Navigate to digest view
            print("View digest action")

        case "READ_NOW":
            // TODO: Navigate to reading view
            print("Read now action")

        case "SNOOZE":
            // TODO: Reschedule reminder
            print("Snooze action")

        default:
            break
        }

        completionHandler()
    }
}
