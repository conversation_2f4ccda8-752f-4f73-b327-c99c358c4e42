import Foundation
import Accelerate

actor SearchEngine {
    private let database: DatabaseManager
    private let hybridStorage: HybridVectorStorage
    
    init(database: DatabaseManager, hybridStorage: HybridVectorStorage = .shared) {
        self.database = database
        self.hybridStorage = hybridStorage
    }
    
    func search(_ query: String) async throws -> [SearchResult] {
        // Parallel keyword and semantic search
        async let keywordResults = performKeywordSearch(query)
        async let semanticResults = performSemanticSearch(query)
        
        // Combine and rank
        let keyword = try await keywordResults
        let semantic = try await semanticResults
        
        return rankResults(keyword: keyword, semantic: semantic)
    }
    
    private func performKeywordSearch(_ query: String) async throws -> [(Article, Double)] {
        let articles = try await database.searchArticles(query: query, limit: 50)
        
        // Calculate BM25-like scores
        return articles.map { article in
            let score = calculateKeywordScore(query: query, article: article)
            return (article, score)
        }
    }
    
    private func performSemanticSearch(_ query: String) async throws -> [(Article, Double)] {
        // Use HybridVectorStorage for optimized semantic search with scores
        let results = try await hybridStorage.searchSimilarWithScores(to: query, limit: 50)
        
        // Convert Float scores to Double for consistency
        return results.map { result in
            (result.article, Double(result.score))
        }
    }
    
    private func rankResults(keyword: [(Article, Double)], semantic: [(Article, Double)]) -> [SearchResult] {
        var scoreMap: [UUID: (article: Article, keywordScore: Double, semanticScore: Double)] = [:]
        
        // Add keyword results
        for (article, score) in keyword {
            scoreMap[article.id] = (article, score, 0)
        }
        
        // Add/merge semantic results
        for (article, score) in semantic {
            if let existing = scoreMap[article.id] {
                scoreMap[article.id] = (existing.article, existing.keywordScore, score)
            } else {
                scoreMap[article.id] = (article, 0, score)
            }
        }
        
        // Calculate combined scores
        let results = scoreMap.values.map { item in
            // Weighted combination: 60% keyword, 40% semantic
            let combinedScore = (item.keywordScore * 0.6) + (item.semanticScore * 0.4)
            
            // Boost if both match types present
            let boost = (item.keywordScore > 0 && item.semanticScore > 0) ? 1.2 : 1.0
            
            return SearchResult(
                article: item.article,
                score: combinedScore * boost,
                keywordScore: item.keywordScore,
                semanticScore: item.semanticScore,
                matchTypes: determineMatchTypes(keyword: item.keywordScore, semantic: item.semanticScore)
            )
        }
        
        // Sort by combined score
        return results.sorted { $0.score > $1.score }.prefix(20).map { $0 }
    }
    
    private func calculateKeywordScore(query: String, article: Article) -> Double {
        let queryTerms = query.lowercased().split(separator: " ").map(String.init)
        var score = 0.0
        
        // Title matches (higher weight)
        let titleLower = article.title.lowercased()
        for term in queryTerms {
            if titleLower.contains(term) {
                score += 2.0
            }
        }
        
        // Content matches
        let contentLower = article.content.lowercased()
        for term in queryTerms {
            let count = contentLower.components(separatedBy: term).count - 1
            score += Double(count) * 0.5
        }
        
        // Keyword matches
        for keyword in article.keywords {
            if queryTerms.contains(keyword.lowercased()) {
                score += 1.5
            }
        }
        
        // Summary matches
        let summaryLower = article.summary.lowercased()
        for term in queryTerms {
            if summaryLower.contains(term) {
                score += 1.0
            }
        }
        
        return score
    }
    
    private func determineMatchTypes(keyword: Double, semantic: Double) -> Set<SearchResult.MatchType> {
        var types: Set<SearchResult.MatchType> = []
        if keyword > 0 { types.insert(.keyword) }
        if semantic > 0 { types.insert(.semantic) }
        return types
    }
}

// MARK: - Search Result

struct SearchResult: Identifiable {
    let id = UUID()
    let article: Article
    let score: Double
    let keywordScore: Double
    let semanticScore: Double
    let matchTypes: Set<MatchType>
    
    enum MatchType: String {
        case keyword
        case semantic
    }
    
    var relevanceIndicator: String {
        switch score {
        case 0.8...:
            return "★★★"
        case 0.5..<0.8:
            return "★★"
        case 0..<0.5:
            return "★"
        default:
            return ""
        }
    }
}

// MARK: - Search Suggestions

extension SearchEngine {
    func getSuggestions(for prefix: String) async throws -> [String] {
        guard !prefix.isEmpty else { return [] }
        
        // Get all unique keywords from recent articles
        let articles = try await database.fetchRecentArticles(limit: 100)
        
        var allKeywords = Set<String>()
        for article in articles {
            allKeywords.formUnion(article.keywords)
        }
        
        // Filter by prefix
        let suggestions = allKeywords
            .filter { $0.lowercased().hasPrefix(prefix.lowercased()) }
            .sorted()
            .prefix(10)
        
        return Array(suggestions)
    }
    
    func getPopularKeywords() async throws -> [(keyword: String, count: Int)] {
        let articles = try await database.fetchRecentArticles(limit: 200)
        
        var keywordCounts: [String: Int] = [:]
        for article in articles {
            for keyword in article.keywords {
                keywordCounts[keyword, default: 0] += 1
            }
        }
        
        return keywordCounts
            .sorted { $0.value > $1.value }
            .prefix(20)
            .map { ($0.key, $0.value) }
    }
}