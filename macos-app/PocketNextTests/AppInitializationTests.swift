import XCTest
@testable import PocketNext
import CloudKit

class AppInitializationTests: XCTestCase {
    
    func testDatabaseInitializationRaceCondition() async throws {
        // This test reproduces the "database is locked" error
        let dbManager = DatabaseManager.shared
        
        // Simulate concurrent initialization attempts
        try await withThrowingTaskGroup(of: Void.self) { group in
            for _ in 0..<5 {
                group.addTask {
                    try await dbManager.initialize()
                }
            }
            
            // At least one should succeed, others might fail
            var successCount = 0
            var errorCount = 0
            
            for try await _ in group {
                successCount += 1
            }
        }
    }
    
    func testCloudKitContainerInitialization() async throws {
        // This test reproduces the CloudKit containerIdentifier nil crash
        // In test environment, CKContainer.default() may fail
        
        do {
            let syncEngine = await SyncEngine()
            await syncEngine.setup()
            
            // Check if container was initialized
            let mirror = Mirror(reflecting: syncEngine)
            let container = mirror.children.first { $0.label == "container" }?.value as? CKContainer
            XCTAssertNotNil(container, "Container should be initialized")
        } catch {
            // In test environment, this is expected to fail
            XCTAssertTrue(error.localizedDescription.contains("containerIdentifier") || 
                         error.localizedDescription.contains("CloudKit"))
        }
    }
    
    func testCloudKitWithMissingEntitlements() async throws {
        // Test CloudKit initialization when entitlements are missing
        let service = await CloudKitChatSyncService()
        
        // The service initializes in its init(), so we just need to wait
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
        
        // Check sync status
        switch await service.syncStatus {
        case .error(let error):
            // Expected in test environment
            XCTAssertNotNil(error)
        case .idle, .syncing, .success:
            // If it works, that's fine too (might have proper entitlements in some environments)
            XCTAssertTrue(true)
        }
    }
    
    func testAppLaunchSequence() async throws {
        // Test the full app launch sequence
        // 1. Database should initialize before CloudKit
        // 2. CloudKit should handle missing entitlements gracefully
        
        var databaseInitialized = false
        var cloudKitInitialized = false
        var cloudKitError: Error?
        
        // Initialize database first
        do {
            try await DatabaseManager.shared.initialize()
            databaseInitialized = true
        } catch {
            XCTFail("Database initialization should not fail: \(error)")
        }
        
        // Then initialize CloudKit
        do {
            let syncEngine = await SyncEngine()
            await syncEngine.setup()
            cloudKitInitialized = true
        } catch {
            cloudKitError = error
            // This is expected in test environment
        }
        
        XCTAssertTrue(databaseInitialized, "Database should initialize successfully")
        // CloudKit might fail in tests, but should not crash
    }
}