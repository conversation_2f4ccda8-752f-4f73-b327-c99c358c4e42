import XCTest
@testable import PocketNext

/// Simple test to verify basic functionality works
@MainActor
final class BasicFunctionalityTest: XCTestCase {
    
    func testDatabaseInitializationAndBasicOperations() async throws {
        // Test 1: Database initialization
        let database = DatabaseManager.shared
        try await database.initialize()
        
        XCTAssertTrue(database.isInitialized, "Database should be initialized")
        
        // Test 2: Save an article
        let testArticle = Article(
            id: UUID(),
            url: "https://example.com/test",
            title: "Test Article",
            content: "This is test content for the article",
            summary: "Test summary",
            keywords: ["test", "example"],
            author: "Test Author",
            publishDate: Date(),
            readingTime: 5,
            contentType: .article,
            capturedAt: Date()
        )
        
        try await database.save(testArticle)
        
        // Test 3: Retrieve the article
        let retrieved = try await database.fetchArticle(id: testArticle.id)
        XCTAssertNotNil(retrieved, "Should retrieve saved article")
        XCTAssertEqual(retrieved?.title, "Test Article")
        XCTAssertEqual(retrieved?.url, testArticle.url)
        
        // Test 4: Search functionality
        let searchResults = try await database.searchArticles(query: "test", limit: 10)
        XCTAssertFalse(searchResults.isEmpty, "Search should find the test article")
        XCTAssertTrue(searchResults.contains { $0.id == testArticle.id })
        
        // Test 5: Update article state
        try await database.markAsRead(testArticle.id)
        let readArticle = try await database.fetchArticle(id: testArticle.id)
        XCTAssertNotNil(readArticle?.lastAccessedAt, "Article should be marked as read")
        
        // Test 6: Archive/Unarchive
        try await database.archive(testArticle.id)
        var archivedArticle = try await database.fetchArticle(id: testArticle.id)
        XCTAssertTrue(archivedArticle?.isArchived ?? false, "Article should be archived")
        
        try await database.unarchive(testArticle.id)
        archivedArticle = try await database.fetchArticle(id: testArticle.id)
        XCTAssertFalse(archivedArticle?.isArchived ?? true, "Article should be unarchived")
        
        // Test 7: Delete
        try await database.delete(testArticle.id)
        let deleted = try await database.fetchArticle(id: testArticle.id)
        XCTAssertNil(deleted, "Article should be deleted")
        
        print("✅ All basic database operations work correctly!")
    }
    
    func testAppStateInitialization() async throws {
        // Test AppState initialization
        let appState = AppState()
        
        // Wait a moment for initialization
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        XCTAssertNotNil(appState.database, "AppState should have database")
        XCTAssertTrue(appState.database.isInitialized, "AppState's database should be initialized")
        
        // Test article refresh
        await appState.refreshArticles()
        
        // Should not crash and articles should be loaded (even if empty)
        XCTAssertNotNil(appState.articles)
        
        print("✅ AppState initialization works correctly!")
    }
    
    func testLLMServiceInitialization() async throws {
        // Test LLM service initialization
        let llmService = LLMService()
        
        // Wait for model to load
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        XCTAssertTrue(llmService.isModelLoaded || llmService.loadingError != nil, 
                     "LLM service should either load a model or report an error")
        
        let modelInfo = llmService.currentModelInfo()
        print("LLM Mode: \(modelInfo.mode), Model: \(modelInfo.modelName), Loaded: \(modelInfo.isLoaded)")
        
        // If API key is available, test response generation
        if ProcessInfo.processInfo.environment["OPENAI_API_KEY"] != nil {
            do {
                let response = try await llmService.generateResponse(
                    prompt: "What is Swift?",
                    context: "Swift is a programming language.",
                    conversationHistory: []
                )
                
                XCTAssertFalse(response.text.isEmpty, "Should generate a response")
                print("✅ LLM API response generation works!")
            } catch {
                print("⚠️ LLM API test failed: \(error)")
            }
        } else {
            print("ℹ️ OPENAI_API_KEY not set, skipping API test")
        }
    }
    
    func testDigestService() async throws {
        let database = DatabaseManager.shared
        try await database.initialize()
        
        // Add some test articles
        for i in 1...5 {
            let article = Article(
                id: UUID(),
                url: "https://example.com/article-\(i)",
                title: "Article \(i)",
                content: "Content for article \(i)",
                summary: "Summary \(i)",
                keywords: ["test", "article\(i)"],
                author: "Author \(i)",
                publishDate: Date(),
                readingTime: i * 2,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(Double(-i * 3600)) // Stagger by hours
            )
            try await database.save(article)
        }
        
        // Test digest generation
        let digestService = DigestService(database: database)
        
        do {
            let digest = try await digestService.generateDigest(type: .daily)
            
            XCTAssertFalse(digest.topArticles.isEmpty, "Digest should have top articles")
            XCTAssertEqual(digest.type, .daily)
            XCTAssertGreaterThan(digest.stats.articlesSaved, 0)
            
            print("✅ Digest generation works! Found \(digest.topArticles.count) top articles")
        } catch {
            print("⚠️ Digest generation failed: \(error)")
            throw error
        }
    }
    
    func testParseServerIntegration() async throws {
        // Check if parse server is running
        let parseServerURL = URL(string: "http://localhost:8000/health")!
        
        do {
            let (data, response) = try await URLSession.shared.data(from: parseServerURL)
            
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200 {
                let health = try JSONDecoder().decode(HealthResponse.self, from: data)
                print("✅ Parse server is running! Status: \(health.status)")
                
                // Test parsing
                await testArticleParsing()
            } else {
                print("⚠️ Parse server returned unexpected response")
            }
        } catch {
            print("⚠️ Parse server not running at localhost:8000 - \(error.localizedDescription)")
            print("ℹ️ Start it with: cd parse-server && python main.py")
        }
    }
    
    private func testArticleParsing() async {
        let testHTML = """
        <html>
        <head><title>Test Article for Parsing</title></head>
        <body>
            <article>
                <h1>Understanding Swift Actors</h1>
                <p>By Jane Developer</p>
                <p>Published: January 2024</p>
                <div class="content">
                    <p>Swift actors provide a way to safely access mutable state in concurrent programs.</p>
                    <p>They ensure that only one piece of code can access the actor's state at a time.</p>
                </div>
            </article>
        </body>
        </html>
        """
        
        let parseRequest = ParseRequest(
            url: "https://example.com/swift-actors",
            htmlContent: testHTML,
            contentType: "article",
            metadata: ["title": "Test Article", "user_id": "test_user"]
        )
        
        do {
            let url = URL(string: "http://localhost:8000/parse")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = try JSONEncoder().encode(parseRequest)
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200 {
                let parsed = try JSONDecoder().decode(ParsedArticleResponse.self, from: data)
                
                XCTAssertEqual(parsed.title, "Understanding Swift Actors")
                XCTAssertTrue(parsed.content.contains("actors"))
                XCTAssertFalse(parsed.keywords.isEmpty)
                XCTAssertEqual(parsed.author, "Jane Developer")
                
                print("✅ Article parsing works! Keywords: \(parsed.keywords)")
            }
        } catch {
            print("⚠️ Article parsing test failed: \(error)")
        }
    }
}

// MARK: - Helper Models

struct HealthResponse: Codable {
    let status: String
    let redis: String
}

struct ParseRequest: Codable {
    let url: String
    let html_content: String
    let content_type: String
    let metadata: [String: String]
}

struct ParsedArticleResponse: Codable {
    let title: String
    let content: String
    let summary: String
    let keywords: [String]
    let author: String?
    let publish_date: String?
    let reading_time: Int
    let content_type: String
}