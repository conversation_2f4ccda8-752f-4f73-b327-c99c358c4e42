import XCTest
@testable import PocketNext

class SimpleIntegrationTest: XCTestCase {
    
    func testAppDoesNotCrashOnInitialization() async throws {
        // Test database initialization
        let dbManager = DatabaseManager.shared
        
        // First initialization should succeed
        try await dbManager.initialize()
        
        // Second initialization should be safe (no-op)
        try await dbManager.initialize()
        
        // Database operations should work
        let articles = try await dbManager.fetchRecentArticles()
        XCTAssertNotNil(articles)
    }
    
    func testCloudKitDoesNotCrashWithoutEntitlements() async {
        // This tests that CloudKit initialization handles missing entitlements gracefully
        let syncEngine = await SyncEngine()
        
        // Setup should not crash even without proper entitlements
        await syncEngine.setup()
        
        // The sync status should indicate an error, but app should not crash
        switch await syncEngine.syncStatus {
        case .idle, .syncing:
            // If CloudKit works, that's fine
            XCTAssertTrue(true)
        case .error(_):
            // Expected in test environment without entitlements
            XCTAssertTrue(true)
        }
    }
    
    func testConcurrentDatabaseAccess() async throws {
        // Test that concurrent database operations don't cause "database locked" errors
        let dbManager = DatabaseManager.shared
        
        try await dbManager.initialize()
        
        // Perform multiple concurrent reads
        try await withThrowingTaskGroup(of: [Article].self) { group in
            for _ in 0..<10 {
                group.addTask {
                    try await dbManager.fetchRecentArticles()
                }
            }
            
            // All should succeed
            for try await _ in group {
                // Success
            }
        }
    }
}