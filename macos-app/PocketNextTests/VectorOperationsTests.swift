import XCTest
@testable import PocketNext

class VectorOperationsTests: XCTestCase {
    
    // MARK: - Compression Tests
    
    func testCompressEmbedding_BasicFunctionality() {
        // Given
        let embedding: [Float] = [0.1, 0.5, -0.3, 0.9, -0.8]
        
        // When
        let compressed = VectorOperations.compressEmbedding(embedding)
        
        // Then
        XCTAssertGreaterThan(compressed.count, 8) // At least min/max values + data
        XCTAssertLessThan(compressed.count, embedding.count * 4) // Should be compressed
    }
    
    func testCompressDecompress_RoundTrip() {
        // Given
        let embedding: [Float] = Array(repeating: 0, count: 1536).map { _ in
            Float.random(in: -1.0...1.0)
        }
        
        // When
        let compressed = VectorOperations.compressEmbedding(embedding)
        let decompressed = VectorOperations.decompressEmbedding(compressed)
        
        // Then
        XCTAssertNotNil(decompressed)
        XCTAssertEqual(decompressed?.count, embedding.count)
        
        // Check that values are close (allowing for quantization error)
        if let decompressed = decompressed {
            for (original, decompressedValue) in zip(embedding, decompressed) {
                XCTAssertEqual(original, decompressedValue, accuracy: 0.02) // ~2% error tolerance
            }
        }
    }
    
    func testCompression_SizeReduction() {
        // Given - typical embedding size
        let embedding = Array(repeating: Float(0.5), count: 1536)
        
        // When
        let compressed = VectorOperations.compressEmbedding(embedding)
        
        // Then - should be roughly 4x smaller
        let originalSize = embedding.count * MemoryLayout<Float>.size
        let compressedSize = compressed.count
        let compressionRatio = Float(originalSize) / Float(compressedSize)
        
        XCTAssertGreaterThan(compressionRatio, 3.5) // Should be close to 4x
        XCTAssertLessThan(compressionRatio, 4.5)
    }
    
    func testDecompression_InvalidData() {
        // Given
        let tooSmallData = Data([1, 2, 3]) // Less than 8 bytes
        let emptyData = Data()
        
        // When/Then
        XCTAssertNil(VectorOperations.decompressEmbedding(tooSmallData))
        XCTAssertNil(VectorOperations.decompressEmbedding(emptyData))
    }
    
    func testCompression_EdgeCases() {
        // Test with extreme values
        let extremeEmbedding: [Float] = [-1.0, 1.0, 0.0, Float.infinity, -Float.infinity]
        let compressed = VectorOperations.compressEmbedding(extremeEmbedding)
        let decompressed = VectorOperations.decompressEmbedding(compressed)
        
        XCTAssertNotNil(decompressed)
        // Infinity values will be clamped during compression
    }
    
    // MARK: - Similarity Tests
    
    func testCosineSimilarity_IdenticalVectors() {
        // Given
        let vector: [Float] = [0.3, 0.4, 0.5]
        
        // When
        let similarity = VectorOperations.cosineSimilarity(vector, vector)
        
        // Then
        XCTAssertEqual(similarity, 1.0, accuracy: 0.0001)
    }
    
    func testCosineSimilarity_OrthogonalVectors() {
        // Given
        let vectorA: [Float] = [1.0, 0.0, 0.0]
        let vectorB: [Float] = [0.0, 1.0, 0.0]
        
        // When
        let similarity = VectorOperations.cosineSimilarity(vectorA, vectorB)
        
        // Then
        XCTAssertEqual(similarity, 0.0, accuracy: 0.0001)
    }
    
    func testCosineSimilarity_OppositeVectors() {
        // Given
        let vectorA: [Float] = [1.0, 2.0, 3.0]
        let vectorB: [Float] = [-1.0, -2.0, -3.0]
        
        // When
        let similarity = VectorOperations.cosineSimilarity(vectorA, vectorB)
        
        // Then
        XCTAssertEqual(similarity, -1.0, accuracy: 0.0001)
    }
    
    func testCosineSimilarity_DifferentLengths() {
        // Given
        let vectorA: [Float] = [1.0, 2.0, 3.0]
        let vectorB: [Float] = [1.0, 2.0]
        
        // When
        let similarity = VectorOperations.cosineSimilarity(vectorA, vectorB)
        
        // Then
        XCTAssertEqual(similarity, 0.0) // Should return 0 for mismatched lengths
    }
    
    func testCosineSimilarity_ZeroVector() {
        // Given
        let vectorA: [Float] = [0.0, 0.0, 0.0]
        let vectorB: [Float] = [1.0, 2.0, 3.0]
        
        // When
        let similarity = VectorOperations.cosineSimilarity(vectorA, vectorB)
        
        // Then
        XCTAssertEqual(similarity, 0.0)
    }
    
    // MARK: - Magnitude Tests
    
    func testMagnitude_UnitVector() {
        // Given
        let vector: [Float] = [1.0, 0.0, 0.0]
        
        // When
        let magnitude = VectorOperations.magnitude(vector)
        
        // Then
        XCTAssertEqual(magnitude, 1.0, accuracy: 0.0001)
    }
    
    func testMagnitude_ZeroVector() {
        // Given
        let vector: [Float] = [0.0, 0.0, 0.0]
        
        // When
        let magnitude = VectorOperations.magnitude(vector)
        
        // Then
        XCTAssertEqual(magnitude, 0.0)
    }
    
    func testMagnitude_345Triangle() {
        // Given - 3-4-5 right triangle
        let vector: [Float] = [3.0, 4.0]
        
        // When
        let magnitude = VectorOperations.magnitude(vector)
        
        // Then
        XCTAssertEqual(magnitude, 5.0, accuracy: 0.0001)
    }
    
    // MARK: - Normalization Tests
    
    func testNormalize_RegularVector() {
        // Given
        let vector: [Float] = [3.0, 4.0]
        
        // When
        let normalized = VectorOperations.normalize(vector)
        
        // Then
        XCTAssertEqual(normalized[0], 0.6, accuracy: 0.0001)
        XCTAssertEqual(normalized[1], 0.8, accuracy: 0.0001)
        
        // Verify it's a unit vector
        let magnitude = VectorOperations.magnitude(normalized)
        XCTAssertEqual(magnitude, 1.0, accuracy: 0.0001)
    }
    
    func testNormalize_ZeroVector() {
        // Given
        let vector: [Float] = [0.0, 0.0, 0.0]
        
        // When
        let normalized = VectorOperations.normalize(vector)
        
        // Then - should return unchanged
        XCTAssertEqual(normalized, vector)
    }
    
    func testNormalize_AlreadyNormalized() {
        // Given
        let vector: [Float] = [0.6, 0.8]
        
        // When
        let normalized = VectorOperations.normalize(vector)
        
        // Then
        XCTAssertEqual(normalized[0], vector[0], accuracy: 0.0001)
        XCTAssertEqual(normalized[1], vector[1], accuracy: 0.0001)
    }
    
    // MARK: - Fast Cosine Similarity Tests
    
    func testCosineSimilarityFast_MatchesRegular() {
        // Given
        let vectorA: [Float] = [1.0, 2.0, 3.0, 4.0, 5.0]
        let vectorB: [Float] = [2.0, 3.0, 4.0, 5.0, 6.0]
        
        let magA = VectorOperations.magnitude(vectorA)
        let magB = VectorOperations.magnitude(vectorB)
        
        // When
        let regularSimilarity = VectorOperations.cosineSimilarity(vectorA, vectorB)
        let fastSimilarity = VectorOperations.cosineSimilarityFast(vectorA, magA, vectorB, magB)
        
        // Then
        XCTAssertEqual(regularSimilarity, fastSimilarity, accuracy: 0.0001)
    }
    
    func testCosineSimilarityFast_Performance() {
        // Given - large vectors
        let vectorA = Array(repeating: 0, count: 1536).map { _ in Float.random(in: -1...1) }
        let vectorB = Array(repeating: 0, count: 1536).map { _ in Float.random(in: -1...1) }
        
        let magA = VectorOperations.magnitude(vectorA)
        let magB = VectorOperations.magnitude(vectorB)
        
        // Measure fast version
        let fastStart = CFAbsoluteTimeGetCurrent()
        for _ in 0..<1000 {
            _ = VectorOperations.cosineSimilarityFast(vectorA, magA, vectorB, magB)
        }
        let fastTime = CFAbsoluteTimeGetCurrent() - fastStart
        
        // Measure regular version
        let regularStart = CFAbsoluteTimeGetCurrent()
        for _ in 0..<1000 {
            _ = VectorOperations.cosineSimilarity(vectorA, vectorB)
        }
        let regularTime = CFAbsoluteTimeGetCurrent() - regularStart
        
        // Fast version should be faster (pre-computed magnitudes)
        XCTAssertLessThan(fastTime, regularTime)
    }
    
    // MARK: - Integration Tests
    
    func testCompressionWithSimilarity() {
        // Given - two similar vectors
        let vectorA: [Float] = Array(repeating: 0, count: 384).enumerated().map { i, _ in
            sin(Float(i) * 0.1)
        }
        let vectorB: [Float] = Array(repeating: 0, count: 384).enumerated().map { i, _ in
            sin(Float(i) * 0.1 + 0.5)
        }
        
        // When - compress and decompress
        let compressedA = VectorOperations.compressEmbedding(vectorA)
        let compressedB = VectorOperations.compressEmbedding(vectorB)
        
        let decompressedA = VectorOperations.decompressEmbedding(compressedA)!
        let decompressedB = VectorOperations.decompressEmbedding(compressedB)!
        
        // Then - similarity should be preserved
        let originalSimilarity = VectorOperations.cosineSimilarity(vectorA, vectorB)
        let decompressedSimilarity = VectorOperations.cosineSimilarity(decompressedA, decompressedB)
        
        XCTAssertEqual(originalSimilarity, decompressedSimilarity, accuracy: 0.05) // 5% tolerance
    }
}