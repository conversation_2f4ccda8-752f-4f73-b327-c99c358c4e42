import XCTest
import Swift<PERSON>
@testable import PocketNext

@MainActor
final class DatabaseInitializationRegressionTests: XCTestCase {
    
    // MARK: - Database Initialization Regression Tests
    
    func testDatabaseInitializationBeforeViewLoading() async throws {
        // This tests the "notInitialized" errors we're seeing
        // Ensure database is initialized before any views try to use it
        
        let appState = AppState()
        
        // Database should initialize in AppState's init
        // Wait for initialization to complete
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Now test that all database operations work
        do {
            // Test article loading
            let articles = try await appState.database.fetchRecentArticles()
            XCTAssertNotNil(articles) // Should succeed even if empty
            
            // Test statistics loading
            let stats = try await appState.database.fetchStatistics()
            XCTAssertNotNil(stats)
            
            // Test saving an article
            let testArticle = Article.testArticle(title: "Regression Test Article")
            try await appState.database.save(testArticle)
            
            // Verify it was saved
            let saved = try await appState.database.fetchArticle(id: testArticle.id)
            XCTAssertNotNil(saved)
            
        } catch DatabaseManager.DatabaseError.notInitialized {
            XCTFail("Database was not initialized - this is the regression we're preventing")
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    func testViewsHandleDatabaseNotInitializedGracefully() async throws {
        // Test that views don't crash if database operations fail
        
        // Create a broken database manager for testing
        let brokenDB = DatabaseManager()
        // Don't initialize it
        
        // Test HomeFeedView
        let homeFeedView = HomeFeedView(database: brokenDB)
            .environmentObject(AppState())
        
        // View creation should not crash
        XCTAssertNotNil(homeFeedView)
        
        // Test DigestView
        let digestView = DigestView(database: brokenDB)
            .environmentObject(AppState())
        
        XCTAssertNotNil(digestView)
    }
    
    func testConcurrentDatabaseAccess() async throws {
        // Test that concurrent database access doesn't cause initialization issues
        let appState = AppState()
        
        // Wait for initial setup
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Perform multiple concurrent operations
        try await withThrowingTaskGroup(of: Void.self) { group in
            // Load articles
            group.addTask {
                _ = try await appState.database.fetchRecentArticles()
            }
            
            // Load statistics
            group.addTask {
                _ = try await appState.database.fetchStatistics()
            }
            
            // Search
            group.addTask {
                _ = try await appState.database.searchArticles(query: "test")
            }
            
            // Save article
            group.addTask {
                let article = Article.testArticle(title: "Concurrent Test")
                try await appState.database.save(article)
            }
            
            // Wait for all to complete
            try await group.waitForAll()
        }
        
        // If we get here, concurrent access worked
        XCTAssertTrue(true)
    }
}

// MARK: - SwiftUI State Management Regression Tests

@MainActor
final class SwiftUIStateRegressionTests: XCTestCase {
    
    func testNoPublishingDuringViewUpdates() async throws {
        // Test that we don't publish changes during view updates
        // This prevents "Publishing changes from within view updates" error
        
        let appState = AppState()
        
        // Create a test that simulates what might cause the issue
        var viewUpdateCount = 0
        let expectation = XCTestExpectation(description: "View updates complete")
        
        // Simulate view body being called
        func simulateViewBody() {
            viewUpdateCount += 1
            
            // This would cause the error if done during view update:
            // DON'T: appState.articles = []
            
            // Instead, dispatch async
            Task { @MainActor in
                if viewUpdateCount == 1 {
                    // Safe way to update state
                    await appState.refreshArticles()
                    expectation.fulfill()
                }
            }
        }
        
        // Simulate multiple view updates
        simulateViewBody()
        simulateViewBody()
        simulateViewBody()
        
        await fulfillment(of: [expectation], timeout: 2.0)
        
        // Verify no crash occurred
        XCTAssertGreaterThan(viewUpdateCount, 0)
    }
    
    func testStateUpdatesAreMainActorIsolated() async {
        // Ensure all state updates happen on main actor
        let appState = AppState()
        
        // This should be safe from any thread
        await appState.refreshArticles()
        
        // Test from background thread
        let expectation = XCTestExpectation(description: "Background update")
        
        Task.detached {
            // This should automatically be dispatched to main actor
            await appState.refreshArticles()
            expectation.fulfill()
        }
        
        await fulfillment(of: [expectation], timeout: 2.0)
    }
}

// MARK: - CloudKit Regression Tests

final class CloudKitRegressionTests: XCTestCase {
    
    func testCloudKitHandlesMissingRecordTypesGracefully() async throws {
        // Test that CloudKit errors don't crash the app
        
        let syncService = await CloudKitChatSyncService()
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Service should handle errors gracefully
        switch await syncService.syncStatus {
        case .error(let error):
            // Expected - record types might not exist
            print("CloudKit error (expected in tests): \(error)")
            XCTAssertTrue(true)
        case .idle, .syncing, .success:
            // Also fine if it works
            XCTAssertTrue(true)
        }
        
        // App should not crash
        XCTAssertNotNil(syncService)
    }
    
    func testCloudKitSubscriptionSetupDoesntCrash() async throws {
        // Test subscription setup handles errors
        let syncService = await CloudKitChatSyncService()
        
        // This might fail but shouldn't crash
        await syncService.setupSubscriptions()
        
        // If we get here, it didn't crash
        XCTAssertTrue(true)
    }
}

// MARK: - LLM Service Regression Tests

@MainActor
final class LLMServiceRegressionTests: XCTestCase {
    
    func testLLMServiceHandlesMissingModelGracefully() async throws {
        // Test that missing LLM model doesn't crash
        let llmService = LLMService()
        
        // Wait for initialization attempt
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Service should handle missing model
        if let error = llmService.loadingError {
            print("LLM loading error (expected): \(error)")
            XCTAssertTrue(true)
        } else if llmService.isModelLoaded {
            // Model loaded successfully
            XCTAssertTrue(true)
        } else {
            // No model but no error - also acceptable
            XCTAssertTrue(true)
        }
    }
    
    func testLLMServiceDoesntCrashOnGenerateWithoutModel() async throws {
        let llmService = LLMService()
        
        do {
            _ = try await llmService.generateResponse(
                prompt: "Test",
                context: "Test context",
                conversationHistory: []
            )
            // If it succeeds, that's fine
            XCTAssertTrue(true)
        } catch {
            // Expected to fail without model
            XCTAssertNotNil(error)
        }
    }
}

// MARK: - Feed Curation Service Regression Tests

@MainActor 
final class FeedCurationRegressionTests: XCTestCase {
    
    func testFeedCurationHandlesUninitializedDatabase() async throws {
        // Create FeedCurationService with potentially uninitialized database
        let database = DatabaseManager()
        let curationService = FeedCurationService(database: database)
        
        // This should not crash even if database isn't ready
        await curationService.refreshFeed()
        
        // Service should handle error gracefully
        XCTAssertFalse(curationService.isProcessing)
    }
}

// MARK: - Search Engine Regression Tests

final class SearchEngineRegressionTests: XCTestCase {
    
    func testSearchEngineHandlesEmptyDatabase() async throws {
        // Test popular keywords with empty/uninitialized database
        let database = DatabaseManager.shared
        try await database.initialize()
        
        let searchEngine = SearchEngine(database: database)
        
        do {
            let keywords = try await searchEngine.getPopularKeywords()
            // Should return empty array, not crash
            XCTAssertNotNil(keywords)
        } catch {
            // Or handle error gracefully
            XCTAssertNotNil(error)
        }
    }
}

// MARK: - Integration Regression Test

@MainActor
final class AppLaunchRegressionTest: XCTestCase {
    
    func testFullAppLaunchSequence() async throws {
        // Comprehensive test of app launch to catch all initialization issues
        
        // 1. Create AppState (simulates app launch)
        let appState = AppState()
        
        // 2. Wait for all initialization
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        
        // 3. Test all major components are initialized
        
        // Database should be ready
        do {
            _ = try await appState.database.fetchRecentArticles()
        } catch {
            XCTFail("Database should be initialized after app launch")
        }
        
        // 4. Create all main views (simulates tab navigation)
        let contentView = ContentView()
            .environmentObject(appState)
        XCTAssertNotNil(contentView)
        
        // 5. Simulate navigation to each tab
        let viewModes: [AppState.ViewMode] = [.feed, .digest, .chat, .search]
        
        for mode in viewModes {
            appState.viewMode = mode
            
            // Give time for any async operations
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            
            // Should not crash
            XCTAssertEqual(appState.viewMode, mode)
        }
        
        // 6. Test search
        appState.searchQuery = "test"
        appState.viewMode = .search
        
        // 7. If we get here, app launched successfully without crashes
        XCTAssertTrue(true)
    }
}