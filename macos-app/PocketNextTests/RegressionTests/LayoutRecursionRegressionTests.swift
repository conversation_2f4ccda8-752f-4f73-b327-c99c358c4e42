import XCTest
import Swift<PERSON>
@testable import PocketNext

@MainActor
final class LayoutRecursionRegressionTests: XCTestCase {
    
    // MARK: - Layout Recursion Prevention Tests
    
    func testNoLayoutRecursionInContentView() async throws {
        // Test that ContentView doesn't cause layout recursion
        // This addresses: "It's not legal to call -layoutSubtreeIfNeeded on a view which is already being laid out"
        
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000) // Wait for initialization
        
        // Create the view hierarchy
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Create a hosting controller (simulates actual app usage)
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .resizable, .miniaturizable, .closable],
            backing: .buffered,
            defer: false
        )
        
        let hostingController = NSHostingController(rootView: contentView)
        window.contentViewController = hostingController
        
        // Force layout
        window.layoutIfNeeded()
        
        // If we get here without crash, layout recursion was avoided
        XCTAssertNotNil(window)
        XCTAssertNotNil(hostingController)
    }
    
    func testNoLayoutRecursionDuringSearch() async throws {
        // Test that search doesn't cause layout recursion
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Create SearchResultsView
        let searchView = SearchResultsView()
            .environmentObject(appState)
        
        // Trigger search
        appState.searchQuery = "test"
        appState.viewMode = .search
        
        // Create hosting controller
        let hostingController = NSHostingController(rootView: searchView)
        let window = NSWindow()
        window.contentViewController = hostingController
        
        // Multiple layout passes shouldn't cause recursion
        for _ in 0..<5 {
            window.layoutIfNeeded()
            try await Task.sleep(nanoseconds: 10_000_000) // 0.01 second
        }
        
        XCTAssertTrue(true) // No crash
    }
    
    func testNoLayoutRecursionInSidebarView() async throws {
        // Test sidebar statistics loading doesn't cause recursion
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000)
        
        let sidebarView = SidebarView()
            .environmentObject(appState)
        
        let hostingController = NSHostingController(rootView: sidebarView)
        let window = NSWindow()
        window.contentViewController = hostingController
        
        // Trigger layout
        window.layoutIfNeeded()
        
        // Update statistics (could trigger layout)
        await appState.refreshArticles()
        
        // Layout again
        window.layoutIfNeeded()
        
        XCTAssertTrue(true) // No recursion
    }
}

// MARK: - View Update Publishing Regression Tests

@MainActor
final class ViewUpdatePublishingRegressionTests: XCTestCase {
    
    func testNoPublishingDuringHomeFeedViewBody() async throws {
        // Prevent "Publishing changes from within view updates is not allowed"
        
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Create HomeFeedView
        let homeFeedView = HomeFeedView(database: appState.database)
            .environmentObject(appState)
        
        // Track if view body is being evaluated
        var isInViewBody = false
        
        // Hook into view updates
        let testView = homeFeedView
            .onAppear {
                isInViewBody = true
                // This would cause the error:
                // DON'T: appState.articles = []
                
                // Safe approach - dispatch async
                Task {
                    await appState.refreshArticles()
                }
                isInViewBody = false
            }
        
        // Create and display
        let hostingController = NSHostingController(rootView: testView)
        let window = NSWindow()
        window.contentViewController = hostingController
        window.layoutIfNeeded()
        
        // No crash means we handled it correctly
        XCTAssertTrue(true)
    }
    
    func testFeedCurationServiceUpdatesAsync() async throws {
        // Test that FeedCurationService doesn't publish during view updates
        
        let database = DatabaseManager.shared
        try await database.initialize()
        
        let curationService = FeedCurationService(database: database)
        
        // This should update state asynchronously
        await curationService.refreshFeed()
        
        // Check that updates happened after view cycle
        XCTAssertFalse(curationService.isProcessing)
    }
}

// MARK: - State Consistency Regression Tests

@MainActor
final class StateConsistencyRegressionTests: XCTestCase {
    
    func testViewModeConsistencyDuringNavigation() async throws {
        // Ensure view mode changes don't cause inconsistent state
        
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Rapid navigation changes
        let modes: [AppState.ViewMode] = [.feed, .digest, .chat, .search, .feed]
        
        for mode in modes {
            appState.viewMode = mode
            
            // Immediate check
            XCTAssertEqual(appState.viewMode, mode)
            
            // Check after a delay
            try await Task.sleep(nanoseconds: 10_000_000)
            XCTAssertEqual(appState.viewMode, mode, "View mode should remain consistent")
        }
    }
    
    func testSearchQueryAndViewModeSync() async throws {
        // Test that search query and view mode stay in sync
        
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Set search query
        appState.searchQuery = "test search"
        appState.viewMode = .search
        
        // Clear search
        appState.searchQuery = ""
        
        // View mode should update
        try await Task.sleep(nanoseconds: 100_000_000)
        
        // Should not be in search mode with empty query
        if appState.searchQuery.isEmpty {
            XCTAssertNotEqual(appState.viewMode, .search, "Empty search should exit search mode")
        }
    }
}

// MARK: - Memory and Performance Regression Tests

final class PerformanceRegressionTests: XCTestCase {
    
    func testNoMemoryLeaksInViewNavigation() async throws {
        // Test that navigating between views doesn't leak memory
        
        weak var weakAppState: AppState?
        weak var weakContentView: NSHostingController<ContentView>?
        
        autoreleasepool {
            let appState = AppState()
            weakAppState = appState
            
            let contentView = ContentView()
                .environmentObject(appState)
            
            let hostingController = NSHostingController(rootView: contentView)
            weakContentView = hostingController
            
            // Navigate through all views
            for mode in [AppState.ViewMode.feed, .digest, .chat, .search] {
                appState.viewMode = mode
                try await Task.sleep(nanoseconds: 50_000_000)
            }
        }
        
        // Give time for cleanup
        try await Task.sleep(nanoseconds: 100_000_000)
        
        // Check for leaks
        XCTAssertNil(weakContentView, "ContentView should be deallocated")
        // Note: AppState might persist due to shared instance
    }
}

// MARK: - Error Recovery Regression Tests

@MainActor
final class ErrorRecoveryRegressionTests: XCTestCase {
    
    func testAppRecoversFromDatabaseError() async throws {
        // Test that app can recover from database errors
        
        let appState = AppState()
        
        // Simulate database error by using uninitialized instance
        let brokenDB = DatabaseManager()
        
        // Try to use it (will fail)
        do {
            _ = try await brokenDB.fetchRecentArticles()
            XCTFail("Should have thrown notInitialized error")
        } catch DatabaseManager.DatabaseError.notInitialized {
            // Expected error
            XCTAssertTrue(true)
        }
        
        // Now use proper database
        try await appState.database.initialize()
        let articles = try await appState.database.fetchRecentArticles()
        XCTAssertNotNil(articles)
    }
    
    func testMultipleInitializationAttempts() async throws {
        // Test that multiple initialization attempts don't cause issues
        
        let database = DatabaseManager.shared
        
        // Multiple initialization attempts
        for _ in 0..<3 {
            do {
                try await database.initialize()
            } catch {
                // Subsequent attempts might fail, that's ok
                print("Additional initialization attempt failed (expected): \(error)")
            }
        }
        
        // Database should still work
        let articles = try await database.fetchRecentArticles()
        XCTAssertNotNil(articles)
    }
}