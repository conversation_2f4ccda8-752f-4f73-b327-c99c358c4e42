import XCTest
@testable import PocketNext

/// Minimal test to verify actual core functionality
final class MinimalFunctionalTest: XCTestCase {
    
    func testBasicDatabaseOperations() async throws {
        // Test database initialization and basic CRUD
        let db = DatabaseManager.shared
        try await db.initialize()
        
        // Create test article
        let article = Article(
            id: UUID(),
            url: "https://test.com",
            title: "Test",
            content: "Content",
            summary: "Summary",
            keywords: ["test"],
            author: "Author",
            publishDate: Date(),
            readingTime: 5,
            contentType: .article,
            capturedAt: Date()
        )
        
        // Save
        try await db.save(article)
        
        // Retrieve
        let fetched = try await db.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.title, "Test")
        
        // Search
        let results = try await db.searchArticles(query: "test", limit: 10)
        XCTAssertFalse(results.isEmpty)
        
        // Clean up
        try await db.delete(article.id)
        
        print("✅ Database operations work!")
    }
}