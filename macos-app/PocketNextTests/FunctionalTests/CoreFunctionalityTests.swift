import XCTest
@testable import PocketNext

/// Tests to verify that core functionality actually works end-to-end
@MainActor
final class CoreFunctionalityTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Use shared database to ensure proper initialization
        database = DatabaseManager.shared
        try await database.initialize()
        
        // Create app state
        appState = AppState()
        
        // Wait for initialization
        await appState.waitForInitialization()
    }
    
    override func tearDown() async throws {
        // Clean up test data
        let articles = try await database.fetchRecentArticles(limit: 1000)
        for article in articles {
            try await database.delete(article.id)
        }
        
        appState = nil
        database = nil
        try await super.tearDown()
    }
    
    // MARK: - Test 1: Basic Article Saving
    
    func testSaveAndRetrieveArticle() async throws {
        // Create a test article
        let article = Article(
            id: UUID(),
            url: "https://example.com/test-article",
            title: "Test Article: Understanding Swift Async/Await",
            content: """
            Swift's async/await syntax provides a more natural way to write asynchronous code.
            Instead of using callbacks or completion handlers, you can write code that looks synchronous
            but executes asynchronously. This makes code easier to read and reason about.
            
            Key concepts include:
            - async functions that can suspend execution
            - await keyword to call async functions
            - Task to create concurrent work
            - Actors for thread-safe state management
            """,
            summary: "An introduction to Swift's async/await syntax and its benefits for writing cleaner asynchronous code.",
            keywords: ["swift", "async", "await", "concurrency", "programming"],
            author: "John Doe",
            publishDate: Date(),
            readingTime: 3,
            contentType: .article,
            capturedAt: Date()
        )
        
        // Save the article
        try await database.save(article)
        
        // Retrieve and verify
        let retrieved = try await database.fetchArticle(id: article.id)
        XCTAssertNotNil(retrieved)
        XCTAssertEqual(retrieved?.title, article.title)
        XCTAssertEqual(retrieved?.url, article.url)
        XCTAssertEqual(retrieved?.keywords.count, 5)
        
        // Test fetching by URL
        let byURL = try await database.fetchArticle(url: article.url)
        XCTAssertNotNil(byURL)
        XCTAssertEqual(byURL?.id, article.id)
    }
    
    // MARK: - Test 2: Full-Text Search
    
    func testFullTextSearch() async throws {
        // Save multiple articles
        let articles = [
            Article.testArticle(
                title: "SwiftUI Fundamentals",
                content: "SwiftUI is Apple's declarative UI framework for building user interfaces across all Apple platforms.",
                keywords: ["swiftui", "ui", "apple", "declarative"]
            ),
            Article.testArticle(
                title: "Machine Learning with Core ML",
                content: "Core ML makes it easy to integrate machine learning models into your iOS apps.",
                keywords: ["machine learning", "coreml", "ios", "ai"]
            ),
            Article.testArticle(
                title: "Advanced Swift Techniques",
                content: "Explore advanced Swift features like property wrappers, result builders, and generics.",
                keywords: ["swift", "advanced", "programming"]
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Search for "swift"
        let swiftResults = try await database.searchArticles(query: "swift", limit: 10)
        XCTAssertGreaterThanOrEqual(swiftResults.count, 2)
        
        // Verify results contain Swift-related articles
        let titles = swiftResults.map { $0.title.lowercased() }
        XCTAssertTrue(titles.contains { $0.contains("swift") })
        
        // Search for "machine learning"
        let mlResults = try await database.searchArticles(query: "machine learning", limit: 10)
        XCTAssertGreaterThanOrEqual(mlResults.count, 1)
        XCTAssertTrue(mlResults.first?.title.contains("Machine Learning") ?? false)
    }
    
    // MARK: - Test 3: Article State Management
    
    func testArticleStateChanges() async throws {
        // Create and save article
        let article = Article.testArticle(
            title: "Test Article for State Changes",
            content: "This article will test state management features."
        )
        try await database.save(article)
        
        // Initially should be unread
        var fetched = try await database.fetchArticle(id: article.id)
        XCTAssertNil(fetched?.lastAccessedAt)
        XCTAssertFalse(fetched?.isRead ?? true)
        
        // Mark as read
        try await database.markAsRead(article.id)
        fetched = try await database.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched?.lastAccessedAt)
        XCTAssertTrue(fetched?.isRead ?? false)
        
        // Archive
        XCTAssertFalse(fetched?.isArchived ?? true)
        try await database.archive(article.id)
        fetched = try await database.fetchArticle(id: article.id)
        XCTAssertTrue(fetched?.isArchived ?? false)
        
        // Unarchive
        try await database.unarchive(article.id)
        fetched = try await database.fetchArticle(id: article.id)
        XCTAssertFalse(fetched?.isArchived ?? true)
    }
    
    // MARK: - Test 4: Digest Generation
    
    func testDailyDigestGeneration() async throws {
        // Save articles from the last 24 hours
        let now = Date()
        let articles = [
            Article.testArticle(
                title: "Morning Read: Swift News",
                content: "Latest updates in Swift development.",
                readingTime: 5,
                capturedAt: now.addingTimeInterval(-3600 * 20) // 20 hours ago
            ),
            Article.testArticle(
                title: "Afternoon Article: iOS Tips",
                content: "Useful tips for iOS developers.",
                readingTime: 8,
                capturedAt: now.addingTimeInterval(-3600 * 10) // 10 hours ago
            ),
            Article.testArticle(
                title: "Quick Read: Tech News",
                content: "Brief tech news update.",
                readingTime: 2,
                capturedAt: now.addingTimeInterval(-3600 * 5) // 5 hours ago
            ),
            Article.testArticle(
                title: "Long Read: AI Deep Dive",
                content: "Comprehensive look at AI advancements.",
                readingTime: 20,
                capturedAt: now.addingTimeInterval(-3600 * 2) // 2 hours ago
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Generate digest
        let digestService = DigestService(database: database)
        let digest = try await digestService.generateDigest(type: .daily)
        
        // Verify digest content
        XCTAssertNotNil(digest)
        XCTAssertFalse(digest.topArticles.isEmpty)
        XCTAssertLessThanOrEqual(digest.topArticles.count, 10)
        
        // Check quick reads (articles with reading time <= 5 minutes)
        XCTAssertFalse(digest.quickReads.isEmpty)
        XCTAssertTrue(digest.quickReads.allSatisfy { $0.readingTime <= 5 })
        
        // Verify stats
        XCTAssertEqual(digest.stats.articlesSaved, 4)
        XCTAssertEqual(digest.stats.articlesRead, 0) // None read yet
    }
    
    // MARK: - Test 5: LLM Integration (API Mode)
    
    func testLLMResponseGeneration() async throws {
        // Skip if no API key
        guard ProcessInfo.processInfo.environment["OPENAI_API_KEY"] != nil else {
            throw XCTSkip("OPENAI_API_KEY not set")
        }
        
        // Initialize LLM service
        let llmService = LLMService()
        
        // Wait for model to load
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        XCTAssertTrue(llmService.isModelLoaded)
        
        // Test generating a response
        let response = try await llmService.generateResponse(
            prompt: "What is Swift?",
            context: "Swift is a programming language developed by Apple.",
            conversationHistory: []
        )
        
        XCTAssertFalse(response.text.isEmpty)
        XCTAssertGreaterThan(response.tokensUsed, 0)
        XCTAssertEqual(response.modelUsed, "gpt-4o-mini")
    }
    
    // MARK: - Test 6: RAG (Retrieval Augmented Generation)
    
    func testRAGWithSavedArticles() async throws {
        // Save some context articles
        let articles = [
            Article.testArticle(
                title: "SwiftUI Button Guide",
                content: """
                SwiftUI buttons are created using the Button view. You can customize them with modifiers.
                Example: Button("Tap Me") { print("Button tapped") }.buttonStyle(.borderedProminent)
                """,
                keywords: ["swiftui", "button", "ui"]
            ),
            Article.testArticle(
                title: "SwiftUI Navigation",
                content: """
                Navigation in SwiftUI uses NavigationStack and NavigationLink.
                NavigationStack provides a container for navigable content.
                """,
                keywords: ["swiftui", "navigation", "navigationstack"]
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Initialize RAG service
        let ragService = RAGService(databaseManager: database)
        
        // Wait for initialization
        let timeout = Date().addingTimeInterval(5)
        while !ragService.isReady && Date() < timeout {
            try await Task.sleep(nanoseconds: 100_000_000)
        }
        
        // Ask a question that should use the saved content
        let response = try await ragService.generateResponse(
            query: "How do I create a button in SwiftUI?",
            conversationId: nil
        )
        
        XCTAssertFalse(response.answer.isEmpty)
        XCTAssertFalse(response.sources.isEmpty)
        
        // Should reference the button guide article
        XCTAssertTrue(response.sources.contains { $0.title.contains("Button") })
    }
    
    // MARK: - Test 7: Native Messaging
    
    func testNativeMessagingArticleSave() async throws {
        // Native messaging in the actual implementation uses stdin/stdout
        // For testing, we'll directly test the article processing flow
        
        let testURL = "https://swift.org/blog/async-await"
        let testHTML = """
        <html>
        <head><title>Async/Await in Swift</title></head>
        <body>
            <h1>Async/Await in Swift</h1>
            <p>By Swift Team</p>
            <p>Swift's new concurrency features make asynchronous programming easier.</p>
        </body>
        </html>
        """
        
        // Create article directly (simulating what would happen after parsing)
        let article = Article(
            id: UUID(),
            url: testURL,
            title: "Async/Await in Swift",
            content: "Swift's new concurrency features make asynchronous programming easier.",
            summary: "Introduction to async/await in Swift",
            keywords: ["swift", "async", "await"],
            author: "Swift Team",
            publishDate: Date(),
            readingTime: 3,
            contentType: .article,
            capturedAt: Date()
        )
        
        // Save the article
        try await database.save(article)
        
        // Verify article was saved
        let saved = try await database.fetchArticle(url: testURL)
        XCTAssertNotNil(saved)
        XCTAssertEqual(saved?.title, "Async/Await in Swift")
        XCTAssertEqual(saved?.author, "Swift Team")
    }
    
    // MARK: - Test 8: Sync Status
    
    func testArticleSyncStatus() async throws {
        // Create articles with different sync states
        let pendingArticle = Article.testArticle(
            title: "Pending Sync Article",
            syncStatus: .pending
        )
        let syncedArticle = Article.testArticle(
            title: "Already Synced Article",
            syncStatus: .synced
        )
        let errorArticle = Article.testArticle(
            title: "Sync Error Article",
            syncStatus: .error
        )
        
        // Save all
        for article in [pendingArticle, syncedArticle, errorArticle] {
            try await database.save(article)
        }
        
        // Fetch unsynced articles
        let unsynced = try await database.fetchUnsyncedArticles()
        
        // Should include pending and error, but not synced
        XCTAssertTrue(unsynced.contains { $0.id == pendingArticle.id })
        XCTAssertTrue(unsynced.contains { $0.id == errorArticle.id })
        XCTAssertFalse(unsynced.contains { $0.id == syncedArticle.id })
        
        // Mark pending as synced
        try await database.markAsSynced(pendingArticle.id)
        
        // Verify it's now synced
        let updated = try await database.fetchArticle(id: pendingArticle.id)
        XCTAssertEqual(updated?.syncStatus, .synced)
    }
    
    // MARK: - Test 9: Performance
    
    func testSearchPerformance() async throws {
        // Add 100 articles
        for i in 1...100 {
            let article = Article.testArticle(
                title: "Article \(i): \(["Swift", "iOS", "macOS", "AI", "Web"].randomElement()!) Development",
                content: "Content about various development topics including Swift, iOS, and more.",
                keywords: ["development", "programming", "technology"]
            )
            try await database.save(article)
        }
        
        // Measure search performance
        let startTime = Date()
        let results = try await database.searchArticles(query: "Swift", limit: 20)
        let duration = Date().timeIntervalSince(startTime)
        
        // Should return results quickly
        XCTAssertLessThan(duration, 0.5) // 500ms
        XCTAssertFalse(results.isEmpty)
        
        print("Search completed in \(duration * 1000)ms, found \(results.count) results")
    }
    
    // MARK: - Test 10: Content Types
    
    func testDifferentContentTypes() async throws {
        let contentTypes: [(Article.ContentType, String)] = [
            (.article, "Regular news article"),
            (.tweet, "Quick tweet update"),
            (.tweetThread, "Long Twitter thread discussion"),
            (.video, "YouTube video transcript"),
            (.pdf, "Research paper PDF")
        ]
        
        for (type, title) in contentTypes {
            let article = Article.testArticle(
                title: title,
                contentType: type
            )
            try await database.save(article)
        }
        
        // Fetch all and verify
        let all = try await database.fetchRecentArticles(limit: 10)
        
        // Should have all content types
        for (type, _) in contentTypes {
            XCTAssertTrue(all.contains { $0.contentType == type })
        }
    }
}

// Extension already exists in TestHelpers.swift