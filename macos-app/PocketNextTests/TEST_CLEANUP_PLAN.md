# Test Cleanup Plan for macOS App

## Tests to Keep (Already Updated or Still Valid)

### ✅ Core Architecture Tests
- **HybridVectorStorageTests.swift** - Current implementation
- **VectorOperationsTests.swift** - New tests for vector operations
- **CloudKitSyncServiceTests.swift** - New tests for CloudKit sync
- **DatabaseManagerSchemaTests.swift** - Updated for new schema
- **ModelTests/ArticleModelTests.swift** - Updated for new Article fields
- **IntegrationTests/HybridArchitectureIntegrationTests.swift** - New integration tests

### ✅ Service Tests (Need Minor Updates)
- **SearchEngineTests.swift** - Needs update to use HybridVectorStorage
- **DigestServiceTests.swift** - Keep if digest feature still used
- **RAGServiceTests.swift** - Keep if RAG feature still used
- **ContentProcessorTests.swift** - Keep if content processing unchanged
- **EmbeddingServiceTests.swift** - Keep if embedding service still used
- **WebSearchServiceTests.swift** - Keep if web search still used
- **NativeMessagingTests.swift** - Keep for browser extension integration

### ✅ Database Tests (Keep Core Functionality)
- **DatabaseManagerTests.swift** - Basic CRUD tests still valid
- **DatabaseManagerLifecycleTests.swift** - Initialization tests still needed
- **IntegrationTests/RealDatabaseIntegrationTests.swift** - Real DB tests valuable

## Tests to Remove or Significantly Update

### ❌ Obsolete Tests
- **VectorStorageTests.swift** - Tests old VectorStorageManager (keep for RAG if needed)
- **ModelTests/ArticleTests.swift** - Duplicate of ArticleModelTests.swift
- **BasicAnimationTests.swift** - Remove if animations not critical

### 🔄 View Tests (Need Review)
- **ViewTests/*.swift** - Review against current UI implementation
- **TabNavigationTests.swift** - Check if tab navigation still exists
- **AppInitializationTests.swift** - May need updates for new initialization

### 🔄 Mock Tests (Need Consolidation)
- **LLMServiceMockTests.swift** - Consolidate with LLMServiceTests.swift
- **Mocks/MockServices.swift** - Update mocks to match current services

## Recommended Actions

1. **Immediate Removals**:
   ```bash
   rm ModelTests/ArticleTests.swift  # Duplicate
   rm BasicAnimationTests.swift      # Not critical
   ```

2. **Update SearchEngineTests**:
   - Remove old vector storage references
   - Add HybridVectorStorage mock
   - Update semantic search tests

3. **Consolidate Mocks**:
   - Create single MockServices.swift with all mocks
   - Ensure mocks match current service interfaces

4. **View Tests Strategy**:
   - Keep only tests for critical UI flows
   - Remove tests for UI that has changed
   - Focus on integration tests over unit tests for views

5. **Add Missing Tests**:
   - Performance benchmarks for vector operations
   - CloudKit sync error scenarios
   - Index corruption recovery
   - Cross-device sync scenarios

## Test Coverage Goals

- **Core Services**: >90% coverage
- **Database Operations**: >85% coverage
- **Vector Operations**: >95% coverage
- **UI/Views**: >60% coverage (focus on critical paths)
- **Integration**: Key user flows covered

## Priority Order

1. ⚡ Fix SearchEngineTests to use HybridVectorStorage
2. ⚡ Remove duplicate/obsolete tests
3. 📝 Update service tests for new APIs
4. 🧪 Add missing integration tests
5. 🎯 Review and update view tests