import XCTest
import SwiftUI
@testable import PocketNext

final class SyncUITests: XCTestCase {
    
    func testSyncStatusViewInitialization() {
        // Test that SyncStatusView can be created
        let syncEngine = SyncEngine()
        let view = SyncStatusView()
            .environmentObject(syncEngine)
        
        XCTAssertNotNil(view)
    }
    
    func testSyncEngineStates() {
        let syncEngine = SyncEngine()
        
        // Test initial state
        XCTAssertFalse(syncEngine.isSyncing)
        XCTAssertEqual(syncEngine.syncStatus, .idle)
        XCTAssertNil(syncEngine.lastSyncDate)
        XCTAssertEqual(syncEngine.pendingChanges, 0)
        XCTAssertEqual(syncEngine.syncProgress, 0.0)
        XCTAssertTrue(syncEngine.syncConflicts.isEmpty)
    }
    
    func testSyncStatusEquality() {
        // Test SyncStatus equality
        let idle1 = SyncEngine.SyncStatus.idle
        let idle2 = SyncEngine.SyncStatus.idle
        let syncing = SyncEngine.SyncStatus.syncing
        let error1 = SyncEngine.SyncStatus.error("Error 1")
        let error2 = SyncEngine.SyncStatus.error("Error 1")
        let error3 = SyncEngine.SyncStatus.error("Error 2")
        
        XCTAssertEqual(idle1, idle2)
        XCTAssertNotEqual(idle1, syncing)
        XCTAssertNotEqual(idle1, error1)
        XCTAssertEqual(error1, error2)
        XCTAssertNotEqual(error1, error3)
    }
    
    func testSyncConflictCreation() {
        let conflict = SyncConflict(
            id: UUID(),
            itemTitle: "Test Article",
            localDate: Date(),
            remoteDate: Date().addingTimeInterval(-3600),
            localChanges: ["Change 1", "Change 2"],
            remoteChanges: ["Change A", "Change B"],
            localContent: "Local content",
            remoteContent: "Remote content"
        )
        
        XCTAssertEqual(conflict.itemTitle, "Test Article")
        XCTAssertEqual(conflict.localChanges.count, 2)
        XCTAssertEqual(conflict.remoteChanges.count, 2)
        XCTAssertEqual(conflict.getPreview(for: .local), "Local content")
        XCTAssertEqual(conflict.getPreview(for: .remote), "Remote content")
    }
    
    func testConflictVersionMetadata() {
        // Test ConflictVersion enum
        let localVersion = ConflictVersion.local
        let remoteVersion = ConflictVersion.remote
        
        XCTAssertEqual(localVersion.title, "Local Version")
        XCTAssertEqual(localVersion.icon, "laptopcomputer")
        XCTAssertEqual(remoteVersion.title, "iCloud Version")
        XCTAssertEqual(remoteVersion.icon, "icloud")
    }
    
    func testSyncDetailsView() {
        let syncEngine = SyncEngine()
        let view = SyncDetailsView()
            .environmentObject(syncEngine)
        
        XCTAssertNotNil(view)
    }
    
    func testSyncConflictView() {
        let conflict = SyncConflict(
            id: UUID(),
            itemTitle: "Test Conflict",
            localDate: Date(),
            remoteDate: Date(),
            localChanges: [],
            remoteChanges: [],
            localContent: "Local",
            remoteContent: "Remote"
        )
        
        let view = SyncConflictView(conflict: conflict)
        XCTAssertNotNil(view)
    }
    
    func testStatCardView() {
        let card = StatCard(
            title: "Articles",
            value: "42",
            icon: "doc.text"
        )
        
        XCTAssertNotNil(card)
    }
    
    func testSyncEngineConflictResolution() async {
        let syncEngine = SyncEngine()
        let conflict = SyncConflict(
            id: UUID(),
            itemTitle: "Test",
            localDate: Date(),
            remoteDate: Date(),
            localChanges: [],
            remoteChanges: [],
            localContent: "",
            remoteContent: ""
        )
        
        // Add conflict
        await MainActor.run {
            syncEngine.syncConflicts.append(conflict)
            XCTAssertEqual(syncEngine.syncConflicts.count, 1)
            
            // Resolve conflict
            syncEngine.resolveConflict(conflict, resolution: .useLocal)
            XCTAssertEqual(syncEngine.syncConflicts.count, 0)
        }
    }
}