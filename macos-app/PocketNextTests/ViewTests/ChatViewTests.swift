import XCTest
import Swift<PERSON>
@testable import PocketNext

@MainActor
final class ChatViewTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    var mockLLMService: MockLLMService!
    
    override func setUp() async throws {
        try await super.setUp()
        database = try await DatabaseManager(inMemory: true)
        appState = AppState()
        mockLLMService = MockLLMService()
    }
    
    override func tearDown() async throws {
        appState = nil
        database = nil
        mockLLMService = nil
        try await super.tearDown()
    }
    
    // MARK: - Chat State Tests
    
    func testInitialChatState() {
        let chatView = ChatView()
            .environmentObject(appState)
        
        XCTAssertNotNil(chatView)
        // Initial state should have empty conversation
    }
    
    func testMessageSending() async throws {
        // Create a chat conversation
        let conversation = ChatConversation(
            id: UUID(),
            title: "Test Chat",
            createdAt: Date(),
            updatedAt: Date(),
            metadata: nil
        )
        
        try await database.save(conversation)
        
        // Add a message
        let userMessage = DBChatMessage(
            id: UUID(),
            conversationId: conversation.id,
            role: "user",
            content: "Test question about articles",
            citedSources: nil,
            timestamp: Date(),
            metadata: nil
        )
        
        try await database.save(userMessage)
        
        // Verify message was saved
        let messages = try await database.fetchMessages(for: conversation.id)
        XCTAssertEqual(messages.count, 1)
        XCTAssertEqual(messages[0].content, "Test question about articles")
        XCTAssertEqual(messages[0].role, "user")
    }
    
    // MARK: - AI Response Tests
    
    func testAIResponseGeneration() async throws {
        mockLLMService.mockResponse = "This is a helpful AI response about your articles."
        
        let response = try await mockLLMService.generateResponse(
            prompt: "Tell me about my saved articles",
            context: "User has 5 articles about Swift programming",
            conversationHistory: nil
        )
        
        XCTAssertEqual(response.text, mockLLMService.mockResponse)
        XCTAssertEqual(mockLLMService.requestCount, 1)
    }
    
    func testConversationContext() async throws {
        // Create conversation with history
        let conversation = ChatConversation(
            id: UUID(),
            title: "Context Test",
            createdAt: Date(),
            updatedAt: Date(),
            metadata: nil
        )
        
        try await database.save(conversation)
        
        // Add message history
        let messages = [
            DBChatMessage(
                id: UUID(),
                conversationId: conversation.id,
                role: "user",
                content: "What articles do I have about Swift?",
                citedSources: nil,
                timestamp: Date().addingTimeInterval(-60),
                metadata: nil
            ),
            DBChatMessage(
                id: UUID(),
                conversationId: conversation.id,
                role: "assistant",
                content: "You have 3 articles about Swift programming.",
                citedSources: nil,
                timestamp: Date().addingTimeInterval(-30),
                metadata: nil
            )
        ]
        
        for message in messages {
            try await database.save(message)
        }
        
        let savedMessages = try await database.fetchMessages(for: conversation.id)
        XCTAssertEqual(savedMessages.count, 2)
        
        // Verify message order
        XCTAssertEqual(savedMessages[0].role, "user")
        XCTAssertEqual(savedMessages[1].role, "assistant")
    }
    
    // MARK: - Related Articles Tests
    
    func testRelatedArticlesDisplay() async throws {
        // Create articles
        let articles = [
            Article.testArticle(id: UUID(), title: "Swift Concurrency"),
            Article.testArticle(id: UUID(), title: "SwiftUI Basics"),
            Article.testArticle(id: UUID(), title: "Python Guide")
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Create message with related articles
        let citedArticleIds = [articles[0].id.uuidString, articles[1].id.uuidString]
        let citedSourcesData = try JSONEncoder().encode(citedArticleIds)
        
        let message = DBChatMessage(
            id: UUID(),
            conversationId: UUID(),
            role: "assistant",
            content: "Based on your Swift articles...",
            citedSources: citedSourcesData,
            timestamp: Date(),
            metadata: nil
        )
        
        XCTAssertNotNil(message.citedSources)
        let decodedCitations = try JSONDecoder().decode([String].self, from: message.citedSources!)
        XCTAssertTrue(decodedCitations.contains(articles[0].id.uuidString))
        XCTAssertTrue(decodedCitations.contains(articles[1].id.uuidString))
    }
    
    // MARK: - Chat Input Tests
    
    func testChatInputValidation() {
        var inputText = ""
        
        // Empty input should be invalid
        XCTAssertTrue(inputText.isEmpty)
        
        // Valid input
        inputText = "Tell me about my articles"
        XCTAssertFalse(inputText.isEmpty)
        
        // Whitespace only should be treated as empty
        inputText = "   "
        XCTAssertTrue(inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
    }
    
    func testMessageLengthLimit() {
        let maxLength = 2000
        let longMessage = String(repeating: "a", count: maxLength + 100)
        
        // Should truncate to max length
        let truncated = String(longMessage.prefix(maxLength))
        XCTAssertEqual(truncated.count, maxLength)
    }
    
    // MARK: - Conversation List Tests
    
    func testConversationsList() async throws {
        // Create multiple conversations
        let conversations = [
            ChatConversation(
                id: UUID(),
                title: "Swift Questions",
                createdAt: Date().addingTimeInterval(-86400),
                updatedAt: Date().addingTimeInterval(-86400),
                metadata: nil
            ),
            ChatConversation(
                id: UUID(),
                title: "Python Help",
                createdAt: Date().addingTimeInterval(-3600),
                updatedAt: Date().addingTimeInterval(-3600),
                metadata: nil
            ),
            ChatConversation(
                id: UUID(),
                title: "General Questions",
                createdAt: Date(),
                updatedAt: Date(),
                metadata: nil
            )
        ]
        
        for conversation in conversations {
            try await database.save(conversation)
        }
        
        let saved = try await database.fetchAllConversations()
        XCTAssertEqual(saved.count, 3)
        
        // Should be sorted by most recent first
        let sorted = saved.sorted { $0.updatedAt > $1.updatedAt }
        XCTAssertEqual(sorted[0].title, "General Questions")
    }
    
    // MARK: - Error Handling Tests
    
    func testAIServiceError() async {
        mockLLMService.shouldFail = true
        
        do {
            _ = try await mockLLMService.generateResponse(
                prompt: "Test",
                context: nil,
                conversationHistory: nil
            )
            XCTFail("Should throw error")
        } catch {
            XCTAssertEqual(error as? LLMError, .apiError("Mock error"))
        }
    }
    
    // MARK: - UI State Tests
    
    func testLoadingStates() {
        var isLoading = false
        
        // Initial state
        XCTAssertFalse(isLoading)
        
        // During message send
        isLoading = true
        XCTAssertTrue(isLoading)
        
        // After response
        isLoading = false
        XCTAssertFalse(isLoading)
    }
    
    func testScrollBehavior() {
        var shouldScrollToBottom = false
        
        // After sending message
        shouldScrollToBottom = true
        XCTAssertTrue(shouldScrollToBottom)
        
        // After receiving response
        shouldScrollToBottom = true
        XCTAssertTrue(shouldScrollToBottom)
    }
}

// MARK: - Message View Tests

final class ChatMessageViewTests: XCTestCase {
    
    func testMessageBubbleStyles() {
        let userMessage = DBChatMessage(
            id: UUID(),
            conversationId: UUID(),
            role: "user",
            content: "User message",
            citedSources: nil,
            timestamp: Date(),
            metadata: nil
        )
        
        let assistantMessage = DBChatMessage(
            id: UUID(),
            conversationId: UUID(),
            role: "assistant",
            content: "Assistant message",
            citedSources: nil,
            timestamp: Date(),
            metadata: nil
        )
        
        // User messages should be right-aligned
        XCTAssertEqual(userMessage.role, "user")
        
        // Assistant messages should be left-aligned
        XCTAssertEqual(assistantMessage.role, "assistant")
    }
    
    func testTimestampFormatting() {
        let now = Date()
        let message = DBChatMessage(
            id: UUID(),
            conversationId: UUID(),
            role: "user",
            content: "Test",
            citedSources: nil,
            timestamp: now,
            metadata: nil
        )
        
        // Timestamp should be present
        XCTAssertEqual(message.timestamp, now)
    }
    
    func testMarkdownRendering() {
        let markdownContent = """
        # Heading
        
        **Bold text** and *italic text*
        
        - List item 1
        - List item 2
        
        `inline code` and
        
        ```swift
        let code = "block"
        ```
        """
        
        let message = DBChatMessage(
            id: UUID(),
            conversationId: UUID(),
            role: "assistant",
            content: markdownContent,
            citedSources: nil,
            timestamp: Date(),
            metadata: nil
        )
        
        // Content should contain markdown
        XCTAssertTrue(message.content.contains("#"))
        XCTAssertTrue(message.content.contains("**"))
        XCTAssertTrue(message.content.contains("```"))
    }
}

// MARK: - Chat Settings Tests

@MainActor
final class ChatSettingsTests: XCTestCase {
    var chatSettings: ChatViewTests.ChatSettings!
    
    override func setUp() {
        super.setUp()
        chatSettings = ChatViewTests.ChatSettings()
    }
    
    func testLLMModelSelection() {
        // Test default model
        XCTAssertEqual(chatSettings.selectedModel, .api)
        
        // Test model switching
        chatSettings.selectedModel = .local
        XCTAssertEqual(chatSettings.selectedModel, .local)
        
        chatSettings.selectedModel = .hybrid
        XCTAssertEqual(chatSettings.selectedModel, .hybrid)
    }
    
    func testChatPreferences() {
        // Test temperature setting
        XCTAssertEqual(chatSettings.temperature, 0.7)
        
        chatSettings.temperature = 0.5
        XCTAssertEqual(chatSettings.temperature, 0.5)
        
        // Test max tokens
        XCTAssertEqual(chatSettings.maxTokens, 1000)
        
        chatSettings.maxTokens = 1500
        XCTAssertEqual(chatSettings.maxTokens, 1500)
    }
    
    func testConversationManagement() {
        // Test auto-title generation
        XCTAssertTrue(chatSettings.autoGenerateTitle)
        
        // Test conversation retention
        XCTAssertEqual(chatSettings.conversationRetentionDays, 30)
        
        // Test clear history option
        var clearHistoryCalled = false
        let clearHistory = {
            clearHistoryCalled = true
        }
        
        clearHistory()
        XCTAssertTrue(clearHistoryCalled)
    }
}

// MARK: - Helper Models

extension ChatViewTests {
    struct ChatSettings {
        var selectedModel: LLMService.InferenceMode = .api
        var temperature: Double = 0.7
        var maxTokens: Int = 1000
        var autoGenerateTitle: Bool = true
        var conversationRetentionDays: Int = 30
    }
}