import XCTest
import Swift<PERSON>
@testable import PocketNext

@MainActor
final class ContentViewTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        database = try await DatabaseManager(inMemory: true)
        appState = AppState()
    }
    
    override func tearDown() async throws {
        appState = nil
        database = nil
        try await super.tearDown()
    }
    
    // MARK: - Layout Tests
    
    func testNavigationSplitViewStructure() {
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Test that view can be created
        XCTAssertNotNil(contentView)
        
        // Test initial state
        XCTAssertEqual(appState.viewMode, .feed)
        XCTAssertNil(appState.selectedArticle)
        XCTAssertTrue(appState.searchQuery.isEmpty)
    }
    
    func testViewModeChanges() {
        // Test switching between view modes
        appState.viewMode = .feed
        XCTAssertEqual(appState.viewMode, .feed)
        
        appState.viewMode = .digest
        XCTAssertEqual(appState.viewMode, .digest)
        
        appState.viewMode = .search
        XCTAssertEqual(appState.viewMode, .search)
        
        appState.viewMode = .chat
        XCTAssertEqual(appState.viewMode, .chat)
        
        appState.viewMode = .reading
        XCTAssertEqual(appState.viewMode, .reading)
    }
    
    // MARK: - Search Tests
    
    func testSearchFunctionality() async {
        // Test empty search
        appState.searchQuery = ""
        XCTAssertEqual(appState.viewMode, .feed)
        
        // Test search with query
        appState.searchQuery = "Swift"
        appState.viewMode = .search
        XCTAssertEqual(appState.viewMode, .search)
        XCTAssertEqual(appState.searchQuery, "Swift")
    }
    
    // MARK: - Capture Overlay Tests
    
    func testCaptureOverlayVisibility() {
        // Initially not capturing
        XCTAssertFalse(appState.isCapturing)
        
        // Trigger capture
        appState.triggerCapture()
        XCTAssertTrue(appState.isCapturing)
        
        // End capture
        appState.endCapture()
        XCTAssertFalse(appState.isCapturing)
    }
    
    // MARK: - Article Selection Tests
    
    func testArticleSelection() async throws {
        let article = Article.testArticle(title: "Test Article")
        try await database.save(article)
        
        // Select article
        appState.selectedArticle = article
        appState.viewMode = .reading
        
        XCTAssertNotNil(appState.selectedArticle)
        XCTAssertEqual(appState.selectedArticle?.id, article.id)
        XCTAssertEqual(appState.viewMode, .reading)
    }
}

// MARK: - Sidebar View Tests

@MainActor
final class SidebarViewTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        database = try await DatabaseManager(inMemory: true)
        appState = AppState()
    }
    
    func testStatisticsBadges() async throws {
        // Add test articles
        let articles = [
            Article.testArticle(title: "Article 1"),
            Article.testArticle(title: "Article 2"),
            Article.testArticle(title: "Article 3", isArchived: true),
            Article.testArticle(title: "Article 4", isArchived: true)
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        let stats = try await database.fetchStatistics()
        
        XCTAssertEqual(stats.totalArticles, 4)
        XCTAssertEqual(stats.unreadArticles, 2)
        XCTAssertEqual(stats.archivedArticles, 2)
    }
    
    func testNavigationSelection() {
        // Test navigation state changes
        appState.viewMode = .feed
        XCTAssertEqual(appState.viewMode, .feed)
        
        appState.viewMode = .digest
        XCTAssertEqual(appState.viewMode, .digest)
        
        appState.viewMode = .chat
        XCTAssertEqual(appState.viewMode, .chat)
    }
    
    func testKeyboardShortcut() {
        // Test save keyboard shortcut state
        XCTAssertFalse(appState.isCapturing)
        
        // Simulate keyboard shortcut
        appState.triggerCapture()
        XCTAssertTrue(appState.isCapturing)
    }
}

// MARK: - Empty State View Tests

final class EmptyStateViewTests: XCTestCase {
    
    func testEmptyStateContent() {
        let view = EmptyStateView()
        XCTAssertNotNil(view)
        
        // Test that view contains expected elements
        // In a real UI test, we'd verify the presence of:
        // - Icon (bookmark.slash)
        // - Title text
        // - Description text
        // - Action button
    }
}

// MARK: - Popular Tags View Tests

@MainActor
final class PopularTagsViewTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        database = try await DatabaseManager(inMemory: true)
        appState = AppState()
    }
    
    func testPopularTagsDisplay() async throws {
        // Add articles with keywords
        let articles = [
            Article.testArticle(keywords: ["swift", "ios", "programming"]),
            Article.testArticle(keywords: ["swift", "macos"]),
            Article.testArticle(keywords: ["python", "web"]),
            Article.testArticle(keywords: ["swift", "ios"]),
            Article.testArticle(keywords: ["javascript", "web"])
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        let searchEngine = SearchEngine(database: database)
        let popularKeywords = try await searchEngine.getPopularKeywords(limit: 5)
        
        // "swift" should be most popular (3 occurrences)
        XCTAssertTrue(popularKeywords.contains { $0.keyword == "swift" && $0.count >= 3 })
        
        // Verify sorting
        for i in 1..<popularKeywords.count {
            XCTAssertGreaterThanOrEqual(
                popularKeywords[i-1].count,
                popularKeywords[i].count,
                "Keywords should be sorted by frequency"
            )
        }
    }
}