import XCTest
@testable import PocketNext

class SimpleDatabaseTest: XCTestCase {
    
    func testDatabaseInitialization() async throws {
        // Test that using shared instance properly initializes
        try await DatabaseManager.shared.initialize()
        
        // This should not crash
        let articles = try await DatabaseManager.shared.fetchRecentArticles()
        XCTAssertNotNil(articles)
    }
}