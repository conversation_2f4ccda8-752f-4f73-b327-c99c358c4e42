import XCTest
@testable import PocketNext

class MinimalRuntimeTest: XCTestCase {
    
    func testDatabaseDoesNotCrashOnInitialization() async throws {
        // This verifies the database initialization fix
        let dbManager = DatabaseManager.shared
        
        // First initialization should succeed without "database locked" error
        do {
            try await dbManager.initialize()
            XCTAssertTrue(true, "Database initialized successfully")
        } catch {
            XCTFail("Database initialization failed: \(error)")
        }
        
        // Second initialization should be safe (no-op)
        do {
            try await dbManager.initialize()
            XCTAssertTrue(true, "Second initialization handled gracefully")
        } catch {
            XCTFail("Second initialization failed: \(error)")
        }
        
        // Basic operations should work
        do {
            let articles = try await dbManager.fetchRecentArticles()
            XCTAssertNotNil(articles, "Articles fetched successfully")
        } catch {
            XCTFail("Failed to fetch articles: \(error)")
        }
    }
}