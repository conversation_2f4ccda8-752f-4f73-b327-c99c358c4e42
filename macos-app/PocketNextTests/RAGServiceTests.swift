import XCTest
import GRDB
@testable import PocketNext

final class RAGServiceTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var ragService: RAGService!
    var dbQueue: DatabaseQueue!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Use shared database manager for testing
        databaseManager = DatabaseManager.shared
        try await databaseManager.initialize()
        
        await MainActor.run {
            ragService = RAGService(databaseManager: databaseManager)
        }
        
        // Wait for service to be ready
        let timeout = Date().addingTimeInterval(10)
        while await !ragService.isReady && Date() < timeout {
            try await Task.sleep(nanoseconds: 100_000_000)
        }
        
        let isReady = await ragService.isReady
        XCTAssertTrue(isReady, "RAG service should be ready")
    }
    
    override func tearDown() async throws {
        ragService = nil
        databaseManager = nil
        dbQueue = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Article Processing Tests
    
    func testProcessArticle() async throws {
        let article = Article(
            id: UUID(),
            url: "https://example.com/test-article",
            title: "Understanding Machine Learning",
            content: """
            Machine learning is a subset of artificial intelligence that enables systems to learn and improve from experience.
            Neural networks are computational models inspired by the human brain.
            Deep learning uses multiple layers of neural networks to process complex patterns.
            """,
            summary: "An introduction to machine learning concepts",
            keywords: ["machine learning", "AI", "neural networks"],
            author: "Test Author",
            publishDate: Date(),
            readingTime: 5,
            contentType: .article,
            capturedAt: Date()
        )
        
        try await databaseManager.save(article)
        try await ragService.processArticle(article)
        
        // Verify chunks were created
        let chunks = try await dbQueue.read { db in
            try DocumentChunk
                .filter(Column("articleId") == article.id)
                .fetchAll(db)
        }
        
        XCTAssertGreaterThan(chunks.count, 0)
        
        // Verify embeddings were created
        for chunk in chunks {
            let embedding = try await dbQueue.read { db in
                try ChunkEmbedding.fetchOne(db, key: chunk.id)
            }
            XCTAssertNotNil(embedding)
        }
    }
    
    func testProcessArticleIdempotency() async throws {
        let article = createTestArticle()
        try await databaseManager.save(article)
        
        // Process article first time
        try await ragService.processArticle(article)
        
        let firstChunkCount = try await dbQueue.read { db in
            try DocumentChunk
                .filter(Column("articleId") == article.id)
                .fetchCount(db)
        }
        
        // Process same article again
        try await ragService.processArticle(article)
        
        let secondChunkCount = try await dbQueue.read { db in
            try DocumentChunk
                .filter(Column("articleId") == article.id)
                .fetchCount(db)
        }
        
        // Should not create duplicate chunks
        XCTAssertEqual(firstChunkCount, secondChunkCount)
    }
    
    // MARK: - Query Tests
    
    func testQueryWithRelevantContent() async throws {
        // Setup test data
        let article = Article(
            id: UUID(),
            url: "https://example.com/swift",
            title: "Swift Programming Guide",
            content: """
            Swift is a powerful programming language for iOS development.
            It provides modern features like optionals and type safety.
            SwiftUI is the modern way to build user interfaces.
            """,
            summary: "Guide to Swift programming",
            keywords: ["swift", "ios", "programming"],
            author: "Apple",
            publishDate: Date(),
            readingTime: 10,
            contentType: .article,
            capturedAt: Date()
        )
        
        try await databaseManager.save(article)
        try await ragService.processArticle(article)
        
        // Query for relevant content
        let response = try await ragService.query("What is Swift programming?")
        
        XCTAssertFalse(response.requiresWebSearch)
        XCTAssertFalse(response.answer.isEmpty)
        XCTAssertGreaterThan(response.confidence, 0.5)
        XCTAssertFalse(response.sources.isEmpty)
    }
    
    func testQueryWithNoRelevantContent() async throws {
        // Setup with unrelated content
        let article = createTestArticle()
        try await databaseManager.save(article)
        try await ragService.processArticle(article)
        
        // Query for content not in knowledge base
        let response = try await ragService.query("What is quantum computing?")
        
        XCTAssertTrue(response.requiresWebSearch)
        XCTAssertEqual(response.confidence, 0.0)
        XCTAssertTrue(response.sources.isEmpty)
    }
    
    // MARK: - Conversation Tests
    
    func testConversationCreation() async throws {
        let conversation = try await ragService.createConversation(title: "Test Conversation")
        
        XCTAssertNotNil(conversation.id)
        XCTAssertEqual(conversation.title, "Test Conversation")
        
        // Verify saved to database
        let savedConversation = try await dbQueue.read { db in
            try ChatConversation.fetchOne(db, key: conversation.id)
        }
        
        XCTAssertNotNil(savedConversation)
    }
    
    func testConversationHistory() async throws {
        // Create conversation
        let conversation = try await ragService.createConversation()
        
        // Setup test article
        let article = createTestArticle()
        try await databaseManager.save(article)
        try await ragService.processArticle(article)
        
        // Make queries
        _ = try await ragService.query("First question", conversationId: conversation.id)
        _ = try await ragService.query("Second question", conversationId: conversation.id)
        
        // Load history
        let messages = try await ragService.loadConversationHistory(conversationId: conversation.id)
        
        XCTAssertEqual(messages.count, 4) // 2 user + 2 assistant messages
        XCTAssertEqual(messages[0].role, "user")
        XCTAssertEqual(messages[1].role, "assistant")
        XCTAssertEqual(messages[2].role, "user")
        XCTAssertEqual(messages[3].role, "assistant")
    }
    
    // MARK: - Source Citation Tests
    
    func testSourceCitation() async throws {
        let articles = [
            Article(
                id: UUID(),
                url: "https://example.com/article1",
                title: "Article 1",
                content: "This article discusses topic A in detail.",
                summary: "About topic A",
                keywords: ["topic", "A"],
                author: "Author 1",
                publishDate: Date(),
                readingTime: 5,
                contentType: .article,
                capturedAt: Date()
            ),
            Article(
                id: UUID(),
                url: "https://example.com/article2",
                title: "Article 2",
                content: "This article explores topic B thoroughly.",
                summary: "About topic B",
                keywords: ["topic", "B"],
                author: "Author 2",
                publishDate: Date(),
                readingTime: 5,
                contentType: .article,
                capturedAt: Date()
            )
        ]
        
        for article in articles {
            try await databaseManager.save(article)
            try await ragService.processArticle(article)
        }
        
        let response = try await ragService.query("Tell me about these topics")
        
        XCTAssertFalse(response.sources.isEmpty)
        
        for source in response.sources {
            XCTAssertTrue(articles.contains { $0.id == source.articleId })
            XCTAssertFalse(source.title.isEmpty)
            XCTAssertFalse(source.url.isEmpty)
            XCTAssertGreaterThan(source.score, 0)
        }
    }
    
    // MARK: - Performance Tests
    
    func testQueryPerformance() async throws {
        // Setup multiple articles
        for i in 0..<10 {
            let article = Article(
                id: UUID(),
                url: "https://example.com/article\(i)",
                title: "Article \(i)",
                content: String(repeating: "Content for article \(i). ", count: 50),
                summary: "Summary \(i)",
                keywords: ["keyword\(i)"],
                author: "Author \(i)",
                publishDate: Date(),
                readingTime: 5,
                contentType: .article,
                capturedAt: Date()
            )
            
            try await databaseManager.save(article)
            try await ragService.processArticle(article)
        }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        _ = try await ragService.query("Find relevant content")
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        print("Query completed in \(timeElapsed) seconds")
        XCTAssertLessThan(timeElapsed, 2.0, "Query should complete within 2 seconds")
    }
    
    func testBatchProcessingPerformance() async throws {
        let articles = (0..<20).map { i in
            Article(
                id: UUID(),
                url: "https://example.com/batch\(i)",
                title: "Batch Article \(i)",
                content: String(repeating: "Batch content \(i). ", count: 100),
                summary: "Batch summary \(i)",
                keywords: ["batch", "test"],
                author: "Batch Author",
                publishDate: Date(),
                readingTime: 5,
                contentType: .article,
                capturedAt: Date()
            )
        }
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        try await ragService.processAllArticles()
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        print("Processed \(articles.count) articles in \(timeElapsed) seconds")
        XCTAssertLessThan(timeElapsed, 30.0, "Batch processing should complete within 30 seconds")
    }
    
    // MARK: - Cache Tests
    
    func testResponseCaching() async throws {
        let article = createTestArticle()
        try await databaseManager.save(article)
        try await ragService.processArticle(article)
        
        let query = "Test query for caching"
        
        // First query
        let startTime1 = CFAbsoluteTimeGetCurrent()
        let response1 = try await ragService.query(query)
        let time1 = CFAbsoluteTimeGetCurrent() - startTime1
        
        // Second query (should be cached)
        let startTime2 = CFAbsoluteTimeGetCurrent()
        let response2 = try await ragService.query(query)
        let time2 = CFAbsoluteTimeGetCurrent() - startTime2
        
        // Cached query should be faster
        XCTAssertLessThan(time2, time1 * 0.5)
        XCTAssertEqual(response1.answer, response2.answer)
    }
    
    // MARK: - Helper Methods
    
    private func createTestArticle() -> Article {
        Article(
            id: UUID(),
            url: "https://example.com/test",
            title: "Test Article",
            content: "This is test content for unit testing purposes.",
            summary: "Test summary",
            keywords: ["test"],
            author: "Test Author",
            publishDate: Date(),
            readingTime: 1,
            contentType: .article,
            capturedAt: Date()
        )
    }
}

// MARK: - Integration Tests

final class RAGIntegrationTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var ragService: RAGService!
    var dbQueue: DatabaseQueue!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Setup with file-based database for integration testing
        let testDir = FileManager.default.temporaryDirectory
            .appendingPathComponent("RAGIntegrationTests", isDirectory: true)
        try FileManager.default.createDirectory(at: testDir, withIntermediateDirectories: true)
        
        let dbPath = testDir.appendingPathComponent("test.db").path
        dbQueue = try DatabaseQueue(path: dbPath)
        
        databaseManager = DatabaseManager()
        await databaseManager.setTestDatabase(dbQueue)
        try await databaseManager.initialize()
        
        await MainActor.run {
            ragService = RAGService(databaseManager: databaseManager)
        }
        
        // Wait for initialization
        let timeout = Date().addingTimeInterval(10)
        while await !ragService.isReady && Date() < timeout {
            try await Task.sleep(nanoseconds: 100_000_000)
        }
    }
    
    override func tearDown() async throws {
        // Cleanup
        if let dbPath = dbQueue?.path {
            try? FileManager.default.removeItem(atPath: dbPath)
        }
        
        ragService = nil
        databaseManager = nil
        dbQueue = nil
        
        try await super.tearDown()
    }
    
    func testEndToEndRAGFlow() async throws {
        // 1. Save multiple articles
        let articles = [
            Article(
                id: UUID(),
                url: "https://swift.org/documentation",
                title: "Swift Documentation",
                content: """
                Swift is a general-purpose programming language built using a modern approach to safety, performance, and software design patterns.
                The goal of the Swift project is to create the best available language for uses ranging from systems programming, to mobile and desktop apps, to scaling up to cloud services.
                """,
                summary: "Official Swift documentation",
                keywords: ["swift", "programming", "language"],
                author: "Swift.org",
                publishDate: Date(),
                readingTime: 15,
                contentType: .article,
                capturedAt: Date()
            ),
            Article(
                id: UUID(),
                url: "https://developer.apple.com/swiftui",
                title: "SwiftUI Overview",
                content: """
                SwiftUI helps you build great-looking apps across all Apple platforms with the power of Swift — and as little code as possible.
                With SwiftUI, you can bring even better experiences to all users, on any Apple device, using just one set of tools and APIs.
                """,
                summary: "Introduction to SwiftUI",
                keywords: ["swiftui", "apple", "ui", "framework"],
                author: "Apple Developer",
                publishDate: Date(),
                readingTime: 10,
                contentType: .article,
                capturedAt: Date()
            )
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // 2. Process all articles
        try await ragService.processAllArticles()
        
        // 3. Create a conversation
        let conversation = try await ragService.createConversation(title: "Learning Swift")
        
        // 4. Ask questions
        let queries = [
            "What is Swift programming language?",
            "How does SwiftUI help developers?",
            "What platforms does Swift support?"
        ]
        
        for query in queries {
            let response = try await ragService.query(query, conversationId: conversation.id)
            
            XCTAssertFalse(response.requiresWebSearch)
            XCTAssertFalse(response.answer.isEmpty)
            XCTAssertGreaterThan(response.confidence, 0.6)
            XCTAssertFalse(response.sources.isEmpty)
        }
        
        // 5. Verify conversation history
        let history = try await ragService.loadConversationHistory(conversationId: conversation.id)
        XCTAssertEqual(history.count, queries.count * 2) // User + assistant messages
    }
}