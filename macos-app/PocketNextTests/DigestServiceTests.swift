import XCTest
@testable import PocketNext

@MainActor
final class DigestServiceTests: XCTestCase {
    var digestService: DigestService!
    var database: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Use in-memory database for tests
        database = try await DatabaseManager(inMemory: true)
        try await database.initialize()
        
        // Create test articles
        try await createTestArticles()
        
        // Initialize digest service
        digestService = DigestService(database: database)
    }
    
    override func tearDown() async throws {
        digestService = nil
        database = nil
        try await super.tearDown()
    }
    
    // MARK: - Test Data Creation
    
    private func createTestArticles() async throws {
        let articles = [
            Article(
                id: UUID(),
                url: "https://example.com/swift-1",
                title: "Advanced Swift Techniques",
                content: "Long article about Swift...",
                summary: "Learn advanced Swift patterns",
                keywords: ["swift", "ios", "programming"],
                author: "Swift Expert",
                publishDate: Date().addingTimeInterval(-86400),
                readingTime: 15,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-3600),
                lastAccessedAt: nil,
                isArchived: false,
                syncStatus: .synced
            ),
            Article(
                id: UUID(),
                url: "https://example.com/quick-tip",
                title: "Quick iOS Tip",
                content: "Short tip about iOS...",
                summary: "A quick tip for iOS developers",
                keywords: ["ios", "tips"],
                author: "iOS Developer",
                publishDate: Date().addingTimeInterval(-172800),
                readingTime: 3,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-7200),
                lastAccessedAt: nil,
                isArchived: false,
                syncStatus: .synced
            ),
            Article(
                id: UUID(),
                url: "https://example.com/ai-article",
                title: "AI in Mobile Development",
                content: "Article about AI integration...",
                summary: "How to integrate AI in mobile apps",
                keywords: ["ai", "mobile", "machine learning"],
                author: "AI Researcher",
                publishDate: Date().addingTimeInterval(-259200),
                readingTime: 20,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-86400),
                lastAccessedAt: Date().addingTimeInterval(-3600), // Already read
                isArchived: false,
                syncStatus: .synced
            ),
            Article(
                id: UUID(),
                url: "https://example.com/archived",
                title: "Old Article",
                content: "This is archived...",
                summary: "An archived article",
                keywords: ["old"],
                author: "Someone",
                publishDate: Date().addingTimeInterval(-604800),
                readingTime: 10,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-518400),
                lastAccessedAt: nil,
                isArchived: true, // Archived
                syncStatus: .synced
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
    }
    
    // MARK: - Digest Generation Tests
    
    func testGenerateDailyDigest() async throws {
        let digest = try await digestService.generateDigest(type: .daily)
        
        XCTAssertEqual(digest.type, .daily)
        XCTAssertFalse(digest.topArticles.isEmpty, "Should have top articles")
        XCTAssertFalse(digest.quickReads.isEmpty, "Should have quick reads")
        
        // Verify no read or archived articles are included
        for article in digest.topArticles {
            XCTAssertNil(article.lastAccessedAt, "Should not include read articles")
            XCTAssertFalse(article.isArchived, "Should not include archived articles")
        }
        
        // Verify quick reads are actually quick
        for article in digest.quickReads {
            XCTAssertLessThanOrEqual(article.readingTime, 5, "Quick reads should be 5 minutes or less")
        }
    }
    
    func testGenerateWeeklyDigest() async throws {
        let digest = try await digestService.generateDigest(type: .weekly)
        
        XCTAssertEqual(digest.type, .weekly)
        XCTAssertNotNil(digest.timeframe)
        
        // Weekly timeframe should be 7 days
        let duration = digest.timeframe.duration
        XCTAssertEqual(Int(duration / 86400), 7, "Weekly digest should cover 7 days")
    }
    
    func testTopArticleSelection() async throws {
        let digest = try await digestService.generateDigest(type: .daily)
        
        // Should prioritize unread articles
        for article in digest.topArticles {
            XCTAssertNil(article.lastAccessedAt)
            XCTAssertFalse(article.isArchived)
        }
        
        // Should be sorted by score (newer articles should generally come first)
        if digest.topArticles.count > 1 {
            let firstArticle = digest.topArticles[0]
            let lastArticle = digest.topArticles[digest.topArticles.count - 1]
            
            // First article should be newer than last (in general)
            XCTAssertGreaterThan(firstArticle.capturedAt, lastArticle.capturedAt.addingTimeInterval(-86400))
        }
    }
    
    func testCategorization() async throws {
        let digest = try await digestService.generateDigest(type: .daily)
        
        XCTAssertFalse(digest.categories.isEmpty, "Should have categorized articles")
        
        // Each category should have articles
        for (category, articles) in digest.categories {
            XCTAssertFalse(articles.isEmpty)
            XCTAssertLessThanOrEqual(articles.count, 3, "Should limit articles per category")
            
            // All articles in category should have that keyword
            for article in articles {
                XCTAssertTrue(
                    article.keywords?.contains(category) ?? false,
                    "Article should contain category keyword"
                )
            }
        }
    }
    
    func testReadingStats() async throws {
        let digest = try await digestService.generateDigest(type: .daily)
        
        let stats = digest.stats
        XCTAssertGreaterThanOrEqual(stats.articlesRead, 0)
        XCTAssertGreaterThanOrEqual(stats.articlesSaved, 0)
        XCTAssertGreaterThanOrEqual(stats.totalReadingTime, 0)
        
        // Should have top categories
        XCTAssertFalse(stats.topCategories.isEmpty)
        
        // Categories should be sorted by count
        if stats.topCategories.count > 1 {
            for i in 0..<stats.topCategories.count - 1 {
                XCTAssertGreaterThanOrEqual(
                    stats.topCategories[i].count,
                    stats.topCategories[i + 1].count
                )
            }
        }
    }
    
    // MARK: - Preference Tests
    
    func testDigestPreferences() {
        let preferences = DigestPreferences.shared
        
        // Test default values
        XCTAssertNotNil(preferences.frequency)
        XCTAssertTrue(preferences.preferredHour >= 0 && preferences.preferredHour < 24)
        XCTAssertTrue(preferences.preferredWeekday >= 1 && preferences.preferredWeekday <= 7)
        
        // Test changing preferences
        preferences.isEnabled = true
        XCTAssertTrue(preferences.isEnabled)
        
        preferences.frequency = .weekly
        XCTAssertEqual(preferences.frequency, .weekly)
        
        preferences.preferredHour = 18
        XCTAssertEqual(preferences.preferredHour, 18)
    }
    
    // MARK: - Persistence Tests
    
    func testSaveAndLoadDigest() async throws {
        // Generate digest
        let digest = try await digestService.generateDigest(type: .daily)
        
        // Save should happen automatically, but let's verify
        let loadedDigests = try await digestService.fetchDigests(limit: 10)
        
        XCTAssertFalse(loadedDigests.isEmpty)
        
        // Most recent should be our digest
        if let mostRecent = loadedDigests.first {
            XCTAssertEqual(mostRecent.id, digest.id)
            XCTAssertEqual(mostRecent.type, digest.type)
            XCTAssertEqual(mostRecent.topArticles.count, digest.topArticles.count)
        }
    }
    
    func testFetchLatestDigest() async throws {
        // Generate multiple digests
        _ = try await digestService.generateDigest(type: .daily)
        try await Task.sleep(nanoseconds: 100_000_000) // 100ms
        let latestDigest = try await digestService.generateDigest(type: .weekly)
        
        // Fetch latest
        let fetched = try await database.fetchLatestDigest()
        
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.id, latestDigest.id)
        XCTAssertEqual(fetched?.type, .weekly)
    }
    
    // MARK: - Edge Cases
    
    func testEmptyDatabase() async throws {
        // Clear all articles
        for article in try await database.fetchRecentArticles(limit: 1000) {
            try await database.delete(article.id)
        }
        
        // Should still generate digest without crashing
        let digest = try await digestService.generateDigest(type: .daily)
        
        XCTAssertTrue(digest.topArticles.isEmpty)
        XCTAssertTrue(digest.quickReads.isEmpty)
        XCTAssertTrue(digest.categories.isEmpty)
    }
    
    func testAllArticlesRead() async throws {
        // Mark all articles as read
        for article in try await database.fetchRecentArticles(limit: 1000) {
            try await database.markAsRead(article.id)
        }
        
        // Should generate empty digest
        let digest = try await digestService.generateDigest(type: .daily)
        
        XCTAssertTrue(digest.topArticles.isEmpty)
        XCTAssertEqual(digest.stats.articlesRead, 4) // We created 4 articles
    }
    
    // MARK: - Performance Tests
    
    func testDigestGenerationPerformance() throws {
        measure {
            let expectation = self.expectation(description: "Digest generation")
            
            Task {
                _ = try await digestService.generateDigest(type: .daily)
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
}

// MARK: - Mock Notification Tests

final class DigestNotificationTests: XCTestCase {
    
    func testNotificationContent() async throws {
        let digest = ArticleDigest(
            id: UUID(),
            type: .daily,
            generatedAt: Date(),
            timeframe: DateInterval(start: Date().addingTimeInterval(-86400), end: Date()),
            topArticles: [
                Article(
                    id: UUID(),
                    url: "https://example.com",
                    title: "Test Article",
                    content: "Content",
                    summary: "Test summary",
                    keywords: ["test"],
                    author: "Test Author",
                    publishDate: Date(),
                    readingTime: 10,
                    contentType: .article,
                    capturedAt: Date(),
                    lastAccessedAt: nil,
                    isArchived: false,
                    syncStatus: .synced
                )
            ],
            quickReads: [],
            categories: [:],
            stats: ReadingStats(
                articlesRead: 5,
                articlesSaved: 10,
                totalReadingTime: 50,
                averageDailyTime: 7,
                topCategories: [ReadingStats.CategoryCount(category: "swift", count: 3)]
            )
        )
        
        let database = DatabaseManager.shared
        try await database.initialize()
        
        let service = await DigestService(database: database)
        
        // We can't actually test notification delivery without permission,
        // but we can verify the service handles it gracefully
        await service.deliverDigest(digest)
        
        // If we get here without crashing, the test passes
        XCTAssertTrue(true)
    }
}