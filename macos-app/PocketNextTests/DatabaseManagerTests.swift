import XCTest
@testable import PocketNext

@MainActor
final class DatabaseManagerTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var mockDatabase: MockDatabase!
    
    override func setUp() async throws {
        try await super.setUp()
        // Create in-memory database for testing
        databaseManager = try await DatabaseManager(inMemory: true)
        mockDatabase = MockDatabase()
    }
    
    override func tearDown() async throws {
        databaseManager = nil
        mockDatabase = nil
        try await super.tearDown()
    }
    
    // MARK: - Database Initialization Tests
    
    func testDatabaseNotInitializedThrows() async throws {
        // Given - create a new instance without initialization
        let uninitializedDB = DatabaseManager()
        
        // When & Then - all operations should throw
        do {
            _ = try await uninitializedDB.fetchRecentArticles()
            XCTFail("Should throw when database not initialized")
        } catch {
            // Expected error
        }
    }
    
    func testDatabaseInitializationCreatesTablesAndIndexes() async throws {
        // Given
        let testDB = DatabaseManager()
        
        // When
        try await testDB.initialize()
        
        // Then - verify tables exist
        let tables = try await testDB.dbQueue!.read { db in
            try String.fetchAll(db, sql: "SELECT name FROM sqlite_master WHERE type='table'")
        }
        
        XCTAssertTrue(tables.contains("articles"))
        XCTAssertTrue(tables.contains("digests"))
        XCTAssertTrue(tables.contains("embeddings"))
        XCTAssertTrue(tables.contains("articles_fts"))
        XCTAssertTrue(tables.contains("chat_conversations"))
        XCTAssertTrue(tables.contains("chat_messages"))
    }
    
    // MARK: - Article CRUD Tests
    
    func testSaveArticle() async throws {
        let article = Article.testArticle(
            title: "Test Article",
            content: "Test content",
            summary: "Test summary"
        )
        
        try await databaseManager.save(article)
        
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.title, article.title)
        XCTAssertEqual(fetched?.content, article.content)
    }
    
    func testFetchRecentArticles() async throws {
        // Save multiple articles
        let articles = (1...5).map { i in
            Article.testArticle(
                title: "Article \(i)",
                capturedAt: Date().addingTimeInterval(TimeInterval(-i * 3600))
            )
        }
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let recent = try await databaseManager.fetchRecentArticles(limit: 3)
        XCTAssertEqual(recent.count, 3)
        // Should be ordered by most recent first
        XCTAssertEqual(recent[0].title, "Article 1")
    }
    
    func testFetchRecentArticlesExcludesArchived() async throws {
        // Given
        let articles = [
            Article.testArticle(title: "Active 1", isArchived: false),
            Article.testArticle(title: "Archived", isArchived: true),
            Article.testArticle(title: "Active 2", isArchived: false)
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // When
        let recent = try await databaseManager.fetchRecentArticles()
        
        // Then
        XCTAssertEqual(recent.count, 2)
        XCTAssertFalse(recent.contains { $0.isArchived })
    }
    
    func testUpdateArticle() async throws {
        let article = Article.testArticle(title: "Original Title")
        try await databaseManager.save(article)
        
        // Update the article
        var updated = article
        updated.title = "Updated Title"
        updated.isRead = true
        
        try await databaseManager.update(updated)
        
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertEqual(fetched?.title, "Updated Title")
        XCTAssertTrue(fetched?.isRead ?? false)
    }
    
    func testDeleteArticle() async throws {
        let article = Article.testArticle()
        try await databaseManager.save(article)
        
        // Verify it exists
        let exists = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(exists)
        
        // Delete it
        try await databaseManager.delete(article.id)
        
        // Verify it's gone
        let deleted = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNil(deleted)
    }
    
    // MARK: - Search Tests
    
    func testSearchArticles() async throws {
        // Save articles with different content
        let articles = [
            Article.testArticle(title: "Swift Programming", content: "Learn Swift"),
            Article.testArticle(title: "Python Guide", content: "Learn Python"),
            Article.testArticle(title: "Swift Concurrency", content: "Async await in Swift")
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Search for "Swift"
        let results = try await databaseManager.searchArticles(query: "Swift")
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.allSatisfy { $0.title.contains("Swift") || $0.content.contains("Swift") })
    }
    
    func testSearchByKeywords() async throws {
        let articles = [
            Article.testArticle(keywords: ["swift", "ios"]),
            Article.testArticle(keywords: ["python", "web"]),
            Article.testArticle(keywords: ["swift", "macos"])
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let results = try await databaseManager.fetchArticles(withKeyword: "swift")
        XCTAssertEqual(results.count, 2)
    }
    
    // MARK: - Filter Tests
    
    func testFetchUnreadArticles() async throws {
        let articles = [
            Article.testArticle(title: "Read 1"),
            Article.testArticle(title: "Unread 1"),
            Article.testArticle(title: "Read 2"),
            Article.testArticle(title: "Unread 2")
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let unread = try await databaseManager.fetchUnreadArticles()
        XCTAssertEqual(unread.count, 2)
        XCTAssertTrue(unread.allSatisfy { !$0.isRead })
    }
    
    func testFetchArchivedArticles() async throws {
        let articles = [
            Article.testArticle(title: "Active 1", isArchived: false),
            Article.testArticle(title: "Archived 1", isArchived: true),
            Article.testArticle(title: "Archived 2", isArchived: true)
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let archived = try await databaseManager.fetchArchivedArticles()
        XCTAssertEqual(archived.count, 2)
        XCTAssertTrue(archived.allSatisfy { $0.isArchived })
    }
    
    // MARK: - Statistics Tests
    
    func testFetchStatistics() async throws {
        // Create articles with different states
        let articles = [
            Article.testArticle(isArchived: false),
            Article.testArticle(isArchived: false),
            Article.testArticle(isArchived: false),
            Article.testArticle(isArchived: true),
            Article.testArticle(isArchived: true)
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let stats = try await databaseManager.fetchStatistics()
        XCTAssertEqual(stats.totalArticles, 5)
        XCTAssertEqual(stats.unreadArticles, 2)
        XCTAssertEqual(stats.archivedArticles, 2)
    }
    
    // MARK: - Chat Tests
    
    func testSaveChatConversation() async throws {
        let conversation = ChatConversation(
            id: UUID(),
            title: "Test Chat",
            startedAt: Date(),
            lastMessageAt: Date(),
            messageCount: 1,
            relatedArticleIDs: [],
            syncStatus: .pending
        )
        
        try await databaseManager.save(conversation)
        
        let fetched = try await databaseManager.fetchConversation(id: conversation.id)
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.title, "Test Chat")
    }
    
    func testSaveChatMessage() async throws {
        // Create conversation first
        let conversation = ChatConversation(
            id: UUID(),
            title: "Test Chat",
            startedAt: Date(),
            lastMessageAt: Date(),
            messageCount: 0,
            relatedArticleIDs: [],
            syncStatus: .pending
        )
        try await databaseManager.save(conversation)
        
        // Add message
        let message = ChatMessage(
            id: UUID(),
            conversationID: conversation.id,
            role: .user,
            content: "Test message",
            timestamp: Date(),
            relatedArticleIDs: [],
            syncStatus: .pending
        )
        
        try await databaseManager.save(message)
        
        let messages = try await databaseManager.fetchMessages(for: conversation.id)
        XCTAssertEqual(messages.count, 1)
        XCTAssertEqual(messages[0].content, "Test message")
    }
    
    // MARK: - Digest Tests
    
    func testSaveDigest() async throws {
        let digest = ArticleDigest(
            id: UUID(),
            generatedAt: Date(),
            type: .daily,
            articleIDs: [UUID(), UUID()],
            summary: "Daily digest summary",
            keyInsights: ["Insight 1", "Insight 2"],
            syncStatus: .synced
        )
        
        try await databaseManager.save(digest)
        
        let fetched = try await databaseManager.fetchDigest(id: digest.id)
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.type, .daily)
        XCTAssertEqual(fetched?.articleIDs.count, 2)
    }
    
    func testFetchLatestDigest() async throws {
        // Create multiple digests
        let digests = [
            ArticleDigest(
                id: UUID(),
                generatedAt: Date().addingTimeInterval(-86400), // Yesterday
                type: .daily,
                articleIDs: [],
                summary: "Yesterday's digest",
                keyInsights: [],
                syncStatus: .synced
            ),
            ArticleDigest(
                id: UUID(),
                generatedAt: Date(), // Today
                type: .daily,
                articleIDs: [],
                summary: "Today's digest",
                keyInsights: [],
                syncStatus: .synced
            )
        ]
        
        for digest in digests {
            try await databaseManager.save(digest)
        }
        
        let latest = try await databaseManager.fetchLatestDigest(type: .daily)
        XCTAssertNotNil(latest)
        XCTAssertEqual(latest?.summary, "Today's digest")
    }
    
    // MARK: - Batch Operations Tests
    
    func testBatchSaveArticles() async throws {
        let articles = (1...10).map { i in
            Article.testArticle(title: "Article \(i)")
        }
        
        try await databaseManager.batchSave(articles)
        
        let fetched = try await databaseManager.fetchRecentArticles(limit: 20)
        XCTAssertEqual(fetched.count, 10)
    }
    
    func testMarkMultipleAsRead() async throws {
        let articles = (1...5).map { i in
            Article.testArticle(title: "Article \(i)")
        }
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        let ids = articles.map { $0.id }
        try await databaseManager.markAsRead(ids)
        
        let fetched = try await databaseManager.fetchUnreadArticles()
        XCTAssertEqual(fetched.count, 0)
    }
    
    // MARK: - Concurrency Tests
    
    func testConcurrentReads() async throws {
        // Given - populate with test data
        let articles = (1...50).map { i in
            Article.testArticle(title: "Article \(i)")
        }
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // When - perform multiple concurrent reads
        let results = try await withThrowingTaskGroup(of: [Article].self) { group in
            for _ in 0..<10 {
                group.addTask {
                    try await self.databaseManager.fetchRecentArticles()
                }
            }
            
            var allResults: [[Article]] = []
            for try await result in group {
                allResults.append(result)
            }
            return allResults
        }
        
        // Then - all reads should succeed
        XCTAssertEqual(results.count, 10)
        for result in results {
            XCTAssertEqual(result.count, 50)
        }
    }
    
    func testConcurrentWrites() async throws {
        // When - perform concurrent writes
        try await withThrowingTaskGroup(of: Void.self) { group in
            for i in 0..<20 {
                group.addTask {
                    let article = Article.testArticle(title: "Concurrent \(i)")
                    try await self.databaseManager.save(article)
                }
            }
        }
        
        // Then - all writes should succeed
        let articles = try await databaseManager.fetchRecentArticles(limit: 50)
        let concurrentArticles = articles.filter { $0.title.contains("Concurrent") }
        XCTAssertEqual(concurrentArticles.count, 20)
    }
    
    func testRaceConditionProtection() async throws {
        // Given
        let article = Article.testArticle(title: "Test Article")
        try await databaseManager.save(article)
        
        // When - concurrent read and update
        try await withThrowingTaskGroup(of: Void.self) { group in
            // Reader
            group.addTask {
                for _ in 0..<10 {
                    _ = try await self.databaseManager.fetchArticle(id: article.id)
                }
            }
            
            // Updater
            group.addTask {
                for i in 0..<10 {
                    var updated = article
                    updated.title = "Updated \(i)"
                    try await self.databaseManager.update(updated)
                }
            }
        }
        
        // Then - final state should be consistent
        let final = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(final)
        XCTAssertTrue(final!.title.contains("Updated"))
    }
    
    // MARK: - Error Handling Tests
    
    func testFetchNonExistentArticle() async throws {
        let result = try await databaseManager.fetchArticle(id: UUID())
        XCTAssertNil(result)
    }
    
    func testDuplicateArticleSave() async throws {
        let article = Article.testArticle()
        
        try await databaseManager.save(article)
        
        // Saving again should update, not create duplicate
        try await databaseManager.save(article)
        
        let all = try await databaseManager.fetchRecentArticles(limit: 10)
        let matching = all.filter { $0.id == article.id }
        XCTAssertEqual(matching.count, 1)
    }
    
    // MARK: - Edge Cases Tests
    
    func testSaveArticleWithSpecialCharacters() async throws {
        // Given
        let article = Article.testArticle(
            title: "Test with 'quotes' and \"double quotes\"",
            content: "Content with\nnewlines\tand\ttabs"
        )
        
        // When
        try await databaseManager.save(article)
        
        // Then
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertEqual(fetched?.title, article.title)
        XCTAssertEqual(fetched?.content, article.content)
    }
    
    func testLargeContentHandling() async throws {
        // Given - article with very large content
        let largeContent = String(repeating: "Lorem ipsum ", count: 10000)
        let article = Article.testArticle(
            title: "Large Article",
            content: largeContent
        )
        
        // When
        try await databaseManager.save(article)
        
        // Then
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertEqual(fetched?.content.count, largeContent.count)
    }
    
    func testEmptySearchQuery() async throws {
        // Given
        let articles = (1...5).map { Article.testArticle(title: "Article \($0)") }
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // When
        let results = try await databaseManager.searchArticles(query: "")
        
        // Then - should return all articles
        XCTAssertEqual(results.count, 5)
    }
    
    // MARK: - Mock Database Tests
    
    func testMockDatabaseOperations() async throws {
        // Test mock database functionality
        let article = Article.testArticle()
        
        try await mockDatabase.save(article)
        XCTAssertEqual(mockDatabase.articles.count, 1)
        
        let fetched = try await mockDatabase.fetchArticle(id: article.id)
        XCTAssertNotNil(fetched)
        
        try await mockDatabase.delete(article.id)
        XCTAssertEqual(mockDatabase.articles.count, 0)
    }
    
    func testMockDatabaseError() async throws {
        mockDatabase.shouldFail = true
        
        do {
            try await mockDatabase.save(Article.testArticle())
            XCTFail("Should throw error")
        } catch {
            XCTAssertEqual(error as? DatabaseError, .saveFailed)
        }
    }
}

// MARK: - Performance Tests

final class DatabasePerformanceTests: XCTestCase {
    var databaseManager: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        databaseManager = try await DatabaseManager(inMemory: true)
    }
    
    func testBulkInsertPerformance() async throws {
        let articles = (1...1000).map { i in
            Article.testArticle(
                title: "Article \(i)",
                content: String(repeating: "Content ", count: 100)
            )
        }
        
        measure {
            let expectation = XCTestExpectation(description: "Bulk insert")
            
            Task {
                try await databaseManager.batchSave(articles)
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    func testSearchPerformance() async throws {
        // Populate database
        let articles = (1...100).map { i in
            Article.testArticle(
                title: "Article \(i)",
                content: "Swift programming article number \(i)"
            )
        }
        
        try await databaseManager.batchSave(articles)
        
        measure {
            let expectation = XCTestExpectation(description: "Search")
            
            Task {
                _ = try await databaseManager.searchArticles(query: "Swift")
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
}