import XCTest
@testable import PocketNext

@MainActor
final class LLMServiceTests: XCTestCase {
    var llmService: LLMService!
    
    override func setUp() async throws {
        try await super.setUp()
        llmService = LLMService()
        
        // Wait for model to initialize
        let timeout = Date().addingTimeInterval(5)
        while !llmService.isModelLoaded && Date() < timeout {
            try await Task.sleep(nanoseconds: 100_000_000)
        }
    }
    
    override func tearDown() async throws {
        llmService = nil
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testServiceInitialization() {
        XCTAssertNotNil(llmService)
        // Service should load in either local or API mode
        XCTAssertTrue(llmService.isModelLoaded || llmService.loadingError != nil)
    }
    
    func testModelInfo() {
        let info = llmService.currentModelInfo()
        XCTAssertNotNil(info)
        XCTAssertFalse(info.modelName.isEmpty)
    }
    
    // MARK: - Prompt Building Tests
    
    func testPromptBuilding() async throws {
        // Skip if model not loaded
        guard llmService.isModelLoaded else {
            throw XCTSkip("LLM model not loaded")
        }
        
        let context = "Swift is a programming language developed by Apple."
        let query = "What is Swift?"
        
        let response = try await llmService.generateResponse(
            prompt: query,
            context: context,
            conversationHistory: []
        )
        
        XCTAssertFalse(response.text.isEmpty)
        XCTAssertGreaterThan(response.tokensUsed, 0)
        XCTAssertFalse(response.modelUsed.isEmpty)
        XCTAssertGreaterThan(response.inferenceTime, 0)
    }
    
    func testPromptWithHistory() async throws {
        guard llmService.isModelLoaded else {
            throw XCTSkip("LLM model not loaded")
        }
        
        let context = "Machine learning is a subset of artificial intelligence."
        let history = [
            Message(role: "user", content: "What is AI?", timestamp: Date()),
            Message(role: "assistant", content: "AI is artificial intelligence.", timestamp: Date())
        ]
        let query = "Tell me more about machine learning"
        
        let response = try await llmService.generateResponse(
            prompt: query,
            context: context,
            conversationHistory: history
        )
        
        XCTAssertFalse(response.text.isEmpty)
    }
    
    // MARK: - Response Generation Tests
    
    func testMaxTokensLimit() async throws {
        guard llmService.isModelLoaded else {
            throw XCTSkip("LLM model not loaded")
        }
        
        let context = String(repeating: "Test content. ", count: 100)
        let query = "Summarize this"
        
        let response = try await llmService.generateResponse(
            prompt: query,
            context: context,
            conversationHistory: [],
            maxTokens: 50
        )
        
        XCTAssertFalse(response.text.isEmpty)
        // In real implementation, would verify token count
    }
    
    // MARK: - Mode Switching Tests
    
    func testModeSwitching() {
        let originalInfo = llmService.currentModelInfo()
        
        llmService.switchMode(.api)
        let apiInfo = llmService.currentModelInfo()
        XCTAssertEqual(apiInfo.mode, .api)
        
        llmService.switchMode(.local)
        let localInfo = llmService.currentModelInfo()
        XCTAssertEqual(localInfo.mode, .local)
        
        llmService.switchMode(.hybrid)
        let hybridInfo = llmService.currentModelInfo()
        XCTAssertEqual(hybridInfo.mode, .hybrid)
    }
    
    // MARK: - Error Handling Tests
    
    func testEmptyPrompt() async throws {
        guard llmService.isModelLoaded else {
            throw XCTSkip("LLM model not loaded")
        }
        
        let response = try await llmService.generateResponse(
            prompt: "",
            context: "Some context",
            conversationHistory: []
        )
        
        // Should handle empty prompt gracefully
        XCTAssertFalse(response.text.isEmpty)
    }
    
    func testVeryLongContext() async throws {
        guard llmService.isModelLoaded else {
            throw XCTSkip("LLM model not loaded")
        }
        
        let longContext = String(repeating: "Long context text. ", count: 1000)
        let query = "What is this about?"
        
        // Should handle long context without crashing
        let response = try await llmService.generateResponse(
            prompt: query,
            context: longContext,
            conversationHistory: []
        )
        
        XCTAssertFalse(response.text.isEmpty)
    }
    
    // MARK: - Performance Tests
    
    func testResponseGenerationPerformance() async throws {
        guard llmService.isModelLoaded else {
            throw XCTSkip("LLM model not loaded")
        }
        
        let context = "Performance test context"
        let query = "Test query"
        
        let startTime = Date()
        
        _ = try await llmService.generateResponse(
            prompt: query,
            context: context,
            conversationHistory: []
        )
        
        let elapsed = Date().timeIntervalSince(startTime)
        
        print("Response generated in \(elapsed) seconds")
        // Local inference should be reasonably fast
        XCTAssertLessThan(elapsed, 10.0, "Response generation took too long")
    }
}

// MARK: - Configuration Service Tests

final class ConfigurationServiceTests: XCTestCase {
    var configService: ConfigurationService!
    
    override func setUp() async throws {
        try await super.setUp()
        await MainActor.run {
            configService = ConfigurationService.shared
        }
    }
    
    // MARK: - API Key Tests
    
    func testAPIKeyStorage() async throws {
        await MainActor.run {
            // Test setting API key
            let testKey = "test-api-key-12345"
            
            do {
                try configService.setOpenAIKey(testKey)
                XCTAssertTrue(configService.hasOpenAIKey)
                
                // Verify retrieval
                let retrievedKey = configService.getOpenAIKey()
                XCTAssertEqual(retrievedKey, testKey)
                
                // Clean up
                configService.removeOpenAIKey()
                XCTAssertFalse(configService.hasOpenAIKey)
                XCTAssertNil(configService.getOpenAIKey())
            } catch {
                XCTFail("Failed to manage API key: \(error)")
            }
        }
    }
    
    // MARK: - Preference Tests
    
    func testModelPreferences() async {
        await MainActor.run {
            let originalPref = configService.preferredModel
            
            // Test each preference
            for preference in ConfigurationService.ModelPreference.allCases {
                configService.updatePreferredModel(preference)
                XCTAssertEqual(configService.preferredModel, preference)
            }
            
            // Restore original
            configService.updatePreferredModel(originalPref)
        }
    }
    
    func testTemperatureSettings() async {
        await MainActor.run {
            let originalTemp = configService.temperature
            
            // Test valid range
            configService.updateTemperature(0.5)
            XCTAssertEqual(configService.temperature, 0.5)
            
            configService.updateTemperature(1.5)
            XCTAssertEqual(configService.temperature, 1.5)
            
            // Test clamping
            configService.updateTemperature(-1)
            XCTAssertEqual(configService.temperature, 0)
            
            configService.updateTemperature(3)
            XCTAssertEqual(configService.temperature, 2)
            
            // Restore
            configService.updateTemperature(originalTemp)
        }
    }
    
    func testContextSizeSettings() async {
        await MainActor.run {
            let originalSize = configService.maxContextSize
            
            configService.updateMaxContextSize(2048)
            XCTAssertEqual(configService.maxContextSize, 2048)
            
            configService.updateMaxContextSize(8192)
            XCTAssertEqual(configService.maxContextSize, 8192)
            
            // Restore
            configService.updateMaxContextSize(originalSize)
        }
    }
    
    func testStreamingSettings() async {
        await MainActor.run {
            let originalStreaming = configService.enableStreaming
            
            configService.updateStreaming(true)
            XCTAssertTrue(configService.enableStreaming)
            
            configService.updateStreaming(false)
            XCTAssertFalse(configService.enableStreaming)
            
            // Restore
            configService.updateStreaming(originalStreaming)
        }
    }
    
    // MARK: - Model Management Tests
    
    func testAvailableModels() async {
        await MainActor.run {
            let models = configService.availableLocalModels()
            
            XCTAssertFalse(models.isEmpty)
            
            for model in models {
                XCTAssertFalse(model.id.isEmpty)
                XCTAssertFalse(model.name.isEmpty)
                XCTAssertFalse(model.size.isEmpty)
                XCTAssertFalse(model.description.isEmpty)
                XCTAssertNotNil(model.downloadURL)
            }
        }
    }
}