import XCTest
@testable import PocketNext

@MainActor
final class ArticleCaptureAndParseTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    var llmService: LLMService!
    var contentProcessor: ContentProcessor!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize services
        database = DatabaseManager.shared
        try await database.initialize()
        
        appState = AppState()
        llmService = LLMService(database: database)
        contentProcessor = ContentProcessor()
    }
    
    override func tearDown() async throws {
        appState = nil
        database = nil
        llmService = nil
        contentProcessor = nil
        try await super.tearDown()
    }
    
    // MARK: - Article Capture Tests
    
    func testCaptureArticleFromURL() async throws {
        // Test data
        let testURL = "https://example.com/article"
        let testHTML = """
        <html>
        <head><title>Understanding Swift Concurrency</title></head>
        <body>
            <article>
                <h1>Understanding Swift Concurrency</h1>
                <p>By <PERSON></p>
                <p>Published: 2024-01-15</p>
                <div class="content">
                    <p><PERSON>'s new concurrency model introduces async/await syntax for handling asynchronous operations. This makes code more readable and maintainable.</p>
                    <p>The key concepts include tasks, actors, and structured concurrency. Tasks represent units of asynchronous work.</p>
                    <p>Actors provide thread-safe access to mutable state, preventing data races in concurrent code.</p>
                </div>
            </article>
        </body>
        </html>
        """
        
        // Process the content
        let processedContent = await contentProcessor.processHTMLContent(
            html: testHTML,
            url: testURL,
            contentType: .article
        )
        
        XCTAssertNotNil(processedContent)
        XCTAssertEqual(processedContent?.title, "Understanding Swift Concurrency")
        XCTAssertTrue(processedContent?.content.contains("async/await") ?? false)
        XCTAssertNotNil(processedContent?.author)
        
        // Generate summary using LLM
        if let content = processedContent?.content {
            let summary = try await llmService.generateSummary(
                content: content,
                title: processedContent?.title ?? ""
            )
            
            XCTAssertFalse(summary.text.isEmpty)
            XCTAssertLessThan(summary.text.count, 500) // Should be concise
        }
        
        // Save to database
        let article = Article(
            id: UUID(),
            url: testURL,
            title: processedContent?.title ?? "Untitled",
            content: processedContent?.content ?? "",
            summary: "Test summary",
            keywords: ["swift", "concurrency", "async"],
            author: processedContent?.author,
            publishDate: Date(),
            readingTime: 5,
            contentType: .article,
            capturedAt: Date()
        )
        
        try await database.save(article)
        
        // Verify saved
        let saved = try await database.fetchArticle(url: testURL)
        XCTAssertNotNil(saved)
        XCTAssertEqual(saved?.title, article.title)
    }
    
    func testCaptureTwitterThread() async throws {
        let tweetHTML = """
        <div class="tweet">
            <div class="author">@swiftlang</div>
            <div class="content">1/ Excited to announce Swift 6.0! This release brings major improvements to concurrency safety.</div>
            <div class="timestamp">2024-01-15</div>
        </div>
        <div class="tweet">
            <div class="author">@swiftlang</div>
            <div class="content">2/ The new @isolated parameters allow you to specify actor isolation requirements at the function level.</div>
        </div>
        """
        
        let processed = await contentProcessor.processHTMLContent(
            html: tweetHTML,
            url: "https://twitter.com/swiftlang/status/123",
            contentType: .tweet
        )
        
        XCTAssertNotNil(processed)
        XCTAssertTrue(processed?.content.contains("Swift 6.0") ?? false)
        XCTAssertEqual(processed?.contentType, .tweet)
    }
    
    // MARK: - LLM Processing Tests
    
    func testGenerateArticleSummary() async throws {
        let longContent = """
        Machine learning has revolutionized how we process and understand data. Deep learning, a subset of machine learning, uses neural networks with multiple layers to progressively extract higher-level features from raw input.
        
        Convolutional Neural Networks (CNNs) are particularly effective for image recognition tasks. They use convolution operations to detect patterns in images, from simple edges to complex objects.
        
        Transformers have become the dominant architecture for natural language processing. Models like GPT and BERT have achieved remarkable results in text generation, translation, and understanding.
        
        The future of AI lies in multimodal models that can process and generate content across different modalities - text, images, audio, and video.
        """
        
        let summary = try await llmService.generateSummary(
            content: longContent,
            title: "Introduction to Deep Learning"
        )
        
        XCTAssertFalse(summary.text.isEmpty)
        XCTAssertLessThan(summary.text.count, longContent.count / 2)
        XCTAssertTrue(summary.text.lowercased().contains("learning") || summary.text.lowercased().contains("ai"))
    }
    
    func testGenerateKeywords() async throws {
        let content = "SwiftUI provides a declarative syntax for building user interfaces across all Apple platforms."
        
        let keywords = try await llmService.generateKeywords(
            content: content,
            title: "SwiftUI Basics"
        )
        
        XCTAssertFalse(keywords.isEmpty)
        XCTAssertTrue(keywords.contains { $0.lowercased().contains("swift") })
        XCTAssertLessThanOrEqual(keywords.count, 10)
    }
    
    // MARK: - Search and Retrieval Tests
    
    func testSemanticSearch() async throws {
        // Save test articles
        let articles = [
            Article.testArticle(
                title: "Introduction to Machine Learning",
                content: "Machine learning is a subset of AI that enables systems to learn from data.",
                keywords: ["machine learning", "AI", "data science"]
            ),
            Article.testArticle(
                title: "Swift Programming Guide",
                content: "Swift is a modern programming language developed by Apple.",
                keywords: ["swift", "programming", "ios"]
            ),
            Article.testArticle(
                title: "Deep Learning with PyTorch",
                content: "PyTorch is a popular framework for deep learning research.",
                keywords: ["deep learning", "pytorch", "AI"]
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Search for AI-related content
        let searchEngine = SearchEngine(database: database)
        let results = try await searchEngine.search("artificial intelligence")
        
        XCTAssertGreaterThan(results.count, 0)
        // Should prioritize ML and DL articles
        XCTAssertTrue(results.first?.article.title.contains("Learning") ?? false)
    }
    
    // MARK: - Chat RAG Tests
    
    func testChatWithRAG() async throws {
        // Save some articles first
        let articles = [
            Article.testArticle(
                title: "SwiftUI Layout System",
                content: "SwiftUI uses a flexible box layout system. Views are laid out using HStack, VStack, and ZStack.",
                keywords: ["swiftui", "layout", "ios"]
            ),
            Article.testArticle(
                title: "SwiftUI Animation",
                content: "Animations in SwiftUI are created using the .animation modifier or withAnimation blocks.",
                keywords: ["swiftui", "animation", "ios"]
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Initialize RAG service
        let ragService = RAGService(databaseManager: database)
        
        // Ask a question
        let response = try await ragService.generateResponse(
            query: "How do I create layouts in SwiftUI?",
            conversationId: nil
        )
        
        XCTAssertFalse(response.answer.isEmpty)
        XCTAssertTrue(response.answer.lowercased().contains("stack") || response.answer.lowercased().contains("layout"))
        XCTAssertFalse(response.sources.isEmpty)
    }
    
    // MARK: - Digest Generation Tests
    
    func testGenerateDigest() async throws {
        // Save articles from today
        let today = Date()
        let articles = [
            Article.testArticle(
                title: "Morning News: Tech Updates",
                content: "Apple announces new features...",
                capturedAt: today.addingTimeInterval(-3600 * 8) // 8 hours ago
            ),
            Article.testArticle(
                title: "Afternoon Read: Swift Tips",
                content: "Advanced Swift techniques...",
                capturedAt: today.addingTimeInterval(-3600 * 4) // 4 hours ago
            ),
            Article.testArticle(
                title: "Evening Article: AI Progress",
                content: "Recent developments in AI...",
                capturedAt: today.addingTimeInterval(-3600) // 1 hour ago
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
        
        // Generate digest
        let digestService = DigestService(database: database)
        let digest = try await digestService.generateDailyDigest()
        
        XCTAssertNotNil(digest)
        XCTAssertEqual(digest?.articles.count, 3)
        XCTAssertNotNil(digest?.summary)
        XCTAssertFalse(digest?.keyTopics.isEmpty ?? true)
    }
    
    // MARK: - Native Messaging Tests
    
    func testNativeMessagingIntegration() async throws {
        let messaging = NativeMessaging.shared
        
        // Test message handling
        let testMessage: [String: Any] = [
            "type": "save",
            "data": [
                "url": "https://example.com/test",
                "htmlContent": "<h1>Test Article</h1><p>Content</p>",
                "metadata": [
                    "title": "Test Article",
                    "type": "article"
                ]
            ]
        ]
        
        let response = await messaging.handleMessage(testMessage)
        XCTAssertNotNil(response["success"])
        
        // Verify article was saved
        let saved = try await database.fetchArticle(url: "https://example.com/test")
        XCTAssertNotNil(saved)
    }
    
    // MARK: - Performance Tests
    
    func testBulkArticleProcessing() async throws {
        self.continueAfterFailure = false
        
        let startTime = Date()
        
        // Process 50 articles
        for i in 1...50 {
            let article = Article.testArticle(
                title: "Article \(i)",
                content: "This is test content for article number \(i). It contains various information about topic \(i).",
                url: "https://example.com/article-\(i)"
            )
            
            try await database.save(article)
        }
        
        let duration = Date().timeIntervalSince(startTime)
        
        // Should process 50 articles in under 5 seconds
        XCTAssertLessThan(duration, 5.0)
        
        // Verify all saved
        let allArticles = try await database.fetchRecentArticles(limit: 100)
        XCTAssertGreaterThanOrEqual(allArticles.count, 50)
    }
    
    // MARK: - Error Handling Tests
    
    func testHandleMalformedHTML() async throws {
        let badHTML = "<div>Unclosed tag <p>Missing closing"
        
        let processed = await contentProcessor.processHTMLContent(
            html: badHTML,
            url: "https://example.com/bad",
            contentType: .article
        )
        
        // Should handle gracefully
        XCTAssertNotNil(processed)
        XCTAssertFalse(processed?.content.isEmpty ?? true)
    }
    
    func testHandleNetworkFailure() async throws {
        // Test with invalid parse server URL
        let messaging = NativeMessaging.shared
        
        // Temporarily set invalid URL
        UserDefaults.standard.set("http://invalid.local:9999", forKey: "parseServerURL")
        
        let testMessage: [String: Any] = [
            "type": "save",
            "data": [
                "url": "https://example.com/test",
                "htmlContent": "<h1>Test</h1>",
                "metadata": ["type": "article"]
            ]
        ]
        
        let response = await messaging.handleMessage(testMessage)
        
        // Should queue for later processing
        XCTAssertTrue(response["queued"] as? Bool ?? false)
        
        // Reset
        UserDefaults.standard.removeObject(forKey: "parseServerURL")
    }
}

// MARK: - Content Processor Extension

extension ContentProcessor {
    func processHTMLContent(html: String, url: String, contentType: Article.ContentType) async -> ProcessedContent? {
        // Simple HTML processing for tests
        // In real implementation, this would call the parse server
        
        var title = ""
        var content = ""
        var author: String?
        
        // Extract title
        if let titleMatch = html.range(of: "<title>(.*?)</title>", options: .regularExpression) {
            title = String(html[titleMatch]).replacingOccurrences(of: "<title>", with: "").replacingOccurrences(of: "</title>", with: "")
        } else if let h1Match = html.range(of: "<h1>(.*?)</h1>", options: .regularExpression) {
            title = String(html[h1Match]).replacingOccurrences(of: "<h1>", with: "").replacingOccurrences(of: "</h1>", with: "")
        }
        
        // Extract content
        let stripped = html.replacingOccurrences(of: "<[^>]+>", with: " ", options: .regularExpression)
        content = stripped.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Extract author
        if let authorMatch = html.range(of: "By ([^<]+)", options: .regularExpression) {
            author = String(html[authorMatch]).replacingOccurrences(of: "By ", with: "")
        }
        
        return ProcessedContent(
            title: title,
            content: content,
            author: author,
            contentType: contentType
        )
    }
}

struct ProcessedContent {
    let title: String
    let content: String
    let author: String?
    let contentType: Article.ContentType
}