import XCTest
@testable import PocketNext

class HybridVectorStorageTests: XCTestCase {
    var sut: HybridVectorStorage!
    var databaseManager: DatabaseManager!
    var mockEmbeddingService: MockEmbeddingService!
    var mockCloudKitSync: MockCloudKitSync!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Setup test database
        databaseManager = DatabaseManager()
        try await databaseManager.initialize()
        
        // Setup mocks
        mockEmbeddingService = MockEmbeddingService()
        mockCloudKitSync = MockCloudKitSync()
        
        // Create HybridVectorStorage with mocks
        sut = HybridVectorStorage.shared
    }
    
    override func tearDown() async throws {
        sut = nil
        databaseManager = nil
        mockEmbeddingService = nil
        mockCloudKitSync = nil
        try await super.tearDown()
    }
    
    // MARK: - Article Processing Tests
    
    func testProcessArticle_GeneratesAndStoresEmbedding() async throws {
        // Given
        let article = createTestArticle()
        mockEmbeddingService.mockEmbedding = Array(repeating: 0.5, count: 1536)
        
        // When
        try await sut.processArticle(article)
        
        // Then
        let updatedArticle = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(updatedArticle?.embeddingData)
        XCTAssertEqual(updatedArticle?.embeddingModelVersion, "openai-text-embedding-3-small-v1")
        XCTAssertTrue(updatedArticle?.hasLocalEmbedding ?? false)
    }
    
    func testProcessArticle_CompressesEmbeddingData() async throws {
        // Given
        let article = createTestArticle()
        let originalEmbedding = Array(repeating: Float(0.5), count: 1536)
        mockEmbeddingService.mockEmbedding = originalEmbedding
        
        // When
        try await sut.processArticle(article)
        
        // Then
        let updatedArticle = try await databaseManager.fetchArticle(id: article.id)
        let embeddingData = updatedArticle?.embeddingData
        XCTAssertNotNil(embeddingData)
        
        // Verify compression worked (should be ~4x smaller)
        let expectedSize = 1536 + 8 // 1536 Int8 values + 8 bytes for min/max
        XCTAssertLessThan(embeddingData!.count, originalEmbedding.count * 4)
        XCTAssertGreaterThanOrEqual(embeddingData!.count, expectedSize)
        
        // Verify decompression works
        let decompressed = VectorOperations.decompressEmbedding(embeddingData!)
        XCTAssertNotNil(decompressed)
        XCTAssertEqual(decompressed?.count, originalEmbedding.count)
    }
    
    // MARK: - Search Tests
    
    func testSearchSimilar_FindsRelatedArticles() async throws {
        // Given - Create and process multiple articles
        let articles = [
            createTestArticle(title: "Swift Programming Guide", keywords: ["swift", "ios", "programming"]),
            createTestArticle(title: "Python Machine Learning", keywords: ["python", "ml", "ai"]),
            createTestArticle(title: "iOS Development Tips", keywords: ["ios", "swift", "xcode"])
        ]
        
        for article in articles {
            mockEmbeddingService.mockEmbedding = generateMockEmbedding(for: article.title)
            try await sut.processArticle(article)
        }
        
        // When - Search for Swift-related content
        mockEmbeddingService.mockEmbedding = generateMockEmbedding(for: "Swift iOS development")
        let results = try await sut.searchSimilar(to: "Swift iOS development", limit: 2)
        
        // Then
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.contains { $0.title.contains("Swift") || $0.title.contains("iOS") })
    }
    
    func testSearchSimilarWithScores_ReturnsScores() async throws {
        // Given
        let article = createTestArticle()
        mockEmbeddingService.mockEmbedding = Array(repeating: 0.5, count: 1536)
        try await sut.processArticle(article)
        
        // When
        mockEmbeddingService.mockEmbedding = Array(repeating: 0.6, count: 1536)
        let results = try await sut.searchSimilarWithScores(to: "test query", limit: 10)
        
        // Then
        XCTAssertFalse(results.isEmpty)
        XCTAssertGreaterThan(results[0].score, 0.7) // Should have high similarity
    }
    
    // MARK: - Index Management Tests
    
    func testRebuildLocalIndex_RecreatesFromCloudKitData() async throws {
        // Given - Articles with embedding data but no local index
        let articles = [
            createTestArticle(withEmbedding: true),
            createTestArticle(withEmbedding: true)
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // When
        try await sut.rebuildLocalIndex()
        
        // Then
        let indexHealth = try await sut.checkIndexHealth()
        XCTAssertTrue(indexHealth)
    }
    
    func testCheckIndexHealth_DetectsMissingEntries() async throws {
        // Given - Article with embedding but no local index
        let article = createTestArticle(withEmbedding: true)
        try await databaseManager.save(article)
        
        // When
        let isHealthy = try await sut.checkIndexHealth()
        
        // Then
        XCTAssertFalse(isHealthy)
    }
    
    // MARK: - Sync Tests
    
    func testHandleSyncedArticle_UpdatesLocalIndex() async throws {
        // Given
        let syncedArticle = createTestArticle(withEmbedding: true)
        
        // When
        try await sut.handleSyncedArticle(syncedArticle)
        
        // Then
        let localArticle = try await databaseManager.fetchArticle(id: syncedArticle.id)
        XCTAssertTrue(localArticle?.hasLocalEmbedding ?? false)
        
        // Verify local index was created
        let indexHealth = try await sut.checkIndexHealth()
        XCTAssertTrue(indexHealth)
    }
    
    // MARK: - Migration Tests
    
    func testMigrateEmbeddingsIfNeeded_UpdatesOldModels() async throws {
        // Given - Article with old model version
        var article = createTestArticle()
        article.embeddingModelVersion = "old-model-v1"
        article.embeddingData = Data(repeating: 0, count: 100)
        try await databaseManager.save(article)
        
        // When
        mockEmbeddingService.mockEmbedding = Array(repeating: 0.5, count: 1536)
        try await sut.migrateEmbeddingsIfNeeded()
        
        // Then
        let updatedArticle = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertEqual(updatedArticle?.embeddingModelVersion, "openai-text-embedding-3-small-v1")
    }
    
    // MARK: - Helper Methods
    
    private func createTestArticle(
        title: String = "Test Article",
        keywords: [String] = ["test", "article"],
        withEmbedding: Bool = false
    ) -> Article {
        var article = Article(
            url: "https://example.com/\(UUID().uuidString)",
            title: title,
            content: "Test content",
            summary: "Test summary",
            keywords: keywords,
            readingTime: 5,
            contentType: .article
        )
        
        if withEmbedding {
            let embedding = Array(repeating: Float(0.5), count: 1536)
            article.embeddingData = VectorOperations.compressEmbedding(embedding)
            article.embeddingModelVersion = "openai-text-embedding-3-small-v1"
        }
        
        return article
    }
    
    private func generateMockEmbedding(for text: String) -> [Float] {
        // Generate deterministic embeddings based on text for testing
        let hash = text.hashValue
        var embedding = Array(repeating: Float(0), count: 1536)
        
        for i in 0..<embedding.count {
            embedding[i] = Float(sin(Double(hash + i))) * 0.5 + 0.5
        }
        
        return embedding
    }
}

// MARK: - Mock Services

class MockEmbeddingService: EmbeddingService {
    var mockEmbedding: [Float] = []
    
    override func generateEmbedding(for text: String) async throws -> [Float] {
        return mockEmbedding
    }
}

class MockCloudKitSync: CloudKitSyncService {
    var syncedArticles: [Article] = []
    
    override func syncArticle(_ article: Article) async throws {
        syncedArticles.append(article)
    }
}