import XCTest
@testable import PocketNext

class HybridArchitectureIntegrationTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var hybridStorage: HybridVectorStorage!
    var cloudKitSync: CloudKitSyncService!
    var searchEngine: SearchEngine!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Setup all components
        databaseManager = try await DatabaseTestHelpers.createTestDatabase()
        try await databaseManager.initialize()
        
        hybridStorage = HybridVectorStorage.shared
        cloudKitSync = CloudKitSyncService.shared
        searchEngine = SearchEngine(database: databaseManager, hybridStorage: hybridStorage)
    }
    
    override func tearDown() async throws {
        databaseManager = nil
        hybridStorage = nil
        cloudKitSync = nil
        searchEngine = nil
        try await super.tearDown()
    }
    
    // MARK: - End-to-End Flow Tests
    
    func testCompleteArticleProcessingFlow() async throws {
        // Given - new article without embedding
        let article = Article.testArticle(
            title: "Machine Learning with Swift",
            content: "This article covers advanced machine learning techniques using Swift and Core ML.",
            keywords: ["swift", "machine learning", "coreml", "ios"]
        )
        
        // Step 1: Save article
        try await databaseManager.save(article)
        
        // Step 2: Process with HybridVectorStorage (generates embedding)
        try await hybridStorage.processArticle(article)
        
        // Step 3: Verify article was updated with embedding
        let processedArticle = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(processedArticle?.embeddingData)
        XCTAssertTrue(processedArticle?.hasLocalEmbedding ?? false)
        
        // Step 4: Verify local vector index was created
        let vectorIndex = try await databaseManager.read { db in
            try LocalVectorIndex
                .filter(LocalVectorIndex.Columns.articleId == article.id)
                .fetchOne(db)
        }
        XCTAssertNotNil(vectorIndex)
        
        // Step 5: Search should find the article
        let searchResults = try await searchEngine.search("machine learning")
        XCTAssertTrue(searchResults.contains { $0.article.id == article.id })
    }
    
    func testCrossDeviceSyncFlow() async throws {
        // Simulate article synced from another device
        
        // Given - article with embedding from CloudKit
        let embedding = MockDataGenerator.generateMockEmbeddings()
        let compressedData = VectorOperations.compressEmbedding(embedding)
        
        let syncedArticle = Article.testArticle(
            title: "Synced from iPhone",
            embeddingData: compressedData,
            embeddingModelVersion: "openai-text-embedding-3-small-v1",
            hasLocalEmbedding: false // Not yet in local index
        )
        
        // Step 1: Handle synced article
        try await hybridStorage.handleSyncedArticle(syncedArticle)
        
        // Step 2: Verify article saved with local index
        let localArticle = try await databaseManager.fetchArticle(id: syncedArticle.id)
        XCTAssertTrue(localArticle?.hasLocalEmbedding ?? false)
        
        // Step 3: Verify searchable
        let results = try await hybridStorage.searchSimilar(to: "iPhone sync", limit: 10)
        XCTAssertTrue(results.contains { $0.id == syncedArticle.id })
    }
    
    func testIndexRebuildingFlow() async throws {
        // Given - multiple articles with embeddings but no local index
        let articles = (0..<5).map { i in
            Article.testArticleWithEmbedding(
                title: "Article \(i)",
                keywords: ["test", "article", "\(i)"]
            )
        }
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Verify index is not healthy (no local vector entries)
        let healthBefore = try await hybridStorage.checkIndexHealth()
        XCTAssertFalse(healthBefore)
        
        // Rebuild index
        try await hybridStorage.rebuildLocalIndex()
        
        // Verify index is now healthy
        let healthAfter = try await hybridStorage.checkIndexHealth()
        XCTAssertTrue(healthAfter)
        
        // Verify all articles are searchable
        let searchResults = try await hybridStorage.searchSimilar(to: "test article", limit: 10)
        XCTAssertEqual(searchResults.count, 5)
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformanceWithLargeDataset() async throws {
        // Create a larger dataset
        let articleCount = 100
        
        // Generate and save articles with embeddings
        for i in 0..<articleCount {
            let article = Article.testArticleWithEmbedding(
                title: "Performance Test Article \(i)",
                keywords: ["performance", "test", "article\(i)"]
            )
            try await databaseManager.save(article)
            try await hybridStorage.processArticle(article)
        }
        
        // Measure search performance
        let (results, duration) = try await AsyncTestUtilities.measure {
            try await searchEngine.search("performance test")
        }
        
        // Should complete quickly even with 100 articles
        XCTAssertLessThan(duration, 0.5) // 500ms
        XCTAssertGreaterThan(results.count, 0)
    }
    
    func testConcurrentOperations() async throws {
        // Test that the system handles concurrent operations correctly
        
        // Create articles concurrently
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<10 {
                group.addTask {
                    let article = Article.testArticle(title: "Concurrent Article \(i)")
                    try? await self.databaseManager.save(article)
                    try? await self.hybridStorage.processArticle(article)
                }
            }
        }
        
        // Verify all articles were processed
        let articles = try await databaseManager.fetchRecentArticles(limit: 20)
        let concurrentArticles = articles.filter { $0.title.contains("Concurrent Article") }
        
        XCTAssertEqual(concurrentArticles.count, 10)
        XCTAssertTrue(concurrentArticles.allSatisfy { $0.embeddingData != nil })
    }
    
    // MARK: - Error Recovery Tests
    
    func testRecoveryFromCorruptedIndex() async throws {
        // Given - article with embedding
        let article = Article.testArticleWithEmbedding()
        try await databaseManager.save(article)
        try await hybridStorage.processArticle(article)
        
        // Corrupt the index by manually deleting entries
        try await databaseManager.write { db in
            try db.execute(sql: "DELETE FROM local_vector_index")
        }
        
        // Index should be unhealthy
        let isHealthy = try await hybridStorage.checkIndexHealth()
        XCTAssertFalse(isHealthy)
        
        // System should still work by rebuilding
        try await hybridStorage.rebuildLocalIndex()
        
        // Search should work again
        let results = try await hybridStorage.searchSimilar(to: "test", limit: 10)
        XCTAssertTrue(results.contains { $0.id == article.id })
    }
    
    func testMigrationBetweenModelVersions() async throws {
        // Given - articles with old model version
        var oldArticle = Article.testArticle(
            embeddingModelVersion: "old-model-v1"
        )
        oldArticle.embeddingData = Data(repeating: 0, count: 100) // Mock old data
        try await databaseManager.save(oldArticle)
        
        // Run migration
        try await hybridStorage.migrateEmbeddingsIfNeeded()
        
        // Verify article was updated
        let migratedArticle = try await databaseManager.fetchArticle(id: oldArticle.id)
        XCTAssertEqual(migratedArticle?.embeddingModelVersion, "openai-text-embedding-3-small-v1")
        XCTAssertNotNil(migratedArticle?.embeddingData)
    }
    
    // MARK: - Edge Cases
    
    func testHandlingOfArticlesWithoutContent() async throws {
        // Given - article with minimal content
        let article = Article.testArticle(
            title: "",
            content: "",
            summary: "",
            keywords: []
        )
        
        // Should handle gracefully
        try await databaseManager.save(article)
        
        do {
            try await hybridStorage.processArticle(article)
            // Should succeed even with empty content
        } catch {
            XCTFail("Should handle empty content gracefully")
        }
    }
    
    func testLargeScaleDataHandling() async throws {
        // Test with realistic data sizes
        let realContent = MockDataGenerator.generateMockContent(wordCount: 5000)
        let article = Article.testArticle(
            title: "Large Article Test",
            content: realContent,
            summary: String(realContent.prefix(500))
        )
        
        // Process
        try await databaseManager.save(article)
        try await hybridStorage.processArticle(article)
        
        // Verify compression is working
        let saved = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(saved?.embeddingData)
        
        // Embedding data should be reasonably sized
        let dataSize = saved?.embeddingData?.count ?? 0
        XCTAssertLessThan(dataSize, 2000) // Should be compressed to ~1.5KB
    }
}