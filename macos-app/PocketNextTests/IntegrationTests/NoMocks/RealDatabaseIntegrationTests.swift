import XCTest
import GRDB
@testable import PocketNext

/// Integration tests that use real database without mocks
/// These tests verify actual database operations, concurrency, and data persistence
class RealDatabaseIntegrationTests: XCTestCase {
    
    var databaseManager: DatabaseManager!
    var testDatabasePath: String!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create a temporary database for testing
        let tempDir = NSTemporaryDirectory()
        testDatabasePath = "\(tempDir)/test_database_\(UUID().uuidString).sqlite"
        
        // Initialize real database manager
        databaseManager = DatabaseManager()
        
        // Initialize database
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        // Clean up test database
        try? FileManager.default.removeItem(atPath: testDatabasePath)
        databaseManager = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Database Initialization Tests
    
    func testDatabaseInitialization() async throws {
        // Database should be initialized successfully
        XCTAssertNotNil(databaseManager.db)
    }
    
    func testDatabaseSchema() async throws {
        // Verify database tables exist
        let tableNames = try await databaseManager.db.read { db in
            try String.fetchAll(db, sql: """
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
        }
        
        XCTAssertTrue(tableNames.contains("articles"))
        XCTAssertTrue(tableNames.contains("embeddings"))
    }
    
    // MARK: - CRUD Operations Tests
    
    func testCreateAndFetchArticle() async throws {
        // Create article
        let article = Article(
            url: "https://test.com/article",
            title: "Test Article",
            content: "Test content",
            summary: "Test summary",
            keywords: ["swift", "testing"],
            author: "Test Author",
            publishDate: Date(),
            readingTime: 5,
            contentType: .article
        )
        
        // Save to database
        let savedId = try await databaseManager.save(article)
        
        // Fetch back
        let fetchedArticle = try await databaseManager.fetch(savedId)
        
        // Verify
        XCTAssertNotNil(fetchedArticle)
        XCTAssertEqual(fetchedArticle?.url, article.url)
        XCTAssertEqual(fetchedArticle?.title, article.title)
        XCTAssertEqual(fetchedArticle?.content, article.content)
        XCTAssertEqual(fetchedArticle?.keywords, article.keywords)
    }
    
    func testUpdateArticle() async throws {
        // Create and save article
        var article = Article(
            url: "https://test.com",
            title: "Original Title",
            content: "Content",
            summary: "Summary",
            keywords: [],
            readingTime: 5,
            contentType: .article
        )
        let savedId = try await databaseManager.save(article)
        
        // Update article
        article.id = savedId
        article.title = "Updated Title"
        article.lastAccessedAt = Date()
        article.isArchived = true
        
        try await databaseManager.update(article)
        
        // Fetch and verify
        let updated = try await databaseManager.fetch(savedId)
        XCTAssertEqual(updated?.title, "Updated Title")
        XCTAssertNotNil(updated?.lastAccessedAt)
        XCTAssertTrue(updated?.isArchived ?? false)
    }
    
    func testDeleteArticle() async throws {
        // Create and save article
        let article = Article(
            url: "https://test.com",
            title: "To Delete",
            content: "Content",
            summary: "Summary",
            keywords: [],
            readingTime: 1,
            contentType: .generic
        )
        let savedId = try await databaseManager.save(article)
        
        // Verify it exists
        let exists = try await databaseManager.fetch(savedId)
        XCTAssertNotNil(exists)
        
        // Delete
        try await databaseManager.delete(savedId)
        
        // Verify it's gone
        let deleted = try await databaseManager.fetch(savedId)
        XCTAssertNil(deleted)
    }
    
    // MARK: - Bulk Operations Tests
    
    func testBulkInsert() async throws {
        // Create multiple articles
        let articles = (1...100).map { index in
            Article(
                url: "https://test.com/article\(index)",
                title: "Article \(index)",
                content: "Content \(index)",
                summary: "Summary \(index)",
                keywords: ["tag\(index)"],
                readingTime: index,
                contentType: .article
            )
        }
        
        // Measure bulk insert performance
        let startTime = Date()
        
        for article in articles {
            _ = try await databaseManager.save(article)
        }
        
        let duration = Date().timeIntervalSince(startTime)
        
        // Verify all inserted
        let count = try await databaseManager.db.read { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertEqual(count, 100)
        XCTAssertLessThan(duration, 5.0, "Bulk insert took too long: \(duration)s")
    }
    
    // MARK: - Search and Query Tests
    
    func testSearchArticles() async throws {
        // Create articles with different content
        let articles = [
            Article(
                url: "https://test1.com",
                title: "SwiftUI Tutorial",
                content: "Learn SwiftUI basics",
                summary: "SwiftUI introduction",
                keywords: ["swift", "ui"],
                readingTime: 5,
                contentType: .article
            ),
            Article(
                url: "https://test2.com",
                title: "UIKit Guide",
                content: "Master UIKit development",
                summary: "UIKit mastery",
                keywords: ["uikit", "ios"],
                readingTime: 10,
                contentType: .article
            ),
            Article(
                url: "https://test3.com",
                title: "Swift Concurrency",
                content: "Understanding async/await",
                summary: "Modern Swift concurrency",
                keywords: ["swift", "async"],
                readingTime: 15,
                contentType: .article
            )
        ]
        
        for article in articles {
            _ = try await databaseManager.save(article)
        }
        
        // Search for "Swift"
        let results = try await databaseManager.search("Swift")
        
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.allSatisfy { article in
            article.title.localizedCaseInsensitiveContains("Swift") ||
            article.content.localizedCaseInsensitiveContains("Swift") ||
            article.summary.localizedCaseInsensitiveContains("Swift")
        })
    }
    
    func testFetchRecentArticles() async throws {
        // Create articles with different captured dates
        let now = Date()
        let articles = [
            Article(
                url: "https://test1.com",
                title: "Today",
                content: "Content",
                summary: "Summary",
                keywords: [],
                readingTime: 1,
                contentType: .article,
                capturedAt: now
            ),
            Article(
                url: "https://test2.com",
                title: "Yesterday",
                content: "Content",
                summary: "Summary",
                keywords: [],
                readingTime: 1,
                contentType: .article,
                capturedAt: now.addingTimeInterval(-86400)
            ),
            Article(
                url: "https://test3.com",
                title: "Last Week",
                content: "Content",
                summary: "Summary",
                keywords: [],
                readingTime: 1,
                contentType: .article,
                capturedAt: now.addingTimeInterval(-604800)
            )
        ]
        
        for article in articles {
            _ = try await databaseManager.save(article)
        }
        
        // Fetch recent (limit 2)
        let recent = try await databaseManager.fetchRecentArticles(limit: 2)
        
        XCTAssertEqual(recent.count, 2)
        XCTAssertEqual(recent[0].title, "Today")
        XCTAssertEqual(recent[1].title, "Yesterday")
    }
    
    // MARK: - Concurrency Tests
    
    func testConcurrentReads() async throws {
        // Create test data
        let article = Article(
            url: "https://test.com",
            title: "Concurrent Test",
            content: "Content",
            summary: "Summary",
            keywords: [],
            readingTime: 1,
            contentType: .article
        )
        let savedId = try await databaseManager.save(article)
        
        // Perform concurrent reads
        await withTaskGroup(of: Article?.self) { group in
            for _ in 0..<10 {
                group.addTask {
                    try? await self.databaseManager.fetch(savedId)
                }
            }
            
            var results: [Article?] = []
            for await result in group {
                results.append(result)
            }
            
            // All reads should succeed
            XCTAssertEqual(results.count, 10)
            XCTAssertTrue(results.allSatisfy { $0 != nil })
        }
    }
    
    func testConcurrentWrites() async throws {
        // Perform concurrent inserts
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<10 {
                group.addTask {
                    let article = Article(
                        url: "https://test.com/concurrent\(i)",
                        title: "Concurrent Article \(i)",
                        content: "Content",
                        summary: "Summary",
                        keywords: [],
                        readingTime: 1,
                        contentType: .article
                    )
                    _ = try? await self.databaseManager.save(article)
                }
            }
        }
        
        // Verify all were inserted
        let count = try await databaseManager.db.read { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertEqual(count, 10)
    }
    
    // MARK: - Archive Operations Tests
    
    func testArchiveUnarchive() async throws {
        // Create article
        let article = Article(
            url: "https://test.com",
            title: "Archive Test",
            content: "Content",
            summary: "Summary",
            keywords: [],
            readingTime: 1,
            contentType: .article
        )
        let savedId = try await databaseManager.save(article)
        
        // Archive it
        try await databaseManager.archive(savedId)
        
        // Verify archived
        let archived = try await databaseManager.fetch(savedId)
        XCTAssertTrue(archived?.isArchived ?? false)
        
        // Unarchive it
        try await databaseManager.unarchive(savedId)
        
        // Verify unarchived
        let unarchived = try await databaseManager.fetch(savedId)
        XCTAssertFalse(unarchived?.isArchived ?? true)
    }
    
    // MARK: - Embedding Tests
    
    func testSaveAndFetchEmbedding() async throws {
        // Create article
        let article = Article(
            url: "https://test.com",
            title: "Embedding Test",
            content: "Content for embedding",
            summary: "Summary",
            keywords: [],
            readingTime: 1,
            contentType: .article
        )
        let articleId = try await databaseManager.save(article)
        
        // Create embedding
        let embedding = ArticleEmbedding(
            articleId: articleId,
            embedding: [0.1, 0.2, 0.3, 0.4, 0.5],
            modelVersion: "test-v1"
        )
        
        // Save embedding
        try await databaseManager.db.write { db in
            try embedding.insert(db)
        }
        
        // Fetch embedding
        let fetched = try await databaseManager.db.read { db in
            try ArticleEmbedding
                .filter(ArticleEmbedding.Columns.articleId == articleId)
                .fetchOne(db)
        }
        
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.embedding, embedding.embedding)
        XCTAssertEqual(fetched?.modelVersion, embedding.modelVersion)
    }
    
    // MARK: - Transaction Tests
    
    func testTransactionRollback() async throws {
        // Start with one article
        let article1 = Article(
            url: "https://test1.com",
            title: "Article 1",
            content: "Content",
            summary: "Summary",
            keywords: [],
            readingTime: 1,
            contentType: .article
        )
        _ = try await databaseManager.save(article1)
        
        // Try to save multiple articles in a transaction that fails
        do {
            try await databaseManager.db.write { db in
                try db.inTransaction { db in
                    // Save article 2
                    let article2 = Article(
                        url: "https://test2.com",
                        title: "Article 2",
                        content: "Content",
                        summary: "Summary",
                        keywords: [],
                        readingTime: 1,
                        contentType: .article
                    )
                    try article2.insert(db)
                    
                    // Force an error
                    throw NSError(domain: "test", code: 1, userInfo: nil)
                }
            }
            
            XCTFail("Transaction should have failed")
        } catch {
            // Expected error
        }
        
        // Verify only original article exists
        let count = try await databaseManager.db.read { db in
            try Article.fetchCount(db)
        }
        
        XCTAssertEqual(count, 1)
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() async throws {
        // Create 1000 articles
        let articles = (1...1000).map { index in
            Article(
                url: "https://test.com/article\(index)",
                title: "Article \(index)",
                content: "This is the content for article number \(index). Some have Swift, some have iOS.",
                summary: "Summary \(index)",
                keywords: ["tag\(index % 10)"],
                readingTime: index % 30,
                contentType: .article
            )
        }
        
        for article in articles {
            _ = try await databaseManager.save(article)
        }
        
        // Measure search performance
        let startTime = Date()
        let results = try await databaseManager.search("article")
        let duration = Date().timeIntervalSince(startTime)
        
        XCTAssertEqual(results.count, 1000)
        XCTAssertLessThan(duration, 1.0, "Search took too long: \(duration)s")
    }
    
    // MARK: - Error Handling Tests
    
    func testInvalidOperations() async throws {
        // Try to fetch non-existent article
        let nonExistent = try await databaseManager.fetch(UUID())
        XCTAssertNil(nonExistent)
        
        // Try to delete non-existent article (should not throw)
        try await databaseManager.delete(UUID())
    }
    
    // MARK: - Content Type Tests
    
    func testDifferentContentTypes() async throws {
        // Test all content types
        for contentType in Article.ContentType.allCases {
            let article = Article(
                url: "https://test.com/\(contentType.rawValue)",
                title: "\(contentType.rawValue) Article",
                content: "Content",
                summary: "Summary",
                keywords: [contentType.rawValue],
                readingTime: 5,
                contentType: contentType
            )
            
            let savedId = try await databaseManager.save(article)
            let fetched = try await databaseManager.fetch(savedId)
            
            XCTAssertEqual(fetched?.contentType, contentType)
        }
    }
    
    // MARK: - Sync Status Tests
    
    func testSyncStatusHandling() async throws {
        // Create articles with different sync statuses
        let statuses: [Article.SyncStatus] = [.pending, .synced, .error]
        
        for status in statuses {
            let article = Article(
                url: "https://test.com/sync-\(status.rawValue)",
                title: "Sync Status \(status.rawValue)",
                content: "Content",
                summary: "Summary",
                keywords: [],
                readingTime: 1,
                contentType: .article,
                syncStatus: status
            )
            
            let savedId = try await databaseManager.save(article)
            let fetched = try await databaseManager.fetch(savedId)
            
            XCTAssertEqual(fetched?.syncStatus, status)
        }
    }
}