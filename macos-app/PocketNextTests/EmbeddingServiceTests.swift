import XCTest
@testable import PocketNext

@MainActor
final class EmbeddingServiceTests: XCTestCase {
    var embeddingService: EmbeddingService!
    
    override func setUp() async throws {
        try await super.setUp()
        embeddingService = EmbeddingService()
        
        // Wait for model to load
        let timeout = Date().addingTimeInterval(5)
        while !embeddingService.isModelLoaded && Date() < timeout {
            try await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }
        
        XCTAssertTrue(embeddingService.isModelLoaded, "Model should load within timeout")
    }
    
    override func tearDown() async throws {
        embeddingService = nil
        try await super.tearDown()
    }
    
    // MARK: - Text Preprocessing Tests
    
    func testTextPreprocessing() {
        let testCases = [
            ("Hello, World!", "hello world"),
            ("Test@123#Special$Chars", "test 123 special chars"),
            ("Multiple   Spaces   Between", "multiple spaces between"),
            ("UPPERCASE lowercase MiXeD", "uppercase lowercase mixed"),
            ("New\nLine\tTabs", "new line tabs")
        ]
        
        for (input, expected) in testCases {
            let result = embeddingService.preprocessText(input)
            XCTAssertEqual(result, expected, "Failed for input: \(input)")
        }
    }
    
    func testEmptyTextPreprocessing() {
        let emptyInputs = ["", "   ", "\n\t", "!!!"]
        
        for input in emptyInputs {
            let result = embeddingService.preprocessText(input)
            XCTAssertTrue(result.isEmpty || result.split(separator: " ").allSatisfy { $0.isEmpty == false })
        }
    }
    
    // MARK: - Embedding Generation Tests
    
    func testEmbeddingGeneration() async throws {
        let text = "This is a test sentence for embedding generation."
        
        let embedding = try await embeddingService.generateEmbedding(for: text)
        
        XCTAssertEqual(embedding.count, 384, "Embedding should have 384 dimensions")
        
        // Verify embedding is normalized
        let magnitude = sqrt(embedding.reduce(0) { $0 + $1 * $1 })
        XCTAssertEqual(magnitude, 1.0, accuracy: 0.001, "Embedding should be normalized")
    }
    
    func testBatchEmbeddingGeneration() async throws {
        let texts = [
            "First test sentence",
            "Second test sentence",
            "Third test sentence"
        ]
        
        let embeddings = try await embeddingService.generateEmbeddings(for: texts)
        
        XCTAssertEqual(embeddings.count, texts.count)
        
        for embedding in embeddings {
            XCTAssertEqual(embedding.count, 384)
            let magnitude = sqrt(embedding.reduce(0) { $0 + $1 * $1 })
            XCTAssertEqual(magnitude, 1.0, accuracy: 0.001)
        }
    }
    
    func testEmbeddingConsistency() async throws {
        let text = "Consistent embedding test"
        
        let embedding1 = try await embeddingService.generateEmbedding(for: text)
        let embedding2 = try await embeddingService.generateEmbedding(for: text)
        
        // In real implementation with actual model, embeddings should be identical
        // For placeholder, they might have slight variations due to randomness
        let similarity = embeddingService.cosineSimilarity(embedding1, embedding2)
        XCTAssertGreaterThan(similarity, 0.8, "Similar text should produce similar embeddings")
    }
    
    // MARK: - Similarity Tests
    
    func testCosineSimilarity() {
        // Test identical vectors
        let vector1 = [Float](repeating: 0.5, count: 384)
        let similarity1 = embeddingService.cosineSimilarity(vector1, vector1)
        XCTAssertEqual(similarity1, 1.0, accuracy: 0.001)
        
        // Test orthogonal vectors
        var vector2 = [Float](repeating: 0, count: 384)
        vector2[0] = 1.0
        var vector3 = [Float](repeating: 0, count: 384)
        vector3[1] = 1.0
        let similarity2 = embeddingService.cosineSimilarity(vector2, vector3)
        XCTAssertEqual(similarity2, 0.0, accuracy: 0.001)
        
        // Test opposite vectors
        let vector4 = vector1.map { -$0 }
        let similarity3 = embeddingService.cosineSimilarity(vector1, vector4)
        XCTAssertLessThan(similarity3, 0)
    }
    
    func testFindMostSimilar() async throws {
        let queryText = "machine learning algorithms"
        let corpusTexts = [
            "deep learning neural networks",  // Should be most similar
            "database management systems",
            "swift programming language",
            "artificial intelligence and ML",  // Should be second most similar
            "web development frameworks"
        ]
        
        let queryEmbedding = try await embeddingService.generateEmbedding(for: queryText)
        let corpusEmbeddings = try await embeddingService.generateEmbeddings(for: corpusTexts)
        
        let topResults = embeddingService.findMostSimilar(
            queryEmbedding: queryEmbedding,
            in: corpusEmbeddings,
            topK: 3
        )
        
        XCTAssertEqual(topResults.count, 3)
        
        // Verify results are sorted by score
        for i in 1..<topResults.count {
            XCTAssertLessThanOrEqual(topResults[i].score, topResults[i-1].score)
        }
    }
    
    // MARK: - Performance Tests
    
    func testEmbeddingGenerationPerformance() async throws {
        let text = String(repeating: "Performance test sentence. ", count: 20)
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        _ = try await embeddingService.generateEmbedding(for: text)
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        print("Generated embedding in \(timeElapsed) seconds")
        XCTAssertLessThan(timeElapsed, 0.1, "Embedding generation should be fast")
    }
    
    func testBatchEmbeddingPerformance() async throws {
        let texts = (0..<10).map { "Test sentence number \($0)" }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        _ = try await embeddingService.generateEmbeddings(for: texts)
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        print("Generated \(texts.count) embeddings in \(timeElapsed) seconds")
        XCTAssertLessThan(timeElapsed, 1.0, "Batch embedding should complete within 1 second")
    }
    
    // MARK: - Error Handling Tests
    
    func testEmptyTextEmbedding() async throws {
        let emptyText = ""
        
        // Should handle empty text gracefully
        let embedding = try await embeddingService.generateEmbedding(for: emptyText)
        XCTAssertEqual(embedding.count, 384)
    }
    
    func testVeryLongTextEmbedding() async throws {
        let longText = String(repeating: "Very long text. ", count: 1000)
        
        // Should handle long text without issues
        let embedding = try await embeddingService.generateEmbedding(for: longText)
        XCTAssertEqual(embedding.count, 384)
    }
}

// MARK: - Embedding Cache Tests

final class EmbeddingCacheTests: XCTestCase {
    var cache: EmbeddingCache!
    
    override func setUp() {
        super.setUp()
        cache = EmbeddingCache()
    }
    
    override func tearDown() {
        cache = nil
        super.tearDown()
    }
    
    func testCacheOperations() async {
        let key = "test_key"
        let embedding = Array(repeating: Float(0.5), count: 384)
        
        // Test cache miss
        let cachedValue = await cache.get(key)
        XCTAssertNil(cachedValue)
        
        // Test cache set and hit
        await cache.set(key, embedding: embedding)
        let retrievedValue = await cache.get(key)
        XCTAssertNotNil(retrievedValue)
        XCTAssertEqual(retrievedValue, embedding)
        
        // Test cache clear
        await cache.clear()
        let clearedValue = await cache.get(key)
        XCTAssertNil(clearedValue)
    }
    
    func testCacheEviction() async {
        // Fill cache to capacity
        for i in 0..<1000 {
            let key = "key_\(i)"
            let embedding = Array(repeating: Float(i) / 1000.0, count: 384)
            await cache.set(key, embedding: embedding)
        }
        
        // Add one more item (should evict oldest)
        let newKey = "new_key"
        let newEmbedding = Array(repeating: Float(1.0), count: 384)
        await cache.set(newKey, embedding: newEmbedding)
        
        // New item should be in cache
        let retrieved = await cache.get(newKey)
        XCTAssertNotNil(retrieved)
        
        // Oldest item should be evicted (simplified test - actual LRU behavior may vary)
        // In production, implement proper LRU tracking
    }
}