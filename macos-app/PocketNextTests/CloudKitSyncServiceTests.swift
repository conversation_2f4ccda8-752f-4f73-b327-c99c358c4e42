import XCTest
import CloudKit
@testable import PocketNext

class CloudKitSyncServiceTests: XCTestCase {
    var sut: CloudKitSyncService!
    var mockContainer: MockCKContainer!
    var mockDatabase: MockCKDatabase!
    var databaseManager: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Setup test database
        databaseManager = try await DatabaseTestHelpers.createTestDatabase()
        try await databaseManager.initialize()
        
        // Setup CloudKit mocks
        mockDatabase = MockCKDatabase()
        mockContainer = MockCKContainer(privateDatabase: mockDatabase)
        
        // Create service (would need dependency injection in real implementation)
        sut = CloudKitSyncService.shared
    }
    
    override func tearDown() async throws {
        sut = nil
        mockContainer = nil
        mockDatabase = nil
        databaseManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Article Sync Tests
    
    func testSyncArticleWithEmbedding() async throws {
        // Given
        let article = Article.testArticleWithEmbedding(
            title: "CloudKit Sync Test",
            keywords: ["cloudkit", "sync", "test"]
        )
        
        // When
        try await sut.syncArticle(article)
        
        // Then - verify record was created with embedding data
        let savedRecords = mockDatabase.savedRecords
        XCTAssertEqual(savedRecords.count, 1)
        
        if let record = savedRecords.first {
            XCTAssertEqual(record.recordID.recordName, article.id.uuidString)
            XCTAssertEqual(record["title"] as? String, article.title)
            XCTAssertNotNil(record["embeddingData"] as? Data)
            XCTAssertEqual(record["embeddingModelVersion"] as? String, article.embeddingModelVersion)
        }
    }
    
    func testSyncArticleWithLargeEmbedding() async throws {
        // Given - article with embedding near CloudKit field limit
        let largeEmbedding = Array(repeating: Float(0.5), count: 200_000) // ~800KB compressed
        let article = Article.testArticle(
            embeddingData: VectorOperations.compressEmbedding(largeEmbedding),
            embeddingModelVersion: "large-model"
        )
        
        // When/Then - should handle gracefully
        do {
            try await sut.syncArticle(article)
            // Check that warning was logged (in real implementation)
        } catch {
            XCTFail("Should handle large embeddings gracefully")
        }
    }
    
    func testFetchArticlesWithEmbeddings() async throws {
        // Given - mock CloudKit records with embeddings
        let embedding = MockDataGenerator.generateMockEmbeddings()
        let compressedData = VectorOperations.compressEmbedding(embedding)
        
        let record = createMockArticleRecord(
            id: UUID(),
            title: "Fetched Article",
            embeddingData: compressedData,
            embeddingModelVersion: "test-v1"
        )
        
        mockDatabase.recordsToReturn = [record]
        
        // When
        let articles = try await sut.fetchArticles()
        
        // Then
        XCTAssertEqual(articles.count, 1)
        
        if let article = articles.first {
            XCTAssertEqual(article.title, "Fetched Article")
            XCTAssertNotNil(article.embeddingData)
            XCTAssertEqual(article.embeddingModelVersion, "test-v1")
            XCTAssertFalse(article.hasLocalEmbedding) // Should be false until processed locally
        }
    }
    
    // MARK: - Sync Status Tests
    
    func testSyncStatusUpdates() async throws {
        // Given
        var article = Article.testArticle(syncStatus: .pending)
        try await databaseManager.save(article)
        
        // When
        try await sut.syncArticle(article)
        
        // Then - verify status was updated
        let updated = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertEqual(updated?.syncStatus, .synced)
    }
    
    func testSyncFailureHandling() async throws {
        // Given
        mockDatabase.shouldFailSave = true
        let article = Article.testArticle()
        
        // When/Then
        do {
            try await sut.syncArticle(article)
            XCTFail("Should throw error")
        } catch {
            // Expected
        }
    }
    
    // MARK: - Batch Sync Tests
    
    func testStartSyncProcessesUnsyncedArticles() async throws {
        // Given - articles with different sync statuses
        let articles = [
            Article.testArticle(title: "Pending 1", syncStatus: .pending),
            Article.testArticle(title: "Already Synced", syncStatus: .synced),
            Article.testArticle(title: "Pending 2", syncStatus: .pending),
            Article.testArticle(title: "Error", syncStatus: .error)
        ]
        
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // When
        sut.startSync()
        
        // Wait for sync to complete
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Then - only pending articles should be synced
        let syncedArticles = mockDatabase.savedRecords.compactMap { record in
            record["title"] as? String
        }
        
        XCTAssertTrue(syncedArticles.contains("Pending 1"))
        XCTAssertTrue(syncedArticles.contains("Pending 2"))
        XCTAssertFalse(syncedArticles.contains("Already Synced"))
    }
    
    // MARK: - CloudKit Integration Tests
    
    func testRecordConversion() async throws {
        // Given
        let article = Article.testArticleWithEmbedding()
        
        // When
        let record = try sut.articleToRecord(article)
        
        // Then
        XCTAssertEqual(record.recordType, "Article")
        XCTAssertEqual(record.recordID.recordName, article.id.uuidString)
        XCTAssertEqual(record["url"] as? String, article.url)
        XCTAssertEqual(record["title"] as? String, article.title)
        XCTAssertNotNil(record["embeddingData"] as? Data)
        
        // Verify keywords are properly encoded
        if let keywordsData = record["keywords"] as? Data,
           let decodedKeywords = try? JSONDecoder().decode([String].self, from: keywordsData) {
            XCTAssertEqual(decodedKeywords, article.keywords)
        } else {
            XCTFail("Keywords not properly encoded")
        }
    }
    
    func testRecordToArticleConversion() {
        // Given
        let recordID = CKRecord.ID(recordName: UUID().uuidString)
        let record = CKRecord(recordType: "Article", recordID: recordID)
        
        // Set required fields
        record["url"] = "https://example.com"
        record["title"] = "Test Title"
        record["content"] = "Test content"
        record["summary"] = "Test summary"
        record["keywords"] = try! JSONEncoder().encode(["test", "keywords"])
        record["contentType"] = "article"
        record["capturedAt"] = Date()
        record["readingTime"] = 5
        
        // Set embedding fields
        let embedding = MockDataGenerator.generateMockEmbeddings()
        record["embeddingData"] = VectorOperations.compressEmbedding(embedding)
        record["embeddingModelVersion"] = "test-v1"
        
        // When
        let article = sut.recordToArticle(record)
        
        // Then
        XCTAssertNotNil(article)
        XCTAssertEqual(article?.title, "Test Title")
        XCTAssertNotNil(article?.embeddingData)
        XCTAssertEqual(article?.embeddingModelVersion, "test-v1")
        XCTAssertFalse(article?.hasLocalEmbedding ?? true)
    }
    
    // MARK: - Helper Methods
    
    private func createMockArticleRecord(
        id: UUID,
        title: String,
        embeddingData: Data? = nil,
        embeddingModelVersion: String? = nil
    ) -> CKRecord {
        let record = CKRecord(recordType: "Article", recordID: CKRecord.ID(recordName: id.uuidString))
        
        record["url"] = "https://example.com/\(id)"
        record["title"] = title
        record["content"] = "Mock content"
        record["summary"] = "Mock summary"
        record["keywords"] = try! JSONEncoder().encode(["mock", "test"])
        record["contentType"] = "article"
        record["capturedAt"] = Date()
        record["readingTime"] = 5
        
        if let embeddingData = embeddingData {
            record["embeddingData"] = embeddingData
            record["embeddingModelVersion"] = embeddingModelVersion
        }
        
        return record
    }
}

// MARK: - Mock CloudKit Classes

class MockCKContainer {
    let privateDatabase: MockCKDatabase
    var accountStatus: CKAccountStatus = .available
    
    init(privateDatabase: MockCKDatabase) {
        self.privateDatabase = privateDatabase
    }
    
    func accountStatus() async throws -> CKAccountStatus {
        return accountStatus
    }
}

class MockCKDatabase {
    var savedRecords: [CKRecord] = []
    var recordsToReturn: [CKRecord] = []
    var shouldFailSave = false
    
    func save(_ record: CKRecord) async throws -> CKRecord {
        if shouldFailSave {
            throw NSError(domain: "MockError", code: 1)
        }
        savedRecords.append(record)
        return record
    }
    
    func records(matching query: CKQuery) async throws -> ([CKRecord.ID: Result<CKRecord, Error>], CKQueryOperation.Cursor?) {
        var results: [CKRecord.ID: Result<CKRecord, Error>] = [:]
        
        for record in recordsToReturn {
            results[record.recordID] = .success(record)
        }
        
        return (results, nil)
    }
    
    func deleteRecord(withID recordID: CKRecord.ID) async throws -> CKRecord.ID {
        savedRecords.removeAll { $0.recordID == recordID }
        return recordID
    }
}