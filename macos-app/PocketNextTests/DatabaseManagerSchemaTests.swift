import XCTest
import GRDB
@testable import PocketNext

class DatabaseManagerSchemaTests: XCTestCase {
    var databaseManager: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        databaseManager = try await DatabaseTestHelpers.createTestDatabase()
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        databaseManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Schema Tests
    
    func testArticlesTableSchema() async throws {
        // Verify articles table has all required columns
        let columns = try await databaseManager.read { db in
            try db.columns(in: "articles").map { $0.name }
        }
        
        let requiredColumns = [
            "id", "url", "title", "content", "summary", "keywords",
            "author", "publishDate", "readingTime", "contentType",
            "capturedAt", "lastAccessedAt", "isArchived", "syncStatus",
            "embeddingData", "embeddingModelVersion", "hasLocalEmbedding"
        ]
        
        for column in requiredColumns {
            XCTAssertTrue(columns.contains(column), "Missing column: \(column)")
        }
    }
    
    func testLocalVectorIndexTable() async throws {
        // Verify local_vector_index table exists and has correct schema
        let tables = try await databaseManager.read { db in
            try String.fetchAll(db, sql: "SELECT name FROM sqlite_master WHERE type='table' AND name='local_vector_index'")
        }
        
        XCTAssertEqual(tables.count, 1)
        
        // Check columns
        let columns = try await databaseManager.read { db in
            try db.columns(in: "local_vector_index").map { $0.name }
        }
        
        let requiredColumns = ["articleId", "embedding", "magnitude", "modelVersion", "createdAt"]
        for column in requiredColumns {
            XCTAssertTrue(columns.contains(column), "Missing column: \(column)")
        }
    }
    
    func testIndexesExist() async throws {
        // Verify performance indexes exist
        let indexes = try await databaseManager.read { db in
            try String.fetchAll(db, sql: """
                SELECT name FROM sqlite_master 
                WHERE type='index' 
                AND name LIKE 'idx_%'
            """)
        }
        
        let expectedIndexes = [
            "idx_articles_captured",
            "idx_articles_archived",
            "idx_articles_url",
            "idx_articles_embedding",
            "idx_vector_index_created"
        ]
        
        for index in expectedIndexes {
            XCTAssertTrue(indexes.contains(index), "Missing index: \(index)")
        }
    }
    
    func testForeignKeyConstraints() async throws {
        // Test that foreign key constraints are enforced
        let articleId = UUID()
        
        // Try to insert vector index for non-existent article
        do {
            try await databaseManager.write { db in
                let vectorIndex = LocalVectorIndex(
                    articleId: articleId,
                    embedding: [0.1, 0.2, 0.3],
                    magnitude: 0.5,
                    modelVersion: "test",
                    createdAt: Date()
                )
                try vectorIndex.save(db)
            }
            XCTFail("Should fail with foreign key constraint")
        } catch {
            // Expected - foreign key constraint should prevent this
        }
    }
    
    // MARK: - Migration Tests
    
    func testMigrationFromOldSchema() async throws {
        // Test that old embeddings table is removed
        let tables = try await databaseManager.read { db in
            try String.fetchAll(db, sql: "SELECT name FROM sqlite_master WHERE type='table' AND name='embeddings'")
        }
        
        XCTAssertEqual(tables.count, 0, "Old embeddings table should be removed")
    }
    
    func testColumnAdditionForExistingTable() async throws {
        // Verify that migration adds new columns to existing tables
        // This tests the alter table logic in createTables
        
        // First, create an article
        let article = Article.testArticle()
        try await databaseManager.save(article)
        
        // Verify we can save an article with embedding data
        var articleWithEmbedding = Article.testArticleWithEmbedding()
        try await databaseManager.save(articleWithEmbedding)
        
        // Fetch and verify
        let fetched = try await databaseManager.fetchArticle(id: articleWithEmbedding.id)
        XCTAssertNotNil(fetched?.embeddingData)
        XCTAssertNotNil(fetched?.embeddingModelVersion)
    }
    
    // MARK: - Vector Index Operations
    
    func testLocalVectorIndexOperations() async throws {
        // Create article with embedding
        let article = Article.testArticleWithEmbedding()
        try await databaseManager.save(article)
        
        // Create vector index entry
        let embedding = MockDataGenerator.generateMockEmbeddings()
        let normalized = VectorOperations.normalize(embedding)
        let magnitude = VectorOperations.magnitude(normalized)
        
        let vectorIndex = LocalVectorIndex(
            articleId: article.id,
            embedding: normalized,
            magnitude: magnitude,
            modelVersion: "test-v1",
            createdAt: Date()
        )
        
        // Save vector index
        try await databaseManager.write { db in
            try vectorIndex.save(db)
        }
        
        // Fetch and verify
        let fetched = try await databaseManager.read { db in
            try LocalVectorIndex.filter(LocalVectorIndex.Columns.articleId == article.id).fetchOne(db)
        }
        
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.articleId, article.id)
        XCTAssertEqual(fetched?.magnitude, magnitude, accuracy: 0.0001)
        XCTAssertEqual(fetched?.modelVersion, "test-v1")
    }
    
    func testVectorIndexCascadeDelete() async throws {
        // Create article with vector index
        let article = Article.testArticleWithEmbedding()
        try await databaseManager.save(article)
        
        let vectorIndex = LocalVectorIndex(
            articleId: article.id,
            embedding: [0.1, 0.2, 0.3],
            magnitude: 0.5,
            modelVersion: "test",
            createdAt: Date()
        )
        
        try await databaseManager.write { db in
            try vectorIndex.save(db)
        }
        
        // Delete article
        try await databaseManager.deleteArticle(id: article.id)
        
        // Verify vector index was cascade deleted
        let remainingIndex = try await databaseManager.read { db in
            try LocalVectorIndex.filter(LocalVectorIndex.Columns.articleId == article.id).fetchOne(db)
        }
        
        XCTAssertNil(remainingIndex)
    }
    
    // MARK: - Performance Tests
    
    func testBulkVectorIndexInsert() async throws {
        // Test performance of bulk vector operations
        let articleCount = 100
        
        // Create articles
        let articles = (0..<articleCount).map { _ in
            Article.testArticleWithEmbedding()
        }
        
        // Measure bulk insert time
        let start = CFAbsoluteTimeGetCurrent()
        
        for article in articles {
            try await databaseManager.save(article)
            
            // Create vector index
            let vectorIndex = LocalVectorIndex(
                articleId: article.id,
                embedding: MockDataGenerator.generateMockEmbeddings(),
                magnitude: Float.random(in: 0.5...1.5),
                modelVersion: "test",
                createdAt: Date()
            )
            
            try await databaseManager.write { db in
                try vectorIndex.save(db)
            }
        }
        
        let elapsed = CFAbsoluteTimeGetCurrent() - start
        
        // Should complete reasonably fast
        XCTAssertLessThan(elapsed, 5.0, "Bulk insert took too long: \(elapsed)s")
        
        // Verify all inserted
        let count = try await databaseManager.read { db in
            try LocalVectorIndex.fetchCount(db)
        }
        
        XCTAssertEqual(count, articleCount)
    }
}