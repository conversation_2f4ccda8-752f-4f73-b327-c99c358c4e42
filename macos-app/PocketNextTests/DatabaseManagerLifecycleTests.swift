import XCTest
@testable import PocketNext

class DatabaseManagerLifecycleTests: XCTestCase {
    
    override func tearDown() async throws {
        // Clean up after each test
        try await super.tearDown()
    }
    
    func testSharedInstanceNotInitializedCausesNilCrash() async throws {
        // This test reproduces the runtime crash
        // The shared instance is created but never initialized
        let dbManager = DatabaseManager.shared
        
        // Verify database is not initialized
        let isInitialized = await dbManager.isInitialized
        XCTAssertFalse(isInitialized, "Database should not be initialized before calling initialize()")
        
        // This would crash with force unwrap
        do {
            _ = try await dbManager.fetchRecentArticles()
            XCTFail("Should have thrown an error or crashed")
        } catch {
            // Expected - this is what we want to handle gracefully
            XCTAssertTrue(true, "Properly handled nil dbQueue")
        }
    }
    
    func testNewInstanceWithoutInitializationCausesNilCrash() async throws {
        // This reproduces the exact bug in PocketNextApp.swift
        let dbManager = DatabaseManager.shared
        
        // Verify database is not initialized
        let isInitialized = await dbManager.isInitialized
        XCTAssertFalse(isInitialized, "Database should not be initialized for new instance")
        
        // Now initialize it
        try await dbManager.initialize()
        
        // Verify database is initialized
        let isInitializedAfter = await dbManager.isInitialized
        XCTAssertTrue(isInitializedAfter, "Database should be initialized after calling initialize()")
        
        // But the shared instance is still not initialized!
        let sharedIsInitialized = await DatabaseManager.shared.isInitialized
        XCTAssertFalse(sharedIsInitialized, "Shared instance is still not initialized!")
    }
    
    func testProperSharedInstanceInitialization() async throws {
        // The correct way to initialize
        try await DatabaseManager.shared.initialize()
        
        // Verify database is initialized
        let isInitialized = await DatabaseManager.shared.isInitialized
        XCTAssertTrue(isInitialized, "Shared instance should be initialized after calling initialize()")
        
        // Now operations should work
        let articles = try await DatabaseManager.shared.fetchRecentArticles()
        XCTAssertNotNil(articles, "Should be able to fetch articles")
    }
    
    func testConcurrentAccessToUninitializedDatabase() async throws {
        // Test what happens when multiple parts of the app try to use an uninitialized database
        let dbManager = DatabaseManager.shared
        
        // Simulate concurrent access from different parts of the app
        await withThrowingTaskGroup(of: Void.self) { group in
            // Multiple concurrent operations trying to access nil dbQueue
            for i in 0..<5 {
                group.addTask {
                    do {
                        if i % 2 == 0 {
                            _ = try await dbManager.fetchRecentArticles()
                        } else {
                            _ = try await dbManager.searchArticles(query: "test")
                        }
                        XCTFail("Should have failed with nil dbQueue")
                    } catch {
                        // Expected - operations should fail gracefully
                    }
                }
            }
        }
    }
}