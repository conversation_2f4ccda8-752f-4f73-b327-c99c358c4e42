import XCTest
@testable import PocketNext

@MainActor
final class SearchEngineTests: XCTestCase {
    var searchEngine: SearchEngine!
    var database: DatabaseManager!
    var hybridStorage: HybridVectorStorage!
    var mockEmbeddingService: MockEmbeddingService!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Use shared database manager
        database = DatabaseManager.shared
        try await database.initialize()
        
        // Initialize hybrid storage with mock
        hybridStorage = HybridVectorStorage.shared
        
        // Create search engine with dependencies
        searchEngine = SearchEngine(database: database, hybridStorage: hybridStorage)
        
        // Create mock embedding service
        mockEmbeddingService = MockEmbeddingService()
        
        // Populate test data
        try await populateTestData()
    }
    
    override func tearDown() async throws {
        searchEngine = nil
        database = nil
        mockEmbeddingService = nil
        try await super.tearDown()
    }
    
    // MARK: - Test Data Population
    
    private func populateTestData() async throws {
        let articles = [
            Article.testArticle(
                title: "Introduction to Swift Programming",
                content: "Swift is a powerful and intuitive programming language for iOS, macOS, watchOS, and tvOS.",
                keywords: ["swift", "programming", "ios", "apple"],
                author: "<PERSON> Doe"
            ),
            Article.testArticle(
                title: "Advanced Swift Techniques",
                content: "Learn advanced Swift concepts including generics, protocols, and concurrency.",
                keywords: ["swift", "advanced", "generics", "protocols"],
                author: "Jane Smith"
            ),
            Article.testArticle(
                title: "Python Machine Learning Guide",
                content: "A comprehensive guide to machine learning with Python and scikit-learn.",
                keywords: ["python", "machine learning", "data science"],
                author: "Bob Johnson"
            ),
            Article.testArticle(
                title: "Web Development with JavaScript",
                content: "Modern web development using JavaScript, React, and Node.js.",
                keywords: ["javascript", "web", "react", "nodejs"],
                author: "Alice Brown"
            ),
            Article.testArticle(
                title: "SwiftUI Tutorial",
                content: "Build beautiful UIs with SwiftUI's declarative syntax.",
                keywords: ["swiftui", "swift", "ui", "apple"],
                author: "John Doe"
            )
        ]
        
        for article in articles {
            try await database.save(article)
        }
    }
    
    // MARK: - Basic Search Tests
    
    func testTextSearch() async throws {
        let results = try await searchEngine.search("Swift")
        
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertTrue(results.allSatisfy { result in
            result.article.title.contains("Swift") || 
            result.article.content.contains("Swift") ||
            result.article.keywords.contains(where: { $0.lowercased().contains("swift") })
        })
        
        // Verify ranking - exact matches should rank higher
        if results.count >= 2 {
            let firstResult = results[0]
            XCTAssertTrue(
                firstResult.article.title.contains("Swift") ||
                firstResult.article.keywords.contains("swift")
            )
        }
    }
    
    func testCaseInsensitiveSearch() async throws {
        let upperResults = try await searchEngine.search("SWIFT")
        let lowerResults = try await searchEngine.search("swift")
        let mixedResults = try await searchEngine.search("SwIfT")
        
        XCTAssertEqual(upperResults.count, lowerResults.count)
        XCTAssertEqual(upperResults.count, mixedResults.count)
    }
    
    func testPartialWordSearch() async throws {
        let results = try await searchEngine.search("prog")
        
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertTrue(results.contains { result in
            result.article.title.lowercased().contains("programming") ||
            result.article.keywords.contains("programming")
        })
    }
    
    // MARK: - Advanced Search Tests
    
    func testMultiWordSearch() async throws {
        let results = try await searchEngine.search("Swift programming")
        
        XCTAssertGreaterThan(results.count, 0)
        
        // Articles with both words should rank higher
        let topResult = results[0]
        XCTAssertTrue(
            (topResult.article.title.contains("Swift") && topResult.article.title.contains("Programming")) ||
            (topResult.article.content.contains("Swift") && topResult.article.content.contains("programming"))
        )
    }
    
    func testSearchByAuthor() async throws {
        let results = try await searchEngine.search("John Doe")
        
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertTrue(results.allSatisfy { $0.article.author == "John Doe" })
    }
    
    func testSearchByKeyword() async throws {
        let results = try await searchEngine.search("machine learning")
        
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertTrue(results.contains { result in
            result.article.keywords.contains("machine learning") ||
            result.article.content.contains("machine learning")
        })
    }
    
    // MARK: - Semantic Search Tests
    
    func testSemanticSearch() async throws {
        // Create search engine with mock embedding service
        let semanticResults = try await searchEngine.semanticSearch(
            "iOS app development",
            embeddingService: mockEmbeddingService
        )
        
        XCTAssertGreaterThan(semanticResults.count, 0)
        
        // Swift/iOS articles should rank high for "iOS app development"
        XCTAssertTrue(semanticResults.contains { result in
            result.article.keywords.contains("ios") ||
            result.article.keywords.contains("swift")
        })
    }
    
    func testHybridSearch() async throws {
        // Hybrid search combines text and semantic search
        let hybridResults = try await searchEngine.hybridSearch(
            "programming tutorials",
            embeddingService: mockEmbeddingService
        )
        
        XCTAssertGreaterThan(hybridResults.count, 0)
        
        // Should include both text matches and semantically similar content
        let titles = hybridResults.map { $0.article.title }
        XCTAssertTrue(titles.contains { $0.contains("Programming") || $0.contains("Tutorial") })
    }
    
    // MARK: - Filtering Tests
    
    func testSearchWithDateFilter() async throws {
        // Add a recent article
        let recentArticle = Article.testArticle(
            title: "Recent Swift News",
            capturedAt: Date()
        )
        try await database.save(recentArticle)
        
        // Search within last 24 hours
        let yesterday = Date().addingTimeInterval(-86400)
        let results = try await searchEngine.search("Swift", after: yesterday)
        
        XCTAssertTrue(results.contains { $0.article.id == recentArticle.id })
        XCTAssertTrue(results.allSatisfy { $0.article.capturedAt > yesterday })
    }
    
    func testSearchUnreadOnly() async throws {
        // Mark some articles as read
        let articles = try await database.fetchRecentArticles(limit: 10)
        for (index, article) in articles.enumerated() {
            if index % 2 == 0 {
                try await database.markAsRead(article.id)
            }
        }
        
        // Search and filter for unread articles
        let allResults = try await searchEngine.search("Swift")
        let unreadResults = allResults.filter { !$0.article.isRead }
        
        XCTAssertTrue(unreadResults.allSatisfy { !$0.article.isRead })
    }
    
    // MARK: - Ranking Tests
    
    func testSearchRanking() async throws {
        let results = try await searchEngine.search("Swift")
        
        // Verify results are ranked by relevance score
        for i in 1..<results.count {
            XCTAssertGreaterThanOrEqual(results[i-1].score, results[i].score)
        }
    }
    
    func testTitleMatchesRankHigher() async throws {
        let results = try await searchEngine.search("Introduction")
        
        if let firstResult = results.first {
            XCTAssertTrue(firstResult.article.title.contains("Introduction"))
            XCTAssertGreaterThan(firstResult.score, 0.5)
        }
    }
    
    // MARK: - Popular Keywords Tests
    
    func testGetPopularKeywords() async throws {
        let popularKeywords = try await searchEngine.getPopularKeywords()
        
        XCTAssertLessThanOrEqual(popularKeywords.count, 5)
        
        // "swift" should be one of the most popular keywords
        let swiftKeyword = popularKeywords.first { $0.keyword == "swift" }
        XCTAssertNotNil(swiftKeyword)
        XCTAssertGreaterThanOrEqual(swiftKeyword?.count ?? 0, 2)
        
        // Keywords should be sorted by count
        for i in 1..<popularKeywords.count {
            XCTAssertGreaterThanOrEqual(popularKeywords[i-1].count, popularKeywords[i].count)
        }
    }
    
    // MARK: - Edge Cases Tests
    
    func testEmptySearch() async throws {
        let results = try await searchEngine.search("")
        XCTAssertTrue(results.isEmpty)
    }
    
    func testSearchNoResults() async throws {
        let results = try await searchEngine.search("NonExistentTermXYZ123")
        XCTAssertTrue(results.isEmpty)
    }
    
    func testSpecialCharacterSearch() async throws {
        // Add article with special characters
        let article = Article.testArticle(
            title: "C++ Programming Guide",
            content: "Learn C++ with examples"
        )
        try await database.save(article)
        
        let results = try await searchEngine.search("C++")
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertTrue(results.contains { $0.article.title.contains("C++") })
    }
    
    // MARK: - Performance Tests
    
    func testSearchPerformance() async throws {
        // Add more articles for performance testing
        let articles = (1...100).map { i in
            Article.testArticle(
                title: "Article \(i)",
                content: "Content for article \(i) with various keywords"
            )
        }
        
        for article in articles {
            try await database.save(article)
        }
        
        measure {
            let expectation = XCTestExpectation(description: "Search performance")
            
            Task {
                _ = try await searchEngine.search("article")
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    // MARK: - Suggestion Tests
    
    func testSearchSuggestions() async throws {
        let suggestions = try await searchEngine.getSuggestions(for: "Sw")
        
        XCTAssertGreaterThan(suggestions.count, 0)
        XCTAssertTrue(suggestions.contains("Swift"))
        XCTAssertTrue(suggestions.contains("SwiftUI"))
        
        // Suggestions should be sorted by relevance/frequency
        XCTAssertTrue(suggestions.allSatisfy { $0.lowercased().hasPrefix("sw") })
    }
    
    func testRecentSearches() async throws {
        // Perform some searches
        _ = try await searchEngine.search("Swift")
        _ = try await searchEngine.search("Python")
        _ = try await searchEngine.search("JavaScript")
        
        let recentSearches = searchEngine.getRecentSearches(limit: 2)
        
        XCTAssertEqual(recentSearches.count, 2)
        XCTAssertEqual(recentSearches[0], "JavaScript") // Most recent
        XCTAssertEqual(recentSearches[1], "Python")
    }
}

// MARK: - Search Engine Extension for Testing

extension SearchEngine {
    // Mock implementation for testing recent searches
    fileprivate static var recentSearches: [String] = []
    
    func getRecentSearches(limit: Int) -> [String] {
        // In real implementation, this would be persisted
        return Array(SearchEngine.recentSearches.prefix(limit))
    }
    
    // Removed duplicate search method to avoid ambiguity
    func updateRecentSearches(_ query: String) {
        // Store search for recent searches
        SearchEngine.recentSearches.insert(query, at: 0)
        if SearchEngine.recentSearches.count > 10 {
            SearchEngine.recentSearches.removeLast()
        }
        
        // Perform actual search
        return try await performSearch(query)
    }
    
    // Helper method for testing
    private func performSearch(_ query: String) async throws -> [SearchResult] {
        guard !query.isEmpty else { return [] }
        
        let articles = try await database.searchArticles(query: query)
        
        return articles.map { article in
            let score = calculateRelevanceScore(article: article, query: query)
            return SearchResult(article: article, relevanceScore: score)
        }.sorted { $0.relevanceScore > $1.relevanceScore }
    }
    
    private func calculateRelevanceScore(article: Article, query: String) -> Double {
        var score = 0.0
        let queryLower = query.lowercased()
        
        // Title match (highest weight)
        if article.title.lowercased().contains(queryLower) {
            score += 1.0
        }
        
        // Keyword match
        if article.keywords.contains(where: { $0.lowercased().contains(queryLower) }) {
            score += 0.5
        }
        
        // Content match
        if article.content.lowercased().contains(queryLower) {
            score += 0.3
        }
        
        // Author match
        if let author = article.author, author.lowercased().contains(queryLower) {
            score += 0.4
        }
        
        return score
    }
    
    func searchUnread(_ query: String) async throws -> [SearchResult] {
        let allResults = try await search(query)
        return allResults.filter { !$0.article.isRead }
    }
    
    func search(_ query: String, after date: Date) async throws -> [SearchResult] {
        let allResults = try await search(query)
        return allResults.filter { $0.article.capturedAt > date }
    }
    
    func getSuggestions(for prefix: String) async throws -> [String] {
        guard !prefix.isEmpty else { return [] }
        
        let allArticles = try await database.fetchRecentArticles(limit: 100)
        var suggestions = Set<String>()
        
        // Extract suggestions from titles and keywords
        for article in allArticles {
            let words = article.title.split(separator: " ") + article.keywords.flatMap { $0.split(separator: " ") }
            
            for word in words {
                let wordStr = String(word)
                if wordStr.lowercased().hasPrefix(prefix.lowercased()) {
                    suggestions.insert(wordStr)
                }
            }
        }
        
        return Array(suggestions).sorted()
    }
    
    func semanticSearch(_ query: String, embeddingService: MockEmbeddingService) async throws -> [SearchResult] {
        // Mock semantic search using embedding service
        let queryEmbedding = try await embeddingService.generateEmbedding(for: query)
        let articles = try await database.fetchRecentArticles(limit: 50)
        
        var results: [(article: Article, score: Float)] = []
        
        for article in articles {
            let contentEmbedding = try await embeddingService.generateEmbedding(for: article.content)
            let similarity = try await embeddingService.similarity(query, article.content)
            results.append((article, similarity))
        }
        
        return results
            .sorted { $0.score > $1.score }
            .map { SearchResult(article: $0.article, relevanceScore: Double($0.score)) }
    }
    
    func hybridSearch(_ query: String, embeddingService: MockEmbeddingService) async throws -> [SearchResult] {
        // Combine text and semantic search
        let textResults = try await search(query)
        let semanticResults = try await semanticSearch(query, embeddingService: embeddingService)
        
        // Merge and re-rank
        var combinedScores: [UUID: Double] = [:]
        
        for result in textResults {
            combinedScores[result.article.id] = result.relevanceScore * 0.6
        }
        
        for result in semanticResults {
            let existingScore = combinedScores[result.article.id] ?? 0
            combinedScores[result.article.id] = existingScore + (result.relevanceScore * 0.4)
        }
        
        // Create final results
        var finalResults: [SearchResult] = []
        var addedArticles = Set<UUID>()
        
        for result in textResults + semanticResults {
            if !addedArticles.contains(result.article.id) {
                addedArticles.insert(result.article.id)
                let finalScore = combinedScores[result.article.id] ?? 0
                finalResults.append(SearchResult(article: result.article, relevanceScore: finalScore))
            }
        }
        
        return finalResults.sorted { $0.relevanceScore > $1.relevanceScore }
    }
}

// MARK: - Search Result Model

struct SearchResult {
    let article: Article
    let relevanceScore: Double
}