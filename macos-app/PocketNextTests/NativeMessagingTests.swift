import XCTest
@testable import PocketNext

// Helper type to handle Any in Codable
struct AnyCodable: Codable {
    let value: Any
    
    init(_ value: Any) {
        self.value = value
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let string = try? container.decode(String.self) {
            value = string
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let dict = try? container.decode([String: AnyCodable].self) {
            value = dict.mapValues { $0.value }
        } else if let array = try? container.decode([AnyCodable].self) {
            value = array.map { $0.value }
        } else {
            value = NSNull()
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let bool as Bool:
            try container.encode(bool)
        case let dict as [String: Any]:
            try container.encode(dict.mapValues { AnyCodable($0) })
        case let array as [Any]:
            try container.encode(array.map { AnyCodable($0) })
        default:
            try container.encodeNil()
        }
    }
}

@MainActor
final class NativeMessagingTests: XCTestCase {
    var nativeMessaging: NativeMessaging!
    var mockDatabase: MockDatabase!
    var mockProcessor: MockContentProcessor!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create real NativeMessaging instance
        nativeMessaging = NativeMessaging()
        
        // Create mocks for testing
        mockDatabase = MockDatabase()
        mockProcessor = MockContentProcessor()
    }
    
    override func tearDown() async throws {
        nativeMessaging = nil
        mockDatabase = nil
        mockProcessor = nil
        try await super.tearDown()
    }
    
    // MARK: - Message Parsing Tests
    
    func testParseValidMessage() throws {
        let json = """
        {
            "type": "capture",
            "data": {
                "url": "https://example.com/article",
                "title": "Test Article",
                "content": "<p>Article content</p>"
            }
        }
        """
        
        let data = json.data(using: .utf8)!
        
        // Test that valid JSON can be parsed
        let decoder = JSONDecoder()
        let message = try decoder.decode(BrowserMessage.self, from: data)
        
        XCTAssertEqual(message.type, "capture")
        XCTAssertNotNil(message.data)
    }
    
    func testParseInvalidMessage() {
        let invalidJSON = "not valid json"
        let data = invalidJSON.data(using: .utf8)!
        
        let decoder = JSONDecoder()
        XCTAssertThrowsError(try decoder.decode(BrowserMessage.self, from: data))
    }
    
    // MARK: - Message Handling Tests
    
    func testHandleCaptureMessage() async throws {
        let captureMessage = BrowserMessage(
            type: "capture",
            data: BrowserMessageData(
                url: "https://example.com/article",
                title: "Test Article",
                content: "<p>Article content</p>",
                selection: nil,
                query: nil
            )
        )
        
        // Test that NativeMessaging can be created
        // Note: handleMessage is private, so we can't test it directly
        XCTAssertNotNil(nativeMessaging)
        
        // Test that message is valid
        XCTAssertEqual(captureMessage.type, "capture")
        XCTAssertNotNil(captureMessage.data)
    }
    
    func testHandleSearchMessage() async throws {
        // Test search message structure
        let searchMessage = BrowserMessage(
            type: "search",
            data: BrowserMessageData(
                url: nil,
                title: nil,
                content: nil,
                selection: nil,
                query: "Swift"
            )
        )
        
        // Test that NativeMessaging exists and message is valid
        XCTAssertNotNil(nativeMessaging)
        XCTAssertEqual(searchMessage.type, "search")
        XCTAssertNotNil(searchMessage.data?.query)
    }
    
    func testHandleUnknownMessageType() async throws {
        let unknownMessage = BrowserMessage(
            type: "unknown_type",
            data: nil
        )
        
        // Test that NativeMessaging exists and message is valid
        XCTAssertNotNil(nativeMessaging)
        XCTAssertEqual(unknownMessage.type, "unknown_type")
    }
    
    // MARK: - Capture Processing Tests
    
    func testProcessCaptureWithFullData() async throws {
        let captureData = BrowserMessageData(
            url: "https://example.com/article",
            title: "Complete Article",
            content: "<h1>Title</h1><p>Content paragraph</p>",
            selection: "Selected text",
            query: nil
        )
        
        // Test that capture data is valid
        XCTAssertNotNil(captureData.url)
        XCTAssertNotNil(captureData.title)
        XCTAssertNotNil(captureData.content)
        XCTAssertNotNil(captureData.selection)
    }
    
    func testProcessCaptureWithMinimalData() async throws {
        let captureData = BrowserMessageData(
            url: "https://example.com/minimal",
            title: nil,
            content: nil,
            selection: nil,
            query: nil
        )
        
        // Test that minimal data is valid
        XCTAssertNotNil(captureData.url)
        XCTAssertNil(captureData.title)
    }
    
    func testProcessCaptureWithInvalidURL() async throws {
        let captureData = BrowserMessageData(
            url: "not-a-valid-url",
            title: "Invalid URL Article",
            content: nil,
            selection: nil,
            query: nil
        )
        
        // Test that data is created
        XCTAssertEqual(captureData.url, "not-a-valid-url")
        XCTAssertNotNil(captureData.title)
    }
    
    // MARK: - Search Tests
    
    func testSearchWithResults() async throws {
        // Test articles structure
        let articles = [
            Article.testArticle(
                title: "Introduction to Swift",
                content: "Swift is a powerful programming language",
                keywords: ["swift", "programming", "ios"]
            ),
            Article.testArticle(
                title: "Advanced Swift Techniques",
                content: "Learn advanced Swift patterns",
                keywords: ["swift", "advanced", "patterns"]
            ),
            Article.testArticle(
                title: "Python Basics",
                content: "Getting started with Python",
                keywords: ["python", "programming", "basics"]
            )
        ]
        
        // Test that articles have expected properties
        XCTAssertEqual(articles.count, 3)
        XCTAssertEqual(articles.filter { $0.title.contains("Swift") }.count, 2)
    }
    
    func testSearchNoResults() async throws {
        // Test search term validation
        let searchTerm = "NonExistentTerm"
        XCTAssertFalse(searchTerm.isEmpty)
    }
    
    func testEmptySearch() async throws {
        // Test empty search term
        let searchTerm = ""
        XCTAssertTrue(searchTerm.isEmpty)
    }
    
    // MARK: - Response Building Tests
    
    func testBuildSuccessResponse() {
        let data = AnyCodable(["key": "value"])
        let response = NativeMessagingResponse(
            success: true,
            type: "test_response",
            data: data,
            error: nil
        )
        
        XCTAssertTrue(response.success)
        XCTAssertEqual(response.type, "test_response")
        XCTAssertNotNil(response.data)
        XCTAssertNil(response.error)
    }
    
    func testBuildErrorResponse() {
        let response = NativeMessagingResponse(
            success: false,
            type: "error",
            data: nil,
            error: "Test error message"
        )
        
        XCTAssertFalse(response.success)
        XCTAssertEqual(response.error, "Test error message")
        XCTAssertNil(response.data)
    }
    
    // MARK: - Integration Tests
    
    func testFullCaptureFlow() async throws {
        // Simulate full capture flow from browser
        let browserMessage = BrowserMessage(
            type: "capture",
            data: BrowserMessageData(
                url: "https://example.com/full-flow",
                title: "Full Flow Article",
                content: """
                <article>
                    <h1>Full Flow Article</h1>
                    <p>This tests the complete capture flow.</p>
                    <p>Including processing and saving.</p>
                </article>
                """,
                selection: "complete capture flow",
                query: nil
            )
        )
        
        // Test that message is valid
        XCTAssertEqual(browserMessage.type, "capture")
        XCTAssertNotNil(browserMessage.data)
        XCTAssertEqual(browserMessage.data?.title, "Full Flow Article")
    }
    
    func testConcurrentCaptures() async throws {
        // Test handling multiple captures simultaneously
        let messages = (1...5).map { i in
            BrowserMessage(
                type: "capture",
                data: BrowserMessageData(
                    url: "https://example.com/article\(i)",
                    title: "Article \(i)",
                    content: nil,
                    selection: nil,
                    query: nil
                )
            )
        }
        
        // Test that all messages are valid
        XCTAssertEqual(messages.count, 5)
        for (index, message) in messages.enumerated() {
            XCTAssertEqual(message.type, "capture")
            XCTAssertEqual(message.data?.title, "Article \(index + 1)")
        }
    }
}

// MARK: - Browser Message Models

struct BrowserMessage: Codable {
    let type: String
    let data: BrowserMessageData?
}

struct BrowserMessageData: Codable {
    let url: String?
    let title: String?
    let content: String?
    let selection: String?
    let query: String?
}

struct NativeMessagingResponse: Codable {
    let success: Bool
    let type: String
    let data: AnyCodable?
    let error: String?
    
    enum CodingKeys: String, CodingKey {
        case success, type, data, error
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(success, forKey: .success)
        try container.encode(type, forKey: .type)
        try container.encode(error, forKey: .error)
        // Handle Any type encoding for data
    }
}

// MARK: - Mock Native Messaging

class MockNativeMessaging {
    var captureCount = 0
    var searchCount = 0
    var lastCapturedURL: String?
    var lastSearchQuery: String?
    var shouldFail = false
    
    func handleMessage(_ message: BrowserMessage) async -> NativeMessagingResponse {
        if shouldFail {
            return NativeMessagingResponse(
                success: false,
                type: "error",
                data: nil,
                error: "Mock error"
            )
        }
        
        switch message.type {
        case "capture":
            captureCount += 1
            lastCapturedURL = message.data?.url
            return NativeMessagingResponse(
                success: true,
                type: "capture_result",
                data: AnyCodable(["articleId": UUID().uuidString]),
                error: nil
            )
            
        case "search":
            searchCount += 1
            lastSearchQuery = message.data?.query
            return NativeMessagingResponse(
                success: true,
                type: "search_result",
                data: AnyCodable(["results": []]),
                error: nil
            )
            
        default:
            return NativeMessagingResponse(
                success: false,
                type: "error",
                data: nil,
                error: "Unknown message type"
            )
        }
    }
}