import Foundation

// Protocol for network operations
protocol NetworkSessionProtocol {
    func data(from url: URL) async throws -> (Data, URLResponse)
    func data(for request: URLRequest) async throws -> (Data, URLResponse)
}

// Extension to make URLSession conform to our protocol
extension URLSession: NetworkSessionProtocol {}

// Mock implementation for testing
class MockNetworkSession: NetworkSessionProtocol {
    var mockResponses: [String: (data: Data, response: URLResponse)] = [:]
    var requestLog: [URLRequest] = []
    var shouldFail = false
    var error: Error?
    
    func data(from url: URL) async throws -> (Data, URLResponse) {
        let request = URLRequest(url: url)
        return try await data(for: request)
    }
    
    func data(for request: URLRequest) async throws -> (Data, URLResponse) {
        requestLog.append(request)
        
        if shouldFail {
            throw error ?? URLError(.notConnectedToInternet)
        }
        
        let urlString = request.url?.absoluteString ?? ""
        
        // Check for exact match first
        if let mockResponse = mockResponses[urlString] {
            return mockResponse
        }
        
        // Check for partial matches
        for (pattern, response) in mockResponses {
            if urlString.contains(pattern) {
                return response
            }
        }
        
        // Default response
        let response = HTTPURLResponse(
            url: request.url!,
            statusCode: 404,
            httpVersion: nil,
            headerFields: nil
        )!
        
        return (Data(), response)
    }
    
    // Helper methods for setting up mocks
    func setMockResponse(for urlPattern: String, data: Data, statusCode: Int = 200) {
        let url = URL(string: urlPattern) ?? URL(string: "https://example.com")!
        let response = HTTPURLResponse(
            url: url,
            statusCode: statusCode,
            httpVersion: nil,
            headerFields: ["Content-Type": "application/json"]
        )!
        mockResponses[urlPattern] = (data, response)
    }
    
    func setMockJSONResponse(for urlPattern: String, json: Any, statusCode: Int = 200) throws {
        let data = try JSONSerialization.data(withJSONObject: json, options: .prettyPrinted)
        setMockResponse(for: urlPattern, data: data, statusCode: statusCode)
    }
}

// Mock response builders
extension MockNetworkSession {
    static func mockDuckDuckGoResponse() -> Data {
        let json = """
        {
            "Abstract": "Test abstract text about the query",
            "AbstractURL": "https://example.com/abstract",
            "Heading": "Test Heading",
            "RelatedTopics": [
                {
                    "Text": "Related topic 1",
                    "FirstURL": "https://example.com/topic1"
                },
                {
                    "Text": "Related topic 2",
                    "FirstURL": "https://example.com/topic2"
                }
            ]
        }
        """
        return json.data(using: .utf8)!
    }
    
    static func mockOpenAIResponse(text: String = "This is a test response from the mock LLM.") -> Data {
        let json = """
        {
            "id": "chatcmpl-test",
            "object": "chat.completion",
            "created": \(Int(Date().timeIntervalSince1970)),
            "model": "gpt-4o-mini",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "\(text)"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            }
        }
        """
        return json.data(using: .utf8)!
    }
}