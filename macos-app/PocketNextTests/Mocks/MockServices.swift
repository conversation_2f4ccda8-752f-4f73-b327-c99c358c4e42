import Foundation
@testable import PocketNext

// MARK: - Mock LLM Service

class MockLLMService {
    var shouldFail = false
    var mockResponse = "This is a mock LLM response."
    var requestCount = 0
    var lastPrompt: String?
    var lastContext: String?
    
    func generateResponse(prompt: String, context: String?, conversationHistory: [Message]?) async throws -> GeneratedResponse {
        requestCount += 1
        lastPrompt = prompt
        lastContext = context
        
        if shouldFail {
            throw LLMError.apiRequestFailed
        }
        
        return GeneratedResponse(
            text: mockResponse,
            tokensUsed: 100,
            modelUsed: "mock-model",
            inferenceTime: 0.1
        )
    }
}

// MARK: - Mock Embedding Service

class MockEmbeddingService {
    var shouldFail = false
    var mockEmbedding: [Float] = Array(repeating: 0.1, count: 384)
    var requestCount = 0
    var embeddings: [String: [Float]] = [:]
    
    func generateEmbedding(for text: String) async throws -> [Float] {
        requestCount += 1
        
        if shouldFail {
            throw EmbeddingError.modelNotLoaded
        }
        
        // Return cached embedding if available
        if let cached = embeddings[text] {
            return cached
        }
        
        // Generate deterministic embedding based on text
        var embedding = mockEmbedding
        let hash = text.hashValue
        for i in 0..<min(10, embedding.count) {
            embedding[i] = Float(hash % 100) / 100.0
        }
        
        embeddings[text] = embedding
        return embedding
    }
    
    func similarity(_ text1: String, _ text2: String) async throws -> Float {
        let emb1 = try await generateEmbedding(for: text1)
        let emb2 = try await generateEmbedding(for: text2)
        
        // Cosine similarity
        var dotProduct: Float = 0
        for i in 0..<emb1.count {
            dotProduct += emb1[i] * emb2[i]
        }
        
        return dotProduct
    }
}

// MARK: - Mock Web Search Service

class MockWebSearchService {
    var shouldFail = false
    var mockResults: [WebSearchResult] = []
    var requestCount = 0
    var lastQuery: String?
    
    func search(_ query: String, limit: Int = 5) async throws -> [WebSearchResult] {
        requestCount += 1
        lastQuery = query
        
        if shouldFail {
            throw SearchError.apiError
        }
        
        if !mockResults.isEmpty {
            return Array(mockResults.prefix(limit))
        }
        
        // Generate default mock results
        return (1...limit).map { i in
            WebSearchResult(
                title: "Result \(i) for: \(query)",
                url: "https://example.com/result\(i)",
                snippet: "This is a snippet for result \(i) about \(query)"
            )
        }
    }
}

// MARK: - Mock Content Processor

class MockContentProcessor {
    var shouldFail = false
    var mockContent = "This is the main content extracted from the webpage."
    var mockTitle = "Test Article Title"
    var mockSummary = "This is a test summary of the article."
    var mockKeywords = ["test", "mock", "article"]
    var mockAuthor = "Test Author"
    var mockReadingTime = 5
    
    func processContent(from html: String, url: String) async throws -> ProcessedContent {
        if shouldFail {
            throw ContentProcessingError.parsingFailed
        }
        
        return ProcessedContent(
            title: mockTitle,
            content: mockContent,
            summary: mockSummary,
            keywords: mockKeywords,
            author: mockAuthor,
            publishDate: Date(),
            readingTime: mockReadingTime,
            contentType: .article
        )
    }
}

// MARK: - Supporting Types

struct ProcessedContent {
    let title: String
    let content: String
    let summary: String
    let keywords: [String]
    let author: String?
    let publishDate: Date?
    let readingTime: Int
    let contentType: Article.ContentType
}

enum ContentProcessingError: Error {
    case parsingFailed
    case invalidHTML
    case unsupportedContentType
}

// MARK: - Mock Database

class MockDatabase {
    var articles: [Article] = []
    var digests: [ArticleDigest] = []
    var conversations: [ChatConversation] = []
    var shouldFail = false
    
    func save(_ article: Article) async throws {
        if shouldFail {
            throw DatabaseError.saveFailed
        }
        articles.append(article)
    }
    
    func fetchRecentArticles(limit: Int) async throws -> [Article] {
        if shouldFail {
            throw DatabaseError.fetchFailed
        }
        return Array(articles.prefix(limit))
    }
    
    func fetchArticle(id: UUID) async throws -> Article? {
        if shouldFail {
            throw DatabaseError.fetchFailed
        }
        return articles.first { $0.id == id }
    }
    
    func delete(_ id: UUID) async throws {
        if shouldFail {
            throw DatabaseError.deleteFailed
        }
        articles.removeAll { $0.id == id }
    }
}

enum DatabaseError: Error {
    case saveFailed
    case fetchFailed
    case deleteFailed
}

// MARK: - Test Helpers

extension Article {
    static func mock(
        id: UUID = UUID(),
        title: String = "Test Article",
        content: String = "Test content",
        summary: String = "Test summary",
        keywords: [String] = ["test"],
        author: String? = "Test Author",
        readingTime: Int = 5,
        capturedAt: Date = Date(),
        isRead: Bool = false,
        isArchived: Bool = false
    ) -> Article {
        Article(
            id: id,
            url: "https://example.com/\(id)",
            title: title,
            content: content,
            summary: summary,
            keywords: keywords,
            author: author,
            publishDate: Date(),
            readingTime: readingTime,
            contentType: .article,
            capturedAt: capturedAt,
            lastAccessedAt: isRead ? Date() : nil,
            isArchived: isArchived,
            syncStatus: .synced
        )
    }
}