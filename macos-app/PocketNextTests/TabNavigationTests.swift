import XCTest
import Swift<PERSON>
@testable import PocketNext

@MainActor
final class TabNavigationTests: XCTestCase {
    var appState: AppState!
    var database: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize database first
        database = try await DatabaseManager(inMemory: true)
        try await database.initialize()
        
        // Create AppState with initialized database
        appState = AppState()
        
        // Give AppState time to initialize
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
    }
    
    override func tearDown() async throws {
        appState = nil
        database = nil
        try await super.tearDown()
    }
    
    // MARK: - View Mode Navigation Tests
    
    func testHomeTabNavigation() async throws {
        // Set view mode to feed
        appState.viewMode = .feed
        
        // Verify state
        XCTAssertEqual(appState.viewMode, .feed)
        
        // Create the view to test if it crashes
        let contentView = ContentView()
            .environmentObject(appState)
        
        // Try to create HomeFeedView directly
        let homeFeedView = HomeFeedView(database: database)
            .environmentObject(appState)
        
        // If we get here, view creation didn't crash
        XCTAssertNotNil(homeFeedView)
    }
    
    func testDigestTabNavigation() async throws {
        // Set view mode to digest
        appState.viewMode = .digest
        
        XCTAssertEqual(appState.viewMode, .digest)
        
        // Try to create DigestView
        let digestView = DigestView(database: database)
            .environmentObject(appState)
        
        XCTAssertNotNil(digestView)
    }
    
    func testChatTabNavigation() async throws {
        // Set view mode to chat
        appState.viewMode = .chat
        
        XCTAssertEqual(appState.viewMode, .chat)
        
        // Try to create ChatView
        let chatView = ChatView()
            .environmentObject(appState)
        
        XCTAssertNotNil(chatView)
    }
    
    func testSearchNavigation() async throws {
        // Set view mode to search
        appState.viewMode = .search
        appState.searchQuery = "test"
        
        XCTAssertEqual(appState.viewMode, .search)
        
        // Try to create SearchResultsView
        let searchView = SearchResultsView()
            .environmentObject(appState)
        
        XCTAssertNotNil(searchView)
    }
    
    func testReadingViewNavigation() async throws {
        // Create a test article
        let article = Article.testArticle(
            title: "Test Article",
            content: "Test content"
        )
        
        // Save it to database
        try await database.save(article)
        
        // Set selected article and view mode
        appState.selectedArticle = article
        appState.viewMode = .reading
        
        XCTAssertEqual(appState.viewMode, .reading)
        XCTAssertNotNil(appState.selectedArticle)
        
        // Try to create ReadingView
        let readingView = ReadingView(article: article)
            .environmentObject(appState)
        
        XCTAssertNotNil(readingView)
    }
    
    // MARK: - Sequential Navigation Test
    
    func testSequentialViewModeChanges() async throws {
        // Test changing view modes in sequence
        let modes: [AppState.ViewMode] = [.feed, .digest, .chat, .search, .reading, .feed]
        
        for mode in modes {
            appState.viewMode = mode
            XCTAssertEqual(appState.viewMode, mode)
            
            // Give time for any async operations
            try await Task.sleep(nanoseconds: 10_000_000) // 0.01 second
        }
    }
    
    // MARK: - Error State Tests
    
    func testNavigationWithNilDatabase() async throws {
        // Test what happens if database is not properly initialized
        // This simulates the runtime error you're experiencing
        
        // Create a new AppState without proper initialization
        let brokenAppState = AppState()
        
        // Immediately try to access views
        brokenAppState.viewMode = .feed
        
        // This might crash if views try to access database immediately
        // Let's see what happens
        do {
            _ = HomeFeedView(database: brokenAppState.database)
                .environmentObject(brokenAppState)
            
            // If we get here, it didn't crash
            XCTAssertNotNil(brokenAppState.database)
        } catch {
            XCTFail("HomeFeedView creation threw error: \(error)")
        }
    }
    
    func testSidebarViewLoading() async throws {
        // Test sidebar view which loads statistics
        let sidebarView = SidebarView()
            .environmentObject(appState)
        
        XCTAssertNotNil(sidebarView)
        
        // The sidebar tries to load statistics on appear
        // This might be where the crash happens
        do {
            let stats = try await database.fetchStatistics()
            XCTAssertNotNil(stats)
        } catch {
            XCTFail("Failed to fetch statistics: \(error)")
        }
    }
    
    func testPopularTagsLoading() async throws {
        // Test popular tags view which uses SearchEngine
        let searchEngine = SearchEngine(database: database)
        
        do {
            let tags = try await searchEngine.getPopularKeywords()
            // Should return empty array for empty database
            XCTAssertNotNil(tags)
        } catch {
            XCTFail("Failed to get popular keywords: \(error)")
        }
    }
}