import XCTest
import GRDB
@testable import PocketNext

final class VectorStorageTests: XCTestCase {
    var databaseManager: DatabaseManager!
    var vectorStorage: VectorStorageManager!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Use the shared database manager
        databaseManager = DatabaseManager.shared
        try await databaseManager.initialize()
        
        // Initialize vector storage
        vectorStorage = VectorStorageManager(databaseManager: databaseManager)
        try await vectorStorage.initializeVectorStorage()
    }
    
    override func tearDown() async throws {
        // Clean up
        databaseManager = nil
        vectorStorage = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Schema Tests
    
    func testVectorStorageSchemaCreation() async throws {
        // Verify tables exist
        let tables = try await databaseManager.read { db in
            try String.fetchAll(db, sql: """
                SELECT name FROM sqlite_master
                WHERE type='table'
                AND name IN ('document_chunks', 'chunk_embeddings', 'chat_conversations', 'chat_messages')
                ORDER BY name
            """)
        }
        
        XCTAssertEqual(tables.count, 4)
        XCTAssertTrue(tables.contains("chat_conversations"))
        XCTAssertTrue(tables.contains("chat_messages"))
        XCTAssertTrue(tables.contains("chunk_embeddings"))
        XCTAssertTrue(tables.contains("document_chunks"))
    }
    
    // MARK: - Chunking Tests
    
    func testDocumentChunking() async throws {
        let articleId = UUID()
        let content = String(repeating: "This is a test sentence. ", count: 100)
        
        let chunks = try await vectorStorage.createDocumentChunks(
            articleId: articleId,
            content: content,
            chunkSize: 200,
            overlap: 50
        )
        
        XCTAssertGreaterThan(chunks.count, 0)
        
        // Verify chunks are saved
        let savedChunks = try await vectorStorage.getChunksForArticle(articleId)
        XCTAssertEqual(chunks.count, savedChunks.count)
        
        // Verify chunk properties
        for (index, chunk) in savedChunks.enumerated() {
            XCTAssertEqual(chunk.articleId, articleId)
            XCTAssertEqual(chunk.chunkIndex, index)
            XCTAssertFalse(chunk.content.isEmpty)
            XCTAssertGreaterThanOrEqual(chunk.endOffset, chunk.startOffset)
        }
    }
    
    func testChunkOverlap() async throws {
        let articleId = UUID()
        let content = "Word1 Word2 Word3 Word4 Word5 Word6 Word7 Word8 Word9 Word10"
        
        let chunks = try await vectorStorage.createDocumentChunks(
            articleId: articleId,
            content: content,
            chunkSize: 30,
            overlap: 10
        )
        
        XCTAssertGreaterThan(chunks.count, 1)
        
        // Check that chunks have overlapping content
        if chunks.count > 1 {
            let firstChunkWords = Set(chunks[0].content.split(separator: " "))
            let secondChunkWords = Set(chunks[1].content.split(separator: " "))
            let overlap = firstChunkWords.intersection(secondChunkWords)
            
            XCTAssertGreaterThan(overlap.count, 0, "Chunks should have overlapping content")
        }
    }
    
    // MARK: - Embedding Tests
    
    func testEmbeddingStorage() async throws {
        let chunkId = UUID()
        let embedding = Array(repeating: Float(0.5), count: 384)
        
        try await vectorStorage.storeEmbedding(
            chunkId: chunkId,
            embedding: embedding,
            modelVersion: "test-model"
        )
        
        // Verify embedding is saved
        let savedEmbedding = try await databaseManager.read { db in
            try ChunkEmbedding.fetchOne(db, key: chunkId)
        }
        
        XCTAssertNotNil(savedEmbedding)
        XCTAssertEqual(savedEmbedding?.chunkId, chunkId)
        XCTAssertEqual(savedEmbedding?.modelVersion, "test-model")
        XCTAssertEqual(savedEmbedding?.embedding.count, 384)
    }
    
    func testEmbeddingNormalization() async throws {
        let chunkId = UUID()
        let embedding = Array(0..<384).map { Float($0) }
        
        try await vectorStorage.storeEmbedding(
            chunkId: chunkId,
            embedding: embedding
        )
        
        let savedEmbedding = try await databaseManager.read { db in
            try ChunkEmbedding.fetchOne(db, key: chunkId)
        }
        
        // Verify embedding is normalized (magnitude ≈ 1)
        let magnitude = sqrt(savedEmbedding!.embedding.reduce(0) { $0 + $1 * $1 })
        XCTAssertEqual(magnitude, 1.0, accuracy: 0.001)
    }
    
    // MARK: - Vector Search Tests
    
    func testVectorSimilaritySearch() async throws {
        // Create test article and chunks
        let articleId = UUID()
        let testChunks = [
            "The quick brown fox jumps over the lazy dog",
            "Machine learning is transforming technology",
            "Swift programming language for iOS development",
            "Database management systems store data efficiently"
        ]
        
        // Create and store chunks with embeddings
        for (index, content) in testChunks.enumerated() {
            let chunk = DocumentChunk(
                id: UUID(),
                articleId: articleId,
                chunkIndex: index,
                content: content,
                startOffset: 0,
                endOffset: content.count,
                metadata: nil,
                createdAt: Date()
            )
            
            try await databaseManager.write { db in
                try chunk.save(db)
            }
            
            // Create unique embedding for each chunk
            var embedding = Array(repeating: Float(0.1), count: 384)
            embedding[index % 384] = 1.0 // Make each embedding distinct
            
            try await vectorStorage.storeEmbedding(
                chunkId: chunk.id,
                embedding: embedding
            )
        }
        
        // Search with a query embedding
        var queryEmbedding = Array(repeating: Float(0.1), count: 384)
        queryEmbedding[0] = 1.0 // Should match first chunk best
        
        let results = try await vectorStorage.searchSimilarChunks(
            queryEmbedding: queryEmbedding,
            topK: 2,
            threshold: 0.5
        )
        
        XCTAssertGreaterThan(results.count, 0)
        XCTAssertLessThanOrEqual(results.count, 2)
        
        // Verify results are sorted by score
        for i in 1..<results.count {
            XCTAssertLessThanOrEqual(results[i].score, results[i-1].score)
        }
    }
    
    // MARK: - Article Indexing Tests
    
    func testArticleIndexing() async throws {
        let article = Article(
            id: UUID(),
            url: "https://example.com/test",
            title: "Test Article",
            content: String(repeating: "This is test content. ", count: 50),
            summary: "Test summary",
            keywords: ["test", "article"],
            author: "Test Author",
            publishDate: Date(),
            readingTime: 5,
            contentType: .article,
            capturedAt: Date()
        )
        
        try await databaseManager.save(article)
        try await vectorStorage.indexArticle(article)
        
        // Verify chunks were created
        let chunks = try await vectorStorage.getChunksForArticle(article.id)
        XCTAssertGreaterThan(chunks.count, 0)
        
        // Verify embeddings were created for each chunk
        for chunk in chunks {
            let embedding = try await databaseManager.read { db in
                try ChunkEmbedding.fetchOne(db, key: chunk.id)
            }
            XCTAssertNotNil(embedding)
        }
    }
    
    // MARK: - Performance Tests
    
    func testChunkingPerformance() async throws {
        let articleId = UUID()
        let largeContent = String(repeating: "This is a performance test sentence. ", count: 1000)
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let chunks = try await vectorStorage.createDocumentChunks(
            articleId: articleId,
            content: largeContent
        )
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        print("Created \(chunks.count) chunks in \(timeElapsed) seconds")
        XCTAssertLessThan(timeElapsed, 1.0, "Chunking should complete within 1 second")
    }
    
    func testSearchPerformance() async throws {
        // Create 100 test chunks
        let articleId = UUID()
        for i in 0..<100 {
            let chunk = DocumentChunk(
                id: UUID(),
                articleId: articleId,
                chunkIndex: i,
                content: "Test chunk \(i)",
                startOffset: 0,
                endOffset: 100,
                metadata: nil,
                createdAt: Date()
            )
            
            try await databaseManager.write { db in
                try chunk.save(db)
            }
            
            let embedding = Array(repeating: Float(0.1), count: 384)
            try await vectorStorage.storeEmbedding(chunkId: chunk.id, embedding: embedding)
        }
        
        let queryEmbedding = Array(repeating: Float(0.2), count: 384)
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let results = try await vectorStorage.searchSimilarChunks(
            queryEmbedding: queryEmbedding,
            topK: 10
        )
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        print("Searched 100 chunks in \(timeElapsed) seconds, found \(results.count) results")
        XCTAssertLessThan(timeElapsed, 0.1, "Search should complete within 100ms")
    }
}

