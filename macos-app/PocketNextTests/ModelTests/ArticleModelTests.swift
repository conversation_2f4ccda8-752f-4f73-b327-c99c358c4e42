import XCTest
import GRDB
@testable import PocketNext

class ArticleModelTests: XCTestCase {
    var databaseManager: DatabaseManager!
    
    override func setUp() async throws {
        try await super.setUp()
        databaseManager = try await DatabaseTestHelpers.createTestDatabase()
        try await databaseManager.initialize()
    }
    
    override func tearDown() async throws {
        databaseManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Basic Model Tests
    
    func testArticleInitialization() {
        // Given
        let id = UUID()
        let url = "https://example.com/article"
        let title = "Test Article"
        let content = "This is test content"
        let summary = "A brief summary"
        let keywords = ["test", "article", "swift"]
        
        // When
        let article = Article(
            id: id,
            url: url,
            title: title,
            content: content,
            summary: summary,
            keywords: keywords,
            readingTime: 5,
            contentType: .article
        )
        
        // Then
        XCTAssertEqual(article.id, id)
        XCTAssertEqual(article.url, url)
        XCTAssertEqual(article.title, title)
        XCTAssertEqual(article.content, content)
        XCTAssertEqual(article.summary, summary)
        XCTAssertEqual(article.keywords, keywords)
        XCTAssertEqual(article.readingTime, 5)
        XCTAssertEqual(article.contentType, .article)
        XCTAssertFalse(article.isArchived)
        XCTAssertEqual(article.syncStatus, .pending)
        XCTAssertNil(article.embeddingData)
        XCTAssertNil(article.embeddingModelVersion)
        XCTAssertFalse(article.hasLocalEmbedding)
    }
    
    func testArticleWithEmbedding() {
        // Given
        let embedding = Array(repeating: Float(0.5), count: 1536)
        let compressedData = VectorOperations.compressEmbedding(embedding)
        
        // When
        let article = Article.testArticle(
            embeddingData: compressedData,
            embeddingModelVersion: "openai-text-embedding-3-small-v1",
            hasLocalEmbedding: true
        )
        
        // Then
        XCTAssertNotNil(article.embeddingData)
        XCTAssertEqual(article.embeddingModelVersion, "openai-text-embedding-3-small-v1")
        XCTAssertTrue(article.hasLocalEmbedding)
        
        // Verify compression worked
        XCTAssertLessThan(article.embeddingData!.count, embedding.count * 4)
    }
    
    // MARK: - GRDB Persistence Tests
    
    func testArticleSaveAndFetch() async throws {
        // Given
        let article = Article.testArticleWithEmbedding(
            title: "Persisted Article",
            keywords: ["persistence", "test"]
        )
        
        // When
        try await databaseManager.save(article)
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        
        // Then
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.id, article.id)
        XCTAssertEqual(fetched?.title, article.title)
        XCTAssertEqual(fetched?.keywords, article.keywords)
        XCTAssertNotNil(fetched?.embeddingData)
        XCTAssertEqual(fetched?.embeddingModelVersion, article.embeddingModelVersion)
        XCTAssertEqual(fetched?.hasLocalEmbedding, article.hasLocalEmbedding)
    }
    
    func testArticleUpdate() async throws {
        // Given
        var article = Article.testArticle()
        try await databaseManager.save(article)
        
        // When - Update with embedding data
        let embedding = MockDataGenerator.generateMockEmbeddings()
        article.embeddingData = VectorOperations.compressEmbedding(embedding)
        article.embeddingModelVersion = "updated-model-v2"
        article.hasLocalEmbedding = true
        
        try await databaseManager.updateArticle(article)
        
        // Then
        let updated = try await databaseManager.fetchArticle(id: article.id)
        XCTAssertNotNil(updated?.embeddingData)
        XCTAssertEqual(updated?.embeddingModelVersion, "updated-model-v2")
        XCTAssertTrue(updated?.hasLocalEmbedding ?? false)
    }
    
    // MARK: - Computed Properties Tests
    
    func testDomainExtraction() {
        // Given
        let testCases = [
            ("https://example.com/article", "example.com"),
            ("https://blog.example.com/post", "blog.example.com"),
            ("https://example.com:8080/page", "example.com"),
            ("invalid-url", "")
        ]
        
        // When/Then
        for (url, expectedDomain) in testCases {
            let article = Article.testArticle(url: url)
            XCTAssertEqual(article.domain, expectedDomain)
        }
    }
    
    func testIsReadProperty() {
        // Given
        let unreadArticle = Article.testArticle(lastAccessedAt: nil)
        let readArticle = Article.testArticle(lastAccessedAt: Date())
        
        // Then
        XCTAssertFalse(unreadArticle.isRead)
        XCTAssertTrue(readArticle.isRead)
    }
    
    // MARK: - ContentType Tests
    
    func testContentTypeIcons() {
        // Test all content types have icons
        for contentType in Article.ContentType.allCases {
            XCTAssertFalse(contentType.icon.isEmpty)
        }
    }
    
    // MARK: - Embedding Compression Tests
    
    func testEmbeddingCompressionRoundTrip() async throws {
        // Given
        let originalEmbedding = MockDataGenerator.generateMockEmbeddings()
        let article = Article.testArticle(
            embeddingData: VectorOperations.compressEmbedding(originalEmbedding),
            embeddingModelVersion: "test-v1",
            hasLocalEmbedding: true
        )
        
        // When
        try await databaseManager.save(article)
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        
        // Then
        XCTAssertNotNil(fetched?.embeddingData)
        
        // Decompress and verify
        let decompressed = VectorOperations.decompressEmbedding(fetched!.embeddingData!)
        XCTAssertNotNil(decompressed)
        XCTAssertEqual(decompressed?.count, originalEmbedding.count)
    }
    
    // MARK: - Migration Tests
    
    func testArticleWithoutEmbedding() async throws {
        // Given - Article saved before embedding support
        let article = Article.testArticle()
        try await databaseManager.save(article)
        
        // When
        let fetched = try await databaseManager.fetchArticle(id: article.id)
        
        // Then
        XCTAssertNil(fetched?.embeddingData)
        XCTAssertNil(fetched?.embeddingModelVersion)
        XCTAssertFalse(fetched?.hasLocalEmbedding ?? true)
    }
    
    // MARK: - Batch Operations Tests
    
    func testBatchArticleSave() async throws {
        // Given
        let articles = (0..<10).map { i in
            Article.testArticleWithEmbedding(
                title: "Batch Article \(i)",
                keywords: ["batch", "test", "\(i)"]
            )
        }
        
        // When
        for article in articles {
            try await databaseManager.save(article)
        }
        
        // Then
        let allArticles = try await databaseManager.fetchRecentArticles(limit: 20)
        XCTAssertGreaterThanOrEqual(allArticles.count, 10)
        
        // Verify all have embeddings
        let batchArticles = allArticles.filter { $0.title.contains("Batch Article") }
        XCTAssertTrue(batchArticles.allSatisfy { $0.embeddingData != nil })
    }
    
    // MARK: - Edge Cases
    
    func testArticleWithLargeEmbedding() {
        // Given - Larger embedding dimension
        let largeEmbedding = Array(repeating: Float(0.5), count: 3072)
        let compressedData = VectorOperations.compressEmbedding(largeEmbedding)
        
        // When
        let article = Article.testArticle(embeddingData: compressedData)
        
        // Then
        XCTAssertNotNil(article.embeddingData)
        // Should still compress to roughly 1/4 size
        XCTAssertLessThan(article.embeddingData!.count, largeEmbedding.count)
    }
    
    func testArticleEquality() {
        // Given
        let id = UUID()
        let article1 = Article.testArticle(id: id)
        let article2 = Article.testArticle(id: id)
        let article3 = Article.testArticle() // Different ID
        
        // Then
        XCTAssertEqual(article1.id, article2.id)
        XCTAssertNotEqual(article1.id, article3.id)
    }
}