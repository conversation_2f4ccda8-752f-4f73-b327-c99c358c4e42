import XCTest
import SwiftUI
import Combine
@testable import PocketNext

@MainActor
class AppStateTests: XCTestCase {
    
    var sut: AppState!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        try await super.setUp()
        sut = AppState()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        sut = nil
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testDefaultInitialization() {
        XCTAssertEqual(sut.viewMode, .feed)
        XCTAssertNil(sut.selectedArticle)
        XCTAssertEqual(sut.articles, [])
        XCTAssertEqual(sut.searchQuery, "")
        XCTAssertFalse(sut.isCapturing)
        XCTAssertNil(sut.lastCapturedArticle)
        XCTAssertFalse(sut.isSyncing)
        XCTAssertNil(sut.lastSyncDate)
        XCTAssertNil(sut.syncError)
    }
    
    // MARK: - ViewMode Tests
    
    func testViewModeEnum() {
        XCTAssertEqual(AppState.ViewMode.feed.rawValue, "feed")
        XCTAssertEqual(AppState.ViewMode.digest.rawValue, "digest")
        XCTAssertEqual(AppState.ViewMode.search.rawValue, "search")
        XCTAssertEqual(AppState.ViewMode.chat.rawValue, "chat")
        XCTAssertEqual(AppState.ViewMode.reading.rawValue, "reading")
    }
    
    func testViewModeChanges() {
        sut.viewMode = .digest
        XCTAssertEqual(sut.viewMode, .digest)
        
        sut.viewMode = .search
        XCTAssertEqual(sut.viewMode, .search)
        
        sut.viewMode = .chat
        XCTAssertEqual(sut.viewMode, .chat)
        
        sut.viewMode = .reading
        XCTAssertEqual(sut.viewMode, .reading)
        
        sut.viewMode = .feed
        XCTAssertEqual(sut.viewMode, .feed)
    }
    
    // MARK: - Article Selection Tests
    
    func testArticleSelection() {
        let article = createTestArticle(title: "Test Article")
        
        sut.selectedArticle = article
        XCTAssertNotNil(sut.selectedArticle)
        XCTAssertEqual(sut.selectedArticle?.id, article.id)
        
        sut.selectedArticle = nil
        XCTAssertNil(sut.selectedArticle)
    }
    
    // MARK: - Capture State Tests
    
    func testTriggerCapture() {
        XCTAssertFalse(sut.isCapturing)
        
        sut.triggerCapture()
        
        XCTAssertTrue(sut.isCapturing)
    }
    
    func testLastCapturedArticle() {
        let article = createTestArticle(title: "Captured")
        
        sut.lastCapturedArticle = article
        XCTAssertNotNil(sut.lastCapturedArticle)
        XCTAssertEqual(sut.lastCapturedArticle?.id, article.id)
    }
    
    // MARK: - Search Tests
    
    func testSearchQueryDebounce() async {
        let expectation = expectation(description: "Search query debounced")
        expectation.isInverted = true // Should NOT trigger immediately
        
        sut.$searchQuery
            .dropFirst() // Skip initial value
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        sut.searchQuery = "test"
        
        // Wait less than debounce time (300ms)
        await fulfillment(of: [expectation], timeout: 0.2)
    }
    
    func testSearchQueryClearsViewMode() async {
        sut.viewMode = .search
        sut.searchQuery = "test"
        
        // Clear search query
        sut.searchQuery = ""
        
        // Wait for debounce
        try? await Task.sleep(nanoseconds: 400_000_000)
        
        XCTAssertEqual(sut.viewMode, .feed)
    }
    
    // MARK: - Delete Article Tests
    
    func testDeleteArticle() async {
        // Create test articles
        let article1 = createTestArticle(title: "Article 1")
        let article2 = createTestArticle(title: "Article 2")
        sut.articles = [article1, article2]
        sut.selectedArticle = article1
        
        // Delete article1
        await sut.deleteArticle(article1)
        
        // Should remove from articles array
        XCTAssertEqual(sut.articles.count, 1)
        XCTAssertEqual(sut.articles.first?.id, article2.id)
        
        // Should clear selection if deleted article was selected
        XCTAssertNil(sut.selectedArticle)
    }
    
    func testDeleteNonSelectedArticle() async {
        let article1 = createTestArticle(title: "Article 1")
        let article2 = createTestArticle(title: "Article 2")
        sut.articles = [article1, article2]
        sut.selectedArticle = article1
        
        // Delete article2 (not selected)
        await sut.deleteArticle(article2)
        
        // Selection should remain
        XCTAssertNotNil(sut.selectedArticle)
        XCTAssertEqual(sut.selectedArticle?.id, article1.id)
    }
    
    // MARK: - Archive Article Tests
    
    func testArchiveArticle() async {
        var article = createTestArticle(title: "Test")
        article.isArchived = false
        sut.articles = [article]
        
        await sut.archiveArticle(article)
        
        // Article should be marked as archived
        XCTAssertTrue(sut.articles.first?.isArchived ?? false)
    }
    
    func testUnarchiveArticle() async {
        var article = createTestArticle(title: "Test")
        article.isArchived = true
        sut.articles = [article]
        
        await sut.unarchiveArticle(article)
        
        // Article should be unmarked as archived
        XCTAssertFalse(sut.articles.first?.isArchived ?? true)
    }
    
    // MARK: - Sync State Tests
    
    func testSyncStateProperties() {
        sut.isSyncing = true
        XCTAssertTrue(sut.isSyncing)
        
        sut.lastSyncDate = Date()
        XCTAssertNotNil(sut.lastSyncDate)
        
        let error = NSError(domain: "test", code: 1, userInfo: nil)
        sut.syncError = error
        XCTAssertNotNil(sut.syncError)
    }
    
    // MARK: - Computed Properties Tests
    
    func testUnreadCount() {
        var article1 = createTestArticle(title: "Read")
        article1.lastAccessedAt = Date()
        
        let article2 = createTestArticle(title: "Unread")
        // article2.lastAccessedAt is nil, so it's unread
        
        sut.articles = [article1, article2]
        
        XCTAssertEqual(sut.unreadCount, 1)
    }
    
    func testArchivedCount() {
        var article1 = createTestArticle(title: "Active")
        article1.isArchived = false
        
        var article2 = createTestArticle(title: "Archived")
        article2.isArchived = true
        
        sut.articles = [article1, article2]
        
        XCTAssertEqual(sut.archivedCount, 1)
    }
    
    func testTotalReadingTime() {
        let article1 = createTestArticle(title: "Article 1", readingTime: 5)
        let article2 = createTestArticle(title: "Article 2", readingTime: 10)
        let article3 = createTestArticle(title: "Article 3", readingTime: 3)
        
        sut.articles = [article1, article2, article3]
        
        XCTAssertEqual(sut.totalReadingTime, 18)
    }
    
    // MARK: - ArticlesForCurrentView Tests
    
    func testArticlesForCurrentViewFeed() {
        var article1 = createTestArticle(title: "Active")
        article1.isArchived = false
        
        var article2 = createTestArticle(title: "Archived")
        article2.isArchived = true
        
        sut.articles = [article1, article2]
        sut.viewMode = .feed
        
        let visibleArticles = sut.articlesForCurrentView()
        
        // Feed view should exclude archived articles
        XCTAssertEqual(visibleArticles.count, 1)
        XCTAssertEqual(visibleArticles.first?.title, "Active")
    }
    
    func testArticlesForCurrentViewOtherModes() {
        var article1 = createTestArticle(title: "Active")
        article1.isArchived = false
        
        var article2 = createTestArticle(title: "Archived")
        article2.isArchived = true
        
        sut.articles = [article1, article2]
        
        // Test other view modes show all articles
        for mode in [AppState.ViewMode.digest, .search, .chat, .reading] {
            sut.viewMode = mode
            let visibleArticles = sut.articlesForCurrentView()
            XCTAssertEqual(visibleArticles.count, 2)
        }
    }
    
    // MARK: - ObservableObject Tests
    
    func testPublishedProperties() {
        // Verify AppState conforms to ObservableObject
        XCTAssertTrue(sut is ObservableObject)
        
        // Test that changing published properties triggers updates
        let expectation = expectation(description: "Published property changed")
        
        let cancellable = sut.objectWillChange.sink { _ in
            expectation.fulfill()
        }
        
        sut.viewMode = .search
        
        wait(for: [expectation], timeout: 1.0)
        cancellable.cancel()
    }
    
    // MARK: - Settings Tests (AppStorage)
    
    func testPreferredLayoutDefault() {
        // Note: Testing @AppStorage is limited in unit tests
        // These properties should have default values
        XCTAssertEqual(sut.preferredLayout, FeedLayout.grid)
    }
    
    func testPreferredSortOrderDefault() {
        XCTAssertEqual(sut.preferredSortOrder, SortOrder.newest)
    }
    
    func testEnableCloudSyncDefault() {
        XCTAssertTrue(sut.enableCloudSync)
    }
    
    // MARK: - Refresh Articles Tests
    
    func testRefreshArticles() async {
        // This would normally test database interaction
        // For now, just verify the method exists and doesn't crash
        await sut.refreshArticles()
        
        XCTAssertTrue(true) // Verify no crash
    }
    
    // MARK: - Helper Methods
    
    private func createTestArticle(
        title: String,
        readingTime: Int = 5,
        isArchived: Bool = false
    ) -> Article {
        Article(
            url: "https://test.com/\(title.lowercased().replacingOccurrences(of: " ", with: "-"))",
            title: title,
            content: "Content for \(title)",
            summary: "Summary for \(title)",
            keywords: ["test"],
            readingTime: readingTime,
            contentType: .article,
            isArchived: isArchived
        )
    }
}

// MARK: - Mock AppState Tests

#if DEBUG
extension AppStateTests {
    
    func testMockAppState() {
        let mockState = AppState.mock()
        
        // Should have pre-populated articles
        XCTAssertGreaterThan(mockState.articles.count, 0)
        
        // Test first mock article
        let firstArticle = mockState.articles.first
        XCTAssertNotNil(firstArticle)
        XCTAssertEqual(firstArticle?.title, "What's New in Swift 6")
        XCTAssertEqual(firstArticle?.contentType, .article)
        XCTAssertEqual(firstArticle?.readingTime, 8)
        
        // Test article variety
        let contentTypes = Set(mockState.articles.map { $0.contentType })
        XCTAssertEqual(contentTypes, [.article]) // All mock articles are .article type
        
        // Test sync statuses
        let syncStatuses = Set(mockState.articles.map { $0.syncStatus })
        XCTAssertTrue(syncStatuses.contains(.synced))
        XCTAssertTrue(syncStatuses.contains(.pending))
    }
}
#endif

// MARK: - Async Observer Tests

extension AppStateTests {
    
    func testCaptureStateObserver() async {
        // Set up expectation for article refresh
        let expectation = expectation(description: "Articles refreshed after capture")
        expectation.isInverted = true // Should not trigger while capturing
        
        sut.$articles
            .dropFirst() // Skip initial value
            .sink { _ in
                if !self.sut.isCapturing {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // Start capturing
        sut.isCapturing = true
        
        // Should not refresh while capturing
        await fulfillment(of: [expectation], timeout: 0.5)
        
        // Now test that it does refresh when capture ends
        let refreshExpectation = expectation(description: "Articles refreshed")
        
        sut.$articles
            .dropFirst()
            .sink { _ in
                refreshExpectation.fulfill()
            }
            .store(in: &cancellables)
        
        // Stop capturing
        sut.isCapturing = false
        
        // Should trigger refresh (in real app, would load from database)
        await fulfillment(of: [refreshExpectation], timeout: 1.0)
    }
}