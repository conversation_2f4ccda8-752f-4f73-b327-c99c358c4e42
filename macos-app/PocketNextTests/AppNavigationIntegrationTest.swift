import XCTest
import Swift<PERSON>
@testable import PocketNext

@MainActor
final class AppNavigationIntegrationTest: XCTestCase {
    
    func testAppStateInitializationAndNavigation() async throws {
        // Create AppState which should use DatabaseManager.shared
        let appState = AppState()
        
        // Give time for initialization
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Test that database is initialized
        XCTAssertNotNil(appState.database)
        
        // Test navigation to each view mode without crashes
        
        // 1. Home/Feed view
        appState.viewMode = .feed
        XCTAssertEqual(appState.viewMode, .feed)
        
        // Create HomeFeedView - this was crashing before
        let homeFeedView = HomeFeedView(database: appState.database)
            .environmentObject(appState)
        XCTAssertNotNil(homeFeedView)
        
        // 2. Digest view
        appState.viewMode = .digest
        XCTAssertEqual(appState.viewMode, .digest)
        
        let digestView = DigestView(database: appState.database)
            .environmentObject(appState)
        XCTAssertNotNil(digestView)
        
        // 3. Chat view
        appState.viewMode = .chat
        XCTAssertEqual(appState.viewMode, .chat)
        
        let chatView = ChatView()
            .environmentObject(appState)
        XCTAssertNotNil(chatView)
        
        // 4. Search view
        appState.viewMode = .search
        appState.searchQuery = "test"
        XCTAssertEqual(appState.viewMode, .search)
        
        let searchView = SearchResultsView()
            .environmentObject(appState)
        XCTAssertNotNil(searchView)
        
        // 5. Test ContentView creation
        let contentView = ContentView()
            .environmentObject(appState)
        XCTAssertNotNil(contentView)
        
        // 6. Test SidebarView statistics loading
        let sidebarView = SidebarView()
            .environmentObject(appState)
        XCTAssertNotNil(sidebarView)
        
        // Test that database operations work
        do {
            let stats = try await appState.database.fetchStatistics()
            XCTAssertNotNil(stats)
            XCTAssertGreaterThanOrEqual(stats.totalArticles, 0)
        } catch {
            XCTFail("Failed to fetch statistics: \(error)")
        }
    }
    
    func testFeedCurationServiceInitialization() async throws {
        // This was another potential crash point
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Create FeedCurationService
        let curationService = FeedCurationService(database: appState.database)
        XCTAssertNotNil(curationService)
        
        // Test that it can be used
        await curationService.refreshFeed()
        XCTAssertFalse(curationService.isProcessing)
    }
    
    func testSearchEngineInitialization() async throws {
        // Test SearchEngine used in PopularTagsView
        let appState = AppState()
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        let searchEngine = SearchEngine(database: appState.database)
        
        do {
            let popularKeywords = try await searchEngine.getPopularKeywords()
            // Should work even with empty database
            XCTAssertNotNil(popularKeywords)
        } catch {
            XCTFail("SearchEngine.getPopularKeywords failed: \(error)")
        }
    }
}