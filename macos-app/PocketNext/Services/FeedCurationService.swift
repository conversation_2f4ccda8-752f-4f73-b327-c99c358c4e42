import Foundation
import CoreML

@MainActor
class FeedCurationService: ObservableObject {
    @Published var isProcessing = false
    @Published var topForYouArticles: [Article] = []
    @Published var lastUpdateDate: Date?
    
    private let database: DatabaseManager
    private let embeddingService: EmbeddingService
    
    // Curation parameters
    private let topForYouLimit = 20
    private let recentWindowDays = 30
    private let minReadingTime = 3
    private let maxReadingTime = 30
    
    // User behavior tracking
    private var userProfile: UserReadingProfile?
    
    init(database: DatabaseManager) {
        self.database = database
        self.embeddingService = EmbeddingService()
        
        Task {
            await loadUserProfile()
            await updateTopForYouFeed()
        }
    }
    
    // MARK: - Feed Curation Algorithm
    
    func updateTopForYouFeed() async {
        isProcessing = true
        defer { isProcessing = false }
        
        do {
            // 1. Load user profile
            if userProfile == nil {
                await loadUserProfile()
            }
            
            // 2. Fetch recent unread articles
            let candidates = try await fetchCandidateArticles()
            
            // 3. Score each article
            let scoredArticles = await scoreArticles(candidates)
            
            // 4. Apply diversity boosting
            let diversifiedArticles = applyDiversityBoosting(scoredArticles)
            
            // 5. Select top articles
            topForYouArticles = Array(diversifiedArticles.prefix(topForYouLimit))
            
            lastUpdateDate = Date()
            
        } catch {
            print("Failed to update Top For You feed: \(error)")
        }
    }
    
    // MARK: - Scoring Algorithm
    
    private func scoreArticles(_ articles: [Article]) async -> [(article: Article, score: Double)] {
        var scoredArticles: [(article: Article, score: Double)] = []
        
        for article in articles {
            let score = await calculateArticleScore(article)
            scoredArticles.append((article, score))
        }
        
        // Sort by score descending
        return scoredArticles.sorted { $0.score > $1.score }
    }
    
    private func calculateArticleScore(_ article: Article) async -> Double {
        var score = 0.0
        
        // 1. Recency Score (0-25 points)
        let daysSinceCapture = Date().timeIntervalSince(article.capturedAt) / 86400
        let recencyScore = max(0, 25 - (daysSinceCapture * 2))
        score += recencyScore
        
        // 2. Reading Time Score (0-20 points)
        let readingTimeScore = calculateReadingTimeScore(article.readingTime)
        score += readingTimeScore
        
        // 3. Keyword Relevance Score (0-30 points)
        if let profile = userProfile {
            let relevanceScore = calculateKeywordRelevance(article.keywords, profile: profile)
            score += relevanceScore
        }
        
        // 4. Author Affinity Score (0-15 points)
        if let author = article.author, let profile = userProfile {
            let authorScore = calculateAuthorAffinity(author, profile: profile)
            score += authorScore
        }
        
        // 5. Content Similarity Score (0-20 points)
        if embeddingService.isModelLoaded {
            let similarityScore = await calculateContentSimilarity(article)
            score += similarityScore
        }
        
        // 6. Time of Day Score (0-10 points)
        let timeScore = calculateTimeOfDayScore(article)
        score += timeScore
        
        // Penalties
        
        // Already read penalty
        if article.lastAccessedAt != nil {
            score *= 0.1 // Heavy penalty for already read articles
        }
        
        // Archived penalty
        if article.isArchived {
            score *= 0.05 // Very heavy penalty for archived articles
        }
        
        return score
    }
    
    // MARK: - Scoring Components
    
    private func calculateReadingTimeScore(_ readingTime: Int) -> Double {
        // Prefer articles between 5-15 minutes
        if readingTime >= 5 && readingTime <= 15 {
            return 20.0
        } else if readingTime >= 3 && readingTime <= 20 {
            return 15.0
        } else if readingTime <= 2 {
            return 5.0 // Too short
        } else {
            return 8.0 // Too long
        }
    }
    
    private func calculateKeywordRelevance(_ keywords: [String], profile: UserReadingProfile) -> Double {
        var score = 0.0
        let maxScore = 30.0
        
        for keyword in keywords {
            if let interest = profile.keywordInterests[keyword.lowercased()] {
                score += interest * 10.0
            }
        }
        
        return min(score, maxScore)
    }
    
    private func calculateAuthorAffinity(_ author: String, profile: UserReadingProfile) -> Double {
        if let affinity = profile.authorAffinities[author] {
            return affinity * 15.0
        }
        return 5.0 // Neutral score for unknown authors
    }
    
    private func calculateContentSimilarity(_ article: Article) async -> Double {
        guard let profile = userProfile else { return 0.0 }
        
        // Compare article embedding with user's preferred content embeddings
        // This is simplified - in production, you'd use actual embeddings
        
        // For now, use keyword overlap as a proxy
        let keywords = article.keywords
        
        var overlapScore = 0.0
        for keyword in keywords {
            if profile.topKeywords.contains(keyword.lowercased()) {
                overlapScore += 4.0
            }
        }
        
        return min(overlapScore, 20.0)
    }
    
    private func calculateTimeOfDayScore(_ article: Article) -> Double {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        
        // Prefer longer reads in evening, shorter in morning
        if hour >= 6 && hour <= 9 {
            // Morning: prefer short reads
            return article.readingTime <= 5 ? 10.0 : 5.0
        } else if hour >= 18 && hour <= 22 {
            // Evening: prefer longer reads
            return article.readingTime >= 10 ? 10.0 : 5.0
        } else {
            // Neutral time
            return 7.0
        }
    }
    
    // MARK: - Diversity Boosting
    
    private func applyDiversityBoosting(_ scoredArticles: [(article: Article, score: Double)]) -> [Article] {
        var selected: [Article] = []
        var usedKeywords = Set<String>()
        var usedAuthors = Set<String>()
        
        for (article, baseScore) in scoredArticles {
            var adjustedScore = baseScore
            
            // Penalize if we already have articles with similar keywords
            let overlap = article.keywords.filter { usedKeywords.contains($0.lowercased()) }.count
            adjustedScore *= pow(0.8, Double(overlap))
            
            // Penalize if we already have articles from the same author
            if let author = article.author, usedAuthors.contains(author) {
                adjustedScore *= 0.7
            }
            
            // Add to selected if score is still good
            if adjustedScore > 20.0 || selected.count < 5 {
                selected.append(article)
                
                // Update used sets
                article.keywords.forEach { usedKeywords.insert($0.lowercased()) }
                if let author = article.author {
                    usedAuthors.insert(author)
                }
            }
            
            if selected.count >= topForYouLimit {
                break
            }
        }
        
        return selected
    }
    
    // MARK: - User Profile Management
    
    private func loadUserProfile() async {
        do {
            // Analyze user's reading history
            let readArticles = try await database.fetchReadArticles(limit: 100)
            
            // Build keyword interests
            var keywordCounts: [String: Int] = [:]
            var authorCounts: [String: Int] = [:]
            var totalReadingTime = 0
            
            for article in readArticles {
                // Count keywords
                for keyword in article.keywords {
                    keywordCounts[keyword.lowercased(), default: 0] += 1
                }
                
                // Count authors
                if let author = article.author {
                    authorCounts[author, default: 0] += 1
                }
                
                totalReadingTime += article.readingTime
            }
            
            // Calculate interests and affinities
            let totalArticles = Double(readArticles.count)
            
            var keywordInterests: [String: Double] = [:]
            for (keyword, count) in keywordCounts {
                keywordInterests[keyword] = Double(count) / totalArticles
            }
            
            var authorAffinities: [String: Double] = [:]
            for (author, count) in authorCounts {
                authorAffinities[author] = Double(count) / totalArticles
            }
            
            // Top keywords
            let topKeywords = keywordCounts
                .sorted { $0.value > $1.value }
                .prefix(20)
                .map { $0.key }
            
            // Average reading time
            let avgReadingTime = totalArticles > 0 ? Double(totalReadingTime) / totalArticles : 10.0
            
            userProfile = UserReadingProfile(
                keywordInterests: keywordInterests,
                authorAffinities: authorAffinities,
                topKeywords: topKeywords,
                averageReadingTime: avgReadingTime,
                totalArticlesRead: Int(totalArticles)
            )
            
        } catch {
            print("Failed to load user profile: \(error)")
            // Use default profile
            userProfile = UserReadingProfile.default
        }
    }
    
    // MARK: - Data Fetching
    
    private func fetchCandidateArticles() async throws -> [Article] {
        let since = Date().addingTimeInterval(-Double(recentWindowDays) * 86400)
        
        return try await database.fetchArticles(
            from: since,
            to: Date(),
            limit: 200
        ).filter { article in
            // Filter out read and archived articles
            article.lastAccessedAt == nil && !article.isArchived
        }
    }
    
    // MARK: - Public Methods
    
    func refreshFeed() async {
        await updateTopForYouFeed()
    }
    
    func markArticleAsInteresting(_ article: Article) async {
        // Update user profile based on explicit feedback
        // This could be used to refine the scoring algorithm
    }
    
    func markArticleAsNotInteresting(_ article: Article) async {
        // Update user profile to down-weight similar content
    }
}

// MARK: - Data Models

struct UserReadingProfile {
    let keywordInterests: [String: Double]  // Keyword -> Interest score (0-1)
    let authorAffinities: [String: Double]  // Author -> Affinity score (0-1)
    let topKeywords: [String]               // Most frequently read keywords
    let averageReadingTime: Double          // Average reading time in minutes
    let totalArticlesRead: Int
    
    static let `default` = UserReadingProfile(
        keywordInterests: [:],
        authorAffinities: [:],
        topKeywords: [],
        averageReadingTime: 10.0,
        totalArticlesRead: 0
    )
}

// MARK: - Database Extensions

extension DatabaseManager {
    func fetchReadArticles(limit: Int) async throws -> [Article] {
        try await read { db in
            try Article
                .filter(key: Article.Columns.lastAccessedAt != nil)
                .order(Article.Columns.lastAccessedAt.desc)
                .limit(limit)
                .fetchAll(db)
        }
    }
}