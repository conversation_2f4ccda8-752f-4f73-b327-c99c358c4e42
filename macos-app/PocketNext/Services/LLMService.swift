import Foundation
import CoreML

@MainActor
class LLMService: ObservableObject {
    @Published var isModelLoaded = false
    @Published var isProcessing = false
    @Published var loadingError: Error?
    
    private var localModel: MLModel?
    private let maxTokens = 2048
    private let temperature: Float = 0.7
    
    // Model configuration
    private var currentMode: InferenceMode = .local
    private let apiKey: String? = ProcessInfo.processInfo.environment["OPENAI_API_KEY"]
    
    enum InferenceMode {
        case local      // Use Core ML model
        case api        // Use OpenAI API
        case hybrid     // Local for simple queries, API for complex
    }
    
    init() {
        Task {
            await loadModel()
        }
    }
    
    // MARK: - Model Loading
    
    private func loadModel() async {
        do {
            // Check if we have a local model available
            if let modelURL = findLocalModel() {
                self.localModel = try MLModel(contentsOf: modelURL)
                self.currentMode = .local
                isModelLoaded = true
                print("Loaded local LLM model")
            } else if apiKey != nil {
                // Fall back to API mode if no local model
                self.currentMode = .api
                isModelLoaded = true
                print("Using API mode for LLM inference")
            } else {
                throw LLMError.noModelAvailable
            }
        } catch {
            loadingError = error
            print("Failed to load LLM: \(error)")
            
            // Try to fall back to API mode
            if apiKey != nil {
                self.currentMode = .api
                isModelLoaded = true
            }
        }
    }
    
    private func findLocalModel() -> URL? {
        // Look for Core ML model in app bundle or downloaded models directory
        let modelNames = ["gpt4o-mini", "llama2-7b", "mistral-7b"]
        
        for modelName in modelNames {
            if let modelURL = Bundle.main.url(forResource: modelName, withExtension: "mlmodelc") {
                return modelURL
            }
            
            // Check in application support directory
            let appSupport = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
            let modelPath = appSupport.appendingPathComponent("Models/\(modelName).mlmodelc")
            if FileManager.default.fileExists(atPath: modelPath.path) {
                return modelPath
            }
        }
        
        return nil
    }
    
    // MARK: - Inference
    
    func generateResponse(
        prompt: String,
        context: String,
        conversationHistory: [Message] = [],
        maxTokens: Int? = nil
    ) async throws -> GeneratedResponse {
        guard isModelLoaded else {
            throw LLMError.modelNotLoaded
        }
        
        isProcessing = true
        defer { isProcessing = false }
        
        // Build the full prompt with context
        let fullPrompt = buildPrompt(
            userQuery: prompt,
            context: context,
            history: conversationHistory
        )
        
        switch currentMode {
        case .local:
            return try await generateLocalResponse(fullPrompt, maxTokens: maxTokens)
        case .api:
            return try await generateAPIResponse(fullPrompt, maxTokens: maxTokens)
        case .hybrid:
            // Use simple heuristic: short prompts go to local, complex ones to API
            if fullPrompt.count < 1000 && context.count < 2000 {
                return try await generateLocalResponse(fullPrompt, maxTokens: maxTokens)
            } else {
                return try await generateAPIResponse(fullPrompt, maxTokens: maxTokens)
            }
        }
    }
    
    // MARK: - Local Inference
    
    private func generateLocalResponse(_ prompt: String, maxTokens: Int?) async throws -> GeneratedResponse {
        guard let model = localModel else {
            throw LLMError.modelNotLoaded
        }
        
        // In a real implementation, this would:
        // 1. Tokenize the prompt
        // 2. Create MLMultiArray input
        // 3. Run inference
        // 4. Decode output tokens
        
        // For now, return a placeholder response
        try await Task.sleep(nanoseconds: 500_000_000) // Simulate processing
        
        return GeneratedResponse(
            text: "This is a placeholder response from the local model. In production, this would use the actual Core ML model for inference.",
            tokensUsed: 50,
            modelUsed: "local-placeholder",
            inferenceTime: 0.5
        )
    }
    
    // MARK: - API Inference
    
    private func generateAPIResponse(_ prompt: String, maxTokens: Int?) async throws -> GeneratedResponse {
        guard let apiKey = apiKey else {
            throw LLMError.apiKeyMissing
        }
        
        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let messages: [[String: String]] = [
            ["role": "system", "content": "You are a helpful assistant that answers questions based on the provided context."],
            ["role": "user", "content": prompt]
        ]
        
        let body: [String: Any] = [
            "model": "gpt-4o-mini",
            "messages": messages,
            "max_tokens": maxTokens ?? self.maxTokens,
            "temperature": temperature
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: body)
        
        let startTime = Date()
        let (data, response) = try await URLSession.shared.data(for: request)
        let inferenceTime = Date().timeIntervalSince(startTime)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw LLMError.apiRequestFailed
        }
        
        let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let choices = json?["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: String],
              let content = message["content"] else {
            throw LLMError.invalidAPIResponse
        }
        
        let usage = json?["usage"] as? [String: Int]
        let tokensUsed = usage?["total_tokens"] ?? 0
        
        return GeneratedResponse(
            text: content,
            tokensUsed: tokensUsed,
            modelUsed: "gpt-4o-mini",
            inferenceTime: inferenceTime
        )
    }
    
    // MARK: - Prompt Building
    
    private func buildPrompt(userQuery: String, context: String, history: [Message]) -> String {
        var prompt = """
        You are an AI assistant helping users find information from their saved articles.
        
        Context from saved articles:
        \(context)
        
        """
        
        // Add conversation history if present
        if !history.isEmpty {
            prompt += "Previous conversation:\n"
            for message in history.suffix(4) { // Include last 4 messages
                prompt += "\(message.role.capitalized): \(message.content)\n"
            }
            prompt += "\n"
        }
        
        prompt += """
        User question: \(userQuery)
        
        Please provide a helpful answer based on the context provided. If the context doesn't contain relevant information, say so clearly.
        """
        
        return prompt
    }
    
    // MARK: - Model Management
    
    func downloadModel(modelName: String) async throws {
        // Implementation for downloading models from a CDN or model hub
        // This would download and install Core ML models for local inference
        throw LLMError.notImplemented
    }
    
    func switchMode(_ mode: InferenceMode) {
        currentMode = mode
    }
    
    func currentModelInfo() -> ModelInfo {
        ModelInfo(
            mode: currentMode,
            modelName: localModel != nil ? "Local Model" : "GPT-4o Mini",
            isLoaded: isModelLoaded,
            supportsStreaming: currentMode == .api
        )
    }
}

// MARK: - Data Models

struct GeneratedResponse {
    let text: String
    let tokensUsed: Int
    let modelUsed: String
    let inferenceTime: TimeInterval
}

struct Message {
    let role: String
    let content: String
    let timestamp: Date
}

struct ModelInfo {
    let mode: LLMService.InferenceMode
    let modelName: String
    let isLoaded: Bool
    let supportsStreaming: Bool
}

// MARK: - Errors

enum LLMError: LocalizedError {
    case modelNotLoaded
    case noModelAvailable
    case apiKeyMissing
    case apiRequestFailed
    case invalidAPIResponse
    case inferenceError(String)
    case notImplemented
    
    var errorDescription: String? {
        switch self {
        case .modelNotLoaded:
            return "LLM model is not loaded"
        case .noModelAvailable:
            return "No LLM model available for inference"
        case .apiKeyMissing:
            return "OpenAI API key is missing"
        case .apiRequestFailed:
            return "API request failed"
        case .invalidAPIResponse:
            return "Invalid response from API"
        case .inferenceError(let message):
            return "Inference error: \(message)"
        case .notImplemented:
            return "Feature not yet implemented"
        }
    }
}

// MARK: - Streaming Support

extension LLMService {
    func generateStreamingResponse(
        prompt: String,
        context: String,
        conversationHistory: [Message] = [],
        onToken: @escaping (String) -> Void,
        onComplete: @escaping (GeneratedResponse) -> Void,
        onError: @escaping (Error) -> Void
    ) {
        guard currentMode == .api else {
            onError(LLMError.inferenceError("Streaming only supported in API mode"))
            return
        }
        
        Task {
            do {
                // Implementation for streaming API responses
                // This would use EventSource or similar for real-time token streaming
                let response = try await generateResponse(
                    prompt: prompt,
                    context: context,
                    conversationHistory: conversationHistory
                )
                onComplete(response)
            } catch {
                onError(error)
            }
        }
    }
}