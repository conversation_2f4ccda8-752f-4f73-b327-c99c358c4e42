import Foundation
import GRDB
import CloudKit

/// Manages hybrid vector storage using CloudKit for sync and local SQLite for fast search
class HybridVectorStorage: ObservableObject {
    static let shared = HybridVectorStorage()
    
    private let databaseManager: DatabaseManager
    private let cloudKitSync: CloudKitSyncService
    private let embeddingService: EmbeddingService
    
    private let currentModelVersion = "openai-text-embedding-3-small-v1"
    private let similarityThreshold: Float = 0.7
    
    @Published var isRebuildingIndex = false
    @Published var indexProgress: Float = 0.0
    
    private init() {
        self.databaseManager = DatabaseManager.shared
        self.cloudKitSync = CloudKitSyncService.shared
        self.embeddingService = EmbeddingService.shared
    }
    
    // MARK: - Article Processing
    
    /// Process a new article: generate embedding, compress for CloudKit, store locally
    func processArticle(_ article: Article) async throws {
        guard article.embeddingData == nil else {
            print("Article already has embedding data")
            return
        }
        
        // Generate embedding
        let embedding = try await embeddingService.generateEmbedding(
            for: "\(article.title) \(article.summary) \(article.keywords.joined(separator: " "))"
        )
        
        // Compress for CloudKit storage
        let compressedData = VectorOperations.compressEmbedding(embedding)
        
        // Update article with embedding data
        var updatedArticle = article
        updatedArticle.embeddingData = compressedData
        updatedArticle.embeddingModelVersion = currentModelVersion
        updatedArticle.hasLocalEmbedding = true
        
        // Save to database
        try await databaseManager.updateArticle(updatedArticle)
        
        // Store in local vector index
        try await storeInLocalIndex(articleId: article.id, embedding: embedding)
        
        // Sync to CloudKit asynchronously
        Task {
            try? await cloudKitSync.syncArticle(updatedArticle)
        }
    }
    
    // MARK: - Local Vector Index
    
    /// Store embedding in local vector index for fast search
    private func storeInLocalIndex(articleId: UUID, embedding: [Float]) async throws {
        let normalized = VectorOperations.normalize(embedding)
        let magnitude = VectorOperations.magnitude(normalized)
        
        let vectorIndex = LocalVectorIndex(
            articleId: articleId,
            embedding: normalized,
            magnitude: magnitude,
            modelVersion: currentModelVersion,
            createdAt: Date()
        )
        
        try await databaseManager.write { db in
            try vectorIndex.save(db)
        }
    }
    
    /// Search for similar articles using local vector index
    func searchSimilar(to query: String, limit: Int = 10) async throws -> [Article] {
        let results = try await searchSimilarWithScores(to: query, limit: limit)
        return results.map { $0.article }
    }
    
    /// Search for similar articles and return with similarity scores
    func searchSimilarWithScores(to query: String, limit: Int = 10) async throws -> [(article: Article, score: Float)] {
        // Generate query embedding
        let queryEmbedding = try await embeddingService.generateEmbedding(for: query)
        let normalizedQuery = VectorOperations.normalize(queryEmbedding)
        let queryMagnitude = VectorOperations.magnitude(normalizedQuery)
        
        // Search local index
        let results = try await databaseManager.read { db in
            var matches: [(articleId: UUID, similarity: Float)] = []
            
            // Fetch all vectors (optimized with early termination)
            let vectors = try LocalVectorIndex.fetchAll(db)
            
            for vector in vectors {
                let similarity = VectorOperations.cosineSimilarityFast(
                    normalizedQuery, queryMagnitude,
                    vector.embedding, vector.magnitude
                )
                
                if similarity >= similarityThreshold {
                    matches.append((vector.articleId, similarity))
                }
            }
            
            // Sort by similarity and take top N
            matches.sort { $0.similarity > $1.similarity }
            let topMatches = matches.prefix(limit)
            
            // Fetch full articles with scores
            var articlesWithScores: [(article: Article, score: Float)] = []
            
            for match in topMatches {
                if let article = try Article.filter(Article.Columns.id == match.articleId).fetchOne(db) {
                    articlesWithScores.append((article, match.similarity))
                }
            }
            
            return articlesWithScores
        }
        
        return results
    }
    
    // MARK: - Index Management
    
    /// Rebuild local vector index from CloudKit data
    func rebuildLocalIndex() async throws {
        await MainActor.run {
            isRebuildingIndex = true
            indexProgress = 0.0
        }
        
        defer {
            Task { @MainActor in
                isRebuildingIndex = false
                indexProgress = 1.0
            }
        }
        
        // Clear existing index
        try await databaseManager.write { db in
            try db.execute(sql: "DELETE FROM local_vector_index")
        }
        
        // Fetch all articles with embeddings
        let articles = try await databaseManager.read { db in
            try Article
                .filter(Article.Columns.embeddingData != nil)
                .fetchAll(db)
        }
        
        // Rebuild index
        for (index, article) in articles.enumerated() {
            if let embeddingData = article.embeddingData,
               let embedding = VectorOperations.decompressEmbedding(embeddingData) {
                try await storeInLocalIndex(articleId: article.id, embedding: embedding)
                
                // Update progress
                await MainActor.run {
                    indexProgress = Float(index + 1) / Float(articles.count)
                }
            }
        }
        
        // Mark all articles as having local embeddings
        try await databaseManager.write { db in
            try db.execute(sql: """
                UPDATE articles 
                SET hasLocalEmbedding = 1 
                WHERE embeddingData IS NOT NULL
            """)
        }
    }
    
    /// Check if local index needs rebuilding
    func checkIndexHealth() async throws -> Bool {
        let stats = try await databaseManager.read { db -> (articlesWithEmbeddings: Int, localIndexCount: Int) in
            let articlesWithEmbeddings = try Article
                .filter(Article.Columns.embeddingData != nil)
                .fetchCount(db)
            
            let localIndexCount = try LocalVectorIndex.fetchCount(db)
            
            return (articlesWithEmbeddings, localIndexCount)
        }
        
        // Index is healthy if counts match
        return stats.articlesWithEmbeddings == stats.localIndexCount
    }
    
    // MARK: - Sync Handling
    
    /// Handle article downloaded from CloudKit
    func handleSyncedArticle(_ article: Article) async throws {
        // Save article to database
        try await databaseManager.updateArticle(article)
        
        // If article has embedding data, update local index
        if let embeddingData = article.embeddingData,
           let embedding = VectorOperations.decompressEmbedding(embeddingData) {
            try await storeInLocalIndex(articleId: article.id, embedding: embedding)
            
            // Mark as having local embedding
            var updatedArticle = article
            updatedArticle.hasLocalEmbedding = true
            try await databaseManager.updateArticle(updatedArticle)
        }
    }
    
    // MARK: - Migration Support
    
    /// Migrate embeddings if model version changed
    func migrateEmbeddingsIfNeeded() async throws {
        let outdatedArticles = try await databaseManager.read { db in
            try Article
                .filter(Article.Columns.embeddingModelVersion != currentModelVersion)
                .fetchAll(db)
        }
        
        if !outdatedArticles.isEmpty {
            print("Migrating \(outdatedArticles.count) articles to new embedding model")
            
            for article in outdatedArticles {
                try await processArticle(article)
            }
        }
    }
}