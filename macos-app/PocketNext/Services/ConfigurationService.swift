import Foundation
import Security

@MainActor
class ConfigurationService: ObservableObject {
    static let shared = ConfigurationService()
    
    @Published var hasOpenAIKey: Bool = false
    @Published var preferredModel: ModelPreference = .auto
    @Published var enableStreaming: Bool = true
    @Published var maxContextSize: Int = 4096
    @Published var temperature: Double = 0.7
    
    private let keychainService = "com.pocketnext.ai"
    private let openAIKeyAccount = "openai-api-key"
    
    enum ModelPreference: String, CaseIterable {
        case auto = "auto"
        case localOnly = "local"
        case apiOnly = "api"
        case hybrid = "hybrid"
        
        var displayName: String {
            switch self {
            case .auto: return "Automatic"
            case .localOnly: return "Local Only"
            case .apiOnly: return "API Only"
            case .hybrid: return "Hybrid"
            }
        }
        
        var description: String {
            switch self {
            case .auto:
                return "Automatically choose the best available option"
            case .localOnly:
                return "Only use local models (no internet required)"
            case .apiOnly:
                return "Always use OpenAI API (requires API key)"
            case .hybrid:
                return "Use local for simple queries, API for complex ones"
            }
        }
    }
    
    private init() {
        loadConfiguration()
    }
    
    // MARK: - Configuration Loading
    
    private func loadConfiguration() {
        // Load from UserDefaults
        if let modelPref = UserDefaults.standard.string(forKey: "preferredModel"),
           let preference = ModelPreference(rawValue: modelPref) {
            self.preferredModel = preference
        }
        
        self.enableStreaming = UserDefaults.standard.bool(forKey: "enableStreaming")
        self.maxContextSize = UserDefaults.standard.integer(forKey: "maxContextSize")
        
        if maxContextSize == 0 {
            maxContextSize = 4096 // Default
        }
        
        let temp = UserDefaults.standard.double(forKey: "temperature")
        if temp > 0 {
            self.temperature = temp
        }
        
        // Check if API key exists
        self.hasOpenAIKey = getOpenAIKey() != nil
    }
    
    // MARK: - API Key Management
    
    func setOpenAIKey(_ key: String) throws {
        let data = key.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: openAIKeyAccount,
            kSecValueData as String: data
        ]
        
        // Delete existing item if any
        SecItemDelete(query as CFDictionary)
        
        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecSuccess {
            hasOpenAIKey = true
            
            // Also set in environment for current session
            setenv("OPENAI_API_KEY", key, 1)
        } else {
            throw ConfigError.keychainError(status)
        }
    }
    
    func getOpenAIKey() -> String? {
        // First check environment variable
        if let envKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"],
           !envKey.isEmpty {
            return envKey
        }
        
        // Then check keychain
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: openAIKeyAccount,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess,
           let data = result as? Data,
           let key = String(data: data, encoding: .utf8) {
            // Set in environment for current session
            setenv("OPENAI_API_KEY", key, 1)
            return key
        }
        
        return nil
    }
    
    func removeOpenAIKey() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: openAIKeyAccount
        ]
        
        SecItemDelete(query as CFDictionary)
        hasOpenAIKey = false
        
        // Remove from environment
        unsetenv("OPENAI_API_KEY")
    }
    
    // MARK: - Preference Updates
    
    func updatePreferredModel(_ model: ModelPreference) {
        preferredModel = model
        UserDefaults.standard.set(model.rawValue, forKey: "preferredModel")
    }
    
    func updateStreaming(_ enabled: Bool) {
        enableStreaming = enabled
        UserDefaults.standard.set(enabled, forKey: "enableStreaming")
    }
    
    func updateMaxContextSize(_ size: Int) {
        maxContextSize = size
        UserDefaults.standard.set(size, forKey: "maxContextSize")
    }
    
    func updateTemperature(_ temp: Double) {
        temperature = max(0, min(2, temp)) // Clamp between 0 and 2
        UserDefaults.standard.set(temperature, forKey: "temperature")
    }
    
    // MARK: - Model Download Management
    
    func availableLocalModels() -> [LocalModel] {
        // In production, this would scan for available models
        return [
            LocalModel(
                id: "llama2-7b",
                name: "Llama 2 7B",
                size: "3.8 GB",
                description: "Fast, efficient model for general tasks",
                isDownloaded: false,
                downloadURL: URL(string: "https://example.com/models/llama2-7b")!
            ),
            LocalModel(
                id: "mistral-7b",
                name: "Mistral 7B",
                size: "4.1 GB",
                description: "High-quality responses with good reasoning",
                isDownloaded: false,
                downloadURL: URL(string: "https://example.com/models/mistral-7b")!
            )
        ]
    }
}

// MARK: - Data Models

struct LocalModel: Identifiable {
    let id: String
    let name: String
    let size: String
    let description: String
    var isDownloaded: Bool
    let downloadURL: URL
}

// MARK: - Errors

enum ConfigError: LocalizedError {
    case keychainError(OSStatus)
    case invalidConfiguration
    
    var errorDescription: String? {
        switch self {
        case .keychainError(let status):
            return "Keychain error: \(status)"
        case .invalidConfiguration:
            return "Invalid configuration"
        }
    }
}