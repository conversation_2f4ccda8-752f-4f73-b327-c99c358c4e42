import Foundation

@MainActor
class WebSearchService: ObservableObject {
    @Published var isSearching = false
    @Published var lastError: Error?
    
    init() {
        // Simple initialization - no API keys needed
    }
    
    // MARK: - Public Methods
    
    func search(_ query: String, limit: Int = 5) async throws -> [WebSearchResult] {
        isSearching = true
        defer { isSearching = false }
        
        do {
            let results = try await searchDuckDuckGo(query, limit: limit)
            lastError = nil
            return results
        } catch {
            lastError = error
            throw error
        }
    }
    
    func enhanceWithWebSearch(
        query: String,
        localResponse: String,
        confidence: Float
    ) async throws -> EnhancedResponse {
        // Only search if confidence is low
        guard confidence < 0.6 else {
            return EnhancedResponse(
                answer: localResponse,
                webSources: [],
                enhanced: false
            )
        }
        
        // Perform web search
        let searchResults = try await search(query, limit: 3)
        
        // Extract relevant content from search results
        let webContext = await extractWebContext(from: searchResults)
        
        // Generate enhanced response if we found useful web content
        if !webContext.isEmpty {
            let enhancedAnswer = await generateEnhancedResponse(
                query: query,
                localResponse: localResponse,
                webContext: webContext
            )
            
            return EnhancedResponse(
                answer: enhancedAnswer,
                webSources: searchResults,
                enhanced: true
            )
        } else {
            return EnhancedResponse(
                answer: localResponse,
                webSources: [],
                enhanced: false
            )
        }
    }
    
    // MARK: - DuckDuckGo Search
    
    private func searchDuckDuckGo(_ query: String, limit: Int) async throws -> [WebSearchResult] {
        // DuckDuckGo doesn't have a proper API, so we'll use their instant answer API
        // which is limited but doesn't require authentication
        var components = URLComponents(string: "https://api.duckduckgo.com/")!
        components.queryItems = [
            URLQueryItem(name: "q", value: query),
            URLQueryItem(name: "format", value: "json"),
            URLQueryItem(name: "no_html", value: "1"),
            URLQueryItem(name: "skip_disambig", value: "1")
        ]
        
        let request = URLRequest(url: components.url!)
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw SearchError.apiError
        }
        
        let searchResponse = try JSONDecoder().decode(DuckDuckGoResponse.self, from: data)
        
        var results: [WebSearchResult] = []
        
        // Add abstract if available
        if !searchResponse.abstract.isEmpty {
            results.append(WebSearchResult(
                title: searchResponse.heading ?? "DuckDuckGo Result",
                url: searchResponse.abstractURL ?? "",
                snippet: searchResponse.abstract
            ))
        }
        
        // Add related topics
        for topic in searchResponse.relatedTopics?.prefix(limit - results.count) ?? [] {
            if case .topic(let topicData) = topic {
                results.append(WebSearchResult(
                    title: topicData.text,
                    url: topicData.firstURL ?? "",
                    snippet: topicData.text
                ))
            }
        }
        
        return results
    }
    
    // MARK: - Content Extraction
    
    private func extractWebContext(from results: [WebSearchResult]) async -> String {
        var context = "Web search results:\n\n"
        
        for (index, result) in results.enumerated() {
            context += "[\(index + 1)] \(result.title)\n"
            context += "URL: \(result.url)\n"
            context += "Summary: \(result.snippet)\n\n"
        }
        
        return context
    }
    
    private func generateEnhancedResponse(
        query: String,
        localResponse: String,
        webContext: String
    ) async -> String {
        // In a real implementation, this would use the LLM service
        // to generate a combined response
        return """
        Based on your saved articles:
        \(localResponse)
        
        Additional information from the web:
        \(webContext)
        """
    }
}

// MARK: - Data Models

struct WebSearchResult: Identifiable {
    let id = UUID()
    let title: String
    let url: String
    let snippet: String
}

struct EnhancedResponse {
    let answer: String
    let webSources: [WebSearchResult]
    let enhanced: Bool
}

// MARK: - API Response Models

struct DuckDuckGoResponse: Codable {
    let abstract: String
    let abstractURL: String?
    let heading: String?
    let relatedTopics: [RelatedTopic]?
    
    enum RelatedTopic: Codable {
        case topic(TopicData)
        case disambiguation
        
        init(from decoder: Decoder) throws {
            if let topicData = try? TopicData(from: decoder) {
                self = .topic(topicData)
            } else {
                self = .disambiguation
            }
        }
        
        func encode(to encoder: Encoder) throws {
            switch self {
            case .topic(let data):
                try data.encode(to: encoder)
            case .disambiguation:
                var container = encoder.singleValueContainer()
                try container.encodeNil()
            }
        }
    }
    
    struct TopicData: Codable {
        let text: String
        let firstURL: String?
        
        enum CodingKeys: String, CodingKey {
            case text = "Text"
            case firstURL = "FirstURL"
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case abstract = "Abstract"
        case abstractURL = "AbstractURL"
        case heading = "Heading"
        case relatedTopics = "RelatedTopics"
    }
}

// MARK: - Errors

enum SearchError: LocalizedError, Equatable {
    case apiError
    case noResults
    case networkError(Error)
    
    static func == (lhs: SearchError, rhs: SearchError) -> Bool {
        switch (lhs, rhs) {
        case (.apiError, .apiError), (.noResults, .noResults):
            return true
        case (.networkError(let lhsError), .networkError(let rhsError)):
            return (lhsError as NSError) == (rhsError as NSError)
        default:
            return false
        }
    }
    
    var errorDescription: String? {
        switch self {
        case .apiError:
            return "Search API returned an error"
        case .noResults:
            return "No search results found"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}