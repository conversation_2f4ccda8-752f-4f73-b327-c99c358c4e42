import Foundation

actor ContentProcessor {
    private let parseServerURL: String
    private let database: DatabaseManager
    
    init(parseServerURL: String = "http://localhost:8000") {
        self.parseServerURL = parseServerURL
        self.database = DatabaseManager.shared
        print("ContentProcessor: Initialized with parse server: \(parseServerURL)")
    }
    
    func process(_ captured: CapturedContent) async throws -> Article {
        print("ContentProcessor: Processing captured content from: \(captured.url)")
        
        // Step 1: Send to parse server
        let parsed: ParsedArticle
        do {
            parsed = try await sendToParseServer(captured)
            print("ContentProcessor: ✅ Parse server returned article: \(parsed.title)")
        } catch {
            print("ContentProcessor: ❌ Parse server error: \(error)")
            throw error
        }
        
        // Step 2: Generate embeddings locally (optional)
        let embeddings = try? await generateLocalEmbeddings(parsed.content)
        if embeddings != nil {
            print("ContentProcessor: ✅ Generated embeddings")
        } else {
            print("ContentProcessor: ⚠️ Embeddings generation skipped")
        }
        
        // Step 3: Create article
        let article = Article(
            id: UUID(),
            url: captured.url,
            title: parsed.title,
            content: parsed.content,
            summary: parsed.summary,
            keywords: parsed.keywords,
            author: parsed.author,
            publishDate: parsePublishDate(parsed.publishDate),
            readingTime: parsed.readingTime,
            contentType: Article.ContentType(rawValue: parsed.contentType) ?? .generic,
            capturedAt: Date(),
            lastAccessedAt: nil,
            isArchived: false,
            syncStatus: .pending
        )
        
        // Step 4: Store article
        try await database.save(article)
        
        // Step 5: Store embeddings
        if let embeddings = embeddings {
            let articleEmbedding = ArticleEmbedding(
                articleId: article.id,
                embedding: embeddings,
                modelVersion: "bert-base-1.0"
            )
            try await database.saveEmbedding(articleEmbedding)
        }
        
        return article
    }
    
    private func sendToParseServer(_ content: CapturedContent) async throws -> ParsedArticle {
        guard let url = URL(string: "\(parseServerURL)/parse") else {
            throw ProcessingError.invalidURL
        }
        
        print("ContentProcessor: Sending to parse server at: \(url)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(getUserID(), forHTTPHeaderField: "X-User-ID")
        request.timeoutInterval = 30
        
        let payload = ParseRequest(
            url: content.url,
            html_content: content.htmlContent,
            content_type: content.metadata.type,
            metadata: ParseRequest.Metadata(
                title: content.title,
                author: content.metadata.author,
                publishDate: content.metadata.publishDate,
                user_id: getUserID()
            )
        )
        
        request.httpBody = try JSONEncoder().encode(payload)
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw ProcessingError.serverError
            }
            
            print("ContentProcessor: Parse server response: \(httpResponse.statusCode)")
            
            guard httpResponse.statusCode == 200 else {
                if let errorData = String(data: data, encoding: .utf8) {
                    print("ContentProcessor: Server error: \(errorData)")
                }
                throw ProcessingError.serverError
            }
            
            return try JSONDecoder().decode(ParsedArticle.self, from: data)
        } catch {
            print("ContentProcessor: Network error: \(error)")
            
            // For testing, return a fallback parsed article
            print("ContentProcessor: Using fallback article for testing")
            return ParsedArticle(
                title: content.title.isEmpty ? "Saved Article" : content.title,
                content: String(content.htmlContent.prefix(1000)),
                summary: "Article saved from \(content.url)",
                keywords: ["saved", "article"],
                author: content.metadata.author,
                publishDate: content.metadata.publishDate,
                readingTime: 5,
                contentType: content.metadata.type
            )
        }
    }
    
    func generateLocalEmbeddings(_ text: String) async throws -> [Float]? {
        // TODO: Implement proper embedding generation
        // For now, return mock embeddings based on text hash
        let hash = text.hashValue
        var embeddings: [Float] = []
        
        // Generate a deterministic 384-dimensional embedding vector
        for i in 0..<384 {
            let seed = hash &+ i
            let value = Float(seed % 1000) / 1000.0 - 0.5 // Range -0.5 to 0.5
            embeddings.append(value)
        }
        
        return embeddings
    }
    
    private func parsePublishDate(_ dateString: String?) -> Date? {
        guard let dateString = dateString else { return nil }
        
        let iso8601Formatter = ISO8601DateFormatter()
        if let date = iso8601Formatter.date(from: dateString) {
            return date
        }
        
        let rfc3339Formatter = DateFormatter.rfc3339
        if let date = rfc3339Formatter.date(from: dateString) {
            return date
        }
        
        let standardFormatter = DateFormatter.standard
        if let date = standardFormatter.date(from: dateString) {
            return date
        }
        
        return nil
    }
    
    private func getUserID() -> String {
        // Get or generate user ID
        if let userID = UserDefaults.standard.string(forKey: "PocketNextUserID") {
            return userID
        } else {
            let newID = "user_\(Date().timeIntervalSince1970)_\(UUID().uuidString.prefix(8))"
            UserDefaults.standard.set(newID, forKey: "PocketNextUserID")
            return newID
        }
    }
}

// MARK: - Supporting Types

struct ParseRequest: Codable {
    let url: String
    let html_content: String
    let content_type: String
    let metadata: Metadata
    
    struct Metadata: Codable {
        let title: String?
        let author: String?
        let publishDate: String?
        let user_id: String
    }
}

enum ProcessingError: LocalizedError {
    case invalidURL
    case serverError
    case parsingFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid parse server URL"
        case .serverError:
            return "Parse server error"
        case .parsingFailed:
            return "Failed to parse content"
        }
    }
}

// MARK: - Date Formatter Extensions

extension DateFormatter {
    static let rfc3339: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
    
    static let standard: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }()
}