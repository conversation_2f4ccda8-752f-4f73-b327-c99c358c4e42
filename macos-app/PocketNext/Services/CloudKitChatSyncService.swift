import Foundation
import CloudKit

enum CloudKitError: Error {
    case accountNotAvailable
    case networkError
    case quotaExceeded
    case serverError
    case unknown(Error)
}

@MainActor
class CloudKitChatSyncService: ObservableObject {
    @Published var isSyncing = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?
    @Published var syncStatus: SyncStatus = .idle
    
    private let container: CKContainer
    private let database: CKDatabase
    private let recordZone: CKRecordZone
    private let databaseManager = DatabaseManager.shared
    
    // Record types
    private let conversationRecordType = "ChatConversation"
    private let messageRecordType = "ChatMessage"
    private let settingRecordType = "ChatSetting"
    
    // Zone and subscription IDs
    private let zoneID = CKRecordZone.ID(zoneName: "ChatZone", ownerName: CKCurrentUserDefaultName)
    private let conversationSubscriptionID = "chat-conversation-changes"
    private let messageSubscriptionID = "chat-message-changes"
    private let settingSubscriptionID = "chat-setting-changes"
    
    // Sync configuration
    private let syncBatchSize = 50
    private var syncTimer: Timer?
    private let syncInterval: TimeInterval = 300 // 5 minutes
    
    enum SyncStatus: Equatable {
        case idle
        case syncing
        case error(Error)
        case success
        
        static func == (lhs: SyncStatus, rhs: SyncStatus) -> Bool {
            switch (lhs, rhs) {
            case (.idle, .idle), (.syncing, .syncing), (.success, .success):
                return true
            case (.error(_), .error(_)):
                return true
            default:
                return false
            }
        }
    }
    
    init() {
        // Use the specific container identifier from entitlements
        self.container = CKContainer(identifier: "iCloud.com.pocketnext.macos")
        self.database = container.privateCloudDatabase
        self.recordZone = CKRecordZone(zoneID: zoneID)
        
        Task {
            do {
                try await setupCloudKit()
            } catch {
                await MainActor.run {
                    self.syncError = error
                    self.syncStatus = .error(error)
                }
            }
        }
    }
    
    // MARK: - Setup
    
    private func setupCloudKit() async throws {
        // Check CloudKit availability
        let status = try await container.accountStatus()
        guard status == .available else {
            throw CloudKitError.accountNotAvailable
        }
        
        // Create custom zone
        try await createZoneIfNeeded()
        
        // Setup subscriptions
        try await setupSubscriptions()
        
        // Start sync timer
        startSyncTimer()
        
        // Perform initial sync
        await performSync()
    }
    
    private func createZoneIfNeeded() async throws {
        do {
            _ = try await database.recordZone(for: zoneID)
        } catch {
            // Zone doesn't exist, create it
            _ = try await database.save(recordZone)
        }
    }
    
    private func setupSubscriptions() async throws {
        // Conversation subscription
        let conversationPredicate = NSPredicate(value: true)
        let conversationSubscription = CKQuerySubscription(
            recordType: conversationRecordType,
            predicate: conversationPredicate,
            subscriptionID: conversationSubscriptionID,
            options: [.firesOnRecordCreation, .firesOnRecordUpdate, .firesOnRecordDeletion]
        )
        
        let notificationInfo = CKSubscription.NotificationInfo()
        notificationInfo.shouldSendContentAvailable = true
        conversationSubscription.notificationInfo = notificationInfo
        conversationSubscription.zoneID = zoneID
        
        // Message subscription
        let messageSubscription = CKQuerySubscription(
            recordType: messageRecordType,
            predicate: conversationPredicate,
            subscriptionID: messageSubscriptionID,
            options: [.firesOnRecordCreation, .firesOnRecordUpdate, .firesOnRecordDeletion]
        )
        messageSubscription.notificationInfo = notificationInfo
        messageSubscription.zoneID = zoneID
        
        // Setting subscription
        let settingSubscription = CKQuerySubscription(
            recordType: settingRecordType,
            predicate: conversationPredicate,
            subscriptionID: settingSubscriptionID,
            options: [.firesOnRecordCreation, .firesOnRecordUpdate]
        )
        settingSubscription.notificationInfo = notificationInfo
        settingSubscription.zoneID = zoneID
        
        // Save subscriptions
        do {
            _ = try await database.save(conversationSubscription)
            _ = try await database.save(messageSubscription)
            _ = try await database.save(settingSubscription)
        } catch {
            // Subscriptions might already exist
            print("Subscription setup error (may be normal): \(error)")
        }
    }
    
    // MARK: - Sync Timer
    
    private func startSyncTimer() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: syncInterval, repeats: true) { _ in
            Task { @MainActor in
                await self.performSync()
            }
        }
    }
    
    func stopSyncTimer() {
        syncTimer?.invalidate()
        syncTimer = nil
    }
    
    // MARK: - Sync Operations
    
    func performSync() async {
        guard syncStatus != .syncing else { return }
        
        isSyncing = true
        syncStatus = .syncing
        syncError = nil
        
        do {
            // Sync conversations
            try await syncConversations()
            
            // Sync messages
            try await syncMessages()
            
            // Sync settings
            try await syncSettings()
            
            lastSyncDate = Date()
            syncStatus = .success
        } catch {
            syncError = error
            syncStatus = .error(error)
            print("Sync failed: \(error)")
        }
        
        isSyncing = false
    }
    
    // MARK: - Conversation Sync
    
    private func syncConversations() async throws {
        // Fetch local conversations
        let localConversations = try await databaseManager.fetchAllConversations()
        
        // Fetch remote conversations
        let query = CKQuery(recordType: conversationRecordType, predicate: NSPredicate(value: true))
        let (matchResults, _) = try await database.records(matching: query, inZoneWith: zoneID, desiredKeys: nil, resultsLimit: CKQueryOperation.maximumResults)
        
        var remoteConversations: [CKRecord] = []
        for (_, result) in matchResults {
            if case .success(let record) = result {
                remoteConversations.append(record)
            }
        }
        
        // Merge conversations
        try await mergeConversations(local: localConversations, remote: remoteConversations)
    }
    
    private func mergeConversations(local: [ChatConversation], remote: [CKRecord]) async throws {
        var localMap = Dictionary(uniqueKeysWithValues: local.map { ($0.id, $0) })
        var processedIDs = Set<UUID>()
        
        // Process remote conversations
        for record in remote {
            guard let idString = record["id"] as? String,
                  let id = UUID(uuidString: idString) else { continue }
            
            processedIDs.insert(id)
            
            if let localConversation = localMap[id] {
                // Compare timestamps and update if needed
                let remoteUpdated = record["updatedAt"] as? Date ?? Date.distantPast
                if remoteUpdated > localConversation.updatedAt {
                    // Update local with remote data
                    try await updateLocalConversation(from: record)
                }
            } else {
                // Create new local conversation from remote
                try await createLocalConversation(from: record)
            }
            
            localMap.removeValue(forKey: id)
        }
        
        // Upload local conversations not in remote
        for (_, conversation) in localMap {
            try await uploadConversation(conversation)
        }
    }
    
    private func createLocalConversation(from record: CKRecord) async throws {
        guard let idString = record["id"] as? String,
              let id = UUID(uuidString: idString) else { return }
        
        let conversation = ChatConversation(
            id: id,
            title: record["title"] as? String,
            createdAt: record["createdAt"] as? Date ?? Date(),
            updatedAt: record["updatedAt"] as? Date ?? Date(),
            metadata: record["metadata"] as? Data
        )
        
        try await databaseManager.write { db in
            try conversation.save(db)
        }
    }
    
    private func updateLocalConversation(from record: CKRecord) async throws {
        guard let idString = record["id"] as? String,
              let id = UUID(uuidString: idString) else { return }
        
        try await databaseManager.write { db in
            try db.execute(
                sql: """
                UPDATE chat_conversations 
                SET title = ?, updatedAt = ?, metadata = ?
                WHERE id = ?
                """,
                arguments: [
                    record["title"] as? String,
                    record["updatedAt"] as? Date ?? Date(),
                    record["metadata"] as? Data,
                    id
                ]
            )
        }
    }
    
    private func uploadConversation(_ conversation: ChatConversation) async throws {
        let record = CKRecord(recordType: conversationRecordType, recordID: recordID(for: conversation.id))
        record["id"] = conversation.id.uuidString
        record["title"] = conversation.title
        record["createdAt"] = conversation.createdAt
        record["updatedAt"] = conversation.updatedAt
        record["metadata"] = conversation.metadata
        
        _ = try await database.save(record)
    }
    
    // MARK: - Message Sync
    
    private func syncMessages() async throws {
        // Fetch conversations to sync messages for
        let conversations = try await databaseManager.fetchAllConversations()
        
        for conversation in conversations {
            try await syncMessagesForConversation(conversation.id)
        }
    }
    
    private func syncMessagesForConversation(_ conversationId: UUID) async throws {
        // Fetch local messages
        let localMessages = try await databaseManager.read { db in
            try DBChatMessage
                .filter(sql: "conversationId = ?", arguments: [conversationId])
                .fetchAll(db)
        }
        
        // Fetch remote messages
        let predicate = NSPredicate(format: "conversationId == %@", conversationId.uuidString)
        let query = CKQuery(recordType: messageRecordType, predicate: predicate)
        let (matchResults, _) = try await database.records(matching: query, inZoneWith: zoneID, desiredKeys: nil, resultsLimit: CKQueryOperation.maximumResults)
        
        var remoteMessages: [CKRecord] = []
        for (_, result) in matchResults {
            if case .success(let record) = result {
                remoteMessages.append(record)
            }
        }
        
        // Merge messages
        try await mergeMessages(local: localMessages, remote: remoteMessages, conversationId: conversationId)
    }
    
    private func mergeMessages(local: [DBChatMessage], remote: [CKRecord], conversationId: UUID) async throws {
        var localMap = Dictionary(uniqueKeysWithValues: local.map { ($0.id, $0) })
        var processedIDs = Set<UUID>()
        
        // Process remote messages
        for record in remote {
            guard let idString = record["id"] as? String,
                  let id = UUID(uuidString: idString) else { continue }
            
            processedIDs.insert(id)
            
            if localMap[id] == nil {
                // Create new local message from remote
                try await createLocalMessage(from: record, conversationId: conversationId)
            }
            
            localMap.removeValue(forKey: id)
        }
        
        // Upload local messages not in remote
        for (_, message) in localMap {
            try await uploadMessage(message)
        }
    }
    
    private func createLocalMessage(from record: CKRecord, conversationId: UUID) async throws {
        guard let idString = record["id"] as? String,
              let id = UUID(uuidString: idString),
              let role = record["role"] as? String,
              let content = record["content"] as? String else { return }
        
        let message = DBChatMessage(
            id: id,
            conversationId: conversationId,
            role: role,
            content: content,
            citedSources: record["citedSources"] as? Data,
            timestamp: record["timestamp"] as? Date ?? Date(),
            metadata: record["metadata"] as? Data
        )
        
        try await databaseManager.write { db in
            try message.save(db)
        }
    }
    
    private func uploadMessage(_ message: DBChatMessage) async throws {
        let record = CKRecord(recordType: messageRecordType, recordID: recordID(for: message.id))
        record["id"] = message.id.uuidString
        record["conversationId"] = message.conversationId.uuidString
        record["role"] = message.role
        record["content"] = message.content
        record["citedSources"] = message.citedSources
        record["timestamp"] = message.timestamp
        record["metadata"] = message.metadata
        
        _ = try await database.save(record)
    }
    
    // MARK: - Settings Sync
    
    private func syncSettings() async throws {
        // Define settings to sync
        let settingsToSync = [
            "llm_model_main",
            "llm_model_fallback",
            "llm_model_research",
            "embedding_batch_size",
            "chat_context_window",
            "web_search_provider"
        ]
        
        for settingKey in settingsToSync {
            try await syncSetting(key: settingKey)
        }
    }
    
    private func syncSetting(key: String) async throws {
        // Fetch local setting
        let localValue = UserDefaults.standard.string(forKey: key)
        
        // Fetch remote setting
        let recordID = CKRecord.ID(recordName: "setting_\(key)", zoneID: zoneID)
        
        do {
            let record = try await database.record(for: recordID)
            let remoteValue = record["value"] as? String
            let remoteUpdated = record["updatedAt"] as? Date ?? Date.distantPast
            
            // If remote is newer, update local
            if let remoteValue = remoteValue,
               remoteUpdated > (UserDefaults.standard.object(forKey: "\(key)_updated") as? Date ?? Date.distantPast) {
                UserDefaults.standard.set(remoteValue, forKey: key)
                UserDefaults.standard.set(remoteUpdated, forKey: "\(key)_updated")
            }
        } catch {
            // Record doesn't exist, upload local if available
            if let localValue = localValue {
                let record = CKRecord(recordType: settingRecordType, recordID: recordID)
                record["key"] = key
                record["value"] = localValue
                record["updatedAt"] = Date()
                
                _ = try await database.save(record)
            }
        }
    }
    
    // MARK: - Helpers
    
    private func recordID(for id: UUID) -> CKRecord.ID {
        CKRecord.ID(recordName: id.uuidString, zoneID: zoneID)
    }
    
    // MARK: - Public Methods
    
    func forceSyncConversation(_ conversationId: UUID) async throws {
        try await syncMessagesForConversation(conversationId)
    }
    
    func deleteSyncedData() async throws {
        // Delete all records in the zone
        _ = try await database.modifyRecordZones(
            saving: [],
            deleting: [zoneID]
        )
        
        // Recreate zone
        try await createZoneIfNeeded()
    }
}


