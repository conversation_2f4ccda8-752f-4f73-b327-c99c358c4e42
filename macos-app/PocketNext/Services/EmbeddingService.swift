import Foundation
import CoreML
import Accelerate

@MainActor
class EmbeddingService: ObservableObject {
    @Published var isModelLoaded = false
    @Published var loadingError: Error?
    
    private var embeddingModel: MLModel?
    private let modelDimension = 384
    
    init() {
        Task {
            await loadModel()
        }
    }
    
    // MARK: - Model Loading
    
    private func loadModel() async {
        do {
            // In a real implementation, we would load a Core ML model here
            // For now, we'll simulate model loading
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            // Placeholder for actual Core ML model loading
            // self.embeddingModel = try MLModel(contentsOf: modelURL)
            
            isModelLoaded = true
        } catch {
            loadingError = error
            print("Failed to load embedding model: \(error)")
        }
    }
    
    // MARK: - Text Processing
    
    func preprocessText(_ text: String) -> String {
        // Basic text preprocessing
        let lowercased = text.lowercased()
        let cleaned = lowercased.replacingOccurrences(of: "[^a-zA-Z0-9\\s]", with: " ", options: .regularExpression)
        let normalized = cleaned.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
            .joined(separator: " ")
        
        return normalized
    }
    
    // MARK: - Embedding Generation
    
    func generateEmbedding(for text: String) async throws -> [Float] {
        guard isModelLoaded else {
            throw EmbeddingError.modelNotLoaded
        }
        
        let processedText = preprocessText(text)
        
        // In a real implementation, we would:
        // 1. Tokenize the text
        // 2. Convert tokens to input features
        // 3. Run inference through Core ML model
        // 4. Extract embedding from model output
        
        // For now, returning a placeholder embedding
        // This simulates the actual embedding generation
        return try await generatePlaceholderEmbedding(for: processedText)
    }
    
    func generateEmbeddings(for texts: [String]) async throws -> [[Float]] {
        var embeddings: [[Float]] = []
        
        for text in texts {
            let embedding = try await generateEmbedding(for: text)
            embeddings.append(embedding)
        }
        
        return embeddings
    }
    
    // MARK: - Placeholder Implementation
    
    private func generatePlaceholderEmbedding(for text: String) async throws -> [Float] {
        // Simulate processing time
        try await Task.sleep(nanoseconds: 10_000_000) // 10ms per embedding
        
        // Generate deterministic pseudo-embeddings based on text
        var embedding = Array(repeating: Float(0), count: modelDimension)
        
        // Use text characteristics to create unique embeddings
        let words = text.split(separator: " ")
        let wordCount = Float(words.count)
        let charCount = Float(text.count)
        
        // Create some variation in the embedding
        for (index, word) in words.enumerated() {
            let wordHash = word.hashValue
            let position = index % modelDimension
            
            embedding[position] += Float(wordHash % 100) / 1000.0
            embedding[(position + 1) % modelDimension] += wordCount / 100.0
            embedding[(position + 2) % modelDimension] += charCount / 1000.0
        }
        
        // Add some randomness for demonstration
        for i in 0..<modelDimension {
            embedding[i] += Float.random(in: -0.1...0.1)
        }
        
        // Normalize the embedding
        return normalizeVector(embedding)
    }
    
    private func normalizeVector(_ vector: [Float]) -> [Float] {
        var magnitude: Float = 0
        vDSP_svesq(vector, 1, &magnitude, vDSP_Length(vector.count))
        magnitude = sqrt(magnitude)
        
        guard magnitude > 0 else { return vector }
        
        var normalizedVector = vector
        var divisor = magnitude
        vDSP_vsdiv(vector, 1, &divisor, &normalizedVector, 1, vDSP_Length(vector.count))
        
        return normalizedVector
    }
    
    // MARK: - Similarity Computation
    
    func cosineSimilarity(_ vectorA: [Float], _ vectorB: [Float]) -> Float {
        guard vectorA.count == vectorB.count else { return 0 }
        
        var dotProduct: Float = 0
        vDSP_dotpr(vectorA, 1, vectorB, 1, &dotProduct, vDSP_Length(vectorA.count))
        
        return dotProduct // Assuming normalized vectors
    }
    
    func findMostSimilar(queryEmbedding: [Float], in embeddings: [[Float]], topK: Int = 5) -> [(index: Int, score: Float)] {
        var similarities: [(index: Int, score: Float)] = []
        
        for (index, embedding) in embeddings.enumerated() {
            let similarity = cosineSimilarity(queryEmbedding, embedding)
            similarities.append((index, similarity))
        }
        
        // Sort by similarity descending
        similarities.sort { $0.score > $1.score }
        
        return Array(similarities.prefix(topK))
    }
}

// MARK: - Error Types

enum EmbeddingError: LocalizedError {
    case modelNotLoaded
    case invalidInput
    case processingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .modelNotLoaded:
            return "Embedding model is not loaded"
        case .invalidInput:
            return "Invalid input provided for embedding generation"
        case .processingFailed(let reason):
            return "Failed to process embedding: \(reason)"
        }
    }
}

// MARK: - Embedding Cache

actor EmbeddingCache {
    private var cache: [String: [Float]] = [:]
    private let maxCacheSize = 1000
    
    func get(_ key: String) -> [Float]? {
        return cache[key]
    }
    
    func set(_ key: String, embedding: [Float]) {
        // Simple LRU eviction
        if cache.count >= maxCacheSize {
            // Remove oldest entry (simplified - in production use proper LRU)
            if let firstKey = cache.keys.first {
                cache.removeValue(forKey: firstKey)
            }
        }
        cache[key] = embedding
    }
    
    func clear() {
        cache.removeAll()
    }
}