import Foundation
import GRDB
import Accelerate

actor VectorStorageManager {
    private let dbManager: DatabaseManager
    private let vectorDimension = 384 // Using MiniLM-L6-v2 dimensions
    
    init(databaseManager: DatabaseManager) {
        self.dbManager = databaseManager
    }
    
    // MARK: - Database Schema Enhancement
    
    func initializeVectorStorage() async throws {
        try await extendDatabaseSchema()
    }
    
    private func extendDatabaseSchema() async throws {
        try await dbManager.write { db in
            // Document chunks table for RAG
            try db.create(table: "document_chunks", ifNotExists: true) { t in
                t.column("id", .text).primaryKey()
                t.column("articleId", .text).notNull()
                    .references("articles", onDelete: .cascade)
                t.column("chunkIndex", .integer).notNull()
                t.column("content", .text).notNull()
                t.column("startOffset", .integer).notNull()
                t.column("endOffset", .integer).notNull()
                t.column("metadata", .blob) // JSON metadata
                t.column("createdAt", .datetime).notNull()
            }
            
            // Enhanced embeddings table for chunks
            try db.create(table: "chunk_embeddings", ifNotExists: true) { t in
                t.column("chunkId", .text).primaryKey()
                    .references("document_chunks", onDelete: .cascade)
                t.column("embedding", .blob).notNull()
                t.column("magnitude", .double).notNull() // Pre-computed for faster cosine similarity
                t.column("modelVersion", .text).notNull()
                t.column("createdAt", .datetime).notNull()
            }
            
            // Conversation history table
            try db.create(table: "chat_conversations", ifNotExists: true) { t in
                t.column("id", .text).primaryKey()
                t.column("title", .text)
                t.column("createdAt", .datetime).notNull()
                t.column("updatedAt", .datetime).notNull()
                t.column("metadata", .blob) // JSON metadata
            }
            
            // Chat messages table
            try db.create(table: "chat_messages", ifNotExists: true) { t in
                t.column("id", .text).primaryKey()
                t.column("conversationId", .text).notNull()
                    .references("chat_conversations", onDelete: .cascade)
                t.column("role", .text).notNull() // "user" or "assistant"
                t.column("content", .text).notNull()
                t.column("citedSources", .blob) // JSON array of chunk IDs
                t.column("timestamp", .datetime).notNull()
                t.column("metadata", .blob) // JSON metadata
            }
            
            // Indexes for efficient retrieval
            try db.create(index: "idx_chunks_article",
                         on: "document_chunks",
                         columns: ["articleId", "chunkIndex"],
                         ifNotExists: true)
            
            try db.create(index: "idx_embeddings_magnitude",
                         on: "chunk_embeddings",
                         columns: ["magnitude"],
                         ifNotExists: true)
            
            try db.create(index: "idx_messages_conversation",
                         on: "chat_messages",
                         columns: ["conversationId", "timestamp"],
                         ifNotExists: true)
        }
    }
    
    // MARK: - Vector Operations
    
    nonisolated func cosineSimilarity(_ vectorA: [Float], _ vectorB: [Float]) -> Float {
        var dotProduct: Float = 0
        vDSP_dotpr(vectorA, 1, vectorB, 1, &dotProduct, vDSP_Length(vectorA.count))
        return dotProduct // Assuming normalized vectors
    }
    
    nonisolated func normalizeVector(_ vector: [Float]) -> [Float] {
        var magnitude: Float = 0
        vDSP_svesq(vector, 1, &magnitude, vDSP_Length(vector.count))
        magnitude = sqrt(magnitude)
        
        var normalizedVector = vector
        var divisor = magnitude
        vDSP_vsdiv(vector, 1, &divisor, &normalizedVector, 1, vDSP_Length(vector.count))
        
        return normalizedVector
    }
    
    // MARK: - Chunk Management
    
    func createDocumentChunks(articleId: UUID, content: String, chunkSize: Int = 512, overlap: Int = 128) async throws -> [DocumentChunk] {
        var chunks: [DocumentChunk] = []
        let words = content.split(separator: " ")
        var currentChunk = ""
        var startOffset = 0
        var chunkIndex = 0
        
        for (index, word) in words.enumerated() {
            currentChunk += word + " "
            
            if currentChunk.count >= chunkSize || index == words.count - 1 {
                let chunk = DocumentChunk(
                    id: UUID(),
                    articleId: articleId,
                    chunkIndex: chunkIndex,
                    content: currentChunk.trimmingCharacters(in: .whitespaces),
                    startOffset: startOffset,
                    endOffset: startOffset + currentChunk.count,
                    metadata: nil,
                    createdAt: Date()
                )
                chunks.append(chunk)
                
                // Calculate overlap for next chunk
                let overlapWords = max(0, overlap / 8) // Approximate words in overlap
                _ = max(0, words.count - overlapWords)
                currentChunk = words.dropFirst(index - overlapWords + 1).prefix(overlapWords).joined(separator: " ") + " "
                startOffset += currentChunk.count - overlap
                chunkIndex += 1
            }
        }
        
        // Save chunks to database
        let chunksToSave = chunks
        try await dbManager.write { db in
            for chunk in chunksToSave {
                try chunk.save(db)
            }
        }
        
        return chunks
    }
    
    // MARK: - Embedding Storage
    
    func storeEmbedding(chunkId: UUID, embedding: [Float], modelVersion: String = "minilm-l6-v2") async throws {
        let normalizedEmbedding = normalizeVector(embedding)
        let magnitude = sqrt(normalizedEmbedding.reduce(0) { $0 + $1 * $1 })
        
        let chunkEmbedding = ChunkEmbedding(
            chunkId: chunkId,
            embedding: normalizedEmbedding,
            magnitude: Double(magnitude),
            modelVersion: modelVersion,
            createdAt: Date()
        )
        
        try await dbManager.write { db in
            try chunkEmbedding.save(db)
        }
    }
    
    // MARK: - Vector Search
    
    func searchSimilarChunks(queryEmbedding: [Float], topK: Int = 5, threshold: Float = 0.7) async throws -> [(chunk: DocumentChunk, score: Float)] {
        let normalizedQuery = normalizeVector(queryEmbedding)
        
        return try await dbManager.read { [self] db in
            let embeddings = try ChunkEmbedding.fetchAll(db)
            var results: [(chunk: DocumentChunk, embedding: ChunkEmbedding, score: Float)] = []
            
            for embedding in embeddings {
                let similarity = self.cosineSimilarity(normalizedQuery, embedding.embedding)
                if similarity >= threshold {
                    if let chunk = try DocumentChunk.fetchOne(db, key: embedding.chunkId) {
                        results.append((chunk, embedding, similarity))
                    }
                }
            }
            
            // Sort by similarity score descending
            results.sort { $0.score > $1.score }
            
            // Return top K results
            return Array(results.prefix(topK)).map { ($0.chunk, $0.score) }
        }
    }
    
    // MARK: - Batch Operations
    
    func indexArticle(_ article: Article) async throws {
        // Create chunks
        let chunks = try await createDocumentChunks(
            articleId: article.id,
            content: article.content
        )
        
        // Generate embeddings for each chunk (placeholder - actual implementation would use Core ML)
        for chunk in chunks {
            // This is where we'd generate embeddings using a local model
            // For now, using placeholder embeddings
            let placeholderEmbedding = Array(repeating: Float(0.1), count: vectorDimension)
            try await storeEmbedding(chunkId: chunk.id, embedding: placeholderEmbedding)
        }
    }
    
    func reindexAllArticles() async throws {
        let articles = try await dbManager.fetchRecentArticles(limit: 1000)
        for article in articles {
            try await indexArticle(article)
        }
    }
    
    // MARK: - Utility Methods
    
    func getChunksForArticle(_ articleId: UUID) async throws -> [DocumentChunk] {
        try await dbManager.read { db in
            try DocumentChunk
                .filter(Column("articleId") == articleId)
                .order(Column("chunkIndex").asc)
                .fetchAll(db)
        }
    }
}

// MARK: - Data Models

struct DocumentChunk: Codable, FetchableRecord, PersistableRecord {
    static let databaseTableName = "document_chunks"
    
    let id: UUID
    let articleId: UUID
    let chunkIndex: Int
    let content: String
    let startOffset: Int
    let endOffset: Int
    let metadata: Data?
    let createdAt: Date
}

struct ChunkEmbedding: Codable, FetchableRecord, PersistableRecord {
    static let databaseTableName = "chunk_embeddings"
    
    let chunkId: UUID
    let embedding: [Float]
    let magnitude: Double
    let modelVersion: String
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case chunkId, magnitude, modelVersion, createdAt
        case embedding
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        chunkId = try container.decode(UUID.self, forKey: .chunkId)
        magnitude = try container.decode(Double.self, forKey: .magnitude)
        modelVersion = try container.decode(String.self, forKey: .modelVersion)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        
        // Decode embedding from blob
        let embeddingData = try container.decode(Data.self, forKey: .embedding)
        embedding = embeddingData.withUnsafeBytes { bytes in
            Array(bytes.bindMemory(to: Float.self))
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(chunkId, forKey: .chunkId)
        try container.encode(magnitude, forKey: .magnitude)
        try container.encode(modelVersion, forKey: .modelVersion)
        try container.encode(createdAt, forKey: .createdAt)
        
        // Encode embedding as blob
        let embeddingData = embedding.withUnsafeBytes { bytes in
            Data(bytes)
        }
        try container.encode(embeddingData, forKey: .embedding)
    }
    
    init(chunkId: UUID, embedding: [Float], magnitude: Double, modelVersion: String, createdAt: Date) {
        self.chunkId = chunkId
        self.embedding = embedding
        self.magnitude = magnitude
        self.modelVersion = modelVersion
        self.createdAt = createdAt
    }
}

struct ChatConversation: Codable, FetchableRecord, PersistableRecord {
    static let databaseTableName = "chat_conversations"
    
    let id: UUID
    var title: String?
    let createdAt: Date
    var updatedAt: Date
    let metadata: Data?
}

struct DBChatMessage: Codable, FetchableRecord, PersistableRecord {
    static let databaseTableName = "chat_messages"
    
    let id: UUID
    let conversationId: UUID
    let role: String
    let content: String
    let citedSources: Data? // JSON array of chunk IDs
    let timestamp: Date
    let metadata: Data?
    
    enum Columns: String, ColumnExpression {
        case id, conversationId, role, content, citedSources, timestamp, metadata
    }
}