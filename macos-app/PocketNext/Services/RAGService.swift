import Foundation
import CoreML

@MainActor
class RAGService: ObservableObject {
    @Published var isReady = false
    @Published var isProcessing = false
    @Published var error: Error?
    
    private let databaseManager: DatabaseManager
    private let vectorStorage: VectorStorageManager
    private let embeddingService: EmbeddingService
    private let llmService: LLMService
    private let webSearchService: WebSearchService
    private let cache = ResponseCache()
    
    // Configuration
    private let chunkSize = 512
    private let chunkOverlap = 128
    private let topK = 5
    private let similarityThreshold: Float = 0.7
    
    init(databaseManager: DatabaseManager) {
        self.databaseManager = databaseManager
        self.vectorStorage = VectorStorageManager(databaseManager: databaseManager)
        self.embeddingService = EmbeddingService()
        self.llmService = LLMService()
        self.webSearchService = WebSearchService()
        
        Task {
            await initialize()
        }
    }
    
    // MARK: - Initialization
    
    private func initialize() async {
        do {
            try await vectorStorage.initializeVectorStorage()
            
            // Wait for models to load
            while !embeddingService.isModelLoaded || !llmService.isModelLoaded {
                try await Task.sleep(nanoseconds: 100_000_000) // 100ms
            }
            
            isReady = true
        } catch {
            self.error = error
            print("Failed to initialize RAG service: \(error)")
        }
    }
    
    // MARK: - Document Processing
    
    func processArticle(_ article: Article) async throws {
        guard isReady else {
            throw RAGError.serviceNotReady
        }
        
        // Check if already processed
        let existingChunks = try await vectorStorage.getChunksForArticle(article.id)
        if !existingChunks.isEmpty {
            print("Article already processed, skipping: \(article.title)")
            return
        }
        
        // Create chunks
        let chunks = try await vectorStorage.createDocumentChunks(
            articleId: article.id,
            content: article.content,
            chunkSize: chunkSize,
            overlap: chunkOverlap
        )
        
        // Generate embeddings for each chunk
        for chunk in chunks {
            let embedding = try await embeddingService.generateEmbedding(for: chunk.content)
            try await vectorStorage.storeEmbedding(chunkId: chunk.id, embedding: embedding)
        }
    }
    
    func processAllArticles() async throws {
        guard isReady else {
            throw RAGError.serviceNotReady
        }
        
        isProcessing = true
        defer { isProcessing = false }
        
        let articles = try await databaseManager.fetchRecentArticles(limit: 1000)
        
        for (index, article) in articles.enumerated() {
            print("Processing article \(index + 1)/\(articles.count): \(article.title)")
            try await processArticle(article)
        }
    }
    
    // MARK: - Query Processing
    
    func query(_ question: String, conversationId: UUID? = nil) async throws -> RAGResponse {
        guard isReady else {
            throw RAGError.serviceNotReady
        }
        
        // Check cache first
        if let cachedResponse = await cache.get(for: question) {
            return cachedResponse
        }
        
        // Generate query embedding
        let queryEmbedding = try await embeddingService.generateEmbedding(for: question)
        
        // Search for relevant chunks
        let relevantChunks = try await vectorStorage.searchSimilarChunks(
            queryEmbedding: queryEmbedding,
            topK: topK,
            threshold: similarityThreshold
        )
        
        // If no relevant chunks found, indicate need for web search
        if relevantChunks.isEmpty {
            return RAGResponse(
                answer: "I couldn't find relevant information in your saved articles. Would you like me to search the web?",
                sources: [],
                requiresWebSearch: true,
                confidence: 0.0
            )
        }
        
        // Generate response using retrieved context
        let response = try await generateResponse(
            question: question,
            context: relevantChunks,
            conversationId: conversationId
        )
        
        // Cache the response
        await cache.set(response, for: question)
        
        return response
    }
    
    // MARK: - Response Generation
    
    private func generateResponse(
        question: String,
        context: [(chunk: DocumentChunk, score: Float)],
        conversationId: UUID?
    ) async throws -> RAGResponse {
        // Prepare context for LLM
        let contextText = context.enumerated().map { index, item in
            "[Source \(index + 1)] \(item.chunk.content)"
        }.joined(separator: "\n\n")
        
        // Load conversation history if available
        var conversationHistory: [Message] = []
        if let conversationId = conversationId {
            let dbMessages = try await loadConversationHistory(conversationId: conversationId)
            conversationHistory = dbMessages.map { msg in
                Message(role: msg.role, content: msg.content, timestamp: msg.timestamp)
            }
        }
        
        // Generate response using LLM
        let response = try await llmService.generateResponse(
            prompt: question,
            context: contextText,
            conversationHistory: conversationHistory
        )
        
        // Extract sources
        let sources = try await extractSources(from: context)
        
        // Calculate confidence based on similarity scores
        let avgScore = context.isEmpty ? 0 : context.reduce(0) { $0 + $1.score } / Float(context.count)
        
        // Save to conversation history if conversationId provided
        if let conversationId = conversationId {
            try await saveToConversation(
                conversationId: conversationId,
                question: question,
                answer: response.text,
                sources: sources
            )
        }
        
        return RAGResponse(
            answer: response.text,
            sources: sources,
            requiresWebSearch: false,
            confidence: avgScore
        )
    }
    
    // MARK: - Web Search Enhancement
    
    func queryWithWebSearch(_ question: String, conversationId: UUID? = nil) async throws -> RAGResponse {
        // First try with local content
        let localResponse = try await query(question, conversationId: conversationId)
        
        // If confidence is high enough or web search not needed, return as is
        if localResponse.confidence >= 0.7 || !localResponse.requiresWebSearch {
            return localResponse
        }
        
        // Enhance with web search
        do {
            let enhanced = try await webSearchService.enhanceWithWebSearch(
                query: question,
                localResponse: localResponse.answer,
                confidence: localResponse.confidence
            )
            
            if enhanced.enhanced {
                // Create web sources
                let webSources = enhanced.webSources.map { result in
                    Source(
                        articleId: UUID(), // Placeholder ID for web results
                        title: result.title,
                        url: result.url,
                        relevantChunks: [],
                        score: 0.5 // Default score for web results
                    )
                }
                
                // Combine sources
                var allSources = localResponse.sources
                allSources.append(contentsOf: webSources)
                
                return RAGResponse(
                    answer: enhanced.answer,
                    sources: allSources,
                    requiresWebSearch: false,
                    confidence: max(localResponse.confidence, 0.7)
                )
            }
        } catch {
            print("Web search enhancement failed: \(error)")
            // Fall back to local response
        }
        
        return localResponse
    }
    
    private func extractSources(from context: [(chunk: DocumentChunk, score: Float)]) async throws -> [Source] {
        var sources: [Source] = []
        var processedArticles = Set<UUID>()
        
        for item in context {
            guard !processedArticles.contains(item.chunk.articleId) else { continue }
            processedArticles.insert(item.chunk.articleId)
            
            if let article = try await databaseManager.fetchArticle(id: item.chunk.articleId) {
                sources.append(Source(
                    articleId: article.id,
                    title: article.title,
                    url: article.url,
                    relevantChunks: [item.chunk.id],
                    score: item.score
                ))
            }
        }
        
        return sources
    }
    
    // MARK: - Conversation Management
    
    func createConversation(title: String? = nil) async throws -> ChatConversation {
        let conversation = ChatConversation(
            id: UUID(),
            title: title,
            createdAt: Date(),
            updatedAt: Date(),
            metadata: nil
        )
        
        try await databaseManager.write { db in
            try conversation.save(db)
        }
        
        return conversation
    }
    
    private func saveToConversation(
        conversationId: UUID,
        question: String,
        answer: String,
        sources: [Source]
    ) async throws {
        let userMessage = DBChatMessage(
            id: UUID(),
            conversationId: conversationId,
            role: "user",
            content: question,
            citedSources: nil,
            timestamp: Date(),
            metadata: nil
        )
        
        let sourcesData = try? JSONEncoder().encode(sources.map { $0.articleId })
        let assistantMessage = DBChatMessage(
            id: UUID(),
            conversationId: conversationId,
            role: "assistant",
            content: answer,
            citedSources: sourcesData,
            timestamp: Date(),
            metadata: nil
        )
        
        try await databaseManager.write { db in
            try userMessage.save(db)
            try assistantMessage.save(db)
            
            // Update conversation timestamp
            try db.execute(
                sql: "UPDATE chat_conversations SET updatedAt = ? WHERE id = ?",
                arguments: [Date(), conversationId]
            )
        }
    }
    
    func loadConversationHistory(conversationId: UUID) async throws -> [DBChatMessage] {
        try await databaseManager.read { db in
            try DBChatMessage
                .filter(sql: "conversationId = ?", arguments: [conversationId])
                .order(sql: "timestamp ASC")
                .fetchAll(db)
        }
    }
}

// MARK: - Response Models

struct RAGResponse {
    let answer: String
    let sources: [Source]
    let requiresWebSearch: Bool
    let confidence: Float
}

struct Source: Codable, Identifiable {
    let id = UUID()
    let articleId: UUID
    let title: String
    let url: String
    let relevantChunks: [UUID]
    let score: Float
    
    enum CodingKeys: String, CodingKey {
        case articleId, title, url, relevantChunks, score
    }
}

// MARK: - Error Types

enum RAGError: LocalizedError {
    case serviceNotReady
    case embeddingGenerationFailed
    case noRelevantContent
    case responseGenerationFailed
    
    var errorDescription: String? {
        switch self {
        case .serviceNotReady:
            return "RAG service is not ready"
        case .embeddingGenerationFailed:
            return "Failed to generate embeddings"
        case .noRelevantContent:
            return "No relevant content found in knowledge base"
        case .responseGenerationFailed:
            return "Failed to generate response"
        }
    }
}

// MARK: - Response Cache

actor ResponseCache {
    private var cache: [String: RAGResponse] = [:]
    private let maxCacheSize = 100
    private let cacheExpiration: TimeInterval = 3600 // 1 hour
    private var timestamps: [String: Date] = [:]
    
    func get(for query: String) -> RAGResponse? {
        guard let response = cache[query],
              let timestamp = timestamps[query],
              Date().timeIntervalSince(timestamp) < cacheExpiration else {
            return nil
        }
        return response
    }
    
    func set(_ response: RAGResponse, for query: String) {
        if cache.count >= maxCacheSize {
            // Remove oldest entry
            if let oldestKey = timestamps.min(by: { $0.value < $1.value })?.key {
                cache.removeValue(forKey: oldestKey)
                timestamps.removeValue(forKey: oldestKey)
            }
        }
        
        cache[query] = response
        timestamps[query] = Date()
    }
    
    func clear() {
        cache.removeAll()
        timestamps.removeAll()
    }
}