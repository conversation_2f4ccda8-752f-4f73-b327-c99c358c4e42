import Foundation
import UserNotifications

#if os(iOS)
import BackgroundTasks
#endif

@MainActor
class DigestService: ObservableObject {
    @Published var isGenerating = false
    @Published var lastGeneratedDate: Date?
    @Published var lastError: Error?
    @Published var currentDigest: ArticleDigest?
    
    private let database: DatabaseManager
    private let preferences: DigestPreferences
    
    // Background task identifiers
    static let dailyDigestTaskID = "com.pocketnext.digest.daily"
    static let weeklyDigestTaskID = "com.pocketnext.digest.weekly"
    
    // Digest configuration
    private let digestArticleLimit = 10
    private let readingTimeThreshold = 5 // minutes
    
    init(database: DatabaseManager) {
        self.database = database
        self.preferences = DigestPreferences.shared
        
        Task {
            await setupBackgroundTasks()
            await checkAndGenerateDigest()
        }
    }
    
    // MARK: - Background Task Setup
    
    private func setupBackgroundTasks() async {
        #if os(iOS)
        // Register background tasks
        BGTaskScheduler.shared.register(forTaskWithIdentifier: Self.dailyDigestTaskID, using: nil) { task in
            self.handleDailyDigest(task: task as! BGProcessingTask)
        }
        
        BGTaskScheduler.shared.register(forTaskWithIdentifier: Self.weeklyDigestTaskID, using: nil) { task in
            self.handleWeeklyDigest(task: task as! BGProcessingTask)
        }
        #endif
        
        // Schedule initial tasks
        await scheduleNextDigest()
    }
    
    private func scheduleNextDigest() async {
        guard preferences.isEnabled else { return }
        
        #if os(iOS)
        let request: BGProcessingTaskRequest
        let nextDate: Date
        
        switch preferences.frequency {
        case .daily:
            request = BGProcessingTaskRequest(identifier: Self.dailyDigestTaskID)
            nextDate = nextDailyDigestDate()
        case .weekly:
            request = BGProcessingTaskRequest(identifier: Self.weeklyDigestTaskID)
            nextDate = nextWeeklyDigestDate()
        }
        
        request.earliestBeginDate = nextDate
        request.requiresNetworkConnectivity = false
        request.requiresExternalPower = false
        
        do {
            try BGTaskScheduler.shared.submit(request)
            print("Scheduled next digest for \(nextDate)")
        } catch {
            print("Failed to schedule digest task: \(error)")
        }
        #else
        // On macOS, use a different scheduling mechanism
        // For now, just log
        let nextDate = preferences.frequency == .daily ? nextDailyDigestDate() : nextWeeklyDigestDate()
        print("Would schedule next digest for \(nextDate) (background tasks not available on macOS)")
        #endif
    }
    
    // MARK: - Background Task Handlers
    
    #if os(iOS)
    private func handleDailyDigest(task: BGProcessingTask) {
        Task {
            do {
                let digest = try await generateDigest(type: .daily)
                await deliverDigest(digest)
                task.setTaskCompleted(success: true)
            } catch {
                task.setTaskCompleted(success: false)
            }
            
            // Schedule next digest
            await scheduleNextDigest()
        }
    }
    
    private func handleWeeklyDigest(task: BGProcessingTask) {
        Task {
            do {
                let digest = try await generateDigest(type: .weekly)
                await deliverDigest(digest)
                task.setTaskCompleted(success: true)
            } catch {
                task.setTaskCompleted(success: false)
            }
            
            // Schedule next digest
            await scheduleNextDigest()
        }
    }
    #endif
    
    // MARK: - Digest Generation
    
    func generateDigest(type: DigestType = .daily) async throws -> ArticleDigest {
        isGenerating = true
        defer { isGenerating = false }
        
        let timeframe = getTimeframe(for: type)
        
        // Fetch articles from timeframe
        let articles = try await database.fetchArticles(
            from: timeframe.start,
            to: timeframe.end,
            limit: 100
        )
        
        // Generate sections
        let topArticles = try await selectTopArticles(from: articles)
        let quickReads = filterQuickReads(from: articles)
        let categories = categorizeArticles(articles)
        let readingStats = calculateReadingStats(for: articles, in: timeframe)
        
        let digest = ArticleDigest(
            id: UUID(),
            type: type,
            generatedAt: Date(),
            timeframe: timeframe,
            topArticles: topArticles,
            quickReads: quickReads,
            categories: categories,
            stats: readingStats
        )
        
        // Save digest
        try await saveDigest(digest)
        
        currentDigest = digest
        lastGeneratedDate = Date()
        
        return digest
    }
    
    private func selectTopArticles(from articles: [Article]) async throws -> [Article] {
        // Score articles based on multiple factors
        let scoredArticles = articles.map { article -> (article: Article, score: Double) in
            var score = 0.0
            
            // Recency factor (newer is better)
            let daysSinceCapture = Date().timeIntervalSince(article.capturedAt) / 86400
            score += max(0, 10 - daysSinceCapture)
            
            // Reading time factor (prefer medium-length articles)
            if article.readingTime >= 5 && article.readingTime <= 15 {
                score += 5
            } else if article.readingTime < 5 {
                score += 2
            }
            
            // Keyword relevance (based on user's reading history)
            if !article.keywords.isEmpty {
                score += Double(article.keywords.count) * 2
            }
            
            // Already read penalty
            if article.lastAccessedAt != nil {
                score -= 10
            }
            
            return (article, score)
        }
        
        // Sort by score and take top articles
        let topArticles = scoredArticles
            .sorted { $0.score > $1.score }
            .prefix(digestArticleLimit)
            .map { $0.article }
        
        return Array(topArticles)
    }
    
    private func filterQuickReads(from articles: [Article]) -> [Article] {
        articles
            .filter { $0.readingTime <= readingTimeThreshold && $0.lastAccessedAt == nil }
            .sorted { $0.capturedAt > $1.capturedAt }
            .prefix(5)
            .map { $0 }
    }
    
    private func categorizeArticles(_ articles: [Article]) -> [String: [Article]] {
        var categories: [String: [Article]] = [:]
        
        for article in articles {
            // Extract primary category from keywords
            if let primaryKeyword = article.keywords.first {
                if categories[primaryKeyword] == nil {
                    categories[primaryKeyword] = []
                }
                categories[primaryKeyword]?.append(article)
            }
        }
        
        // Limit articles per category
        for (key, value) in categories {
            categories[key] = Array(value.prefix(3))
        }
        
        return categories
    }
    
    private func calculateReadingStats(for articles: [Article], in timeframe: DateInterval) -> ReadingStats {
        let readArticles = articles.filter { $0.lastAccessedAt != nil }
        let totalReadingTime = readArticles.reduce(0) { $0 + $1.readingTime }
        let savedCount = articles.count
        let readCount = readArticles.count
        
        // Calculate daily average
        let days = timeframe.duration / 86400
        let dailyAverage = days > 0 ? Double(totalReadingTime) / days : 0
        
        let topCategoriesData = findTopCategories(from: articles).map { 
            ReadingStats.CategoryCount(category: $0.category, count: $0.count)
        }
        
        return ReadingStats(
            articlesRead: readCount,
            articlesSaved: savedCount,
            totalReadingTime: totalReadingTime,
            averageDailyTime: Int(dailyAverage),
            topCategories: topCategoriesData
        )
    }
    
    private func findTopCategories(from articles: [Article]) -> [(category: String, count: Int)] {
        var categoryCounts: [String: Int] = [:]
        
        for article in articles {
            for keyword in article.keywords {
                categoryCounts[keyword, default: 0] += 1
            }
        }
        
        return categoryCounts
            .sorted { $0.value > $1.value }
            .prefix(5)
            .map { (category: $0.key, count: $0.value) }
    }
    
    // MARK: - Digest Delivery
    
    func deliverDigest(_ digest: ArticleDigest) async {
        // Request notification permission if needed
        await requestNotificationPermission()
        
        // Create notification
        let content = UNMutableNotificationContent()
        content.title = "Your \(digest.type.rawValue.capitalized) Digest is Ready"
        content.subtitle = "\(digest.topArticles.count) articles selected for you"
        content.body = createDigestSummary(digest)
        content.categoryIdentifier = "DIGEST"
        content.sound = .default
        
        // Add custom data
        content.userInfo = [
            "digestId": digest.id.uuidString,
            "type": digest.type.rawValue
        ]
        
        // Create trigger (immediate delivery)
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        
        // Create request
        let request = UNNotificationRequest(
            identifier: digest.id.uuidString,
            content: content,
            trigger: trigger
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
        } catch {
            print("Failed to deliver digest notification: \(error)")
        }
    }
    
    private func createDigestSummary(_ digest: ArticleDigest) -> String {
        var summary = ""
        
        if digest.stats.articlesRead > 0 {
            summary += "You read \(digest.stats.articlesRead) articles this \(digest.type.rawValue). "
        }
        
        if let topCategory = digest.stats.topCategories.first {
            summary += "Your top interest was \(topCategory.category). "
        }
        
        summary += "Tap to explore your personalized selection."
        
        return summary
    }
    
    // MARK: - Helper Methods
    
    private func getTimeframe(for type: DigestType) -> DateInterval {
        let now = Date()
        let calendar = Calendar.current
        
        switch type {
        case .daily:
            let startOfDay = calendar.startOfDay(for: now.addingTimeInterval(-86400))
            let endOfDay = calendar.startOfDay(for: now)
            return DateInterval(start: startOfDay, end: endOfDay)
            
        case .weekly:
            let weekAgo = now.addingTimeInterval(-7 * 86400)
            let startOfWeek = calendar.startOfDay(for: weekAgo)
            return DateInterval(start: startOfWeek, end: now)
        }
    }
    
    private func nextDailyDigestDate() -> Date {
        let calendar = Calendar.current
        let now = Date()
        
        var components = calendar.dateComponents([.year, .month, .day], from: now)
        components.hour = preferences.preferredHour
        components.minute = 0
        
        var nextDate = calendar.date(from: components)!
        
        // If time has passed today, schedule for tomorrow
        if nextDate <= now {
            nextDate = calendar.date(byAdding: .day, value: 1, to: nextDate)!
        }
        
        return nextDate
    }
    
    private func nextWeeklyDigestDate() -> Date {
        let calendar = Calendar.current
        let now = Date()
        
        var components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now)
        components.weekday = preferences.preferredWeekday
        components.hour = preferences.preferredHour
        components.minute = 0
        
        var nextDate = calendar.date(from: components)!
        
        // If time has passed this week, schedule for next week
        if nextDate <= now {
            nextDate = calendar.date(byAdding: .weekOfYear, value: 1, to: nextDate)!
        }
        
        return nextDate
    }
    
    private func requestNotificationPermission() async {
        do {
            let granted = try await UNUserNotificationCenter.current()
                .requestAuthorization(options: [.alert, .badge, .sound])
            
            if !granted {
                print("Notification permission denied")
            }
        } catch {
            print("Failed to request notification permission: \(error)")
        }
    }
    
    // MARK: - Persistence
    
    private func saveDigest(_ digest: ArticleDigest) async throws {
        // Save to database
        try await database.saveDigest(digest)
    }
    
    func fetchDigests(limit: Int = 10) async throws -> [ArticleDigest] {
        try await database.fetchDigests(limit: limit)
    }
    
    // MARK: - Manual Controls
    
    func checkAndGenerateDigest() async {
        guard preferences.isEnabled else { return }
        
        do {
            // Check if digest is due
            let lastDigest = try await database.fetchLatestDigest()
            
            if shouldGenerateDigest(lastDigest: lastDigest) {
                _ = try await generateDigest(type: preferences.frequency == .daily ? .daily : .weekly)
            }
        } catch {
            lastError = error
        }
    }
    
    private func shouldGenerateDigest(lastDigest: ArticleDigest?) -> Bool {
        guard let lastDigest = lastDigest else { return true }
        
        let calendar = Calendar.current
        let now = Date()
        
        switch preferences.frequency {
        case .daily:
            return !calendar.isDateInToday(lastDigest.generatedAt)
        case .weekly:
            return now.timeIntervalSince(lastDigest.generatedAt) > 6 * 86400
        }
    }
}

// MARK: - Data Models

struct ArticleDigest: Identifiable, Codable, Hashable {
    let id: UUID
    let type: DigestType
    let generatedAt: Date
    let timeframe: DateInterval
    let topArticles: [Article]
    let quickReads: [Article]
    let categories: [String: [Article]]
    let stats: ReadingStats
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: ArticleDigest, rhs: ArticleDigest) -> Bool {
        lhs.id == rhs.id
    }
}

struct ReadingStats: Codable, Hashable {
    let articlesRead: Int
    let articlesSaved: Int
    let totalReadingTime: Int
    let averageDailyTime: Int
    let topCategories: [CategoryCount]
    
    struct CategoryCount: Codable, Hashable {
        let category: String
        let count: Int
    }
}

enum DigestType: String, Codable, CaseIterable {
    case daily
    case weekly
}

// MARK: - Preferences

@MainActor
class DigestPreferences: ObservableObject {
    static let shared = DigestPreferences()
    
    @Published var isEnabled: Bool {
        didSet { UserDefaults.standard.set(isEnabled, forKey: "digestEnabled") }
    }
    
    @Published var frequency: DigestFrequency {
        didSet { UserDefaults.standard.set(frequency.rawValue, forKey: "digestFrequency") }
    }
    
    @Published var preferredHour: Int {
        didSet { UserDefaults.standard.set(preferredHour, forKey: "digestHour") }
    }
    
    @Published var preferredWeekday: Int {
        didSet { UserDefaults.standard.set(preferredWeekday, forKey: "digestWeekday") }
    }
    
    @Published var includeCategories: [String] {
        didSet { UserDefaults.standard.set(includeCategories, forKey: "digestCategories") }
    }
    
    private init() {
        self.isEnabled = UserDefaults.standard.bool(forKey: "digestEnabled")
        self.frequency = DigestFrequency(rawValue: UserDefaults.standard.string(forKey: "digestFrequency") ?? "daily") ?? .daily
        self.preferredHour = UserDefaults.standard.integer(forKey: "digestHour") == 0 ? 9 : UserDefaults.standard.integer(forKey: "digestHour")
        self.preferredWeekday = UserDefaults.standard.integer(forKey: "digestWeekday") == 0 ? 2 : UserDefaults.standard.integer(forKey: "digestWeekday") // Monday
        self.includeCategories = UserDefaults.standard.stringArray(forKey: "digestCategories") ?? []
    }
}

enum DigestFrequency: String, CaseIterable {
    case daily
    case weekly
}