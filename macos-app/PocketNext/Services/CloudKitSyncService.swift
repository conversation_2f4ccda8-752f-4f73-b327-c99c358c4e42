import Foundation
import CloudKit
import Combine

/// Service responsible for syncing articles with CloudKit, including embedding data
class CloudKitSyncService: ObservableObject {
    static let shared = CloudKitSyncService()
    
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var syncProgress: Double = 0
    @Published var syncErrors: [SyncError] = []
    
    private let container: CKContainer
    private let privateDatabase: CKDatabase
    
    private var syncQueue = DispatchQueue(label: "com.pocketnext.cloudkit.sync", qos: .background)
    private var cancellables = Set<AnyCancellable>()
    
    // Record types
    private let articleRecordType = "Article"
    
    // CloudKit field size limits
    private let maxEmbeddingDataSize = 1024 * 900  // ~900KB to be safe (CloudKit limit is 1MB per field)
    
    private init() {
        container = CKContainer(identifier: "iCloud.com.pocketnext.app")
        privateDatabase = container.privateCloudDatabase
        
        setupSubscriptions()
        checkAccountStatus()
    }
    
    // MARK: - Public Methods
    
    /// Start a full sync operation
    func startSync() {
        guard syncStatus != .syncing else { return }
        
        syncStatus = .syncing
        syncProgress = 0
        
        Task {
            do {
                // Check iCloud availability
                try await checkiCloudAvailability()
                
                // Sync articles (including embeddings)
                try await syncArticles()
                
                await MainActor.run {
                    self.syncStatus = .completed
                    self.lastSyncDate = Date()
                }
                
                // Notify HybridVectorStorage to check for new embeddings
                NotificationCenter.default.post(name: .cloudKitSyncCompleted, object: nil)
                
            } catch {
                await MainActor.run {
                    self.syncStatus = .failed
                    self.syncErrors.append(SyncError(error: error, timestamp: Date()))
                }
            }
        }
    }
    
    /// Sync a single article (including embedding data)
    func syncArticle(_ article: Article) async throws {
        let record = try articleToRecord(article)
        
        do {
            _ = try await privateDatabase.save(record)
            print("Article synced: \(article.id)")
            
            // Update sync status locally
            var updatedArticle = article
            updatedArticle.syncStatus = .synced
            try await DatabaseManager.shared.updateArticle(updatedArticle)
            
        } catch {
            print("Failed to sync article: \(error)")
            throw error
        }
    }
    
    /// Delete an article from CloudKit
    func deleteArticle(_ articleId: UUID) async throws {
        let recordID = CKRecord.ID(recordName: articleId.uuidString)
        
        do {
            _ = try await privateDatabase.deleteRecord(withID: recordID)
            print("Article deleted from CloudKit: \(articleId)")
        } catch {
            print("Failed to delete article: \(error)")
            throw error
        }
    }
    
    /// Fetch all articles from CloudKit
    func fetchArticles() async throws -> [Article] {
        let query = CKQuery(recordType: articleRecordType, predicate: NSPredicate(value: true))
        query.sortDescriptors = [NSSortDescriptor(key: "capturedAt", ascending: false)]
        
        var articles: [Article] = []
        
        do {
            let (matchResults, _) = try await privateDatabase.records(matching: query)
            
            for (_, result) in matchResults {
                switch result {
                case .success(let record):
                    if let article = recordToArticle(record) {
                        articles.append(article)
                    }
                case .failure(let error):
                    print("Failed to fetch record: \(error)")
                }
            }
            
            return articles
        } catch {
            print("Failed to fetch articles: \(error)")
            throw error
        }
    }
    
    // MARK: - Private Methods
    
    private func checkAccountStatus() {
        Task {
            do {
                let status = try await container.accountStatus()
                if status != .available {
                    print("iCloud account not available: \(status)")
                    await MainActor.run {
                        self.syncStatus = .disabled
                    }
                }
            } catch {
                print("Failed to check account status: \(error)")
            }
        }
    }
    
    private func checkiCloudAvailability() async throws {
        let status = try await container.accountStatus()
        guard status == .available else {
            throw CloudKitError.iCloudAccountNotAvailable
        }
    }
    
    private func syncArticles() async throws {
        // Get articles that need syncing
        let unsyncedArticles = try await DatabaseManager.shared.read { db in
            try Article
                .filter(Article.Columns.syncStatus != SyncStatus.synced.rawValue)
                .fetchAll(db)
        }
        
        // Sync each article
        for (index, article) in unsyncedArticles.enumerated() {
            try await syncArticle(article)
            
            // Update progress
            await MainActor.run {
                self.syncProgress = Double(index + 1) / Double(unsyncedArticles.count)
            }
        }
        
        // Fetch remote changes
        try await fetchRemoteChanges()
    }
    
    private func fetchRemoteChanges() async throws {
        // Use change tokens for efficient sync
        let articles = try await fetchArticles()
        
        // Process each article
        for article in articles {
            // Let HybridVectorStorage handle the article
            try await HybridVectorStorage.shared.handleSyncedArticle(article)
        }
    }
    
    private func setupSubscriptions() {
        Task {
            do {
                // Article changes subscription
                let articleSubscription = CKQuerySubscription(
                    recordType: articleRecordType,
                    predicate: NSPredicate(value: true),
                    subscriptionID: "article-changes",
                    options: [.firesOnRecordCreation, .firesOnRecordUpdate, .firesOnRecordDeletion]
                )
                
                let notificationInfo = CKSubscription.NotificationInfo()
                notificationInfo.shouldSendContentAvailable = true
                articleSubscription.notificationInfo = notificationInfo
                
                _ = try await privateDatabase.save(articleSubscription)
                print("CloudKit subscription created")
                
            } catch {
                print("Failed to create subscription: \(error)")
            }
        }
    }
    
    // MARK: - Data Conversion
    
    private func articleToRecord(_ article: Article) throws -> CKRecord {
        let recordID = CKRecord.ID(recordName: article.id.uuidString)
        let record = CKRecord(recordType: articleRecordType, recordID: recordID)
        
        // Basic fields
        record["url"] = article.url
        record["title"] = article.title
        record["content"] = article.content
        record["summary"] = article.summary
        record["keywords"] = try JSONEncoder().encode(article.keywords)
        record["author"] = article.author
        record["publishDate"] = article.publishDate
        record["readingTime"] = article.readingTime
        record["contentType"] = article.contentType.rawValue
        record["capturedAt"] = article.capturedAt
        record["lastAccessedAt"] = article.lastAccessedAt
        record["isArchived"] = article.isArchived ? 1 : 0
        
        // Embedding fields
        if let embeddingData = article.embeddingData {
            // Check size limit
            if embeddingData.count > maxEmbeddingDataSize {
                print("Warning: Embedding data too large for CloudKit (\(embeddingData.count) bytes)")
                // Could implement chunking here if needed
            } else {
                record["embeddingData"] = embeddingData
                record["embeddingModelVersion"] = article.embeddingModelVersion
            }
        }
        
        return record
    }
    
    private func recordToArticle(_ record: CKRecord) -> Article? {
        guard let url = record["url"] as? String,
              let title = record["title"] as? String,
              let content = record["content"] as? String,
              let summary = record["summary"] as? String,
              let keywordsData = record["keywords"] as? Data,
              let keywords = try? JSONDecoder().decode([String].self, from: keywordsData),
              let contentTypeRaw = record["contentType"] as? String,
              let contentType = Article.ContentType(rawValue: contentTypeRaw),
              let capturedAt = record["capturedAt"] as? Date else {
            return nil
        }
        
        let article = Article(
            id: UUID(uuidString: record.recordID.recordName) ?? UUID(),
            url: url,
            title: title,
            content: content,
            summary: summary,
            keywords: keywords,
            author: record["author"] as? String,
            publishDate: record["publishDate"] as? Date,
            readingTime: record["readingTime"] as? Int ?? 5,
            contentType: contentType,
            capturedAt: capturedAt,
            lastAccessedAt: record["lastAccessedAt"] as? Date,
            isArchived: (record["isArchived"] as? Int ?? 0) == 1,
            syncStatus: .synced,
            embeddingData: record["embeddingData"] as? Data,
            embeddingModelVersion: record["embeddingModelVersion"] as? String,
            hasLocalEmbedding: false  // Will be set when processed locally
        )
        
        return article
    }
}

// MARK: - Supporting Types

enum SyncStatus {
    case idle
    case syncing
    case completed
    case failed
    case disabled
}

struct SyncError: Identifiable {
    let id = UUID()
    let error: Error
    let timestamp: Date
}

enum CloudKitError: LocalizedError {
    case iCloudAccountNotAvailable
    case syncInProgress
    case recordNotFound
    case conflictResolutionFailed
    
    var errorDescription: String? {
        switch self {
        case .iCloudAccountNotAvailable:
            return "iCloud account is not available. Please sign in to iCloud in System Preferences."
        case .syncInProgress:
            return "Sync is already in progress."
        case .recordNotFound:
            return "Record not found in CloudKit."
        case .conflictResolutionFailed:
            return "Failed to resolve sync conflict."
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let cloudKitSyncCompleted = Notification.Name("cloudKitSyncCompleted")
}