import Foundation
import AppKit

/// Handles native messaging communication with browser extensions
class NativeMessaging: NSObject {
    static let shared = NativeMessaging()
    
    private var inputPipe: Pipe?
    private var outputPipe: Pipe?
    private var messageHandler: ((NativeMessage) -> Void)?
    
    private let decoder = J<PERSON>NDecoder()
    private let encoder = JSONEncoder()
    
    override init() {
        super.init()
        print("🔌 NativeMessaging: Initializing native messaging service")
        setupNativeMessaging()
    }
    
    // MARK: - Setup
    
    private func setupNativeMessaging() {
        print("🔌 NativeMessaging: Setting up native messaging")
        
        // Register the native messaging host
        registerNativeHost()
        
        // Set up pipes for stdin/stdout communication
        inputPipe = Pipe()
        outputPipe = Pipe()
        
        // Start listening for messages
        print("🔌 NativeMessaging: Starting message listener")
        startListening()
    }
    
    private func registerNativeHost() {
        print("🔌 NativeMessaging: Registering native host")
        
        // Get the actual executable path
        let executablePath = Bundle.main.executablePath ?? ""
        print("🔌 NativeMessaging: Executable path: \(executablePath)")
        
        // Create native messaging host manifest
        let manifest = NativeHostManifest(
            name: "com.pocketnext.nativemessaging",
            description: "Pocket-next Native Messaging Host",
            path: executablePath,
            type: "stdio",
            allowed_origins: [
                "chrome-extension://YOUR_EXTENSION_ID/",
                "chrome-extension://YOUR_FIREFOX_ID/"
            ]
        )
        
        print("🔌 NativeMessaging: ⚠️ Extension IDs need to be updated!")
        print("🔌 NativeMessaging: Replace YOUR_EXTENSION_ID with actual Chrome extension ID")
        print("🔌 NativeMessaging: Replace YOUR_FIREFOX_ID with actual Firefox extension ID")
        
        // Write manifest to appropriate locations
        writeManifestForChrome(manifest)
        writeManifestForFirefox(manifest)
    }
    
    private func writeManifestForChrome(_ manifest: NativeHostManifest) {
        // Get the actual home directory, not the sandboxed one
        let homeURL: URL
        if let pw = getpwuid(getuid()), let home = String(cString: pw.pointee.pw_dir, encoding: .utf8) {
            homeURL = URL(fileURLWithPath: home)
        } else {
            homeURL = FileManager.default.homeDirectoryForCurrentUser
        }
        
        let chromeNativeHostsPath = homeURL
            .appendingPathComponent("Library/Application Support/Google/Chrome/NativeMessagingHosts")
        
        print("🔌 NativeMessaging: Chrome manifest directory: \(chromeNativeHostsPath.path)")
        
        // Check if we're sandboxed
        if chromeNativeHostsPath.path.contains("/Library/Containers/") {
            print("🔌 NativeMessaging: ⚠️ App is sandboxed! Native messaging requires:")
            print("   1. Disable App Sandbox in entitlements")
            print("   2. Or manually copy manifest to: ~/Library/Application Support/Google/Chrome/NativeMessagingHosts/")
            
            // Still write to sandboxed location for manual copying
        }
        
        do {
            try FileManager.default.createDirectory(at: chromeNativeHostsPath, withIntermediateDirectories: true)
            
            let manifestPath = chromeNativeHostsPath.appendingPathComponent("\(manifest.name).json")
            let manifestData = try encoder.encode(manifest)
            
            // Log the manifest content
            if let jsonString = String(data: manifestData, encoding: .utf8) {
                print("🔌 NativeMessaging: Chrome manifest content:")
                print(jsonString)
            }
            
            try manifestData.write(to: manifestPath)
            
            print("🔌 NativeMessaging: ✅ Chrome native messaging host registered at: \(manifestPath.path)")
        } catch {
            print("🔌 NativeMessaging: ❌ Failed to register Chrome native host: \(error)")
        }
    }
    
    private func writeManifestForFirefox(_ manifest: NativeHostManifest) {
        let firefoxNativeHostsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent("Library/Application Support/Mozilla/NativeMessagingHosts")
        
        do {
            try FileManager.default.createDirectory(at: firefoxNativeHostsPath, withIntermediateDirectories: true)
            
            let manifestPath = firefoxNativeHostsPath.appendingPathComponent("\(manifest.name).json")
            let manifestData = try encoder.encode(manifest)
            try manifestData.write(to: manifestPath)
            
            print("Firefox native messaging host registered at: \(manifestPath.path)")
        } catch {
            print("Failed to register Firefox native host: \(error)")
        }
    }
    
    // MARK: - Message Handling
    
    private func startListening() {
        print("🔌 NativeMessaging: Setting up stdin listener")
        let stdin = FileHandle.standardInput
        
        stdin.readabilityHandler = { handle in
            let data = handle.availableData
            
            print("🔌 NativeMessaging: Received data on stdin, size: \(data.count) bytes")
            
            if data.count > 0 {
                self.handleIncomingData(data)
            }
        }
        
        print("🔌 NativeMessaging: Listener ready")
    }
    
    private func handleIncomingData(_ data: Data) {
        print("🔌 NativeMessaging: Processing incoming data")
        
        // Native messaging protocol: first 4 bytes contain message length
        guard data.count >= 4 else {
            print("🔌 NativeMessaging: ❌ Data too short (< 4 bytes)")
            return
        }
        
        let lengthData = data.subdata(in: 0..<4)
        let length = lengthData.withUnsafeBytes { $0.load(as: UInt32.self) }
        print("🔌 NativeMessaging: Message length: \(length) bytes")
        
        guard data.count >= Int(length) + 4 else {
            print("🔌 NativeMessaging: ❌ Incomplete message (expected \(length + 4) bytes, got \(data.count))")
            return
        }
        
        let messageData = data.subdata(in: 4..<Int(length) + 4)
        
        do {
            let message = try decoder.decode(NativeMessage.self, from: messageData)
            print("🔌 NativeMessaging: ✅ Decoded message type: \(message.type)")
            handleMessage(message)
        } catch {
            print("🔌 NativeMessaging: ❌ Failed to decode message: \(error)")
            if let jsonString = String(data: messageData, encoding: .utf8) {
                print("🔌 NativeMessaging: Raw message: \(jsonString)")
            }
        }
    }
    
    private func handleMessage(_ message: NativeMessage) {
        print("🔌 NativeMessaging: Handling message type: \(message.type)")
        
        switch message.type {
        case .capture:
            print("🔌 NativeMessaging: 📥 Capture message received")
            handleCaptureMessage(message)
        case .ping:
            print("🔌 NativeMessaging: 🏓 Ping received, sending pong")
            sendResponse(NativeMessage(type: .pong, data: nil))
        case .getStatus:
            print("🔌 NativeMessaging: 📊 Status request received")
            handleStatusRequest()
        default:
            print("🔌 NativeMessaging: ❓ Unknown message type: \(message.type)")
        }
    }
    
    private func handleCaptureMessage(_ message: NativeMessage) {
        print("🔌 NativeMessaging: Processing capture message")
        
        guard let data = message.data else {
            print("🔌 NativeMessaging: ❌ No data in capture message")
            sendResponse(NativeMessage(type: .error, data: "No data in message".data(using: .utf8)))
            return
        }
        
        print("🔌 NativeMessaging: Data size: \(data.count) bytes")
        
        do {
            let capturedContent = try decoder.decode(CapturedContent.self, from: data)
            print("🔌 NativeMessaging: ✅ Decoded captured content:")
            print("  - URL: \(capturedContent.url)")
            print("  - Title: \(capturedContent.title)")
            print("  - HTML length: \(capturedContent.htmlContent.count) chars")
        
        // Process the captured content
        Task {
            do {
                print("🔌 NativeMessaging: Creating ContentProcessor")
                let processor = ContentProcessor()
                
                print("🔌 NativeMessaging: Processing content...")
                let article = try await processor.process(capturedContent)
                
                print("🔌 NativeMessaging: ✅ Article processed: \(article.title)")
                
                // Send success response
                let successResponse = SuccessResponse(
                    success: true,
                    articleId: article.id.uuidString,
                    title: article.title
                )
                let successData = try encoder.encode(successResponse)
                
                print("🔌 NativeMessaging: Sending success response")
                sendResponse(NativeMessage(type: .success, data: successData))
                
                // Notify the app
                print("🔌 NativeMessaging: Posting article captured notification")
                await MainActor.run {
                    NotificationCenter.default.post(
                        name: .articleCaptured,
                        object: nil,
                        userInfo: ["article": article]
                    )
                }
                
                print("🔌 NativeMessaging: ✅ Capture complete!")
                
            } catch {
                print("🔌 NativeMessaging: ❌ Error processing article: \(error)")
                let errorData = "Failed to process article: \(error.localizedDescription)".data(using: .utf8)
                sendResponse(NativeMessage(type: .error, data: errorData))
            }
        }
        
        } catch {
            print("🔌 NativeMessaging: ❌ Failed to decode CapturedContent: \(error)")
            sendResponse(NativeMessage(type: .error, data: "Invalid capture data: \(error)".data(using: .utf8)))
        }
    }
    
    private func handleStatusRequest() {
        let status = StatusResponse(
            connected: true,
            version: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0",
            capabilities: ["capture", "search", "sync"]
        )
        
        do {
            let statusData = try encoder.encode(status)
            sendResponse(NativeMessage(type: .status, data: statusData))
        } catch {
            print("Failed to encode status: \(error)")
        }
    }
    
    // MARK: - Message Sending
    
    private func sendResponse(_ message: NativeMessage) {
        print("🔌 NativeMessaging: Sending response type: \(message.type)")
        
        do {
            let messageData = try encoder.encode(message)
            print("🔌 NativeMessaging: Response size: \(messageData.count) bytes")
            
            // Create length header (4 bytes)
            var length = UInt32(messageData.count)
            let lengthData = Data(bytes: &length, count: 4)
            
            // Write to stdout
            let stdout = FileHandle.standardOutput
            stdout.write(lengthData)
            stdout.write(messageData)
            stdout.synchronizeFile()
            
            print("🔌 NativeMessaging: ✅ Response sent")
            
        } catch {
            print("🔌 NativeMessaging: ❌ Failed to send response: \(error)")
        }
    }
    
    // MARK: - Public Methods
    
    func checkForPendingMessages() {
        // Check if there are any pending messages from the browser
        // This can be called when the app launches
    }
    
    func sendMessage(_ message: NativeMessage) {
        sendResponse(message)
    }
}

// MARK: - Data Models

struct NativeMessage: Codable {
    let type: MessageType
    let data: Data?
    
    enum MessageType: String, Codable {
        case capture
        case ping
        case pong
        case status
        case getStatus
        case success
        case error
    }
}

struct NativeHostManifest: Codable {
    let name: String
    let description: String
    let path: String
    let type: String
    let allowed_origins: [String]
}

// MARK: - Response Types

struct SuccessResponse: Codable {
    let success: Bool
    let articleId: String
    let title: String
}

struct StatusResponse: Codable {
    let connected: Bool
    let version: String
    let capabilities: [String]
}

// MARK: - Notifications

extension Notification.Name {
    static let articleCaptured = Notification.Name("articleCaptured")
}

// MARK: - Helper for Launch

extension NativeMessaging {
    /// Check if the app was launched by native messaging
    static func isLaunchedByNativeMessaging() -> Bool {
        // Check if stdin is connected (indicates native messaging)
        let result = isatty(STDIN_FILENO) == 0
        print("🔌 NativeMessaging: Launched by native messaging? \(result)")
        return result
    }
    
    /// Handle native messaging mode
    static func handleNativeMessagingMode() {
        print("🔌 NativeMessaging: Checking if should run in native messaging mode")
        if isLaunchedByNativeMessaging() {
            print("🔌 NativeMessaging: ✅ Running in native messaging mode")
            // Run in native messaging mode
            let messaging = NativeMessaging.shared
            
            // Keep the app running
            print("🔌 NativeMessaging: Starting RunLoop for native messaging")
            RunLoop.main.run()
        } else {
            print("🔌 NativeMessaging: 🚶 Running in normal app mode")
        }
    }
}