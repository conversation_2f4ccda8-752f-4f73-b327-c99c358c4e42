import SwiftUI

struct CaptureOverlay: View {
    @EnvironmentObject var appState: AppState
    @State private var captureState = CaptureState.waiting
    @State private var capturedContent: BrowserCapturedContent?
    @State private var error: CaptureError?
    @State private var progress: Double = 0
    
    enum CaptureState {
        case waiting
        case capturing
        case processing
        case success
        case error
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Status bar
            HStack(spacing: 12) {
                // Icon
                Group {
                    switch captureState {
                    case .waiting:
                        Image(systemName: "arrow.down.doc")
                            .symbolEffect(.pulse)
                    case .capturing:
                        ProgressView()
                            .controlSize(.small)
                    case .processing:
                        Image(systemName: "brain")
                            .symbolEffect(.pulse)
                    case .success:
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundStyle(.green)
                    case .error:
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundStyle(.red)
                    }
                }
                .font(.title3)
                
                // Status text
                VStack(alignment: .leading, spacing: 2) {
                    Text(statusTitle)
                        .font(.headline)
                    
                    Text(statusDescription)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                Spacer()
                
                // Actions
                HStack(spacing: 8) {
                    if captureState == .success {
                        Button("View") {
                            viewCapturedArticle()
                        }
                        .controlSize(.small)
                    }
                    
                    Button(captureState == .success ? "Done" : "Cancel") {
                        dismissOverlay()
                    }
                    .controlSize(.small)
                    .keyboardShortcut(.escape, modifiers: [])
                }
            }
            .padding()
            
            // Progress bar
            if captureState == .capturing || captureState == .processing {
                ProgressView(value: progress)
                    .progressViewStyle(.linear)
                    .frame(height: 2)
            }
        }
        .frame(maxWidth: 500)
        .background(.regularMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 10))
        .shadow(radius: 10)
        .padding()
        .transition(.asymmetric(
            insertion: .scale(scale: 0.8).combined(with: .opacity),
            removal: .scale(scale: 0.8).combined(with: .opacity)
        ))
        .task {
            await startCapture()
        }
    }
    
    private var statusTitle: String {
        switch captureState {
        case .waiting:
            return "Ready to Capture"
        case .capturing:
            return "Capturing Content..."
        case .processing:
            return "Processing Article..."
        case .success:
            return "Saved Successfully!"
        case .error:
            return "Capture Failed"
        }
    }
    
    private var statusDescription: String {
        switch captureState {
        case .waiting:
            return "Waiting for browser extension"
        case .capturing:
            return "Receiving content from browser (using mock data)"
        case .processing:
            return "Analyzing and storing article"
        case .success:
            if let content = capturedContent {
                return "\"\(content.title)\" has been saved"
            }
            return "Article saved to your library"
        case .error:
            return error?.localizedDescription ?? "An error occurred"
        }
    }
    
    private func startCapture() async {
        print("CaptureOverlay: Starting capture process")
        captureState = .capturing
        
        // Simulate progress
        for i in 0...30 {
            progress = Double(i) / 100
            try? await Task.sleep(nanoseconds: 10_000_000)
        }
        
        do {
            print("CaptureOverlay: Attempting to receive content from browser")
            // Get content from browser extension via native messaging
            guard let content = await receiveCapturedContent() else {
                print("CaptureOverlay: No content received")
                throw CaptureError.noContent
            }
            
            capturedContent = content
            captureState = .processing
            print("CaptureOverlay: Content received, processing article")
            
            // Continue progress
            for i in 31...70 {
                progress = Double(i) / 100
                try? await Task.sleep(nanoseconds: 10_000_000)
            }
            
            // Process the content
            print("CaptureOverlay: Processing content with ContentProcessor")
            let processor = ContentProcessor()
            let article = try await processor.process(content)
            
            print("CaptureOverlay: Article processed successfully: \(article.title)")
            
            // Add to app state
            await MainActor.run {
                appState.articles.insert(article, at: 0)
                appState.lastCapturedArticle = article
                print("CaptureOverlay: Article added to app state")
            }
            
            // Complete progress
            for i in 71...100 {
                progress = Double(i) / 100
                try? await Task.sleep(nanoseconds: 5_000_000)
            }
            
            captureState = .success
            print("CaptureOverlay: Capture completed successfully")
            
            // Auto-dismiss after success
            try? await Task.sleep(nanoseconds: 2_000_000_000)
            if captureState == .success {
                dismissOverlay()
            }
            
        } catch {
            self.error = error as? CaptureError ?? .unknown
            print("CaptureOverlay: Setting error state")
            captureState = .error
        }
    }
    
    private func receiveCapturedContent() async -> BrowserCapturedContent? {
        print("CaptureOverlay: Attempting to receive content from browser")
        
        // FUTURE IMPLEMENTATION:
        // 1. Native Messaging Communication:
        //    - Listen for messages from browser extension via NativeMessaging.shared
        //    - Browser extension sends captured HTML when user clicks extension icon
        //    - Or when user uses keyboard shortcut (Cmd+Shift+S)
        
        // 2. AppleScript Browser Integration (Alternative):
        //    - Use AppleScript to get current tab URL and title from Safari/Chrome
        //    - Example: tell application "Safari" to get {URL, name} of current tab
        //    - Then fetch full HTML content via HTTP request
        
        // 3. System-wide Capture (Future Enhancement):
        //    - Use Accessibility API to capture content from any app
        //    - Extract text from PDFs, emails, notes apps
        //    - Not limited to just web browsers
        
        // For now, check if NativeMessaging has pending content
        if NativeMessaging.isLaunchedByNativeMessaging() {
            print("CaptureOverlay: App launched by native messaging, waiting for content...")
            // TODO: Implement actual message reception
        }
        
        // Mock implementation for testing
        print("CaptureOverlay: Using mock data for testing")
        
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 500_000_000)
        
        return CapturedContent(
            url: "https://example.com/article",
            title: "Sample Article Title",
            htmlContent: "<h1>Sample Article</h1><p>Content...</p>",
            timestamp: Date(),
            metadata: CapturedContent.Metadata(
                description: "Sample article description",
                author: "John Doe",
                publishDate: "2024-01-01",
                type: "article",
                userId: nil
            )
        )
    }
    
    private func viewCapturedArticle() {
        if let article = appState.lastCapturedArticle {
            appState.selectedArticle = article
            appState.viewMode = .reading
        }
        dismissOverlay()
    }
    
    private func dismissOverlay() {
        withAnimation {
            appState.isCapturing = false
        }
    }
}

// MARK: - Capture Indicator (Menu Bar)

struct CaptureIndicator: View {
    @EnvironmentObject var appState: AppState
    @State private var isHovering = false
    
    var body: some View {
        Button(action: { appState.triggerCapture() }) {
            HStack(spacing: 4) {
                Image(systemName: "arrow.down.doc.fill")
                    .font(.body)
                
                if isHovering {
                    Text("Save")
                        .font(.caption)
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(isHovering ? Color.accentColor.opacity(0.1) : Color.clear)
            .clipShape(Capsule())
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
        .keyboardShortcut("s", modifiers: [.command, .shift])
        .help("Save current page (⌘⇧S)")
    }
}

// MARK: - Data Types

typealias BrowserCapturedContent = CapturedContent

enum CaptureError: LocalizedError {
    case noContent
    case browserNotConnected
    case processingFailed
    case networkError
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .noContent:
            return "No content received from browser"
        case .browserNotConnected:
            return "Browser extension not connected"
        case .processingFailed:
            return "Failed to process article"
        case .networkError:
            return "Network connection error"
        case .unknown:
            return "An unknown error occurred"
        }
    }
}

// MARK: - Preview

#Preview("Capture Overlay") {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        CaptureOverlay()
            .environmentObject(AppState())
    }
    .frame(width: 600, height: 400)
}

#Preview("Capture Indicator") {
    HStack {
        Spacer()
        CaptureIndicator()
            .environmentObject(AppState())
    }
    .padding()
    .frame(width: 300, height: 100)
}