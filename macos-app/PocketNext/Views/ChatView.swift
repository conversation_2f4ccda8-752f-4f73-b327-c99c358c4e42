import SwiftUI

struct ChatView: View {
    @EnvironmentObject var appState: AppState
    @State private var messages: [ChatMessage] = []
    @State private var inputText = ""
    @State private var isProcessing = false
    @State private var conversationId: UUID?
    @FocusState private var isInputFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Messages list
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 16) {
                        // Welcome message
                        if messages.isEmpty {
                            WelcomeMessageView()
                                .id("welcome")
                        }
                        
                        // Chat messages
                        ForEach(messages) { message in
                            MessageBubble(message: message)
                                .id(message.id)
                        }
                        
                        // Processing indicator
                        if isProcessing {
                            ProcessingIndicator()
                                .id("processing")
                        }
                    }
                    .padding()
                }
                .onChange(of: messages.count) { _ in
                    withAnimation {
                        if let lastMessage = messages.last {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        } else if isProcessing {
                            proxy.scrollTo("processing", anchor: .bottom)
                        }
                    }
                }
            }
            
            Divider()
            
            // Input area
            ChatInputArea(
                text: $inputText,
                isProcessing: isProcessing,
                onSubmit: sendMessage
            )
            .focused($isInputFocused)
        }
        .navigationTitle("Chat")
        .navigationSubtitle("Ask questions about your saved articles")
        .toolbar {
            ToolbarItemGroup {
                // Sync status
                if appState.enableCloudSync {
                    ChatSyncStatusView()
                }
                
                // Clear chat
                Button(action: clearChat) {
                    Label("Clear Chat", systemImage: "trash")
                }
                .disabled(messages.isEmpty)
                
                // Settings
                Button(action: showSettings) {
                    Label("Settings", systemImage: "gear")
                }
            }
        }
        .task {
            // Load chat history if available
            await loadChatHistory()
        }
        .onAppear {
            isInputFocused = true
        }
    }
    
    private func sendMessage() {
        guard !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = ChatMessage(
            role: .user,
            content: inputText,
            timestamp: Date()
        )
        
        messages.append(userMessage)
        let query = inputText
        inputText = ""
        
        Task {
            await processQuery(query)
        }
    }
    
    private func processQuery(_ query: String) async {
        isProcessing = true
        
        do {
            // Create conversation if needed
            if conversationId == nil {
                if let ragService = appState.ragService {
                    let conversation = try await ragService.createConversation()
                    conversationId = conversation.id
                }
            }
            
            // Use RAG service for response
            guard let ragService = appState.ragService else {
                throw ChatError.serviceNotAvailable
            }
            
            let response = try await ragService.query(query, conversationId: conversationId)
            
            // Check if web search is needed
            if response.requiresWebSearch && response.confidence < 0.6 {
                // Automatically search the web if confidence is very low
                isProcessing = true
                let enhancedResponse = try await ragService.queryWithWebSearch(query, conversationId: conversationId)
                isProcessing = false
                
                let assistantMessage = ChatMessage(
                    role: .assistant,
                    content: enhancedResponse.answer,
                    sources: enhancedResponse.sources,
                    timestamp: Date(),
                    enhancedWithWeb: true
                )
                messages.append(assistantMessage)
            } else if response.requiresWebSearch {
                // Ask user if they want web search
                let webMessage = ChatMessage(
                    role: .assistant,
                    content: response.answer + "\n\nWould you like me to search the web for more information?",
                    sources: response.sources,
                    timestamp: Date(),
                    needsWebSearch: true
                )
                messages.append(webMessage)
            } else {
                let assistantMessage = ChatMessage(
                    role: .assistant,
                    content: response.answer,
                    sources: response.sources,
                    timestamp: Date()
                )
                messages.append(assistantMessage)
            }
            
        } catch {
            let errorMessage = ChatMessage(
                role: .assistant,
                content: "I encountered an error while processing your question: \(error.localizedDescription)",
                timestamp: Date(),
                isError: true
            )
            messages.append(errorMessage)
        }
        
        isProcessing = false
    }
    
    private func showSettings() {
        // Open settings view
        appState.viewMode = .feed // Temporarily
        // TODO: Add settings sheet
    }
    
    private func clearChat() {
        messages = []
        conversationId = nil
    }
    
    private func loadChatHistory() async {
        // Load most recent conversation if available
        guard let ragService = appState.ragService else { return }
        
        do {
            // Load the most recent conversation
            let conversations = try await appState.database.fetchAllConversations()
            if let mostRecent = conversations.last {
                conversationId = mostRecent.id
                
                // Load messages for this conversation
                let dbMessages = try await ragService.loadConversationHistory(conversationId: mostRecent.id)
                
                messages = dbMessages.map { dbMessage in
                    ChatMessage(
                        role: dbMessage.role == "user" ? .user : .assistant,
                        content: dbMessage.content,
                        timestamp: dbMessage.timestamp
                    )
                }
            }
        } catch {
            print("Failed to load chat history: \(error)")
        }
    }
}

// MARK: - Message Bubble

struct MessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.role == .user {
                Spacer(minLength: 60)
            }
            
            // Avatar
            Image(systemName: message.role == .user ? "person.circle.fill" : "brain.head.profile")
                .font(.title2)
                .foregroundStyle(message.role == .user ? .blue : .purple)
                .frame(width: 32, height: 32)
            
            // Message content
            VStack(alignment: .leading, spacing: 8) {
                // Role and timestamp
                HStack {
                    Text(message.role == .user ? "You" : "Assistant")
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
                
                // Message text
                if message.isError {
                    Text(message.content)
                        .foregroundStyle(.red)
                } else {
                    Text(message.content)
                }
                
                // Sources
                if !message.sources.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Sources:")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                            
                            if message.enhancedWithWeb {
                                Label("Enhanced with web search", systemImage: "globe")
                                    .font(.caption2)
                                    .foregroundStyle(.blue)
                            }
                        }
                        
                        ForEach(message.sources) { source in
                            SourceLink(source: source)
                        }
                    }
                    .padding(.top, 4)
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(message.role == .user ? Color.blue.opacity(0.1) : Color.purple.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(message.isError ? Color.red.opacity(0.3) : Color.clear, lineWidth: 1)
            )
            
            if message.role == .assistant {
                Spacer(minLength: 60)
            }
        }
    }
}

// MARK: - Chat Input Area

struct ChatInputArea: View {
    @Binding var text: String
    let isProcessing: Bool
    let onSubmit: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Text editor
            TextEditor(text: $text)
                .font(.body)
                .scrollContentBackground(.hidden)
                .padding(8)
                .background(Color(NSColor.textBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(.separator, lineWidth: 1)
                )
                .frame(minHeight: 40, maxHeight: 120)
                .onSubmit {
                    if !text.isEmpty {
                        onSubmit()
                    }
                }
            
            // Send button
            Button(action: onSubmit) {
                Image(systemName: "arrow.up.circle.fill")
                    .font(.title)
                    .foregroundStyle(.white, .blue)
            }
            .buttonStyle(.plain)
            .disabled(text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isProcessing)
            .keyboardShortcut(.return, modifiers: [])
        }
        .padding()
    }
}

// MARK: - Welcome Message

struct WelcomeMessageView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "bubble.left.and.bubble.right.fill")
                .font(.system(size: 48))
                .foregroundStyle(.purple)
            
            Text("Welcome to Pocket-next Chat")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Ask questions about your saved articles and I'll help you find relevant information")
                .font(.body)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .frame(maxWidth: 400)
            
            // Example questions
            VStack(alignment: .leading, spacing: 12) {
                Text("Try asking:")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                
                ForEach(exampleQuestions, id: \.self) { question in
                    ExampleQuestionButton(question: question)
                }
            }
            .padding(.top)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private var exampleQuestions: [String] {
        [
            "What have I saved about machine learning?",
            "Summarize my articles about Swift",
            "Find articles I saved last week",
            "What are the key insights from my productivity articles?"
        ]
    }
}

// MARK: - Supporting Views

struct ProcessingIndicator: View {
    @State private var dots = ""
    
    var body: some View {
        HStack {
            Image(systemName: "brain.head.profile")
                .font(.title2)
                .foregroundStyle(.purple)
            
            Text("Thinking\(dots)")
                .font(.body)
                .foregroundStyle(.secondary)
            
            Spacer()
        }
        .padding()
        .task {
            while true {
                try? await Task.sleep(nanoseconds: 500_000_000)
                dots = dots.count < 3 ? dots + "." : ""
            }
        }
    }
}

struct ExampleQuestionButton: View {
    let question: String
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        Button(action: {
            // TODO: Trigger the question in chat
        }) {
            HStack {
                Image(systemName: "quote.bubble")
                Text(question)
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.accentColor.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(.plain)
    }
}

struct SourceLink: View {
    let source: Source
    @EnvironmentObject var appState: AppState
    @State private var article: Article?
    
    var body: some View {
        Button(action: {
            if let article = article {
                appState.selectedArticle = article
                appState.viewMode = .reading
            } else if source.relevantChunks.isEmpty {
                // Open web URL
                if let url = URL(string: source.url) {
                    NSWorkspace.shared.open(url)
                }
            }
        }) {
            HStack {
                Image(systemName: "doc.text")
                    .font(.caption)
                
                Text(source.title)
                    .font(.caption)
                    .lineLimit(1)
                
                Spacer()
                
                if source.relevantChunks.isEmpty {
                    // Web source
                    Image(systemName: "globe")
                        .font(.caption2)
                        .foregroundStyle(.blue)
                } else {
                    // Local source with score
                    Text("\(Int(source.score * 100))%")
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
                
                Image(systemName: "arrow.right")
                    .font(.caption2)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(.quaternary)
            .clipShape(RoundedRectangle(cornerRadius: 6))
        }
        .buttonStyle(.plain)
        .task {
            // Load article details
            do {
                article = try await appState.database.fetchArticle(id: source.articleId)
            } catch {
                print("Failed to load article: \(error)")
            }
        }
    }
}

// MARK: - Data Models

struct ChatMessage: Identifiable {
    let id = UUID()
    let role: Role
    let content: String
    var sources: [Source] = []
    let timestamp: Date
    var isError: Bool = false
    var needsWebSearch: Bool = false
    var enhancedWithWeb: Bool = false
    
    enum Role {
        case user
        case assistant
    }
}


enum ChatError: LocalizedError {
    case serviceNotAvailable
    case conversationCreationFailed
    
    var errorDescription: String? {
        switch self {
        case .serviceNotAvailable:
            return "Chat service is not available. Please try again later."
        case .conversationCreationFailed:
            return "Failed to create conversation."
        }
    }
}

// MARK: - Sync Status View

struct ChatSyncStatusView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        if let syncService = appState.chatSyncService {
            HStack(spacing: 4) {
                switch syncService.syncStatus {
                case .idle:
                    Image(systemName: "checkmark.icloud")
                        .foregroundStyle(.green)
                    if let lastSync = syncService.lastSyncDate {
                        Text("Synced \(lastSync.formatted(.relative(presentation: .numeric)))")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                case .syncing:
                    ProgressView()
                        .scaleEffect(0.7)
                    Text("Syncing...")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                case .error(let error):
                    Image(systemName: "exclamationmark.icloud")
                        .foregroundStyle(.red)
                        .help(error.localizedDescription)
                    
                case .success:
                    Image(systemName: "checkmark.icloud")
                        .foregroundStyle(.green)
                }
            }
        }
    }
}

// MARK: - Preview

#Preview {
    NavigationStack {
        ChatView()
            .environmentObject(AppState())
    }
    .frame(width: 800, height: 600)
}