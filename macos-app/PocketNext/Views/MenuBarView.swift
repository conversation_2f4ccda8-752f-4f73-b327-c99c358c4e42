import SwiftUI

struct MenuBarView: View {
    @EnvironmentObject var appState: AppState
    @State private var recentArticles: [Article] = []
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                Image(systemName: "bookmark.fill")
                    .font(.title2)
                    .foregroundStyle(.blue)
                
                Text("Pocket-next")
                    .font(.headline)
                
                Spacer()
                
                if appState.articles.filter({ !$0.isRead }).count > 0 {
                    Text("\(appState.articles.filter { !$0.isRead }.count)")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(.blue)
                        .foregroundStyle(.white)
                        .clipShape(Capsule())
                }
            }
            .padding()
            
            Divider()
            
            // Quick Actions
            QuickActionButton(title: "Save Current Page", icon: "plus.circle") {
                print("MenuBarView: Save Current Page clicked")
                // Open main window to show capture overlay
                openMainWindow()
                // Trigger capture after a small delay to ensure window is ready
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    appState.triggerCapture()
                }
            }
            
            QuickActionButton(title: "Open Pocket-next", icon: "book") {
                openMainWindow()
            }
            
            QuickActionButton(title: "Quick Search", icon: "magnifyingglass") {
                openSearchWindow()
            }
            
            Divider()
                .padding(.vertical, 4)
            
            // Recent Articles
            if !recentArticles.isEmpty {
                Text("Recent Articles")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .padding(.bottom, 4)
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 0) {
                        ForEach(recentArticles.prefix(10)) { article in
                            RecentArticleRow(article: article)
                        }
                    }
                }
                .frame(maxHeight: 300)
            } else {
                VStack(spacing: 8) {
                    Image(systemName: "bookmark.slash")
                        .font(.largeTitle)
                        .foregroundStyle(.quaternary)
                    
                    Text("No articles yet")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Text("Save your first article with ⌘⇧S")
                        .font(.caption2)
                        .foregroundStyle(.tertiary)
                }
                .frame(maxWidth: .infinity, maxHeight: 150)
                .padding()
            }
            
            Divider()
            
            // Footer
            HStack {
                Button("Preferences...") {
                    openPreferences()
                }
                .buttonStyle(.plain)
                .font(.caption)
                
                Spacer()
                
                // TODO: Add sync status display once sync engine is connected
                /*
                if let lastSync = appState.syncEngine?.lastSyncDate {
                    Text("Synced \(lastSync, format: .relative(presentation: .abbreviated))")
                        .font(.caption2)
                        .foregroundStyle(.tertiary)
                }
                */
                
                Spacer()
                
                Button("Quit") {
                    NSApplication.shared.terminate(nil)
                }
                .buttonStyle(.plain)
                .font(.caption)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
        .frame(width: 350)
        .task {
            await loadRecentArticles()
        }
        .onReceive(NotificationCenter.default.publisher(for: .articlesDidUpdate)) { _ in
            Task {
                await loadRecentArticles()
            }
        }
    }
    
    private func loadRecentArticles() async {
        do {
            // Use shared instance, not a new one
            recentArticles = try await DatabaseManager.shared.fetchRecentArticles(limit: 10)
        } catch {
            print("Failed to load recent articles: \(error)")
        }
    }
    
    private func openMainWindow() {
        print("MenuBarView: Opening main window")
        NSApp.activate(ignoringOtherApps: true)
        
        // Find the main window or create it
        if let window = NSApp.windows.first(where: { !($0 is NSPanel) && $0.canBecomeMain }) {
            window.makeKeyAndOrderFront(nil)
            print("MenuBarView: Window brought to front")
        } else {
            print("MenuBarView: ⚠️ No main window found, creating new one")
            // Use OpenURL to create a new window
            if let url = URL(string: "pocketnext://main") {
                NSWorkspace.shared.open(url)
            }
        }
    }
    
    private func openSearchWindow() {
        openMainWindow()
        appState.viewMode = .search
        // Focus search field
        DispatchQueue.main.async {
            NSApp.sendAction(#selector(NSResponder.selectAll(_:)), to: nil, from: nil)
        }
    }
    
    private func openPreferences() {
        NSApp.sendAction(Selector(("showSettingsWindow:")), to: nil, from: nil)
    }
}

// MARK: - Quick Action Button

struct QuickActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    @State private var isHovering = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .frame(width: 20)
                
                Text(title)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isHovering ? Color.accentColor.opacity(0.1) : Color.clear)
            .clipShape(RoundedRectangle(cornerRadius: 4))
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            isHovering = hovering
        }
    }
}

// MARK: - Recent Article Row

struct RecentArticleRow: View {
    let article: Article
    @EnvironmentObject var appState: AppState
    @State private var isHovering = false
    
    var body: some View {
        Button(action: openArticle) {
            HStack(alignment: .top, spacing: 8) {
                // Content type icon
                Image(systemName: article.contentType.icon)
                    .font(.caption)
                    .foregroundStyle(isHovering ? .primary : .secondary)
                    .frame(width: 16)
                
                VStack(alignment: .leading, spacing: 2) {
                    // Title
                    Text(article.title)
                        .font(.system(size: 11, weight: .medium))
                        .lineLimit(2)
                        .foregroundStyle(isHovering ? Color.primary : Color.primary.opacity(0.9))
                    
                    // Metadata
                    HStack(spacing: 4) {
                        Text(article.domain)
                            .foregroundStyle(.secondary)
                        
                        Text("•")
                            .foregroundStyle(.tertiary)
                        
                        Text("\(article.readingTime) min")
                            .foregroundStyle(.secondary)
                        
                        if !article.isRead {
                            Text("•")
                                .foregroundStyle(.tertiary)
                            
                            Text("New")
                                .foregroundStyle(.blue)
                                .fontWeight(.medium)
                        }
                    }
                    .font(.system(size: 9))
                }
                
                Spacer(minLength: 0)
                
                // Time
                Text(RelativeDateTimeFormatter().localizedString(for: article.capturedAt, relativeTo: Date()))
                    .font(.system(size: 9))
                    .foregroundStyle(.tertiary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isHovering ? Color.accentColor.opacity(0.05) : Color.clear)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            isHovering = hovering
        }
    }
    
    private func openArticle() {
        appState.selectedArticle = article
        appState.viewMode = .reading
        
        NSApp.activate(ignoringOtherApps: true)
        if let window = NSApp.windows.first {
            window.makeKeyAndOrderFront(nil)
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let articlesDidUpdate = Notification.Name("articlesDidUpdate")
}

// MARK: - Preview

#Preview {
    MenuBarView()
        .environmentObject(AppState())
        .frame(width: 350, height: 500)
}