import SwiftUI
import UserNotifications

struct DigestView: View {
    @EnvironmentObject var appState: AppState
    @StateObject private var digestService: DigestService
    @State private var selectedDigest: ArticleDigest?
    @State private var showingSettings = false
    @State private var isGenerating = false
    
    init(database: DatabaseManager) {
        _digestService = StateObject(wrappedValue: DigestService(database: database))
    }
    
    var body: some View {
        NavigationSplitView {
            // Sidebar with digest list
            digestSidebar
        } detail: {
            // Detail view with selected digest
            if let digest = selectedDigest ?? digestService.currentDigest {
                DigestDetailView(digest: digest)
                    .environmentObject(appState)
            } else {
                DigestEmptyView(onGenerate: generateDigest)
            }
        }
        .navigationTitle("Daily Digest")
        .toolbar {
            ToolbarItemGroup {
                Button(action: generateDigest) {
                    Label("Generate Digest", systemImage: "sparkles")
                }
                .disabled(isGenerating)
                
                Button(action: { showingSettings = true }) {
                    Label("Settings", systemImage: "gear")
                }
            }
        }
        .sheet(isPresented: $showingSettings) {
            DigestSettingsView()
        }
        .task {
            await checkNotificationPermission()
            await loadDigests()
        }
    }
    
    // MARK: - Sidebar
    
    private var digestSidebar: some View {
        List(selection: $selectedDigest) {
            Section("Recent Digests") {
                if digestService.isGenerating {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Generating digest...")
                            .foregroundStyle(.secondary)
                    }
                    .padding(.vertical, 8)
                }
                
                ForEach(digestHistory) { digest in
                    DigestRow(digest: digest)
                        .tag(digest)
                }
            }
            
            Section("Quick Stats") {
                if let currentDigest = digestService.currentDigest {
                    StatsSummaryView(stats: currentDigest.stats)
                }
            }
        }
        .listStyle(.sidebar)
        .frame(minWidth: 250)
    }
    
    @State private var digestHistory: [ArticleDigest] = []
    
    // MARK: - Actions
    
    private func generateDigest() {
        Task {
            isGenerating = true
            do {
                let digest = try await digestService.generateDigest()
                selectedDigest = digest
                await loadDigests()
            } catch {
                print("Failed to generate digest: \(error)")
            }
            isGenerating = false
        }
    }
    
    private func loadDigests() async {
        do {
            digestHistory = try await digestService.fetchDigests(limit: 20)
        } catch {
            print("Failed to load digests: \(error)")
        }
    }
    
    private func checkNotificationPermission() async {
        let center = UNUserNotificationCenter.current()
        let settings = await center.notificationSettings()
        
        if settings.authorizationStatus == .notDetermined {
            do {
                _ = try await center.requestAuthorization(options: [.alert, .badge, .sound])
            } catch {
                print("Failed to request notification permission: \(error)")
            }
        }
    }
}

// MARK: - Digest Row

struct DigestRow: View {
    let digest: ArticleDigest
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: digest.type == .daily ? "sun.max.fill" : "calendar")
                    .foregroundStyle(.orange)
                
                Text(digest.type.rawValue.capitalized)
                    .font(.headline)
                
                Spacer()
                
                Text(digest.generatedAt.formatted(date: .abbreviated, time: .omitted))
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Text("\(digest.topArticles.count) articles • \(digest.stats.totalReadingTime) min")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Digest Detail View

struct DigestDetailView: View {
    let digest: ArticleDigest
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Header
                digestHeader
                
                Divider()
                
                // Top Articles
                if !digest.topArticles.isEmpty {
                    topArticlesSection
                }
                
                // Quick Reads
                if !digest.quickReads.isEmpty {
                    quickReadsSection
                }
                
                // Categories
                if !digest.categories.isEmpty {
                    categoriesSection
                }
                
                // Reading Stats
                readingStatsSection
            }
            .padding()
        }
    }
    
    private var digestHeader: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: digest.type == .daily ? "sun.max.fill" : "calendar")
                    .font(.largeTitle)
                    .foregroundStyle(.orange)
                
                VStack(alignment: .leading) {
                    Text("Your \(digest.type.rawValue.capitalized) Digest")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text(digest.generatedAt.formatted(date: .complete, time: .shortened))
                        .foregroundStyle(.secondary)
                }
                
                Spacer()
            }
            
            if digest.stats.articlesSaved > 0 {
                Text("You saved \(digest.stats.articlesSaved) articles this \(digest.type.rawValue)")
                    .font(.title3)
                    .foregroundStyle(.secondary)
            }
        }
    }
    
    private var topArticlesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Top Picks for You", systemImage: "star.fill")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundStyle(.purple)
            
            ForEach(digest.topArticles) { article in
                DigestArticleCard(article: article)
                    .onTapGesture {
                        appState.selectedArticle = article
                        appState.viewMode = .reading
                    }
            }
        }
    }
    
    private var quickReadsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Quick Reads (< 5 min)", systemImage: "bolt.fill")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundStyle(.blue)
            
            ForEach(digest.quickReads) { article in
                QuickReadCard(article: article)
                    .onTapGesture {
                        appState.selectedArticle = article
                        appState.viewMode = .reading
                    }
            }
        }
    }
    
    private var categoriesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("By Category", systemImage: "folder.fill")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundStyle(.green)
            
            ForEach(digest.categories.keys.sorted(), id: \.self) { category in
                if let articles = digest.categories[category] {
                    CategorySection(category: category, articles: articles)
                        .environmentObject(appState)
                }
            }
        }
    }
    
    private var readingStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Your Reading Stats", systemImage: "chart.bar.fill")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundStyle(.orange)
            
            ReadingStatsCard(stats: digest.stats)
        }
    }
}

// MARK: - Article Cards

struct DigestArticleCard: View {
    let article: Article
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Thumbnail or icon
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.accentColor.opacity(0.1))
                .frame(width: 80, height: 80)
                .overlay(
                    Image(systemName: "doc.text.fill")
                        .font(.title2)
                        .foregroundStyle(Color.accentColor)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(article.title)
                    .font(.headline)
                    .lineLimit(2)
                
                if !article.summary.isEmpty {
                    Text(article.summary)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                        .lineLimit(2)
                }
                
                HStack {
                    Label("\(article.readingTime) min", systemImage: "clock")
                    
                    if let author = article.author {
                        Label(author, systemImage: "person")
                    }
                    
                    Spacer()
                }
                .font(.caption)
                .foregroundStyle(.tertiary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(radius: 2)
    }
}

struct QuickReadCard: View {
    let article: Article
    
    var body: some View {
        HStack {
            Image(systemName: "bolt.circle.fill")
                .font(.title2)
                .foregroundStyle(.blue)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(article.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text("\(article.readingTime) min read")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundStyle(.tertiary)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.blue.opacity(0.05))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Category Section

struct CategorySection: View {
    let category: String
    let articles: [Article]
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Label(category.capitalized, systemImage: "tag.fill")
                    .font(.headline)
                    .foregroundStyle(.green)
                
                Spacer()
                
                Text("\(articles.count) articles")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            ForEach(articles.prefix(3)) { article in
                HStack {
                    Text(article.title)
                        .font(.subheadline)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Text("\(article.readingTime) min")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.green.opacity(0.05))
                .clipShape(RoundedRectangle(cornerRadius: 6))
                .onTapGesture {
                    appState.selectedArticle = article
                    appState.viewMode = .reading
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Stats Views

struct StatsSummaryView: View {
    let stats: ReadingStats
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "book.fill")
                Text("\(stats.articlesRead) read")
            }
            
            HStack {
                Image(systemName: "clock.fill")
                Text("\(stats.totalReadingTime) min")
            }
            
            if let topCategory = stats.topCategories.first {
                HStack {
                    Image(systemName: "star.fill")
                    Text(topCategory.category)
                }
            }
        }
        .font(.caption)
        .foregroundStyle(.secondary)
    }
}

struct ReadingStatsCard: View {
    let stats: ReadingStats
    
    var body: some View {
        VStack(spacing: 16) {
            HStack(spacing: 24) {
                StatItem(
                    title: "Articles Read",
                    value: "\(stats.articlesRead)",
                    icon: "book.fill",
                    color: .blue
                )
                
                StatItem(
                    title: "Articles Saved",
                    value: "\(stats.articlesSaved)",
                    icon: "bookmark.fill",
                    color: .green
                )
                
                StatItem(
                    title: "Reading Time",
                    value: "\(stats.totalReadingTime) min",
                    icon: "clock.fill",
                    color: .orange
                )
                
                StatItem(
                    title: "Daily Average",
                    value: "\(stats.averageDailyTime) min",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .purple
                )
            }
            
            if !stats.topCategories.isEmpty {
                Divider()
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Top Categories")
                        .font(.headline)
                    
                    HStack(spacing: 8) {
                        ForEach(stats.topCategories.prefix(5), id: \.category) { item in
                            CategoryTag(
                                name: item.category,
                                count: item.count
                            )
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

struct StatItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct CategoryTag: View {
    let name: String
    let count: Int
    
    var body: some View {
        HStack(spacing: 4) {
            Text(name)
            Text("(\(count))")
                .foregroundStyle(.secondary)
        }
        .font(.caption)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.accentColor.opacity(0.1))
        .clipShape(Capsule())
    }
}

// MARK: - Empty State

struct DigestEmptyView: View {
    let onGenerate: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "sparkles")
                .font(.system(size: 64))
                .foregroundStyle(.secondary)
            
            Text("No Digest Available")
                .font(.largeTitle)
                .fontWeight(.semibold)
            
            Text("Generate a personalized digest based on your saved articles")
                .font(.title3)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .frame(maxWidth: 400)
            
            Button(action: onGenerate) {
                Label("Generate Digest", systemImage: "sparkles")
                    .font(.title3)
            }
            .controlSize(.large)
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Settings View

struct DigestSettingsView: View {
    @StateObject private var preferences = DigestPreferences.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Digest Settings")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Done") {
                    dismiss()
                }
            }
            .padding()
            
            Divider()
            
            // Settings
            Form {
                Section("Digest Delivery") {
                    Toggle("Enable Daily Digest", isOn: $preferences.isEnabled)
                    
                    Picker("Frequency", selection: $preferences.frequency) {
                        ForEach(DigestFrequency.allCases, id: \.self) { frequency in
                            Text(frequency.rawValue.capitalized)
                                .tag(frequency)
                        }
                    }
                    
                    Picker("Delivery Time", selection: $preferences.preferredHour) {
                        ForEach(0..<24) { hour in
                            Text(formatHour(hour))
                                .tag(hour)
                        }
                    }
                    
                    if preferences.frequency == .weekly {
                        Picker("Day of Week", selection: $preferences.preferredWeekday) {
                            ForEach(1...7, id: \.self) { day in
                                Text(weekdayName(day))
                                    .tag(day)
                            }
                        }
                    }
                }
                
                Section("Content Preferences") {
                    // Category selection would go here
                    Text("Category filtering coming soon...")
                        .foregroundStyle(.secondary)
                }
            }
            .formStyle(.grouped)
        }
        .frame(width: 500, height: 400)
    }
    
    private func formatHour(_ hour: Int) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        
        var components = DateComponents()
        components.hour = hour
        
        let date = Calendar.current.date(from: components) ?? Date()
        return formatter.string(from: date)
    }
    
    private func weekdayName(_ day: Int) -> String {
        let formatter = DateFormatter()
        return formatter.weekdaySymbols[day - 1]
    }
}

// MARK: - Preview

#Preview {
    DigestView(database: DatabaseManager())
        .environmentObject(AppState())
        .frame(width: 1000, height: 700)
}