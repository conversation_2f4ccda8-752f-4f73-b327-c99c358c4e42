import SwiftUI
import CloudKit

// MARK: - Sync Status View
struct SyncStatusView: View {
    @EnvironmentObject var syncEngine: SyncEngine
    @State private var showDetails = false
    @State private var animateSync = false
    
    var body: some View {
        HStack(spacing: 8) {
            // Status icon
            statusIcon
                .font(.system(size: 12))
                .foregroundStyle(statusColor)
                .rotationEffect(.degrees(animateSync ? 360 : 0))
                .animation(
                    animateSync ? .linear(duration: 1).repeatForever(autoreverses: false) : .default,
                    value: animateSync
                )
            
            // Status text
            Text(statusText)
                .font(.caption)
                .foregroundStyle(.secondary)
            
            // Details button
            if syncEngine.syncStatus == .error("") {
                Button(action: { showDetails = true }) {
                    Image(systemName: "info.circle")
                        .font(.caption)
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(statusBackgroundColor)
        .clipShape(Capsule())
        .onAppear {
            animateSync = syncEngine.isSyncing
        }
        .onChange(of: syncEngine.isSyncing) { newValue in
            animateSync = newValue
        }
        .popover(isPresented: $showDetails) {
            SyncDetailsView()
                .frame(width: 300, height: 400)
        }
    }
    
    private var statusIcon: some View {
        Group {
            switch syncEngine.syncStatus {
            case .idle:
                Image(systemName: "checkmark.circle.fill")
            case .syncing:
                Image(systemName: "arrow.triangle.2.circlepath")
            case .error:
                Image(systemName: "exclamationmark.triangle.fill")
            }
        }
    }
    
    private var statusColor: Color {
        switch syncEngine.syncStatus {
        case .idle:
            return .green
        case .syncing:
            return .blue
        case .error:
            return .red
        }
    }
    
    private var statusText: String {
        switch syncEngine.syncStatus {
        case .idle:
            if let lastSync = syncEngine.lastSyncDate {
                let formatter = RelativeDateTimeFormatter()
                formatter.unitsStyle = .abbreviated
                return "Synced \(formatter.localizedString(for: lastSync, relativeTo: Date()))"
            }
            return "Up to date"
        case .syncing:
            return "Syncing..."
        case .error(let message):
            return message.isEmpty ? "Sync error" : message
        }
    }
    
    private var statusBackgroundColor: Color {
        switch syncEngine.syncStatus {
        case .idle:
            return Color.green.opacity(0.1)
        case .syncing:
            return Color.blue.opacity(0.1)
        case .error:
            return Color.red.opacity(0.1)
        }
    }
}

// MARK: - Sync Details View
struct SyncDetailsView: View {
    @EnvironmentObject var syncEngine: SyncEngine
    @State private var showingAccountAlert = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Sync Details")
                    .font(.headline)
                Spacer()
                Button(action: { /* Dismiss */ }) {
                    Image(systemName: "xmark.circle.fill")
                        .symbolRenderingMode(.hierarchical)
                }
                .buttonStyle(.plain)
            }
            .padding()
            
            Divider()
            
            // Content
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // Account status
                    accountSection
                    
                    Divider()
                    
                    // Sync statistics
                    syncStatsSection
                    
                    Divider()
                    
                    // Recent activity
                    recentActivitySection
                    
                    Divider()
                    
                    // Actions
                    actionsSection
                }
                .padding()
            }
        }
        .alert("iCloud Account Required", isPresented: $showingAccountAlert) {
            Button("Open System Preferences") {
                NSWorkspace.shared.open(URL(string: "x-apple.systempreferences:com.apple.preferences.internetaccounts")!)
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Please sign in to iCloud in System Preferences to enable sync.")
        }
    }
    
    private var accountSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Label("iCloud Account", systemImage: "person.crop.circle")
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack {
                if let accountStatus = getAccountStatus() {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(accountStatus.email ?? "Unknown Account")
                            .font(.caption)
                        Text(accountStatus.status)
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                    }
                } else {
                    Text("Not signed in")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Button("Sign In") {
                        showingAccountAlert = true
                    }
                    .font(.caption)
                }
            }
        }
    }
    
    private var syncStatsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Label("Sync Statistics", systemImage: "chart.bar")
                .font(.subheadline)
                .fontWeight(.medium)
            
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
                StatCard(title: "Articles", value: "127", icon: "doc.text")
                StatCard(title: "Pending", value: "3", icon: "clock")
                StatCard(title: "Conflicts", value: "0", icon: "exclamationmark.triangle")
                StatCard(title: "Last Sync", value: "2m ago", icon: "arrow.triangle.2.circlepath")
            }
        }
    }
    
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Label("Recent Activity", systemImage: "clock.arrow.circlepath")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(alignment: .leading, spacing: 4) {
                ForEach(mockRecentActivity, id: \.self) { activity in
                    HStack {
                        Circle()
                            .fill(.green)
                            .frame(width: 6, height: 6)
                        Text(activity)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        Spacer()
                    }
                }
            }
        }
    }
    
    private var actionsSection: some View {
        VStack(spacing: 8) {
            Button(action: { Task { await syncEngine.sync() } }) {
                Label("Sync Now", systemImage: "arrow.triangle.2.circlepath")
                    .frame(maxWidth: .infinity)
            }
            .disabled(syncEngine.isSyncing)
            
            Button(action: { /* Reset sync */ }) {
                Label("Reset Sync Data", systemImage: "trash")
                    .frame(maxWidth: .infinity)
            }
            .buttonStyle(.plain)
            .foregroundStyle(.red)
        }
    }
    
    private func getAccountStatus() -> (email: String?, status: String)? {
        // Mock implementation - would check actual CloudKit account
        return ("<EMAIL>", "Active")
    }
    
    private var mockRecentActivity: [String] {
        [
            "Synced 5 articles",
            "Updated reading progress",
            "Resolved 1 conflict",
            "Synced preferences"
        ]
    }
}

// MARK: - Stat Card
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                Spacer()
            }
            
            Text(value)
                .font(.title3)
                .fontWeight(.medium)
            
            Text(title)
                .font(.caption2)
                .foregroundStyle(.secondary)
        }
        .padding(8)
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 6))
    }
}

// MARK: - Sync Conflict Resolution
struct SyncConflictView: View {
    let conflict: SyncConflict
    @State private var selectedVersion: ConflictVersion = .local
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                VStack(alignment: .leading) {
                    Text("Sync Conflict")
                        .font(.headline)
                    Text(conflict.itemTitle)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                }
                
                Spacer()
                
                Button("Cancel") {
                    dismiss()
                }
            }
            .padding()
            
            Divider()
            
            // Conflict details
            ScrollView {
                VStack(spacing: 16) {
                    // Version comparison
                    HStack(spacing: 16) {
                        ConflictVersionCard(
                            version: .local,
                            date: conflict.localDate,
                            changes: conflict.localChanges,
                            isSelected: selectedVersion == .local,
                            onSelect: { selectedVersion = .local }
                        )
                        
                        ConflictVersionCard(
                            version: .remote,
                            date: conflict.remoteDate,
                            changes: conflict.remoteChanges,
                            isSelected: selectedVersion == .remote,
                            onSelect: { selectedVersion = .remote }
                        )
                    }
                    .padding()
                    
                    // Preview
                    if let preview = conflict.getPreview(for: selectedVersion) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Preview")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text(preview)
                                .font(.caption)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color(NSColor.controlBackgroundColor))
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                        .padding(.horizontal)
                    }
                }
            }
            
            Divider()
            
            // Actions
            HStack {
                Button("Keep Both") {
                    resolveConflict(with: .keepBoth)
                }
                
                Spacer()
                
                Button("Use Selected") {
                    resolveConflict(with: selectedVersion == .local ? .useLocal : .useRemote)
                }
                .keyboardShortcut(.defaultAction)
            }
            .padding()
        }
        .frame(width: 600, height: 400)
    }
    
    private func resolveConflict(with resolution: ConflictResolution) {
        // Handle conflict resolution
        dismiss()
    }
}

// MARK: - Conflict Version Card
struct ConflictVersionCard: View {
    let version: ConflictVersion
    let date: Date
    let changes: [String]
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Label(version.title, systemImage: version.icon)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundStyle(.blue)
                }
            }
            
            Text(date.formatted(date: .abbreviated, time: .shortened))
                .font(.caption)
                .foregroundStyle(.secondary)
            
            Divider()
            
            VStack(alignment: .leading, spacing: 4) {
                ForEach(changes, id: \.self) { change in
                    HStack {
                        Text("•")
                        Text(change)
                    }
                    .font(.caption2)
                    .foregroundStyle(.secondary)
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(isSelected ? Color.accentColor.opacity(0.1) : Color(NSColor.controlBackgroundColor))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 2)
        )
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onTapGesture(perform: onSelect)
    }
}

// MARK: - Data Models
struct SyncConflict {
    let id: UUID
    let itemTitle: String
    let localDate: Date
    let remoteDate: Date
    let localChanges: [String]
    let remoteChanges: [String]
    let localContent: String
    let remoteContent: String
    
    func getPreview(for version: ConflictVersion) -> String? {
        switch version {
        case .local:
            return localContent
        case .remote:
            return remoteContent
        }
    }
}

enum ConflictVersion {
    case local
    case remote
    
    var title: String {
        switch self {
        case .local:
            return "Local Version"
        case .remote:
            return "iCloud Version"
        }
    }
    
    var icon: String {
        switch self {
        case .local:
            return "laptopcomputer"
        case .remote:
            return "icloud"
        }
    }
}

enum ConflictResolution {
    case useLocal
    case useRemote
    case keepBoth
}

// MARK: - Preview
#Preview("Sync Status") {
    HStack {
        SyncStatusView()
            .environmentObject(SyncEngine())
    }
    .padding()
    .frame(width: 400, height: 100)
}

#Preview("Sync Details") {
    SyncDetailsView()
        .environmentObject(SyncEngine())
        .frame(width: 300, height: 400)
}

#Preview("Conflict Resolution") {
    SyncConflictView(
        conflict: SyncConflict(
            id: UUID(),
            itemTitle: "Machine Learning Research Paper",
            localDate: Date().addingTimeInterval(-3600),
            remoteDate: Date(),
            localChanges: ["Updated summary", "Added tags"],
            remoteChanges: ["Changed title", "Modified content"],
            localContent: "Local version of the article content...",
            remoteContent: "Remote version of the article content..."
        )
    )
}