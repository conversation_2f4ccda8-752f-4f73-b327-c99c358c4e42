import SwiftUI

struct ContentView: View {
    @EnvironmentObject var appState: AppState
    @State private var columnVisibility = NavigationSplitViewVisibility.all
    
    var body: some View {
        NavigationSplitView(columnVisibility: $columnVisibility) {
            // Sidebar
            SidebarView()
                .navigationSplitViewColumnWidth(min: 200, ideal: 240, max: 300)
        } content: {
            // Content list
            switch appState.viewMode {
            case .feed:
                HomeFeedView(database: appState.database)
            case .digest:
                DigestView(database: appState.database)
            case .search:
                SearchResultsView()
            case .chat:
                ChatView()
            case .reading:
                EmptyView() // Handled in detail view
            }
        } detail: {
            // Detail view
            if let selectedArticle = appState.selectedArticle {
                ReadingView(article: selectedArticle)
            } else {
                EmptyStateView()
            }
        }
        .navigationSplitViewStyle(.balanced)
        .searchable(text: $appState.searchQuery, placement: .toolbar)
        .onSubmit(of: .search) {
            Task {
                await performSearch()
            }
        }
        .toolbar {
            ToolbarItem(placement: .automatic) {
                SyncStatusView()
            }
        }
        .overlay(alignment: .bottom) {
            // Capture overlay
            if appState.isCapturing {
                CaptureOverlay()
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .move(edge: .bottom).combined(with: .opacity)
                    ))
                    .zIndex(999) // Ensure it's on top
                    .animation(.easeInOut(duration: 0.3), value: appState.isCapturing)
            }
        }
    }
    
    private func performSearch() async {
        guard !appState.searchQuery.isEmpty else {
            appState.viewMode = .feed
            return
        }
        
        appState.viewMode = .search
        // Search will be handled by SearchResultsView
    }
}

// MARK: - Sidebar View

struct SidebarView: View {
    @EnvironmentObject var appState: AppState
    @State private var statistics = DatabaseManager.Statistics(
        totalArticles: 0,
        unreadArticles: 0,
        archivedArticles: 0
    )
    
    var body: some View {
        List(selection: Binding(
            get: { appState.viewMode },
            set: { appState.viewMode = $0 }
        )) {
            Section {
                Label("Home", systemImage: "house")
                    .tag(AppState.ViewMode.feed)
                    .badge(statistics.unreadArticles)
                
                Label("Digest", systemImage: "sparkles")
                    .tag(AppState.ViewMode.digest)
                
                Label("Chat", systemImage: "bubble.left.and.bubble.right")
                    .tag(AppState.ViewMode.chat)
            }
            
            Section("Library") {
                Label("All Articles", systemImage: "doc.text")
                    .badge(statistics.totalArticles)
                
                Label("Unread", systemImage: "circle")
                    .badge(statistics.unreadArticles)
                
                Label("Archived", systemImage: "archivebox")
                    .badge(statistics.archivedArticles)
            }
            
            Section("Tags") {
                PopularTagsView()
            }
        }
        .listStyle(.sidebar)
        .navigationTitle("Pocket-next")
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button(action: { appState.triggerCapture() }) {
                    Label("Save", systemImage: "plus.circle")
                }
                .keyboardShortcut("s", modifiers: [.command, .shift])
            }
        }
        .task {
            await loadStatistics()
        }
    }
    
    private func loadStatistics() async {
        do {
            statistics = try await appState.database.fetchStatistics()
        } catch {
            print("Failed to load statistics: \(error)")
        }
    }
}

// MARK: - Empty State View

struct EmptyStateView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "bookmark.slash")
                .font(.system(size: 64))
                .foregroundStyle(.secondary)
            
            Text("No Article Selected")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("Select an article from the list or save a new one")
                .foregroundStyle(.secondary)
            
            HStack(spacing: 16) {
                Button("Save from Browser") {
                    // TODO: Import from browser
                }
                .controlSize(.large)
                
                Text("or press ⌘⇧S")
                    .foregroundStyle(.secondary)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Popular Tags View

struct PopularTagsView: View {
    @State private var popularTags: [(keyword: String, count: Int)] = []
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        ForEach(popularTags.prefix(10), id: \.keyword) { tag in
            HStack {
                Label(tag.keyword, systemImage: "tag")
                Spacer()
                Text("\(tag.count)")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
        .task {
            await loadPopularTags()
        }
    }
    
    private func loadPopularTags() async {
        do {
            let searchEngine = SearchEngine(database: appState.database)
            popularTags = try await searchEngine.getPopularKeywords()
        } catch {
            print("Failed to load popular tags: \(error)")
        }
    }
}

// MARK: - Preview

#Preview {
    ContentView()
        .environmentObject(AppState())
        .frame(width: 1200, height: 800)
}