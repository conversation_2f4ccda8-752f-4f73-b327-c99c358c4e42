import SwiftUI

struct ReadingView: View {
    let article: Article
    @EnvironmentObject var appState: AppState
    @State private var fontSize: CGFloat = 18
    @State private var lineSpacing: CGFloat = 8
    @State private var showAnnotations = false
    @State private var annotations: [TextAnnotation] = []
    @State private var isFullScreen = false
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header
                    if !isFullScreen {
                        ArticleHeader(article: article)
                            .id("header")
                    }
                    
                    // Content
                    Text(article.content)
                        .font(.system(size: fontSize))
                        .lineSpacing(lineSpacing)
                        .textSelection(.enabled)
                        .overlay(alignment: .topLeading) {
                            if showAnnotations {
                                AnnotationOverlay(annotations: annotations)
                            }
                        }
                        .padding(.horizontal, isFullScreen ? 120 : 60)
                        .id("content")
                }
                .frame(maxWidth: 800)
                .padding(.vertical, 40)
            }
            .background(Color(NSColor.textBackgroundColor))
            .toolbar {
                ReadingToolbar(
                    fontSize: $fontSize,
                    lineSpacing: $lineSpacing,
                    showAnnotations: $showAnnotations,
                    isFullScreen: $isFullScreen,
                    article: article
                )
            }
            .navigationTitle(isFullScreen ? "" : article.title)
            .navigationBarBackButtonHidden(isFullScreen)
        }
        .task {
            await markAsRead()
            await loadAnnotations()
        }
        .sheet(isPresented: $isFullScreen) {
            FullScreenReadingView(
                article: article,
                fontSize: $fontSize,
                lineSpacing: $lineSpacing,
                showAnnotations: $showAnnotations,
                isFullScreen: $isFullScreen
            )
            .frame(minWidth: 800, minHeight: 600)
        }
    }
    
    private func markAsRead() async {
        do {
            try await DatabaseManager.shared.markAsRead(article.id)
        } catch {
            print("Failed to mark as read: \(error)")
        }
    }
    
    private func loadAnnotations() async {
        // TODO: Load AI-generated annotations
        // For now, create sample annotations
        annotations = [
            TextAnnotation(
                id: UUID(),
                range: NSRange(location: 100, length: 50),
                text: "Key insight",
                explanation: "This paragraph contains the main argument of the article."
            )
        ]
    }
}

// MARK: - Article Header

struct ArticleHeader: View {
    let article: Article
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title
            Text(article.title)
                .font(.largeTitle)
                .fontWeight(.bold)
                .textSelection(.enabled)
            
            // Metadata
            HStack(spacing: 24) {
                if let author = article.author {
                    Label(author, systemImage: "person")
                }
                
                Label(article.domain, systemImage: "globe")
                
                if let publishDate = article.publishDate {
                    Label(publishDate.formatted(date: .abbreviated, time: .omitted),
                          systemImage: "calendar")
                }
                
                Label("\(article.readingTime) min", systemImage: "timer")
            }
            .font(.subheadline)
            .foregroundStyle(.secondary)
            
            // Summary
            Text(article.summary)
                .font(.title3)
                .foregroundStyle(.secondary)
                .padding(.vertical, 8)
                .textSelection(.enabled)
            
            // Keywords
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(article.keywords, id: \.self) { keyword in
                        Text(keyword)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(.quaternary)
                            .clipShape(Capsule())
                    }
                }
            }
            
            Divider()
        }
        .padding(.horizontal, 60)
    }
}

// MARK: - Reading Toolbar

struct ReadingToolbar: ToolbarContent {
    @Binding var fontSize: CGFloat
    @Binding var lineSpacing: CGFloat
    @Binding var showAnnotations: Bool
    @Binding var isFullScreen: Bool
    let article: Article
    
    var body: some ToolbarContent {
        ToolbarItemGroup {
            // Font size controls
            Menu {
                Button("Small") { withAnimation { fontSize = 16 } }
                Button("Medium") { withAnimation { fontSize = 18 } }
                Button("Large") { withAnimation { fontSize = 20 } }
                Button("Extra Large") { withAnimation { fontSize = 24 } }
                
                Divider()
                
                Slider(value: $fontSize, in: 14...28, step: 1) {
                    Text("Font Size")
                }
                .frame(width: 200)
            } label: {
                Label("Text Size", systemImage: "textformat.size")
            }
            
            // Annotations toggle
            Button(action: { showAnnotations.toggle() }) {
                Label("Annotations", systemImage: showAnnotations ? "bubble.left.fill" : "bubble.left")
            }
            
            // Full screen
            Button(action: { isFullScreen.toggle() }) {
                Label("Full Screen", systemImage: isFullScreen ? "arrow.down.right.and.arrow.up.left" : "arrow.up.left.and.arrow.down.right")
            }
            
            Divider()
            
            // Share
            ShareLink(item: URL(string: article.url)!) {
                Label("Share", systemImage: "square.and.arrow.up")
            }
            
            // Archive
            Button(action: { archiveArticle() }) {
                Label("Archive", systemImage: "archivebox")
            }
        }
    }
    
    private func archiveArticle() {
        Task {
            do {
                try await DatabaseManager.shared.archive(article.id)
            } catch {
                print("Failed to archive: \(error)")
            }
        }
    }
}

// MARK: - Full Screen Reading View

struct FullScreenReadingView: View {
    let article: Article
    @Binding var fontSize: CGFloat
    @Binding var lineSpacing: CGFloat
    @Binding var showAnnotations: Bool
    @Binding var isFullScreen: Bool
    
    var body: some View {
        ZStack {
            // Background
            Color(NSColor.textBackgroundColor)
                .ignoresSafeArea()
            
            // Content
            ScrollView {
                VStack(spacing: 40) {
                    // Minimal header
                    VStack(spacing: 16) {
                        Text(article.title)
                            .font(.system(size: 36, weight: .bold))
                        
                        HStack {
                            if let author = article.author {
                                Text(author)
                            }
                            
                            if article.author != nil && article.publishDate != nil {
                                Text("•")
                            }
                            
                            if let date = article.publishDate {
                                Text(date.formatted(date: .long, time: .omitted))
                            }
                        }
                        .font(.title3)
                        .foregroundStyle(.secondary)
                    }
                    .padding(.top, 60)
                    
                    // Article content
                    Text(article.content)
                        .font(.system(size: fontSize))
                        .lineSpacing(lineSpacing)
                        .textSelection(.enabled)
                }
                .frame(maxWidth: 700)
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            
            // Floating controls
            VStack {
                HStack {
                    Spacer()
                    
                    Button(action: { isFullScreen = false }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .symbolRenderingMode(.hierarchical)
                    }
                    .buttonStyle(.plain)
                    .padding()
                }
                
                Spacer()
            }
        }
    }
}

// MARK: - Annotation Support

struct TextAnnotation: Identifiable {
    let id: UUID
    let range: NSRange
    let text: String
    let explanation: String
}

struct AnnotationOverlay: View {
    let annotations: [TextAnnotation]
    
    var body: some View {
        // TODO: Implement annotation overlay
        EmptyView()
    }
}

