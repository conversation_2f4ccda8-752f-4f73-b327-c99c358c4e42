import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var syncEngine: SyncEngine
    @State private var selectedTab = SettingsTab.general
    
    enum SettingsTab: String, CaseIterable {
        case general = "General"
        case parsing = "Parsing"
        case sync = "Sync"
        case advanced = "Advanced"
        
        var icon: String {
            switch self {
            case .general: return "gear"
            case .parsing: return "doc.text.magnifyingglass"
            case .sync: return "icloud"
            case .advanced: return "wrench.and.screwdriver"
            }
        }
    }
    
    var body: some View {
        TabView(selection: $selectedTab) {
            GeneralSettingsView()
                .tabItem {
                    Label(SettingsTab.general.rawValue, systemImage: SettingsTab.general.icon)
                }
                .tag(SettingsTab.general)
            
            ParsingSettingsView()
                .tabItem {
                    Label(SettingsTab.parsing.rawValue, systemImage: SettingsTab.parsing.icon)
                }
                .tag(SettingsTab.parsing)
            
            SyncSettingsView()
                .tabItem {
                    Label(SettingsTab.sync.rawValue, systemImage: SettingsTab.sync.icon)
                }
                .tag(SettingsTab.sync)
            
            AdvancedSettingsView()
                .tabItem {
                    Label(SettingsTab.advanced.rawValue, systemImage: SettingsTab.advanced.icon)
                }
                .tag(SettingsTab.advanced)
        }
        .frame(width: 500, height: 400)
    }
}

// MARK: - General Settings

struct GeneralSettingsView: View {
    @AppStorage("defaultLayout") private var defaultLayout = FeedLayout.grid
    @AppStorage("defaultSortOrder") private var defaultSortOrder = SortOrder.newest
    @AppStorage("articlesPerPage") private var articlesPerPage = 50
    @AppStorage("showReadingProgress") private var showReadingProgress = true
    @AppStorage("autoArchiveAfterDays") private var autoArchiveAfterDays = 30
    @AppStorage("enableMenuBarIcon") private var enableMenuBarIcon = true
    
    var body: some View {
        Form {
            Section("Display") {
                Picker("Default Layout:", selection: $defaultLayout) {
                    ForEach(FeedLayout.allCases) { layout in
                        Label(layout.title, systemImage: layout.icon)
                            .tag(layout)
                    }
                }
                
                Picker("Default Sort Order:", selection: $defaultSortOrder) {
                    ForEach(SortOrder.allCases) { order in
                        Label(order.title, systemImage: order.icon)
                            .tag(order)
                    }
                }
                
                HStack {
                    Stepper("Articles per page:", value: $articlesPerPage, in: 10...200, step: 10)
                    Text("\(articlesPerPage)")
                        .monospacedDigit()
                }
                
                Toggle("Show reading progress indicators", isOn: $showReadingProgress)
            }
            
            Section("Behavior") {
                HStack {
                    Text("Auto-archive articles after:")
                    Picker("", selection: $autoArchiveAfterDays) {
                        Text("Never").tag(0)
                        Text("7 days").tag(7)
                        Text("14 days").tag(14)
                        Text("30 days").tag(30)
                        Text("60 days").tag(60)
                        Text("90 days").tag(90)
                    }
                    .labelsHidden()
                }
                
                Toggle("Show menu bar icon", isOn: $enableMenuBarIcon)
                    .help("Provides quick access to save pages and recent articles")
            }
        }
        .formStyle(.grouped)
        .padding()
    }
}

// MARK: - Parsing Settings

struct ParsingSettingsView: View {
    @AppStorage("parseServerURL") private var parseServerURL = "http://localhost:8000"
    @AppStorage("enableLocalParsing") private var enableLocalParsing = false
    @AppStorage("parseTimeout") private var parseTimeout = 30
    @AppStorage("maxRetries") private var maxRetries = 3
    @State private var isTestingConnection = false
    @State private var connectionStatus: ConnectionStatus?
    
    enum ConnectionStatus {
        case success
        case failure(String)
    }
    
    var body: some View {
        Form {
            Section("Parse Server") {
                TextField("Server URL:", text: $parseServerURL)
                    .textFieldStyle(.roundedBorder)
                
                HStack {
                    Button("Test Connection") {
                        testConnection()
                    }
                    .disabled(isTestingConnection)
                    
                    if isTestingConnection {
                        ProgressView()
                            .controlSize(.small)
                    }
                    
                    if let status = connectionStatus {
                        switch status {
                        case .success:
                            Label("Connected", systemImage: "checkmark.circle.fill")
                                .foregroundStyle(.green)
                        case .failure(let error):
                            Label(error, systemImage: "exclamationmark.triangle.fill")
                                .foregroundStyle(.red)
                        }
                    }
                }
                
                Toggle("Enable local parsing fallback", isOn: $enableLocalParsing)
                    .help("Use basic parsing when server is unavailable")
            }
            
            Section("Browser Extension") {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "puzzlepiece.extension")
                            .font(.title2)
                            .foregroundStyle(.blue)
                        VStack(alignment: .leading) {
                            Text("Browser Extension")
                                .font(.headline)
                            Text("Save content from any website")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        Spacer()
                    }
                    
                    Divider()
                    
                    // Twitter Integration
                    HStack {
                        Image(systemName: "bubble.left.and.text.bubble.right")
                            .font(.body)
                            .foregroundStyle(.blue)
                        Text("Twitter/X Bookmarks")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Text("Save all your Twitter bookmarks with one click using the browser extension")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    HStack {
                        Button("Install Extension") {
                            NSWorkspace.shared.open(URL(string: "https://github.com/your-repo/browser-extension")!)
                        }
                        
                        Button("View Guide") {
                            NSWorkspace.shared.open(URL(string: "https://github.com/your-repo/docs/twitter-import")!)
                        }
                    }
                }
                .padding(.vertical, 4)
            }
            
            Section("Performance") {
                HStack {
                    Stepper("Timeout (seconds):", value: $parseTimeout, in: 10...120, step: 10)
                    Text("\(parseTimeout)")
                        .monospacedDigit()
                }
                
                HStack {
                    Stepper("Max retries:", value: $maxRetries, in: 0...5)
                    Text("\(maxRetries)")
                        .monospacedDigit()
                }
            }
        }
        .formStyle(.grouped)
        .padding()
    }
    
    private func testConnection() {
        isTestingConnection = true
        connectionStatus = nil
        
        Task {
            do {
                let url = URL(string: parseServerURL + "/health")!
                let (_, response) = try await URLSession.shared.data(from: url)
                
                if let httpResponse = response as? HTTPURLResponse,
                   httpResponse.statusCode == 200 {
                    connectionStatus = .success
                } else {
                    connectionStatus = .failure("Invalid response")
                }
            } catch {
                connectionStatus = .failure(error.localizedDescription)
            }
            
            isTestingConnection = false
        }
    }
}

// MARK: - Sync Settings

struct SyncSettingsView: View {
    @EnvironmentObject var syncEngine: SyncEngine
    @AppStorage("enableCloudSync") private var enableCloudSync = true
    @AppStorage("syncFrequency") private var syncFrequency = 5 // minutes
    @AppStorage("syncOnlyOnWiFi") private var syncOnlyOnWiFi = false
    @State private var isSyncing = false
    @State private var showSyncDetails = false
    @State private var showConflictResolution = false
    
    var body: some View {
        Form {
            Section("iCloud Sync") {
                Toggle("Enable iCloud sync", isOn: $enableCloudSync)
                    .onChange(of: enableCloudSync) { _, newValue in
                        if newValue {
                            Task {
                                await syncEngine.setup()
                            }
                        }
                    }
                
                if enableCloudSync {
                    HStack {
                        Text("Sync frequency:")
                        Picker("", selection: $syncFrequency) {
                            Text("Every 5 minutes").tag(5)
                            Text("Every 15 minutes").tag(15)
                            Text("Every 30 minutes").tag(30)
                            Text("Every hour").tag(60)
                            Text("Manual only").tag(0)
                        }
                        .labelsHidden()
                    }
                    
                    Toggle("Only sync on Wi-Fi", isOn: $syncOnlyOnWiFi)
                }
            }
            
            Section("Sync Status") {
                // Sync status indicator
                HStack {
                    Text("Status:")
                    Spacer()
                    SyncStatusView()
                }
                
                // Last sync time
                HStack {
                    Text("Last sync:")
                    Spacer()
                    Text(syncEngine.lastSyncDate?.formatted() ?? "Never")
                        .foregroundStyle(.secondary)
                }
                
                // Pending changes
                if syncEngine.pendingChanges > 0 {
                    HStack {
                        Text("Pending changes:")
                        Spacer()
                        Text("\(syncEngine.pendingChanges)")
                            .foregroundStyle(.orange)
                    }
                }
                
                // Conflicts
                if !syncEngine.syncConflicts.isEmpty {
                    HStack {
                        Text("Conflicts:")
                        Spacer()
                        Button("\(syncEngine.syncConflicts.count) conflicts") {
                            showConflictResolution = true
                        }
                        .foregroundStyle(.red)
                    }
                }
                
                // Sync progress
                if syncEngine.isSyncing {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Syncing...")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        ProgressView(value: syncEngine.syncProgress)
                    }
                }
                
                // Actions
                HStack {
                    Button("Sync Now") {
                        Task {
                            await syncEngine.sync()
                        }
                    }
                    .disabled(syncEngine.isSyncing || !enableCloudSync)
                    
                    Spacer()
                    
                    Button("View Details...") {
                        showSyncDetails = true
                    }
                }
            }
        }
        .formStyle(.grouped)
        .padding()
        .sheet(isPresented: $showSyncDetails) {
            SyncDetailsView()
                .frame(width: 400, height: 500)
        }
        .sheet(isPresented: $showConflictResolution) {
            if let firstConflict = syncEngine.syncConflicts.first {
                SyncConflictView(conflict: firstConflict)
            }
        }
    }
}

// MARK: - Advanced Settings

struct AdvancedSettingsView: View {
    @AppStorage("enableDebugLogging") private var enableDebugLogging = false
    @AppStorage("cacheSize") private var cacheSize = 100 // MB
    @State private var showingClearCacheAlert = false
    @State private var showingResetAlert = false
    @State private var databaseSize: String = "Calculating..."
    
    var body: some View {
        Form {
            Section("Database") {
                HStack {
                    Text("Database size:")
                    Text(databaseSize)
                        .foregroundStyle(.secondary)
                }
                
                Button("Optimize Database") {
                    optimizeDatabase()
                }
                
                Button("Export All Articles") {
                    exportArticles()
                }
            }
            
            Section("Cache") {
                HStack {
                    Stepper("Cache size (MB):", value: $cacheSize, in: 50...500, step: 50)
                    Text("\(cacheSize)")
                        .monospacedDigit()
                }
                
                Button("Clear Cache") {
                    showingClearCacheAlert = true
                }
                .alert("Clear Cache?", isPresented: $showingClearCacheAlert) {
                    Button("Cancel", role: .cancel) { }
                    Button("Clear", role: .destructive) {
                        clearCache()
                    }
                } message: {
                    Text("This will remove all cached parsing results. Articles will remain.")
                }
            }
            
            Section("Debug") {
                Toggle("Enable debug logging", isOn: $enableDebugLogging)
                
                Button("Open Logs Folder") {
                    openLogsFolder()
                }
            }
            
            Section {
                Button("Reset All Settings") {
                    showingResetAlert = true
                }
                .foregroundStyle(.red)
                .alert("Reset All Settings?", isPresented: $showingResetAlert) {
                    Button("Cancel", role: .cancel) { }
                    Button("Reset", role: .destructive) {
                        resetSettings()
                    }
                } message: {
                    Text("This will reset all settings to their defaults. Articles will not be affected.")
                }
            }
        }
        .formStyle(.grouped)
        .padding()
        .task {
            await calculateDatabaseSize()
        }
    }
    
    private func calculateDatabaseSize() async {
        // Calculate actual database size
        databaseSize = "42.3 MB" // Mock value
    }
    
    private func optimizeDatabase() {
        // Optimize database
    }
    
    private func exportArticles() {
        // Export articles
    }
    
    private func clearCache() {
        // Clear cache
    }
    
    private func openLogsFolder() {
        // Open logs folder
    }
    
    private func resetSettings() {
        // Reset all settings
    }
}

// MARK: - Preview

#Preview {
    SettingsView()
        .environmentObject(AppState())
        .environmentObject(SyncEngine())
}