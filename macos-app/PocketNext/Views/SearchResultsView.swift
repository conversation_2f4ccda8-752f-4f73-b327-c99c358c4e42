import SwiftUI

struct SearchResultsView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchResults: [SearchResult] = []
    @State private var isSearching = false
    @State private var searchError: Error?
    @State private var groupByMatch = true
    
    var body: some View {
        ScrollView {
            if isSearching {
                ProgressView("Searching...")
                    .frame(maxWidth: .infinity)
                    .padding(50)
            } else if searchResults.isEmpty && !appState.searchQuery.isEmpty {
                EmptySearchView(query: appState.searchQuery)
            } else {
                SearchResultsList(
                    results: searchResults,
                    groupByMatch: groupByMatch
                )
            }
        }
        .navigationTitle("Search Results")
        .navigationSubtitle("\(searchResults.count) results for \"\(appState.searchQuery)\"")
        .toolbar {
            ToolbarItemGroup {
                Toggle("Group by match type", isOn: $groupByMatch)
                    .toggleStyle(.checkbox)
                
                Button("Clear Search") {
                    clearSearch()
                }
                .disabled(appState.searchQuery.isEmpty)
            }
        }
        .task(id: appState.searchQuery) {
            await performSearch()
        }
        .alert("Search Error", isPresented: .constant(searchError != nil)) {
            Button("OK") { searchError = nil }
        } message: {
            Text(searchError?.localizedDescription ?? "Unknown error")
        }
    }
    
    private func performSearch() async {
        guard !appState.searchQuery.isEmpty else {
            searchResults = []
            return
        }
        
        isSearching = true
        searchError = nil
        
        do {
            let searchEngine = try await SearchEngine(database: DatabaseManager.shared)
            searchResults = try await searchEngine.search(appState.searchQuery)
        } catch {
            searchError = error
            searchResults = []
        }
        
        isSearching = false
    }
    
    private func clearSearch() {
        appState.searchQuery = ""
        searchResults = []
        appState.viewMode = .feed
    }
}

// MARK: - Search Results List

struct SearchResultsList: View {
    let results: [SearchResult]
    let groupByMatch: Bool
    @EnvironmentObject var appState: AppState
    
    private var groupedResults: [(key: String, results: [SearchResult])] {
        if !groupByMatch {
            return [("All Results", results)]
        }
        
        let keywordResults = results.filter { $0.matchTypes.contains(.keyword) }
        let semanticResults = results.filter { $0.matchTypes.contains(.semantic) && !$0.matchTypes.contains(.keyword) }
        
        var groups: [(String, [SearchResult])] = []
        
        if !keywordResults.isEmpty {
            groups.append(("Keyword Matches", keywordResults))
        }
        
        if !semanticResults.isEmpty {
            groups.append(("Semantic Matches", semanticResults))
        }
        
        return groups
    }
    
    var body: some View {
        LazyVStack(alignment: .leading, spacing: 20) {
            ForEach(groupedResults, id: \.key) { group in
                if groupByMatch && groupedResults.count > 1 {
                    Text(group.key)
                        .font(.headline)
                        .foregroundStyle(.secondary)
                        .padding(.horizontal)
                }
                
                ForEach(group.results) { result in
                    SearchResultRow(result: result)
                        .onTapGesture {
                            appState.selectedArticle = result.article
                            appState.viewMode = .reading
                        }
                    
                    if result.id != group.results.last?.id {
                        Divider()
                            .padding(.horizontal)
                    }
                }
            }
        }
        .padding(.vertical)
    }
}

// MARK: - Search Result Row

struct SearchResultRow: View {
    let result: SearchResult
    @State private var isHovering = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            relevanceIndicator
            
            VStack(alignment: .leading, spacing: 8) {
                titleView
                summaryView
                matchDetailsView
                keywordsView
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(isHovering ? Color.accentColor.opacity(0.05) : Color.clear)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
    }
    
    private var relevanceIndicator: some View {
        VStack {
            Text(result.relevanceIndicator)
                .font(.caption)
                .foregroundStyle(.orange)
            
            if result.matchTypes.count > 1 {
                Image(systemName: "sparkles")
                    .font(.caption2)
                    .foregroundStyle(.purple)
                    .help("Matches both keyword and semantic search")
            }
        }
        .frame(width: 40)
    }
    
    private var titleView: some View {
        Text(result.article.title)
            .font(.headline)
            .lineLimit(2)
            .foregroundStyle(isHovering ? Color.primary : Color.primary.opacity(0.9))
    }
    
    private var summaryView: some View {
        Text(result.article.summary)
            .font(.subheadline)
            .lineLimit(3)
            .foregroundStyle(.secondary)
    }
    
    private var matchDetailsView: some View {
        HStack(spacing: 16) {
            matchTypeBadges
            Spacer()
            metadataView
        }
    }
    
    private var matchTypeBadges: some View {
        HStack(spacing: 8) {
            if result.matchTypes.contains(.keyword) {
                Label("Keyword", systemImage: "magnifyingglass")
                    .font(.caption)
                    .foregroundStyle(.blue)
            }
            
            if result.matchTypes.contains(.semantic) {
                Label("Semantic", systemImage: "brain")
                    .font(.caption)
                    .foregroundStyle(.purple)
            }
        }
    }
    
    private var metadataView: some View {
        HStack(spacing: 12) {
            Label(result.article.domain, systemImage: "globe")
            
            if let date = result.article.publishDate {
                Label(date.formatted(date: .abbreviated, time: .omitted),
                      systemImage: "calendar")
            }
            
            Label("\(result.article.readingTime) min", systemImage: "timer")
        }
        .font(.caption)
        .foregroundStyle(.tertiary)
    }
    
    @ViewBuilder
    private var keywordsView: some View {
        if !result.article.keywords.isEmpty {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(result.article.keywords.prefix(5), id: \.self) { keyword in
                        Text(keyword)
                            .font(.caption2)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(keywordMatchesQuery(keyword) ? Color.accentColor.opacity(0.2) : Color(NSColor.quaternaryLabelColor))
                            .clipShape(Capsule())
                    }
                }
            }
        }
    }
    
    private func keywordMatchesQuery(_ keyword: String) -> Bool {
        // This would ideally check against the actual search query
        // For now, just highlight if score is high
        return result.keywordScore > 0.5
    }
}

// MARK: - Empty Search View

struct EmptySearchView: View {
    let query: String
    @State private var suggestions: [String] = []
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 48))
                .foregroundStyle(.secondary)
            
            Text("No results for \"\(query)\"")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("Try different keywords or check your spelling")
                .foregroundStyle(.secondary)
            
            if !suggestions.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Try searching for:")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                    
                    ForEach(suggestions, id: \.self) { suggestion in
                        Button(action: {
                            appState.searchQuery = suggestion
                        }) {
                            HStack {
                                Image(systemName: "magnifyingglass")
                                Text(suggestion)
                                Spacer()
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(.quaternary)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                        .buttonStyle(.plain)
                    }
                }
                .frame(maxWidth: 300)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
        .task {
            await loadSuggestions()
        }
    }
    
    private func loadSuggestions() async {
        do {
            let searchEngine = try await SearchEngine(database: DatabaseManager.shared)
            let keywords = try await searchEngine.getPopularKeywords()
            suggestions = keywords.prefix(5).map { $0.keyword }
        } catch {
            print("Failed to load suggestions: \(error)")
        }
    }
}

// MARK: - Preview

#Preview {
    NavigationStack {
        SearchResultsView()
            .environmentObject(AppState())
    }
    .frame(width: 800, height: 600)
}