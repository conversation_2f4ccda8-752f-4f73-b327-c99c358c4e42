import SwiftUI

struct HomeFeedView: View {
    @EnvironmentObject var appState: AppState
    @State private var layout = FeedLayout.grid
    @State private var sortOrder = SortOrder.newest
    @State private var feedType: FeedType = .chronological
    @StateObject private var curationService: FeedCurationService
    
    init(database: DatabaseManager) {
        _curationService = StateObject(wrappedValue: FeedCurationService(database: database))
    }
    
    private var currentArticles: [Article] {
        if feedType == .forYou {
            return curationService.topForYouArticles
        } else {
            return sortedArticles
        }
    }
    
    private var sortedArticles: [Article] {
        switch sortOrder {
        case .newest:
            return appState.articles.sorted { $0.capturedAt > $1.capturedAt }
        case .oldest:
            return appState.articles.sorted { $0.capturedAt < $1.capturedAt }
        case .readingTime:
            return appState.articles.sorted { $0.readingTime < $1.readingTime }
        case .unread:
            return appState.articles.filter { !$0.isRead } + 
                   appState.articles.filter { $0.isRead }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Feed type selector
            FeedTypeSelector(selectedType: $feedType)
                .padding(.horizontal)
                .padding(.vertical, 8)
            
            Divider()
            
            // Main content
            if feedType == .forYou && curationService.isProcessing {
                // Loading state for curated feed
                VStack(spacing: 16) {
                    ProgressView()
                    Text("Curating your personalized feed...")
                        .foregroundStyle(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(.top, 100)
            } else if currentArticles.isEmpty {
                // Empty state
                EmptyFeedView(feedType: feedType)
            } else {
                // Article feed
                ScrollView {
                    switch layout {
                    case .grid:
                        GridLayoutView(articles: currentArticles)
                    case .list:
                        ListLayoutView(articles: currentArticles)
                    case .magazine:
                        MagazineLayoutView(articles: currentArticles)
                    }
                }
            }
        }
        .navigationTitle(feedType == .forYou ? "For You" : "All Articles")
        .navigationSubtitle("\(currentArticles.count) articles")
        .toolbar {
            ToolbarItemGroup {
                // Refresh button
                Button(action: refreshFeed) {
                    Label("Refresh", systemImage: "arrow.clockwise")
                }
                .disabled(curationService.isProcessing)
                
                // Sort menu (only for chronological feed)
                if feedType == .chronological {
                    Menu {
                        Picker("Sort by", selection: $sortOrder) {
                            ForEach(SortOrder.allCases) { order in
                                Label(order.title, systemImage: order.icon)
                                    .tag(order)
                            }
                        }
                    } label: {
                        Label("Sort", systemImage: "arrow.up.arrow.down")
                    }
                }
                
                // Layout picker
                Picker("Layout", selection: $layout) {
                    ForEach(FeedLayout.allCases) { layout in
                        Label(layout.title, systemImage: layout.icon)
                            .tag(layout)
                    }
                }
                .pickerStyle(.segmented)
            }
        }
        .animation(.default, value: layout)
        .animation(.default, value: sortOrder)
        .animation(.default, value: feedType)
        .task {
            if feedType == .forYou {
                await curationService.refreshFeed()
            }
        }
        .onChange(of: feedType) { _, newType in
            if newType == .forYou {
                Task {
                    await curationService.refreshFeed()
                }
            }
        }
    }
    
    private func refreshFeed() {
        Task {
            if feedType == .forYou {
                await curationService.refreshFeed()
            } else {
                await appState.refreshArticles()
            }
        }
    }
}

// MARK: - Feed Type

enum FeedType: String, CaseIterable {
    case chronological = "All"
    case forYou = "For You"
    
    var icon: String {
        switch self {
        case .chronological:
            return "clock"
        case .forYou:
            return "sparkles"
        }
    }
}

// MARK: - Feed Type Selector

struct FeedTypeSelector: View {
    @Binding var selectedType: FeedType
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(FeedType.allCases, id: \.self) { type in
                Button(action: { 
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedType = type
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: type.icon)
                            .font(.system(size: 14, weight: .medium))
                        
                        Text(type.rawValue)
                            .font(.system(size: 15, weight: .medium))
                    }
                    .foregroundStyle(selectedType == type ? .white : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedType == type ? Color.accentColor : Color.clear)
                    )
                    .contentShape(Rectangle())
                }
                .buttonStyle(.plain)
            }
        }
        .padding(3)
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 10))
        .frame(maxWidth: 300)
    }
}

// MARK: - Empty Feed View

struct EmptyFeedView: View {
    let feedType: FeedType
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: feedType == .forYou ? "sparkles" : "tray")
                .font(.system(size: 64))
                .foregroundStyle(.secondary)
            
            Text(feedType == .forYou ? "No Personalized Articles Yet" : "No Articles")
                .font(.largeTitle)
                .fontWeight(.semibold)
            
            Text(feedType == .forYou 
                ? "Save more articles to get personalized recommendations"
                : "Start saving articles from your browser")
                .font(.title3)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .frame(maxWidth: 400)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Layout Types

enum FeedLayout: String, CaseIterable, Identifiable {
    case grid, list, magazine
    
    var id: String { rawValue }
    
    var title: String {
        switch self {
        case .grid: return "Grid"
        case .list: return "List"
        case .magazine: return "Magazine"
        }
    }
    
    var icon: String {
        switch self {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        case .magazine: return "rectangle.stack"
        }
    }
}

enum SortOrder: String, CaseIterable, Identifiable {
    case newest, oldest, readingTime, unread
    
    var id: String { rawValue }
    
    var title: String {
        switch self {
        case .newest: return "Newest First"
        case .oldest: return "Oldest First"
        case .readingTime: return "Reading Time"
        case .unread: return "Unread First"
        }
    }
    
    var icon: String {
        switch self {
        case .newest: return "clock"
        case .oldest: return "clock.arrow.circlepath"
        case .readingTime: return "timer"
        case .unread: return "circle"
        }
    }
}

// MARK: - Grid Layout

struct GridLayoutView: View {
    let articles: [Article]
    @EnvironmentObject var appState: AppState
    
    private let columns = [
        GridItem(.adaptive(minimum: 280, maximum: 400), spacing: 16)
    ]
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(articles) { article in
                ArticleCard(article: article)
                    .onTapGesture {
                        appState.selectedArticle = article
                        appState.viewMode = .reading
                    }
            }
        }
        .padding()
    }
}

// MARK: - List Layout

struct ListLayoutView: View {
    let articles: [Article]
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        LazyVStack(spacing: 1) {
            ForEach(articles) { article in
                ArticleRow(article: article)
                    .onTapGesture {
                        appState.selectedArticle = article
                        appState.viewMode = .reading
                    }
                
                Divider()
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - Magazine Layout

struct MagazineLayoutView: View {
    let articles: [Article]
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        LazyVStack(spacing: 24) {
            ForEach(Array(articles.enumerated()), id: \.element.id) { index, article in
                if index == 0 {
                    // Featured article
                    FeaturedArticleCard(article: article)
                        .onTapGesture {
                            appState.selectedArticle = article
                            appState.viewMode = .reading
                        }
                } else {
                    // Regular articles in pairs
                    if index % 2 == 1 {
                        HStack(spacing: 16) {
                            ArticleCard(article: article)
                                .onTapGesture {
                                    appState.selectedArticle = article
                                    appState.viewMode = .reading
                                }
                            
                            if index + 1 < articles.count {
                                ArticleCard(article: articles[index + 1])
                                    .onTapGesture {
                                        appState.selectedArticle = articles[index + 1]
                                        appState.viewMode = .reading
                                    }
                            } else {
                                Spacer()
                            }
                        }
                    }
                }
            }
        }
        .padding()
    }
}

// MARK: - Article Card

struct ArticleCard: View {
    let article: Article
    @State private var isHovered = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Content type badge
            HStack {
                Label(article.contentType.rawValue.capitalized,
                      systemImage: article.contentType.icon)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                if !article.isRead {
                    Circle()
                        .fill(.blue)
                        .frame(width: 8, height: 8)
                }
            }
            
            // Title
            Text(article.title)
                .font(.headline)
                .lineLimit(2)
                .foregroundStyle(.primary)
            
            // Summary
            Text(article.summary)
                .font(.subheadline)
                .lineLimit(3)
                .foregroundStyle(.secondary)
            
            // Metadata
            HStack {
                Label("\(article.readingTime) min", systemImage: "timer")
                
                Spacer()
                
                Text(article.capturedAt, format: .relative(presentation: .named))
            }
            .font(.caption)
            .foregroundStyle(.tertiary)
            
            // Keywords
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(article.keywords.prefix(5), id: \.self) { keyword in
                        Text(keyword)
                            .font(.caption2)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(.quaternary)
                            .clipShape(Capsule())
                    }
                }
            }
        }
        .padding()
        .background(.background)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isHovered ? Color.accentColor : Color(NSColor.separatorColor), lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(isHovered ? 0.1 : 0.05), radius: isHovered ? 4 : 2, y: isHovered ? 2 : 1)
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
    }
}

// MARK: - Article Row

struct ArticleRow: View {
    let article: Article
    @State private var isHovered = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // Read indicator
            Circle()
                .fill(article.isRead ? Color.clear : .blue)
                .frame(width: 8, height: 8)
                .padding(.top, 6)
            
            VStack(alignment: .leading, spacing: 8) {
                // Title and domain
                HStack(alignment: .top) {
                    Text(article.title)
                        .font(.headline)
                        .lineLimit(2)
                    
                    Spacer()
                    
                    Text(article.domain)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                // Summary
                Text(article.summary)
                    .font(.subheadline)
                    .lineLimit(2)
                    .foregroundStyle(.secondary)
                
                // Metadata
                HStack {
                    Label(article.contentType.rawValue.capitalized,
                          systemImage: article.contentType.icon)
                    
                    Label("\(article.readingTime) min", systemImage: "timer")
                    
                    Spacer()
                    
                    Text(article.capturedAt.formatted(date: .abbreviated, time: .shortened))
                        .foregroundStyle(.tertiary)
                }
                .font(.caption)
                .foregroundStyle(.secondary)
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, isHovered ? 8 : 0)
        .background(isHovered ? Color(NSColor.controlBackgroundColor) : Color.clear)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.15)) {
                isHovered = hovering
            }
        }
    }
}

// MARK: - Featured Article Card

struct FeaturedArticleCard: View {
    let article: Article
    @State private var isHovered = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Featured badge
            HStack {
                Label("Featured", systemImage: "star.fill")
                    .font(.caption)
                    .foregroundStyle(.orange)
                
                Spacer()
                
                if !article.isRead {
                    Circle()
                        .fill(.blue)
                        .frame(width: 10, height: 10)
                }
            }
            
            // Large title
            Text(article.title)
                .font(.largeTitle)
                .fontWeight(.bold)
                .lineLimit(3)
            
            // Author and date
            if let author = article.author {
                HStack {
                    Text("by \(author)")
                    Text("•")
                    Text(article.capturedAt.formatted(date: .long, time: .omitted))
                }
                .font(.subheadline)
                .foregroundStyle(.secondary)
            }
            
            // Summary
            Text(article.summary)
                .font(.title3)
                .lineLimit(4)
                .foregroundStyle(.secondary)
            
            // Keywords and metadata
            HStack {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(article.keywords.prefix(5), id: \.self) { keyword in
                            Text(keyword)
                                .font(.callout)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(.quaternary)
                                .clipShape(Capsule())
                        }
                    }
                }
                
                Spacer()
                
                Label("\(article.readingTime) min read", systemImage: "timer")
                    .font(.callout)
            }
        }
        .padding(24)
        .background(
            LinearGradient(
                colors: [.accentColor.opacity(isHovered ? 0.15 : 0.1), .clear],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(isHovered ? Color.accentColor : Color(NSColor.separatorColor), lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .scaleEffect(isHovered ? 1.01 : 1.0)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
    }
}