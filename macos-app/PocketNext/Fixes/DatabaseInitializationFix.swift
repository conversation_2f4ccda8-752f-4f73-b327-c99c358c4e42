import Foundation
import SwiftUI

// MARK: - Database Initialization Fix

extension DatabaseManager {
    /// Ensures database is initialized before any operations
    /// Fixes: "Failed to load articles: notInitialized" errors
    func ensureInitialized() async throws {
        if !isInitialized {
            if isInitializing {
                // Wait for ongoing initialization
                var attempts = 0
                while isInitializing && attempts < 30 {
                    try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
                    attempts += 1
                }
                
                if !isInitialized {
                    throw DatabaseError.initializationFailed("Initialization timeout")
                }
            } else {
                // Initialize if needed
                try await initialize()
            }
        }
    }
    
    /// Safe wrapper for all database operations
    private func performOperation<T>(_ operation: () async throws -> T) async throws -> T {
        try await ensureInitialized()
        return try await operation()
    }
}

// MARK: - AppState Initialization Fix

extension AppState {
    /// Ensures all services are initialized before use
    func waitForInitialization() async {
        // Wait for database
        do {
            try await database.ensureInitialized()
        } catch {
            print("Database initialization failed: \(error)")
        }
        
        // Wait for other services
        var attempts = 0
        while (ragService == nil || chatSyncService == nil) && attempts < 20 {
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
            attempts += 1
        }
    }
}

// Note: View loading fixes removed - HomeFeedView and DigestView should handle
// initialization internally or use the .task modifier directly where they're used