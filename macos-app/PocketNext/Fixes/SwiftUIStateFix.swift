import SwiftUI

// MARK: - SwiftUI State Update Fixes

/// Safe state updater that prevents "Publishing changes from within view updates"
@MainActor
struct SafeStateUpdater {
    /// Delays state updates to next run loop to avoid publishing during view updates
    static func updateAsync<T>(_ update: @escaping () async -> T) {
        Task { @MainActor in
            // Defer to next run loop
            try? await Task.sleep(nanoseconds: 1)
            _ = await update()
        }
    }
    
    /// Updates state with a delay to ensure it's not during view update
    static func updateDelayed<T>(_ delay: TimeInterval = 0.01, _ update: @escaping () async -> T) {
        Task { @MainActor in
            try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            _ = await update()
        }
    }
}

// MARK: - View Modifier for Safe Updates

struct SafeStateUpdateModifier: ViewModifier {
    let action: () async -> Void
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                SafeStateUpdater.updateAsync(action)
            }
    }
}

extension View {
    /// Performs an async action safely, avoiding state updates during view updates
    func safeOnAppear(perform action: @escaping () async -> Void) -> some View {
        modifier(SafeStateUpdateModifier(action: action))
    }
}

// MARK: - Layout Recursion Prevention

struct LayoutRecursionPreventer: ViewModifier {
    @State private var isLayingOut = false
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                guard !isLayingOut else { return }
                isLayingOut = true
                
                // Reset flag after layout completes
                DispatchQueue.main.async {
                    isLayingOut = false
                }
            }
    }
}

extension View {
    /// Prevents layout recursion by tracking layout state
    func preventLayoutRecursion() -> some View {
        modifier(LayoutRecursionPreventer())
    }
}

// Note: ContentView extensions removed to avoid accessing private properties
// The view modifiers above can be used directly in ContentView if needed