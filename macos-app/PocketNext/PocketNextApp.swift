import SwiftUI
import CloudKit

@main
struct PocketNextApp: App {
    init() {
        print("🚀 PocketNext: App starting...")
        
        // Check if launched by native messaging
        if NativeMessaging.isLaunchedByNativeMessaging() {
            print("🚀 PocketNext: Launched by native messaging - running in bridge mode")
            NativeMessaging.handleNativeMessagingMode()
        } else {
            print("🚀 PocketNext: Normal app launch")
            
            // Initialize native messaging on first run
            _ = NativeMessaging.shared
        }
    }
    @StateObject private var appState = AppState()
    @StateObject private var syncEngine = SyncEngine()
    
    var body: some Scene {
        WindowGroup(id: "main") {
            ContentView()
                .environmentObject(appState)
                .environmentObject(syncEngine)
                .task {
                    // Initialize app state and database
                    do {
                        try await DatabaseManager.shared.initialize()
                        print("PocketNextApp: ✅ Database initialized")
                    } catch {
                        print("PocketNextApp: ❌ Failed to initialize database: \(error)")
                    }
                    
                    // Start sync engine only after database is ready
                    // CloudKit initialization is optional and should not crash the app
                    await syncEngine.setup()
                }
        }
        .windowResizability(.contentSize)
        .handlesExternalEvents(matching: ["main"])
        .commands {
            // Custom menu commands
            CommandGroup(replacing: .newItem) {
                Button("Save Page") {
                    appState.triggerCapture()
                }
                .keyboardShortcut("S", modifiers: [.command, .shift])
            }
            
            CommandGroup(after: .newItem) {
                Button("Import from Browser") {
                    // TODO: Implement browser import
                    print("Import from browser")
                }
            }
        }
        
        #if os(macOS)
        // Menu bar extra for quick access
        MenuBarExtra("Pocket-next", systemImage: "bookmark.fill") {
            MenuBarView()
                .environmentObject(appState)
        }
        .menuBarExtraStyle(.window)
        #endif
        
        // Settings window
        Settings {
            SettingsView()
                .environmentObject(appState)
                .environmentObject(syncEngine)
        }
    }
}

// MARK: - Sync Engine
@MainActor
class SyncEngine: ObservableObject {
    @Published var isSyncing = false
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var syncConflicts: [SyncConflict] = []
    @Published var pendingChanges: Int = 0
    @Published var syncProgress: Double = 0.0
    
    private var container: CKContainer?
    private var database: CKDatabase?
    private var syncTimer: Timer?
    
    enum SyncStatus: Equatable {
        case idle
        case syncing
        case error(String)
        
        static func == (lhs: SyncStatus, rhs: SyncStatus) -> Bool {
            switch (lhs, rhs) {
            case (.idle, .idle), (.syncing, .syncing):
                return true
            case let (.error(lhsMsg), .error(rhsMsg)):
                return lhsMsg == rhsMsg
            default:
                return false
            }
        }
    }
    
    func setup() async {
        // Use the specific container identifier from entitlements
        do {
            // Try to use the container specified in entitlements
            container = CKContainer(identifier: "iCloud.com.pocketnext.macos")
            database = container?.privateCloudDatabase
            
            // Check CloudKit availability
            guard let container = container else {
                syncStatus = .error("CloudKit container not available")
                return
            }
            
            let status = try await container.accountStatus()
            if status != .available {
                syncStatus = .error("iCloud not available")
            } else {
                // Start periodic sync
                startPeriodicSync()
            }
        } catch {
            syncStatus = .error(error.localizedDescription)
            print("CloudKit setup error: \(error)")
        }
    }
    
    func sync() async {
        guard database != nil else { return }
        
        isSyncing = true
        syncStatus = .syncing
        syncProgress = 0.0
        
        do {
            // Simulate sync progress
            for i in 1...10 {
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
                syncProgress = Double(i) / 10.0
            }
            
            // Update sync state
            lastSyncDate = Date()
            syncStatus = .idle
            pendingChanges = 0
            isSyncing = false
        } catch {
            syncStatus = .error(error.localizedDescription)
            isSyncing = false
        }
    }
    
    func resolveConflict(_ conflict: SyncConflict, resolution: ConflictResolution) {
        // Remove from conflicts list
        syncConflicts.removeAll { $0.id == conflict.id }
        
        // Apply resolution
        Task {
            // Implement actual conflict resolution
            await sync()
        }
    }
    
    private func startPeriodicSync() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { _ in
            Task {
                await self.sync()
            }
        }
    }
    
    deinit {
        syncTimer?.invalidate()
    }
}