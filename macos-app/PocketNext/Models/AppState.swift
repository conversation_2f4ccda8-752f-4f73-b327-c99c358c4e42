import SwiftUI
import Combine

@MainActor
class AppState: ObservableObject {
    // Navigation
    @Published var viewMode: ViewMode = .feed
    @Published var selectedArticle: Article?
    
    // Data
    @Published var articles: [Article] = []
    @Published var searchQuery = ""
    
    // Capture state
    @Published var isCapturing = false
    @Published var lastCapturedArticle: Article?
    
    // Sync state
    @Published var isSyncing = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?
    
    // Settings
    @AppStorage("preferredLayout") var preferredLayout = FeedLayout.grid
    @AppStorage("preferredSortOrder") var preferredSortOrder = SortOrder.newest
    @AppStorage("enableCloudSync") var enableCloudSync = true
    
    // Services
    let database = DatabaseManager.shared // Made public for chat view access
    private let configService = ConfigurationService.shared
    private(set) var ragService: RAGService?
    private(set) var chatSyncService: CloudKitChatSyncService?
    
    private var cancellables = Set<AnyCancellable>()
    
    enum ViewMode: String {
        case feed
        case digest
        case search
        case chat
        case reading
    }
    
    init() {
        setupObservers()
        Task {
            await initializeDatabase()
            await loadArticles()
            await initializeRAGService()
            await initializeChatSyncService()
            await startSyncIfEnabled()
        }
    }
    
    // MARK: - Public Methods
    
    func triggerCapture() {
        print("AppState: triggerCapture() called")
        
        // Prevent multiple captures at once
        guard !isCapturing else {
            print("AppState: ⚠️ Capture already in progress, ignoring")
            return
        }
        
        print("AppState: Setting isCapturing = true to show capture overlay")
        isCapturing = true
        
        // TODO: Future implementation will:
        // 1. Communicate with browser extension via native messaging
        // 2. Get the current active browser tab's content
        // 3. Send to parse server for AI extraction
        // 4. Save the processed article to database
        print("AppState: Capture overlay should now be visible")
    }
    
    func refreshArticles() async {
        await loadArticles()
    }
    
    func deleteArticle(_ article: Article) async {
        do {
            try await database.delete(article.id)
            articles.removeAll { $0.id == article.id }
            
            if selectedArticle?.id == article.id {
                selectedArticle = nil
            }
        } catch {
            print("Failed to delete article: \(error)")
        }
    }
    
    func archiveArticle(_ article: Article) async {
        do {
            try await database.archive(article.id)
            
            if let index = articles.firstIndex(where: { $0.id == article.id }) {
                articles[index].isArchived = true
            }
        } catch {
            print("Failed to archive article: \(error)")
        }
    }
    
    func unarchiveArticle(_ article: Article) async {
        do {
            try await database.unarchive(article.id)
            
            if let index = articles.firstIndex(where: { $0.id == article.id }) {
                articles[index].isArchived = false
            }
        } catch {
            print("Failed to unarchive article: \(error)")
        }
    }
    
    // MARK: - Private Methods
    
    private func initializeDatabase() async {
        print("AppState: Initializing database...")
        do {
            try await database.initialize()
            print("AppState: ✅ Database initialized successfully")
        } catch {
            print("AppState: ❌ Failed to initialize database: \(error)")
        }
    }
    
    private func initializeRAGService() async {
        ragService = RAGService(databaseManager: database)
        
        // Wait for RAG service to be ready
        let timeout = Date().addingTimeInterval(10)
        while ragService?.isReady != true && Date() < timeout {
            try? await Task.sleep(nanoseconds: 100_000_000)
        }
        
        if ragService?.isReady == true {
            print("RAG service initialized successfully")
        } else {
            print("RAG service initialization timed out")
        }
    }
    
    private func initializeChatSyncService() async {
        guard enableCloudSync else { return }
        
        chatSyncService = CloudKitChatSyncService()
        
        // Observe sync status changes
        chatSyncService?.$syncStatus
            .sink { [weak self] status in
                switch status {
                case .error(let error):
                    self?.syncError = error
                case .success:
                    self?.lastSyncDate = Date()
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupObservers() {
        // Observe search query changes
        $searchQuery
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] query in
                if query.isEmpty {
                    self?.viewMode = .feed
                }
            }
            .store(in: &cancellables)
        
        // Observe capture state
        $isCapturing
            .filter { !$0 }
            .sink { [weak self] _ in
                Task {
                    await self?.loadArticles()
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadArticles() async {
        do {
            let fetchedArticles = try await database.fetchRecentArticles(limit: 1000)
            
            await MainActor.run {
                self.articles = fetchedArticles
            }
        } catch {
            print("Failed to load articles: \(error)")
        }
    }
    
    private func startSyncIfEnabled() async {
        guard enableCloudSync else { return }
        
        // Chat sync is handled by CloudKitChatSyncService
        // Article sync can be implemented separately if needed
        await performSync()
    }
    
    private func performSync() async {
        isSyncing = true
        syncError = nil
        
        do {
            // Trigger chat sync if available
            if let chatSyncService = chatSyncService {
                await chatSyncService.performSync()
            }
            
            // TODO: Implement article CloudKit sync
            // For now, just simulate article sync
            try await Task.sleep(nanoseconds: 1_000_000_000)
            
            lastSyncDate = Date()
        } catch {
            syncError = error
        }
        
        isSyncing = false
    }
}

// MARK: - Extensions

extension AppState {
    var unreadCount: Int {
        articles.filter { !$0.isRead }.count
    }
    
    var archivedCount: Int {
        articles.filter { $0.isArchived }.count
    }
    
    var totalReadingTime: Int {
        articles.reduce(0) { $0 + $1.readingTime }
    }
    
    func articlesForCurrentView() -> [Article] {
        switch viewMode {
        case .feed:
            return articles.filter { !$0.isArchived }
        default:
            return articles
        }
    }
}

// MARK: - Mock Data

#if DEBUG
extension AppState {
    static func mock() -> AppState {
        let state = AppState()
        
        // Add mock articles
        state.articles = [
            Article(
                id: UUID(),
                url: "https://example.com/swift-6",
                title: "What's New in Swift 6",
                content: "Swift 6 brings exciting new features...",
                summary: "An overview of the latest Swift 6 features including improved concurrency and performance.",
                keywords: ["swift", "programming", "ios", "development"],
                author: "Jane Developer",
                publishDate: Date().addingTimeInterval(-86400),
                readingTime: 8,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-3600),
                lastAccessedAt: nil,
                isArchived: false,
                syncStatus: .synced
            ),
            Article(
                id: UUID(),
                url: "https://example.com/ai-future",
                title: "The Future of AI in Software Development",
                content: "Artificial Intelligence is transforming how we write code...",
                summary: "Exploring how AI tools are changing the software development landscape.",
                keywords: ["ai", "machine learning", "software", "future"],
                author: "Tech Writer",
                publishDate: Date().addingTimeInterval(-172800),
                readingTime: 12,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-7200),
                lastAccessedAt: Date().addingTimeInterval(-3600),
                isArchived: false,
                syncStatus: .synced
            ),
            Article(
                id: UUID(),
                url: "https://arxiv.org/papers/123",
                title: "Advances in Neural Architecture Search",
                content: "This paper presents novel approaches to NAS...",
                summary: "A comprehensive study on neural architecture search methods and their applications.",
                keywords: ["neural networks", "architecture search", "deep learning", "research"],
                author: "Research Team",
                publishDate: Date().addingTimeInterval(-259200),
                readingTime: 25,
                contentType: .article,
                capturedAt: Date().addingTimeInterval(-86400),
                lastAccessedAt: nil,
                isArchived: false,
                syncStatus: .pending
            )
        ]
        
        return state
    }
}
#endif