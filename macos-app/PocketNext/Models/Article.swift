import Foundation
import GRDB

// MARK: - Article Model
struct Article: Identifiable, Codable {
    let id: UUID
    let url: String
    let title: String
    let content: String
    let summary: String
    let keywords: [String]
    let author: String?
    let publishDate: Date?
    let readingTime: Int
    let contentType: ContentType
    let capturedAt: Date
    var lastAccessedAt: Date?
    var isArchived: Bool
    var syncStatus: SyncStatus
    
    // Embedding fields for hybrid architecture
    var embeddingData: Data?  // Compressed Int8 embedding for CloudKit
    var embeddingModelVersion: String?  // Track model version for migrations
    var hasLocalEmbedding: Bool  // Flag to track if local vector index is built
    
    init(
        id: UUID = UUID(),
        url: String,
        title: String,
        content: String,
        summary: String,
        keywords: [String],
        author: String? = nil,
        publishDate: Date? = nil,
        readingTime: Int,
        contentType: ContentType,
        capturedAt: Date = Date(),
        lastAccessedAt: Date? = nil,
        isArchived: Bool = false,
        syncStatus: SyncStatus = .pending,
        embeddingData: Data? = nil,
        embeddingModelVersion: String? = nil,
        hasLocalEmbedding: Bool = false
    ) {
        self.id = id
        self.url = url
        self.title = title
        self.content = content
        self.summary = summary
        self.keywords = keywords
        self.author = author
        self.publishDate = publishDate
        self.readingTime = readingTime
        self.contentType = contentType
        self.capturedAt = capturedAt
        self.lastAccessedAt = lastAccessedAt
        self.isArchived = isArchived
        self.syncStatus = syncStatus
        self.embeddingData = embeddingData
        self.embeddingModelVersion = embeddingModelVersion
        self.hasLocalEmbedding = hasLocalEmbedding
    }
    
    // Computed properties
    var domain: String {
        URL(string: url)?.host ?? ""
    }
    
    var isRead: Bool {
        lastAccessedAt != nil
    }
    
    enum ContentType: String, Codable, CaseIterable {
        case article = "article"
        case twitter = "twitter"
        case youtube = "youtube"
        case github = "github"
        case reddit = "reddit"
        case pdf = "pdf"
        case generic = "generic"
        
        var icon: String {
            switch self {
            case .article: return "doc.text"
            case .twitter: return "bubble.left"
            case .youtube: return "play.rectangle"
            case .github: return "chevron.left.forwardslash.chevron.right"
            case .reddit: return "bubble.left.and.bubble.right"
            case .pdf: return "doc.richtext"
            case .generic: return "globe"
            }
        }
    }
    
    enum SyncStatus: Int, Codable {
        case pending = 0
        case synced = 1
        case error = 2
    }
}

// MARK: - GRDB Support
extension Article: FetchableRecord, PersistableRecord {
    static let databaseTableName = "articles"
    
    enum Columns: String, ColumnExpression {
        case id, url, title, content, summary, keywords
        case author, publishDate, readingTime, contentType
        case capturedAt, lastAccessedAt, isArchived, syncStatus
        case embeddingData, embeddingModelVersion, hasLocalEmbedding
    }
    
    init(row: Row) {
        id = row[Columns.id]
        url = row[Columns.url]
        title = row[Columns.title]
        content = row[Columns.content]
        summary = row[Columns.summary]
        keywords = (try? JSONDecoder().decode([String].self, from: row[Columns.keywords])) ?? []
        author = row[Columns.author]
        publishDate = row[Columns.publishDate]
        readingTime = row[Columns.readingTime]
        contentType = ContentType(rawValue: row[Columns.contentType]) ?? .generic
        capturedAt = row[Columns.capturedAt]
        lastAccessedAt = row[Columns.lastAccessedAt]
        isArchived = row[Columns.isArchived]
        syncStatus = SyncStatus(rawValue: row[Columns.syncStatus]) ?? .pending
        embeddingData = row[Columns.embeddingData]
        embeddingModelVersion = row[Columns.embeddingModelVersion]
        hasLocalEmbedding = row[Columns.hasLocalEmbedding]
    }
    
    func encode(to container: inout PersistenceContainer) {
        container[Columns.id] = id
        container[Columns.url] = url
        container[Columns.title] = title
        container[Columns.content] = content
        container[Columns.summary] = summary
        container[Columns.keywords] = try? JSONEncoder().encode(keywords)
        container[Columns.author] = author
        container[Columns.publishDate] = publishDate
        container[Columns.readingTime] = readingTime
        container[Columns.contentType] = contentType.rawValue
        container[Columns.capturedAt] = capturedAt
        container[Columns.lastAccessedAt] = lastAccessedAt
        container[Columns.isArchived] = isArchived
        container[Columns.syncStatus] = syncStatus.rawValue
        container[Columns.embeddingData] = embeddingData
        container[Columns.embeddingModelVersion] = embeddingModelVersion
        container[Columns.hasLocalEmbedding] = hasLocalEmbedding
    }
}

// MARK: - Local Vector Index Model
struct LocalVectorIndex: Codable {
    let articleId: UUID
    let embedding: [Float]  // Normalized Float32 for fast local search
    let magnitude: Float    // Pre-computed for fast cosine similarity
    let modelVersion: String
    let createdAt: Date
}

extension LocalVectorIndex: FetchableRecord, PersistableRecord {
    static let databaseTableName = "local_vector_index"
    
    enum Columns: String, ColumnExpression {
        case articleId, embedding, magnitude, modelVersion, createdAt
    }
    
    init(row: Row) {
        articleId = row[Columns.articleId]
        embedding = (try? JSONDecoder().decode([Float].self, from: row[Columns.embedding])) ?? []
        magnitude = row[Columns.magnitude]
        modelVersion = row[Columns.modelVersion]
        createdAt = row[Columns.createdAt]
    }
    
    func encode(to container: inout PersistenceContainer) {
        container[Columns.articleId] = articleId
        container[Columns.embedding] = try? JSONEncoder().encode(embedding)
        container[Columns.magnitude] = magnitude
        container[Columns.modelVersion] = modelVersion
        container[Columns.createdAt] = createdAt
    }
}

// MARK: - Captured Content (from browser)
struct CapturedContent: Codable {
    let url: String
    let title: String
    let htmlContent: String
    let timestamp: Date
    let metadata: Metadata
    
    struct Metadata: Codable {
        let description: String?
        let author: String?
        let publishDate: String?
        let type: String
        let userId: String?
    }
}

// MARK: - Parsed Article (from parse server)
struct ParsedArticle: Codable {
    let title: String
    let content: String
    let summary: String
    let keywords: [String]
    let author: String?
    let publishDate: String?
    let readingTime: Int
    let contentType: String
    
    enum CodingKeys: String, CodingKey {
        case title, content, summary, keywords, author
        case publishDate = "publish_date"
        case readingTime = "reading_time"
        case contentType = "content_type"
    }
}