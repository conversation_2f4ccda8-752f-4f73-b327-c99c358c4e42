import SwiftUI

// MARK: - Circular Progress Indicator
struct CircularProgressView: View {
    let progress: Double
    let lineWidth: CGFloat
    let size: CGFloat
    let primaryColor: Color
    let secondaryColor: Color
    
    init(
        progress: Double,
        lineWidth: CGFloat = 4,
        size: CGFloat = 50,
        primaryColor: Color = .blue,
        secondaryColor: Color = .gray.opacity(0.3)
    ) {
        self.progress = progress
        self.lineWidth = lineWidth
        self.size = size
        self.primaryColor = primaryColor
        self.secondaryColor = secondaryColor
    }
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(secondaryColor, lineWidth: lineWidth)
                .frame(width: size, height: size)
            
            // Progress arc
            Circle()
                .trim(from: 0, to: min(progress, 1.0))
                .stroke(
                    primaryColor,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.3), value: progress)
            
            // Progress text
            Text("\(Int(progress * 100))%")
                .font(.system(size: size * 0.3, weight: .medium))
                .foregroundColor(primaryColor)
        }
    }
}

// MARK: - Sync Status Indicator
struct SyncStatusIndicator: View {
    enum SyncState {
        case idle
        case syncing
        case success
        case error(String)
    }
    
    let state: SyncState
    @State private var rotation: Double = 0
    @State private var scale: CGFloat = 1.0
    
    var body: some View {
        HStack(spacing: 8) {
            // Icon
            ZStack {
                switch state {
                case .idle:
                    Image(systemName: "cloud")
                        .foregroundColor(.gray)
                    
                case .syncing:
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.blue)
                        .rotationEffect(.degrees(rotation))
                        .onAppear {
                            withAnimation(
                                Animation.linear(duration: 1)
                                    .repeatForever(autoreverses: false)
                            ) {
                                rotation = 360
                            }
                        }
                    
                case .success:
                    Image(systemName: "checkmark.cloud")
                        .foregroundColor(.green)
                        .scaleEffect(scale)
                        .onAppear {
                            withAnimation(.spring()) {
                                scale = 1.2
                            }
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                withAnimation(.spring()) {
                                    scale = 1.0
                                }
                            }
                        }
                    
                case .error:
                    Image(systemName: "exclamationmark.cloud")
                        .foregroundColor(.red)
                }
            }
            .font(.system(size: 20))
            
            // Status text
            Text(statusText)
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Capsule()
                .fill(Color(NSColor.controlBackgroundColor))
                .shadow(radius: 2)
        )
    }
    
    private var statusText: String {
        switch state {
        case .idle: return "Up to date"
        case .syncing: return "Syncing..."
        case .success: return "Synced"
        case .error(let message): return message
        }
    }
}

// MARK: - Loading Dots
struct LoadingDots: View {
    @State private var showDot1 = false
    @State private var showDot2 = false
    @State private var showDot3 = false
    
    let dotSize: CGFloat
    let color: Color
    
    init(dotSize: CGFloat = 8, color: Color = .blue) {
        self.dotSize = dotSize
        self.color = color
    }
    
    var body: some View {
        HStack(spacing: dotSize / 2) {
            Circle()
                .fill(color)
                .frame(width: dotSize, height: dotSize)
                .scaleEffect(showDot1 ? 1 : 0.5)
                .opacity(showDot1 ? 1 : 0.3)
            
            Circle()
                .fill(color)
                .frame(width: dotSize, height: dotSize)
                .scaleEffect(showDot2 ? 1 : 0.5)
                .opacity(showDot2 ? 1 : 0.3)
            
            Circle()
                .fill(color)
                .frame(width: dotSize, height: dotSize)
                .scaleEffect(showDot3 ? 1 : 0.5)
                .opacity(showDot3 ? 1 : 0.3)
        }
        .onAppear {
            animateDots()
        }
    }
    
    private func animateDots() {
        withAnimation(.easeInOut(duration: 0.4)) {
            showDot1 = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeInOut(duration: 0.4)) {
                showDot2 = true
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeInOut(duration: 0.4)) {
                showDot3 = true
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeInOut(duration: 0.4)) {
                showDot1 = false
                showDot2 = false
                showDot3 = false
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) {
            animateDots()
        }
    }
}

// MARK: - Skeleton Loader
struct SkeletonLoader: View {
    let height: CGFloat
    let cornerRadius: CGFloat
    
    init(height: CGFloat = 20, cornerRadius: CGFloat = 4) {
        self.height = height
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(Color.gray.opacity(0.3))
            .frame(height: height)
            .shimmer()
    }
}

// MARK: - Preview
struct ProgressIndicators_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            // Circular progress examples
            HStack(spacing: 30) {
                CircularProgressView(progress: 0.0)
                CircularProgressView(progress: 0.3)
                CircularProgressView(progress: 0.7)
                CircularProgressView(progress: 1.0)
            }
            
            // Sync status examples
            VStack(alignment: .leading, spacing: 20) {
                SyncStatusIndicator(state: .idle)
                SyncStatusIndicator(state: .syncing)
                SyncStatusIndicator(state: .success)
                SyncStatusIndicator(state: .error("Connection failed"))
            }
            
            // Loading dots
            LoadingDots()
            
            // Skeleton loaders
            VStack(spacing: 10) {
                SkeletonLoader()
                SkeletonLoader(height: 60, cornerRadius: 8)
                SkeletonLoader(height: 100, cornerRadius: 12)
            }
            .frame(width: 300)
        }
        .padding(50)
        .frame(width: 600, height: 600)
    }
}