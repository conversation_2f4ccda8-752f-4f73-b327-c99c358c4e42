import SwiftUI

// MARK: - Swipe to Delete/Archive
struct SwipeActionView<Content: View>: View {
    @ViewBuilder let content: Content
    let deleteAction: () -> Void
    let archiveAction: () -> Void
    
    @GestureState private var dragAmount = CGSize.zero
    @State private var offset = CGSize.zero
    @State private var deleteProgress: CGFloat = 0
    @State private var archiveProgress: CGFloat = 0
    
    private let actionThreshold: CGFloat = 100
    private let maxDrag: CGFloat = 200
    
    var body: some View {
        ZStack {
            // Background actions
            HStack(spacing: 0) {
                // Archive action (left side)
                ZStack {
                    Color.blue
                    Image(systemName: "archivebox.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .scaleEffect(archiveProgress)
                        .opacity(archiveProgress)
                }
                .frame(width: abs(offset.width))
                
                Spacer()
                
                // Delete action (right side)
                ZStack {
                    Color.red
                    Image(systemName: "trash.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .scaleEffect(deleteProgress)
                        .opacity(deleteProgress)
                }
                .frame(width: abs(offset.width))
            }
            
            // Main content
            content
                .offset(x: offset.width + dragAmount.width)
                .gesture(
                    DragGesture()
                        .updating($dragAmount) { value, state, _ in
                            state = value.translation
                        }
                        .onChanged { value in
                            let translation = value.translation.width
                            
                            // Update progress indicators
                            if translation > 0 {
                                archiveProgress = min(translation / actionThreshold, 1)
                                deleteProgress = 0
                            } else {
                                deleteProgress = min(abs(translation) / actionThreshold, 1)
                                archiveProgress = 0
                            }
                        }
                        .onEnded { value in
                            let translation = value.translation.width
                            
                            if translation > actionThreshold {
                                // Archive
                                withAnimation(.spring()) {
                                    offset.width = maxDrag
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    archiveAction()
                                    offset = .zero
                                    archiveProgress = 0
                                }
                            } else if translation < -actionThreshold {
                                // Delete
                                withAnimation(.spring()) {
                                    offset.width = -maxDrag
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                    deleteAction()
                                    offset = .zero
                                    deleteProgress = 0
                                }
                            } else {
                                // Snap back
                                withAnimation(.spring()) {
                                    offset = .zero
                                    deleteProgress = 0
                                    archiveProgress = 0
                                }
                            }
                        }
                )
        }
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Long Press Context Menu
struct LongPressContextMenu<Content: View>: View {
    @ViewBuilder let content: Content
    let menuItems: [(title: String, icon: String, action: () -> Void)]
    
    @State private var isPressed = false
    @State private var showMenu = false
    @State private var menuScale: CGFloat = 0.8
    @State private var menuOpacity: Double = 0
    
    var body: some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .overlay(
                Group {
                    if showMenu {
                        // Context menu
                        VStack(alignment: .leading, spacing: 0) {
                            ForEach(menuItems.indices, id: \.self) { index in
                                let item = menuItems[index]
                                Button(action: {
                                    item.action()
                                    withAnimation(.spring(response: 0.3)) {
                                        showMenu = false
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: item.icon)
                                            .frame(width: 20)
                                        Text(item.title)
                                        Spacer()
                                    }
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .contentShape(Rectangle())
                                }
                                .buttonStyle(PlainButtonStyle())
                                .background(
                                    Color(NSColor.controlBackgroundColor)
                                        .opacity(0.01)
                                )
                                .onHover { hovering in
                                    if hovering {
                                        NSCursor.pointingHand.push()
                                    } else {
                                        NSCursor.pop()
                                    }
                                }
                                
                                if index < menuItems.count - 1 {
                                    Divider()
                                        .padding(.horizontal, 8)
                                }
                            }
                        }
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(NSColor.controlBackgroundColor))
                                .shadow(radius: 10)
                        )
                        .scaleEffect(menuScale)
                        .opacity(menuOpacity)
                        .offset(y: -60)
                        .onAppear {
                            withAnimation(.spring(response: 0.3)) {
                                menuScale = 1.0
                                menuOpacity = 1.0
                            }
                        }
                    }
                },
                alignment: .top
            )
            .onLongPressGesture(
                minimumDuration: 0.5,
                pressing: { pressing in
                    isPressed = pressing
                    if pressing {
                        // Haptic feedback on iOS
                        #if os(iOS)
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        #endif
                    }
                },
                perform: {
                    showMenu = true
                    menuScale = 0.8
                    menuOpacity = 0
                }
            )
            .onTapGesture {
                if showMenu {
                    withAnimation(.spring(response: 0.3)) {
                        showMenu = false
                    }
                }
            }
    }
}

// MARK: - Pinch to Zoom
struct PinchToZoomView<Content: View>: View {
    @ViewBuilder let content: Content
    @State private var currentScale: CGFloat = 1.0
    @State private var finalScale: CGFloat = 1.0
    
    var body: some View {
        content
            .scaleEffect(finalScale)
            .gesture(
                MagnificationGesture()
                    .onChanged { value in
                        currentScale = value
                    }
                    .onEnded { value in
                        withAnimation(.spring()) {
                            finalScale *= value
                            // Limit scale between 0.5 and 3.0
                            finalScale = min(max(finalScale, 0.5), 3.0)
                        }
                        currentScale = 1.0
                    }
            )
            .onTapGesture(count: 2) {
                withAnimation(.spring()) {
                    finalScale = finalScale == 1.0 ? 2.0 : 1.0
                }
            }
    }
}

// MARK: - Pull to Refresh
struct PullToRefreshView<Content: View>: View {
    @ViewBuilder let content: Content
    let onRefresh: () async -> Void
    
    @State private var isRefreshing = false
    @State private var pullProgress: CGFloat = 0
    @State private var rotation: Double = 0
    
    private let threshold: CGFloat = 80
    
    var body: some View {
        ScrollView {
            GeometryReader { geometry in
                let offset = geometry.frame(in: .named("pullToRefresh")).minY
                
                Color.clear
                    .preference(
                        key: ScrollOffsetPreferenceKey.self,
                        value: offset
                    )
            }
            .frame(height: 0)
            
            // Refresh indicator
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 3)
                    .frame(width: 30, height: 30)
                
                Circle()
                    .trim(from: 0, to: pullProgress)
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: 30, height: 30)
                    .rotationEffect(.degrees(-90))
                
                if isRefreshing {
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.blue)
                        .rotationEffect(.degrees(rotation))
                        .onAppear {
                            withAnimation(
                                Animation.linear(duration: 1)
                                    .repeatForever(autoreverses: false)
                            ) {
                                rotation = 360
                            }
                        }
                }
            }
            .opacity(pullProgress > 0 ? 1 : 0)
            .scaleEffect(pullProgress)
            .offset(y: -40)
            
            content
        }
        .coordinateSpace(name: "pullToRefresh")
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
            if !isRefreshing {
                pullProgress = min(max(offset / threshold, 0), 1)
                
                if offset > threshold && !isRefreshing {
                    isRefreshing = true
                    Task {
                        await onRefresh()
                        withAnimation {
                            isRefreshing = false
                            pullProgress = 0
                            rotation = 0
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Preference Key
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Preview
struct GestureAnimations_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 40) {
            // Swipe action example
            SwipeActionView(
                content: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.2))
                        .frame(height: 80)
                        .overlay(Text("Swipe me"))
                },
                deleteAction: { print("Deleted") },
                archiveAction: { print("Archived") }
            )
            .frame(width: 300)
            
            // Long press menu example
            LongPressContextMenu(
                content: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.2))
                        .frame(width: 200, height: 80)
                        .overlay(Text("Long press me"))
                },
                menuItems: [
                    ("Copy", "doc.on.doc", { print("Copy") }),
                    ("Share", "square.and.arrow.up", { print("Share") }),
                    ("Delete", "trash", { print("Delete") })
                ]
            )
        }
        .padding(50)
        .frame(width: 600, height: 600)
    }
}