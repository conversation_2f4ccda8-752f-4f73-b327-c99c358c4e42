import SwiftUI

// MARK: - Card Flip Transition
struct CardFlipModifier: AnimatableModifier {
    var rotation: Double
    var isFlipped: Bool
    
    var animatableData: Double {
        get { rotation }
        set { rotation = newValue }
    }
    
    func body(content: Content) -> some View {
        ZStack {
            content
                .opacity(rotation < 90 ? 1 : 0)
            
            content
                .rotation3DEffect(
                    .degrees(180),
                    axis: (x: 0, y: 1, z: 0)
                )
                .opacity(rotation < 90 ? 0 : 1)
        }
        .rotation3DEffect(
            .degrees(rotation),
            axis: (x: 0, y: 1, z: 0)
        )
    }
}

extension View {
    func cardFlip(isFlipped: Bool) -> some View {
        modifier(CardFlipModifier(rotation: isFlipped ? 180 : 0, isFlipped: isFlipped))
    }
}

// MARK: - Slide and Fade Transition
struct SlideAndFade: ViewModifier {
    let isActive: Bool
    
    func body(content: Content) -> some View {
        content
            .offset(x: isActive ? 0 : 50)
            .opacity(isActive ? 1 : 0)
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isActive)
    }
}

// MARK: - Matched Geometry Navigation
struct NavigationTransition: View {
    @Namespace private var namespace
    @State private var selectedItem: String?
    
    let items = ["Article 1", "Article 2", "Article 3"]
    
    var body: some View {
        ZStack {
            // List view
            if selectedItem == nil {
                ScrollView {
                    VStack(spacing: 16) {
                        ForEach(items, id: \.self) { item in
                            CardView(
                                title: item,
                                namespace: namespace,
                                isSelected: false,
                                onTap: { selectedItem = item }
                            )
                        }
                    }
                    .padding()
                }
            }
            
            // Detail view
            if let selected = selectedItem {
                DetailView(
                    title: selected,
                    namespace: namespace,
                    onDismiss: { selectedItem = nil }
                )
            }
        }
        .animation(.spring(), value: selectedItem)
    }
}

struct CardView: View {
    let title: String
    let namespace: Namespace.ID
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                Text(title)
                    .font(.headline)
                    .matchedGeometryEffect(id: "\(title)-title", in: namespace)
                
                Rectangle()
                    .fill(Color.blue.opacity(0.3))
                    .frame(height: isSelected ? 400 : 100)
                    .matchedGeometryEffect(id: "\(title)-bg", in: namespace)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .matchedGeometryEffect(id: "\(title)-container", in: namespace)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct DetailView: View {
    let title: String
    let namespace: Namespace.ID
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text(title)
                    .font(.largeTitle)
                    .matchedGeometryEffect(id: "\(title)-title", in: namespace)
                
                Spacer()
                
                Button(action: onDismiss) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Rectangle()
                .fill(Color.blue.opacity(0.3))
                .frame(height: 400)
                .matchedGeometryEffect(id: "\(title)-bg", in: namespace)
            
            Text("Article content goes here...")
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(NSColor.controlBackgroundColor))
                .matchedGeometryEffect(id: "\(title)-container", in: namespace)
        )
    }
}

// MARK: - Loading Shimmer Effect
struct ShimmerModifier: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.white.opacity(0),
                        Color.white.opacity(0.3),
                        Color.white.opacity(0)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .offset(x: phase * 200 - 100)
                .mask(content)
            )
            .onAppear {
                withAnimation(
                    Animation.linear(duration: 1.5)
                        .repeatForever(autoreverses: false)
                ) {
                    phase = 1
                }
            }
    }
}

extension View {
    func shimmer() -> some View {
        modifier(ShimmerModifier())
    }
}

// MARK: - Preview
struct ContentTransitions_Previews: PreviewProvider {
    static var previews: some View {
        NavigationTransition()
            .frame(width: 600, height: 800)
    }
}