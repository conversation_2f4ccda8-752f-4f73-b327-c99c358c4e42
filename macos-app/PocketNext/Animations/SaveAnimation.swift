import SwiftUI

// MARK: - Save Button Animation
struct SaveAnimation: View {
    @State private var isAnimating = false
    @State private var showCheckmark = false
    let onSave: () -> Void
    
    var body: some View {
        Button(action: {
            triggerAnimation()
            onSave()
        }) {
            ZStack {
                // Background circle
                Circle()
                    .fill(isAnimating ? Color.green : Color.blue)
                    .frame(width: 50, height: 50)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isAnimating)
                
                // Icon transition
                if showCheckmark {
                    Image(systemName: "checkmark")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                        .transition(.scale.combined(with: .opacity))
                } else {
                    Image(systemName: "bookmark")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .shadow(color: isAnimating ? Color.green.opacity(0.3) : Color.black.opacity(0.1), 
                    radius: isAnimating ? 15 : 5)
            .animation(.easeInOut(duration: 0.3), value: showCheckmark)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func triggerAnimation() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            isAnimating = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation {
                showCheckmark = true
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation {
                isAnimating = false
                showCheckmark = false
            }
        }
    }
}

// MARK: - Floating Save Button
struct FloatingSaveButton: View {
    @Binding var isExpanded: Bool
    let onSave: (String, String) -> Void
    @State private var url: String = ""
    @State private var note: String = ""
    @State private var dragOffset: CGSize = .zero
    @State private var position: CGPoint = CGPoint(x: 300, y: 200)
    
    var body: some View {
        ZStack {
            // Expanded capture interface
            if isExpanded {
                VStack(spacing: 16) {
                    HStack {
                        Text("Save Content")
                            .font(.headline)
                        Spacer()
                        Button(action: { 
                            withAnimation(.spring()) {
                                isExpanded = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    TextField("URL", text: $url)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    TextField("Add a note...", text: $note)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button(action: {
                        onSave(url, note)
                        withAnimation(.spring()) {
                            isExpanded = false
                            url = ""
                            note = ""
                        }
                    }) {
                        Text("Save")
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding()
                .frame(width: 300)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(NSColor.controlBackgroundColor))
                        .shadow(radius: 20)
                )
                .transition(.scale.combined(with: .opacity))
                .position(position)
            } else {
                // Collapsed floating button
                Button(action: {
                    withAnimation(.spring()) {
                        isExpanded = true
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                        .background(
                            Circle()
                                .fill(Color(NSColor.controlBackgroundColor))
                                .shadow(radius: 10)
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .position(position)
                .offset(dragOffset)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            dragOffset = value.translation
                        }
                        .onEnded { value in
                            position.x += value.translation.width
                            position.y += value.translation.height
                            dragOffset = .zero
                        }
                )
            }
        }
    }
}

// MARK: - Preview
struct SaveAnimation_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 50) {
            SaveAnimation(onSave: {})
            
            FloatingSaveButton(isExpanded: .constant(false)) { _, _ in }
                .frame(width: 400, height: 300)
                .background(Color.gray.opacity(0.1))
        }
        .padding()
    }
}