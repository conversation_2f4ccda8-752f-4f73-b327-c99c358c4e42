# macOS App Capture Implementation

## Current State

When you click "Save Current Page" in the menu bar:
1. `triggerCapture()` is called in AppState
2. Sets `isCapturing = true`
3. ContentView shows the CaptureOverlay
4. CaptureOverlay displays a progress UI
5. Currently uses mock data for testing

## How It Will Actually Work

### Method 1: Browser Extension + Native Messaging (Primary)

This is the intended implementation that's partially built:

1. **Browser Extension Captures Page**
   - User clicks extension icon or presses Cmd+Shift+S
   - Extension captures FULL page HTML (not just visible content)
   - Includes all resources, styles, and scripts

2. **Native Messaging Bridge**
   - Browser extension sends message to macOS app
   - Uses NativeMessaging.swift (already implemented)
   - App receives full HTML content + metadata

3. **Content Processing**
   - ContentProcessor sends HTML to parse server
   - GPT-4o mini extracts article content
   - Returns structured data (title, summary, keywords)

4. **Save to Database**
   - Article saved to GRDB database
   - Full-text search index updated
   - Available for RAG queries

**Advantages:**
- Gets complete HTML, not just visible portion
- Works with dynamic content (JavaScript rendered)
- Browser extension has full DOM access
- Already implemented in browser extension code

### Method 2: AppleScript Integration (Fallback)

For when browser extension isn't installed:

```applescript
-- Get current Safari tab
tell application "Safari"
    set currentURL to URL of current tab of window 1
    set currentTitle to name of current tab of window 1
end tell

-- Similar for Chrome
tell application "Google Chrome"
    set currentURL to URL of active tab of front window
    set currentTitle to title of active tab of front window
end tell
```

**Process:**
1. Get URL and title via AppleScript
2. Fetch HTML content via HTTP request
3. Send to parse server
4. Save processed article

**Limitations:**
- May not get JavaScript-rendered content
- Requires separate HTTP request
- Can't access paywalled content

### Method 3: System-Wide Capture (Future Enhancement)

For capturing from ANY application:

1. **Accessibility API**
   - Access text from any app window
   - Works with PDFs, emails, notes apps
   - Requires accessibility permissions

2. **Screen OCR (Advanced)**
   - Capture screenshot of selected area
   - Use Vision framework for text extraction
   - Send extracted text to parse server

3. **Share Extension**
   - System-wide share menu integration
   - Apps can share content directly
   - Similar to iOS implementation

## What Content Gets Captured

### From Web Browsers:
- **Full HTML DOM** - entire page structure
- **Metadata** - title, author, publish date
- **Text Content** - all paragraphs, headings
- **Images** - URLs for later download
- **Comments** - if part of main content

### From Other Apps (Future):
- **PDFs** - full text extraction
- **Emails** - subject, body, attachments
- **Notes** - formatted text
- **Documents** - Word, Pages, etc.

## Technical Implementation Details

### Browser Extension Flow:
```javascript
// Browser extension captures
const content = {
    url: window.location.href,
    htmlContent: document.documentElement.innerHTML,  // Full HTML
    metadata: {
        title: document.title,
        author: getAuthorMeta(),
        description: getDescription()
    }
}

// Send to native app
chrome.runtime.sendNativeMessage('com.pocketnext.app', content)
```

### Native App Reception:
```swift
// NativeMessaging.swift handles incoming messages
func handleMessage(_ message: NativeMessage) {
    switch message.type {
    case .capture:
        // Process captured content
        let processor = ContentProcessor()
        let article = await processor.process(message.data)
        // Save to database
    }
}
```

### Parse Server Processing:
```python
# Parse server extracts content
@app.post("/parse")
async def parse_content(request: ParseRequest):
    # GPT-4o mini analyzes HTML
    # Returns structured article data
    return ParsedArticle(
        title=extracted_title,
        content=clean_content,
        summary=ai_summary,
        keywords=extracted_keywords
    )
```

## Why This Approach?

1. **Complete Content Capture**
   - Browser extensions have full DOM access
   - Can execute JavaScript to get dynamic content
   - Access to authentication cookies

2. **AI-Powered Extraction**
   - GPT-4o mini understands any content
   - No need for site-specific parsers
   - Handles articles, tweets, PDFs, etc.

3. **Application Agnostic (Future)**
   - Not limited to browsers
   - Can capture from any app
   - System-wide integration

4. **Offline Support**
   - Content stored locally
   - Queue for later processing
   - Sync when online

## Current Limitations

1. **Browser Extension Required**
   - Must be installed and enabled
   - Needs native messaging manifest

2. **No Direct Browser Access**
   - macOS app can't directly access browser tabs
   - Security restrictions prevent this

3. **Manual Trigger**
   - User must click button or use shortcut
   - No automatic capture (by design)

## Setup Requirements

1. Install browser extension
2. Enable native messaging host
3. Grant necessary permissions
4. Parse server must be running

## Future Enhancements

1. **Smart Capture**
   - Auto-detect article boundaries
   - Remove ads and clutter
   - Preserve important formatting

2. **Batch Capture**
   - Save all open tabs
   - Import bookmarks
   - RSS feed integration

3. **Rich Media**
   - Download and store images
   - Extract video transcripts
   - PDF full-text indexing

4. **Cross-Platform Sync**
   - CloudKit for Apple devices
   - Web API for other platforms
   - Real-time collaboration