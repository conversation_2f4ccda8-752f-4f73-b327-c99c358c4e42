// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A1B2C3D2B5A6E7F00123456 /* PocketNextApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3C2B5A6E7F00123456 /* PocketNextApp.swift */; };
		1A1B2C3F2B5A6E8100123456 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3E2B5A6E8100123456 /* ContentView.swift */; };
		1A1B2C412B5A6E8200123456 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A1B2C402B5A6E8200123456 /* Assets.xcassets */; };
		1A1B2C442B5A6E8200123456 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A1B2C432B5A6E8200123456 /* Preview Assets.xcassets */; };
		1A1B2C4B2B5A6F0000123456 /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C4A2B5A6F0000123456 /* AppState.swift */; };
		1A1B2C4D2B5A6F0100123456 /* Article.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C4C2B5A6F0100123456 /* Article.swift */; };
		1A1B2C502B5A6F2000123456 /* DatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C4F2B5A6F2000123456 /* DatabaseManager.swift */; };
		1A1B2C522B5A6F2100123456 /* NativeMessaging.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C512B5A6F2100123456 /* NativeMessaging.swift */; };
		1A1B2C542B5A6F2200123456 /* ContentProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C532B5A6F2200123456 /* ContentProcessor.swift */; };
		1A1B2C562B5A6F2300123456 /* SearchEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C552B5A6F2300123456 /* SearchEngine.swift */; };
		1A1B2C582B5A6F2400123456 /* CloudKitChatSyncService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C572B5A6F2400123456 /* CloudKitChatSyncService.swift */; };
		1A1B2C5A2B5A6F2500123456 /* ConfigurationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C592B5A6F2500123456 /* ConfigurationService.swift */; };
		1A1B2C5C2B5A6F2600123456 /* DigestService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C5B2B5A6F2600123456 /* DigestService.swift */; };
		1A1B2C5E2B5A6F2700123456 /* EmbeddingService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C5D2B5A6F2700123456 /* EmbeddingService.swift */; };
		1A1B2C602B5A6F2800123456 /* FeedCurationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C5F2B5A6F2800123456 /* FeedCurationService.swift */; };
		1A1B2C622B5A6F2900123456 /* LLMService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C612B5A6F2900123456 /* LLMService.swift */; };
		1A1B2C642B5A6F2A00123456 /* RAGService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C632B5A6F2A00123456 /* RAGService.swift */; };
		1A1B2C662B5A6F2B00123456 /* VectorStorageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C652B5A6F2B00123456 /* VectorStorageManager.swift */; };
		1A1B2C682B5A6F2C00123456 /* WebSearchService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C672B5A6F2C00123456 /* WebSearchService.swift */; };
		1A1B2C6B2B5A6F4000123456 /* HomeFeedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C6A2B5A6F4000123456 /* HomeFeedView.swift */; };
		1A1B2C6D2B5A6F4100123456 /* ChatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C6C2B5A6F4100123456 /* ChatView.swift */; };
		1A1B2C6F2B5A6F4200123456 /* ReadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C6E2B5A6F4200123456 /* ReadingView.swift */; };
		1A1B2C712B5A6F4300123456 /* SearchResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C702B5A6F4300123456 /* SearchResultsView.swift */; };
		1A1B2C732B5A6F4400123456 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C722B5A6F4400123456 /* SettingsView.swift */; };
		1A1B2C752B5A6F4500123456 /* MenuBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C742B5A6F4500123456 /* MenuBarView.swift */; };
		1A1B2C772B5A6F4600123456 /* CaptureOverlay.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C762B5A6F4600123456 /* CaptureOverlay.swift */; };
		1A1B2C792B5A6F4700123456 /* SyncStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C782B5A6F4700123456 /* SyncStatusView.swift */; };
		1A1B2C7B2B5A6F4800123456 /* DigestView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C7A2B5A6F4800123456 /* DigestView.swift */; };
		1A1B2C7E2B5A6F6000123456 /* ContentTransitions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C7D2B5A6F6000123456 /* ContentTransitions.swift */; };
		1A1B2C802B5A6F6100123456 /* GestureAnimations.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C7F2B5A6F6100123456 /* GestureAnimations.swift */; };
		1A1B2C822B5A6F6200123456 /* ProgressIndicators.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C812B5A6F6200123456 /* ProgressIndicators.swift */; };
		1A1B2C842B5A6F6300123456 /* SaveAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C832B5A6F6300123456 /* SaveAnimation.swift */; };
		1A1B2C872B5A6F8000123456 /* GRDB in Frameworks */ = {isa = PBXBuildFile; productRef = 1A1B2C862B5A6F8000123456 /* GRDB */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A1B2C392B5A6E7F00123456 /* PocketNext.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PocketNext.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A1B2C3C2B5A6E7F00123456 /* PocketNextApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PocketNextApp.swift; sourceTree = "<group>"; };
		1A1B2C3E2B5A6E8100123456 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1A1B2C402B5A6E8200123456 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A1B2C432B5A6E8200123456 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1A1B2C452B5A6E8200123456 /* PocketNext.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = PocketNext.entitlements; sourceTree = "<group>"; };
		1A1B2C4A2B5A6F0000123456 /* AppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppState.swift; sourceTree = "<group>"; };
		1A1B2C4C2B5A6F0100123456 /* Article.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Article.swift; sourceTree = "<group>"; };
		1A1B2C4F2B5A6F2000123456 /* DatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseManager.swift; sourceTree = "<group>"; };
		1A1B2C512B5A6F2100123456 /* NativeMessaging.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NativeMessaging.swift; sourceTree = "<group>"; };
		1A1B2C532B5A6F2200123456 /* ContentProcessor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentProcessor.swift; sourceTree = "<group>"; };
		1A1B2C552B5A6F2300123456 /* SearchEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchEngine.swift; sourceTree = "<group>"; };
		1A1B2C572B5A6F2400123456 /* CloudKitChatSyncService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloudKitChatSyncService.swift; sourceTree = "<group>"; };
		1A1B2C592B5A6F2500123456 /* ConfigurationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationService.swift; sourceTree = "<group>"; };
		1A1B2C5B2B5A6F2600123456 /* DigestService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DigestService.swift; sourceTree = "<group>"; };
		1A1B2C5D2B5A6F2700123456 /* EmbeddingService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmbeddingService.swift; sourceTree = "<group>"; };
		1A1B2C5F2B5A6F2800123456 /* FeedCurationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeedCurationService.swift; sourceTree = "<group>"; };
		1A1B2C612B5A6F2900123456 /* LLMService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LLMService.swift; sourceTree = "<group>"; };
		1A1B2C632B5A6F2A00123456 /* RAGService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RAGService.swift; sourceTree = "<group>"; };
		1A1B2C652B5A6F2B00123456 /* VectorStorageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VectorStorageManager.swift; sourceTree = "<group>"; };
		1A1B2C672B5A6F2C00123456 /* WebSearchService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebSearchService.swift; sourceTree = "<group>"; };
		1A1B2C6A2B5A6F4000123456 /* HomeFeedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeFeedView.swift; sourceTree = "<group>"; };
		1A1B2C6C2B5A6F4100123456 /* ChatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatView.swift; sourceTree = "<group>"; };
		1A1B2C6E2B5A6F4200123456 /* ReadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReadingView.swift; sourceTree = "<group>"; };
		1A1B2C702B5A6F4300123456 /* SearchResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchResultsView.swift; sourceTree = "<group>"; };
		1A1B2C722B5A6F4400123456 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		1A1B2C742B5A6F4500123456 /* MenuBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuBarView.swift; sourceTree = "<group>"; };
		1A1B2C762B5A6F4600123456 /* CaptureOverlay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CaptureOverlay.swift; sourceTree = "<group>"; };
		1A1B2C782B5A6F4700123456 /* SyncStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyncStatusView.swift; sourceTree = "<group>"; };
		1A1B2C7A2B5A6F4800123456 /* DigestView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DigestView.swift; sourceTree = "<group>"; };
		1A1B2C7D2B5A6F6000123456 /* ContentTransitions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentTransitions.swift; sourceTree = "<group>"; };
		1A1B2C7F2B5A6F6100123456 /* GestureAnimations.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GestureAnimations.swift; sourceTree = "<group>"; };
		1A1B2C812B5A6F6200123456 /* ProgressIndicators.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressIndicators.swift; sourceTree = "<group>"; };
		1A1B2C832B5A6F6300123456 /* SaveAnimation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SaveAnimation.swift; sourceTree = "<group>"; };
		1A1B2C8B2B5A6FA000123456 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A1B2C362B5A6E7F00123456 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A1B2C872B5A6F8000123456 /* GRDB in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A1B2C302B5A6E7F00123456 = {
			isa = PBXGroup;
			children = (
				1A1B2C3B2B5A6E7F00123456 /* PocketNext */,
				1A1B2C3A2B5A6E7F00123456 /* Products */,
			);
			sourceTree = "<group>";
		};
		1A1B2C3A2B5A6E7F00123456 /* Products */ = {
			isa = PBXGroup;
			children = (
				1A1B2C392B5A6E7F00123456 /* PocketNext.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A1B2C3B2B5A6E7F00123456 /* PocketNext */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3C2B5A6E7F00123456 /* PocketNextApp.swift */,
				1A1B2C8B2B5A6FA000123456 /* Info.plist */,
				1A1B2C452B5A6E8200123456 /* PocketNext.entitlements */,
				1A1B2C402B5A6E8200123456 /* Assets.xcassets */,
				1A1B2C7C2B5A6F5000123456 /* Animations */,
				1A1B2C492B5A6EF000123456 /* Models */,
				1A1B2C4E2B5A6F1000123456 /* Services */,
				1A1B2C692B5A6F3000123456 /* Views */,
				1A1B2C422B5A6E8200123456 /* Preview Content */,
			);
			path = PocketNext;
			sourceTree = "<group>";
		};
		1A1B2C422B5A6E8200123456 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A1B2C432B5A6E8200123456 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		1A1B2C492B5A6EF000123456 /* Models */ = {
			isa = PBXGroup;
			children = (
				1A1B2C4A2B5A6F0000123456 /* AppState.swift */,
				1A1B2C4C2B5A6F0100123456 /* Article.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		1A1B2C4E2B5A6F1000123456 /* Services */ = {
			isa = PBXGroup;
			children = (
				1A1B2C4F2B5A6F2000123456 /* DatabaseManager.swift */,
				1A1B2C512B5A6F2100123456 /* NativeMessaging.swift */,
				1A1B2C532B5A6F2200123456 /* ContentProcessor.swift */,
				1A1B2C552B5A6F2300123456 /* SearchEngine.swift */,
				1A1B2C572B5A6F2400123456 /* CloudKitChatSyncService.swift */,
				1A1B2C592B5A6F2500123456 /* ConfigurationService.swift */,
				1A1B2C5B2B5A6F2600123456 /* DigestService.swift */,
				1A1B2C5D2B5A6F2700123456 /* EmbeddingService.swift */,
				1A1B2C5F2B5A6F2800123456 /* FeedCurationService.swift */,
				1A1B2C612B5A6F2900123456 /* LLMService.swift */,
				1A1B2C632B5A6F2A00123456 /* RAGService.swift */,
				1A1B2C652B5A6F2B00123456 /* VectorStorageManager.swift */,
				1A1B2C672B5A6F2C00123456 /* WebSearchService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		1A1B2C692B5A6F3000123456 /* Views */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3E2B5A6E8100123456 /* ContentView.swift */,
				1A1B2C6A2B5A6F4000123456 /* HomeFeedView.swift */,
				1A1B2C6C2B5A6F4100123456 /* ChatView.swift */,
				1A1B2C6E2B5A6F4200123456 /* ReadingView.swift */,
				1A1B2C702B5A6F4300123456 /* SearchResultsView.swift */,
				1A1B2C722B5A6F4400123456 /* SettingsView.swift */,
				1A1B2C742B5A6F4500123456 /* MenuBarView.swift */,
				1A1B2C762B5A6F4600123456 /* CaptureOverlay.swift */,
				1A1B2C782B5A6F4700123456 /* SyncStatusView.swift */,
				1A1B2C7A2B5A6F4800123456 /* DigestView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A1B2C7C2B5A6F5000123456 /* Animations */ = {
			isa = PBXGroup;
			children = (
				1A1B2C7D2B5A6F6000123456 /* ContentTransitions.swift */,
				1A1B2C7F2B5A6F6100123456 /* GestureAnimations.swift */,
				1A1B2C812B5A6F6200123456 /* ProgressIndicators.swift */,
				1A1B2C832B5A6F6300123456 /* SaveAnimation.swift */,
			);
			path = Animations;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A1B2C382B5A6E7F00123456 /* PocketNext */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A1B2C482B5A6E8200123456 /* Build configuration list for PBXNativeTarget "PocketNext" */;
			buildPhases = (
				1A1B2C352B5A6E7F00123456 /* Sources */,
				1A1B2C362B5A6E7F00123456 /* Frameworks */,
				1A1B2C372B5A6E7F00123456 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PocketNext;
			packageProductDependencies = (
				1A1B2C862B5A6F8000123456 /* GRDB */,
			);
			productName = PocketNext;
			productReference = 1A1B2C392B5A6E7F00123456 /* PocketNext.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A1B2C312B5A6E7F00123456 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					1A1B2C382B5A6E7F00123456 = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 1A1B2C342B5A6E7F00123456 /* Build configuration list for PBXProject "PocketNext" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A1B2C302B5A6E7F00123456;
			packageReferences = (
				1A1B2C852B5A6F8000123456 /* XCRemoteSwiftPackageReference "GRDB.swift" */,
			);
			productRefGroup = 1A1B2C3A2B5A6E7F00123456 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A1B2C382B5A6E7F00123456 /* PocketNext */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A1B2C372B5A6E7F00123456 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A1B2C442B5A6E8200123456 /* Preview Assets.xcassets in Resources */,
				1A1B2C412B5A6E8200123456 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A1B2C352B5A6E7F00123456 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A1B2C3F2B5A6E8100123456 /* ContentView.swift in Sources */,
				1A1B2C3D2B5A6E7F00123456 /* PocketNextApp.swift in Sources */,
				1A1B2C4B2B5A6F0000123456 /* AppState.swift in Sources */,
				1A1B2C4D2B5A6F0100123456 /* Article.swift in Sources */,
				1A1B2C502B5A6F2000123456 /* DatabaseManager.swift in Sources */,
				1A1B2C522B5A6F2100123456 /* NativeMessaging.swift in Sources */,
				1A1B2C542B5A6F2200123456 /* ContentProcessor.swift in Sources */,
				1A1B2C562B5A6F2300123456 /* SearchEngine.swift in Sources */,
				1A1B2C582B5A6F2400123456 /* CloudKitChatSyncService.swift in Sources */,
				1A1B2C5A2B5A6F2500123456 /* ConfigurationService.swift in Sources */,
				1A1B2C5C2B5A6F2600123456 /* DigestService.swift in Sources */,
				1A1B2C5E2B5A6F2700123456 /* EmbeddingService.swift in Sources */,
				1A1B2C602B5A6F2800123456 /* FeedCurationService.swift in Sources */,
				1A1B2C622B5A6F2900123456 /* LLMService.swift in Sources */,
				1A1B2C642B5A6F2A00123456 /* RAGService.swift in Sources */,
				1A1B2C662B5A6F2B00123456 /* VectorStorageManager.swift in Sources */,
				1A1B2C682B5A6F2C00123456 /* WebSearchService.swift in Sources */,
				1A1B2C6B2B5A6F4000123456 /* HomeFeedView.swift in Sources */,
				1A1B2C6D2B5A6F4100123456 /* ChatView.swift in Sources */,
				1A1B2C6F2B5A6F4200123456 /* ReadingView.swift in Sources */,
				1A1B2C712B5A6F4300123456 /* SearchResultsView.swift in Sources */,
				1A1B2C732B5A6F4400123456 /* SettingsView.swift in Sources */,
				1A1B2C752B5A6F4500123456 /* MenuBarView.swift in Sources */,
				1A1B2C772B5A6F4600123456 /* CaptureOverlay.swift in Sources */,
				1A1B2C792B5A6F4700123456 /* SyncStatusView.swift in Sources */,
				1A1B2C7B2B5A6F4800123456 /* DigestView.swift in Sources */,
				1A1B2C7E2B5A6F6000123456 /* ContentTransitions.swift in Sources */,
				1A1B2C802B5A6F6100123456 /* GestureAnimations.swift in Sources */,
				1A1B2C822B5A6F6200123456 /* ProgressIndicators.swift in Sources */,
				1A1B2C842B5A6F6300123456 /* SaveAnimation.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A1B2C462B5A6E8200123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A1B2C472B5A6E8200123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		1A1B2C492B5A6E8200123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PocketNext/PocketNext.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PocketNext/Preview Content\"";
				DEVELOPMENT_TEAM = 25853VT55X;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PocketNext/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PocketNext;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pocketnext.macos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "PocketNext/PocketNext-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		1A1B2C4A2B5A6E8200123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PocketNext/PocketNext.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PocketNext/Preview Content\"";
				DEVELOPMENT_TEAM = 25853VT55X;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PocketNext/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PocketNext;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.pocketnext.macos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "PocketNext/PocketNext-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A1B2C342B5A6E7F00123456 /* Build configuration list for PBXProject "PocketNext" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A1B2C462B5A6E8200123456 /* Debug */,
				1A1B2C472B5A6E8200123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A1B2C482B5A6E8200123456 /* Build configuration list for PBXNativeTarget "PocketNext" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A1B2C492B5A6E8200123456 /* Debug */,
				1A1B2C4A2B5A6E8200123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		1A1B2C852B5A6F8000123456 /* XCRemoteSwiftPackageReference "GRDB.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/groue/GRDB.swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.24.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1A1B2C862B5A6F8000123456 /* GRDB */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1A1B2C852B5A6F8000123456 /* XCRemoteSwiftPackageReference "GRDB.swift" */;
			productName = GRDB;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 1A1B2C312B5A6E7F00123456 /* Project object */;
}
