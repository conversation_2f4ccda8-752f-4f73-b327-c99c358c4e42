// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "PocketNext",
    platforms: [
        .macOS(.v14)
    ],
    products: [
        .executable(
            name: "PocketNext",
            targets: ["PocketNext"]),
    ],
    dependencies: [
        // SQLite database
        .package(url: "https://github.com/groue/GRDB.swift.git", from: "6.29.3"),
        
        // Testing
        .package(url: "https://github.com/nalexn/ViewInspector.git", from: "0.9.0"),
    ],
    targets: [
        .executableTarget(
            name: "PocketNext",
            dependencies: [
                .product(name: "GRDB", package: "GRDB.swift"),
            ],
            path: "PocketNext",
            resources: [
                .process("Resources"),
                .process("Assets.xcassets"),
                .process("Preview Content")
            ],
            swiftSettings: [
                .unsafeFlags(["-suppress-warnings"])
            ]
        ),
        .testTarget(
            name: "PocketNextTests",
            dependencies: [
                "PocketNext",
                .product(name: "ViewInspector", package: "ViewInspector"),
            ],
            path: "PocketNextTests"
        ),
    ]
)