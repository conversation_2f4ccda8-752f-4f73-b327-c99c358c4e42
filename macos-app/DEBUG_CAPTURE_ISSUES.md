# Debug Capture Issues

## Issues Found in Logs

1. **Database Not Initialized**
   - All operations failing with `notInitialized`
   - Need to ensure database initializes on app start

2. **Multiple Capture Attempts**
   - User clicked multiple times creating overlapping captures
   - Fixed by adding guard to prevent multiple captures

3. **Overlay Not Visible**
   - `isCapturing = true` but no visual feedback
   - Menu bar doesn't open main window automatically

4. **CloudKit Errors**
   - ChatConversation record type not found
   - This is normal if CloudKit isn't configured

## Fixes Applied

1. **Prevent Multiple Captures**
   ```swift
   guard !isCapturing else {
       print("AppState: ⚠️ Capture already in progress, ignoring")
       return
   }
   ```

2. **Open Main Window First**
   - Menu bar now opens main window before showing overlay
   - Small delay ensures window is ready

3. **Use Shared Database Instance**
   - MenuBarView was creating new instance instead of using shared

4. **Added Debug Logging**
   - Database initialization status
   - Window opening status
   - Error states

## Test Steps

1. **Restart the app completely**
   - Quit and relaunch
   - Check console for "Database initialized successfully"

2. **Click Save Current Page once**
   - Should open main window
   - Should show overlay at bottom

3. **Check for errors**
   - Look for database initialization errors
   - Check if window opens

## Potential Issues

1. **Window Management**
   - Menu bar app might not have main window
   - Overlay needs window to attach to

2. **SwiftUI State**
   - Published property might not update UI
   - Animation/transition conflicts

3. **Database Path**
   - Might be permission issues
   - Check if database file can be created

## Alternative Approach

If overlay still doesn't show, we could:
1. Use a sheet instead of overlay
2. Create a separate window for capture
3. Show status in menu bar itself

## Console Commands to Test

```swift
// In Xcode debugger console:
po appState.isCapturing
po appState.database.isInitialized
po NSApp.windows.count
```