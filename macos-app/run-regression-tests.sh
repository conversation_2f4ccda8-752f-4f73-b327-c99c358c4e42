#!/bin/bash

echo "Running Regression Tests for macOS App Runtime Issues..."
echo "================================================"
echo ""
echo "These tests prevent the following issues:"
echo "1. Database 'notInitialized' errors"
echo "2. Layout recursion warnings"
echo "3. CloudKit record type errors" 
echo "4. Publishing changes during view updates"
echo "5. LLM model loading failures"
echo ""

# Create test output directory
mkdir -p test-results

# Run specific regression tests
echo "Running Database Initialization Regression Tests..."
swift test --filter DatabaseInitializationRegressionTests 2>&1 | tee test-results/database-regression.log

echo ""
echo "Running Layout Recursion Regression Tests..."
swift test --filter LayoutRecursionRegressionTests 2>&1 | tee test-results/layout-regression.log

echo ""
echo "Running SwiftUI State Regression Tests..."
swift test --filter SwiftUIStateRegressionTests 2>&1 | tee test-results/swiftui-regression.log

echo ""
echo "Running CloudKit Regression Tests..."
swift test --filter CloudKitRegressionTests 2>&1 | tee test-results/cloudkit-regression.log

echo ""
echo "Running LLM Service Regression Tests..."
swift test --filter LLMServiceRegressionTests 2>&1 | tee test-results/llm-regression.log

echo ""
echo "Running View Update Publishing Regression Tests..."
swift test --filter ViewUpdatePublishingRegressionTests 2>&1 | tee test-results/view-update-regression.log

echo ""
echo "Running App Launch Integration Test..."
swift test --filter AppLaunchRegressionTest 2>&1 | tee test-results/app-launch-regression.log

echo ""
echo "================================================"
echo "Regression Test Summary:"
echo ""

# Check for failures
if grep -q "failed" test-results/*.log; then
    echo "❌ Some regression tests failed!"
    echo "Check test-results/ directory for details"
    exit 1
else
    echo "✅ All regression tests passed!"
    echo "The app should not experience the reported runtime issues"
fi