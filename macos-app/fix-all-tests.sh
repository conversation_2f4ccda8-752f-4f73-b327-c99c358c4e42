#!/bin/bash

echo "Fixing common test issues..."

# 1. Fix isRead property (it's read-only, articles are created with isRead=false by default)
echo "Removing isRead parameters from Article.testArticle calls..."
find PocketNextTests -name "*.swift" -exec sed -i '' 's/, isRead: true//g' {} \;
find PocketNextTests -name "*.swift" -exec sed -i '' 's/, isRead: false//g' {} \;
find PocketNextTests -name "*.swift" -exec sed -i '' 's/isRead: true, //g' {} \;
find PocketNextTests -name "*.swift" -exec sed -i '' 's/isRead: false, //g' {} \;

# 2. Fix parameter order issues (readingTime must come before capturedAt)
echo "Fixing parameter order in Article.testArticle calls..."
# This is more complex, let's do it manually in the affected files

# 3. Fix ChatConversation initialization
echo "Fixing ChatConversation initialization..."
# Need to add createdAt, updatedAt, metadata parameters

# 4. Fix database property assignments (it's a let constant)
echo "Removing database assignments in tests..."
find PocketNextTests -name "*.swift" -exec sed -i '' '/appState\.database = database/d' {} \;

# 5. Fix dbQueue access (use DatabaseManager's public API)
echo "Fixing dbQueue access..."
# This needs manual fixes

echo "Done with automatic fixes. Manual fixes still needed for:"
echo "- Parameter order in Article.testArticle calls"
echo "- ChatConversation initialization parameters"
echo "- dbQueue direct access (use DatabaseManager public API)"
echo "- Missing DatabaseManager methods (fetch, update, etc.)"