import XCTest

final class TabNavigationFunctionalTests: XCTestCase {
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launch()
        
        // Wait for app to fully load
        let mainWindow = app.windows.firstMatch
        XCTAssertTrue(mainWindow.waitForExistence(timeout: 5), "App failed to launch")
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - Tab Navigation Tests
    
    func testHomeTabNavigation() throws {
        // Find and click the Home tab
        let homeTab = app.staticTexts["Home"]
        XCTAssertTrue(homeTab.waitForExistence(timeout: 3), "Home tab not found")
        
        homeTab.click()
        
        // Verify we're on the Home/Feed view
        // Should see either articles or empty state
        let feedViewExists = app.scrollViews.firstMatch.exists ||
                            app.staticTexts["No articles yet"].exists ||
                            app.otherElements["HomeFeedView"].exists
        
        XCTAssertTrue(feedViewExists, "Home feed view not loaded after clicking Home tab")
        
        // Check for runtime errors by verifying UI elements are accessible
        verifyNoRuntimeErrors()
    }
    
    func testDigestTabNavigation() throws {
        // Find and click the Digest tab
        let digestTab = app.staticTexts["Digest"]
        XCTAssertTrue(digestTab.waitForExistence(timeout: 3), "Digest tab not found")
        
        digestTab.click()
        
        // Wait a moment for view transition
        Thread.sleep(forTimeInterval: 0.5)
        
        // Verify we're on the Digest view
        let digestViewExists = app.staticTexts["Daily Digest"].exists ||
                              app.staticTexts["Weekly Digest"].exists ||
                              app.staticTexts["Your Digest"].exists ||
                              app.staticTexts["No digest available"].exists ||
                              app.otherElements["DigestView"].exists
        
        XCTAssertTrue(digestViewExists, "Digest view not loaded after clicking Digest tab")
        
        // Check for runtime errors
        verifyNoRuntimeErrors()
    }
    
    func testChatTabNavigation() throws {
        // Find and click the Chat tab
        let chatTab = app.staticTexts["Chat"]
        XCTAssertTrue(chatTab.waitForExistence(timeout: 3), "Chat tab not found")
        
        chatTab.click()
        
        // Wait a moment for view transition
        Thread.sleep(forTimeInterval: 0.5)
        
        // Verify we're on the Chat view
        let chatViewExists = app.textViews.firstMatch.exists ||
                            app.staticTexts["Ask me anything about your articles"].exists ||
                            app.buttons["Send"].exists ||
                            app.otherElements["ChatView"].exists ||
                            app.scrollViews["ChatMessagesScrollView"].exists
        
        XCTAssertTrue(chatViewExists, "Chat view not loaded after clicking Chat tab")
        
        // Check for runtime errors
        verifyNoRuntimeErrors()
    }
    
    func testAllArticlesNavigation() throws {
        // Find and click the All Articles item
        let allArticlesItem = app.staticTexts["All Articles"]
        XCTAssertTrue(allArticlesItem.waitForExistence(timeout: 3), "All Articles item not found")
        
        allArticlesItem.click()
        
        // Should navigate to feed view with all articles
        Thread.sleep(forTimeInterval: 0.5)
        
        // Verify we see article list or empty state
        let articlesViewExists = app.scrollViews.firstMatch.exists ||
                                app.staticTexts["No articles"].exists ||
                                app.collectionViews.firstMatch.exists
        
        XCTAssertTrue(articlesViewExists, "Articles view not loaded after clicking All Articles")
        
        verifyNoRuntimeErrors()
    }
    
    func testUnreadNavigation() throws {
        // Find and click the Unread item
        let unreadItem = app.staticTexts["Unread"]
        XCTAssertTrue(unreadItem.waitForExistence(timeout: 3), "Unread item not found")
        
        unreadItem.click()
        
        Thread.sleep(forTimeInterval: 0.5)
        
        // Should show filtered articles view
        let unreadViewExists = app.scrollViews.firstMatch.exists ||
                              app.staticTexts["No unread articles"].exists ||
                              app.collectionViews.firstMatch.exists
        
        XCTAssertTrue(unreadViewExists, "Unread view not loaded after clicking Unread")
        
        verifyNoRuntimeErrors()
    }
    
    func testArchivedNavigation() throws {
        // Find and click the Archived item
        let archivedItem = app.staticTexts["Archived"]
        XCTAssertTrue(archivedItem.waitForExistence(timeout: 3), "Archived item not found")
        
        archivedItem.click()
        
        Thread.sleep(forTimeInterval: 0.5)
        
        // Should show archived articles view
        let archivedViewExists = app.scrollViews.firstMatch.exists ||
                                app.staticTexts["No archived articles"].exists ||
                                app.collectionViews.firstMatch.exists
        
        XCTAssertTrue(archivedViewExists, "Archived view not loaded after clicking Archived")
        
        verifyNoRuntimeErrors()
    }
    
    // MARK: - Sequential Navigation Test
    
    func testSequentialTabNavigation() throws {
        // This test navigates through all tabs in sequence to ensure
        // no runtime errors occur when switching between views
        
        let tabs = [
            ("Home", 1.0),
            ("Digest", 1.0),
            ("Chat", 1.0),
            ("All Articles", 0.5),
            ("Unread", 0.5),
            ("Archived", 0.5),
            ("Home", 1.0), // Return to home
        ]
        
        for (tabName, waitTime) in tabs {
            let tab = app.staticTexts[tabName]
            
            if tab.waitForExistence(timeout: 3) {
                tab.click()
                Thread.sleep(forTimeInterval: waitTime)
                
                // Verify app is still responsive
                XCTAssertTrue(app.windows.firstMatch.exists, "App window disappeared after clicking \(tabName)")
                verifyNoRuntimeErrors()
            } else {
                XCTFail("Tab '\(tabName)' not found during sequential navigation")
            }
        }
    }
    
    // MARK: - Search Navigation Test
    
    func testSearchNavigation() throws {
        // Test search functionality which should change view mode
        let searchField = app.searchFields.firstMatch
        
        if searchField.waitForExistence(timeout: 3) {
            searchField.click()
            searchField.typeText("test search")
            
            // Press Enter to submit search
            app.typeText("\n")
            
            Thread.sleep(forTimeInterval: 1.0)
            
            // Should be in search results view
            let searchResultsExist = app.staticTexts["Search Results"].exists ||
                                    app.staticTexts["No results found"].exists ||
                                    app.otherElements["SearchResultsView"].exists ||
                                    app.scrollViews.element(boundBy: 0).exists
            
            XCTAssertTrue(searchResultsExist, "Search results view not loaded after search")
            
            verifyNoRuntimeErrors()
            
            // Clear search to return to feed
            searchField.click()
            searchField.clearAndEnterText("")
            app.typeText("\n")
            
            Thread.sleep(forTimeInterval: 0.5)
            verifyNoRuntimeErrors()
        } else {
            XCTFail("Search field not found")
        }
    }
    
    // MARK: - Save Button Test
    
    func testSaveButtonInteraction() throws {
        // Test the Save button doesn't cause crashes
        let saveButton = app.buttons["Save"].firstMatch
        
        if !saveButton.exists {
            // Try finding by image
            let plusButton = app.buttons.containing(.image, identifier: "plus.circle").firstMatch
            if plusButton.waitForExistence(timeout: 3) {
                plusButton.click()
                Thread.sleep(forTimeInterval: 0.5)
                
                // Should show capture overlay
                let captureOverlayExists = app.otherElements["CaptureOverlay"].exists ||
                                          app.staticTexts["Capturing..."].exists
                
                // Overlay might appear and disappear quickly
                verifyNoRuntimeErrors()
            }
        } else if saveButton.waitForExistence(timeout: 3) {
            saveButton.click()
            Thread.sleep(forTimeInterval: 0.5)
            verifyNoRuntimeErrors()
        }
    }
    
    // MARK: - Helper Methods
    
    private func verifyNoRuntimeErrors() {
        // Check that key UI elements are still present and accessible
        let window = app.windows.firstMatch
        XCTAssertTrue(window.exists, "App window disappeared - possible crash")
        
        // Check sidebar is still visible
        let sidebar = app.outlines.firstMatch
        let sidebarExists = sidebar.exists || app.lists.firstMatch.exists
        XCTAssertTrue(sidebarExists, "Sidebar disappeared - possible navigation error")
        
        // Check that we don't see error alerts
        let errorAlert = app.alerts.firstMatch
        XCTAssertFalse(errorAlert.exists, "Error alert appeared: \(errorAlert.label)")
        
        // Check for common error text
        let errorTexts = [
            "Fatal error",
            "Thread 1: Fatal error",
            "unexpectedly found nil",
            "Index out of range",
            "Precondition failed"
        ]
        
        for errorText in errorTexts {
            XCTAssertFalse(app.staticTexts[errorText].exists, "Runtime error text found: \(errorText)")
        }
    }
}

// MARK: - XCUIElement Extension

extension XCUIElement {
    func clearAndEnterText(_ text: String) {
        guard let stringValue = self.value as? String else {
            XCTFail("Tried to clear and enter text on non string value")
            return
        }
        
        self.tap()
        let deleteString = String(repeating: XCUIKeyboardKey.delete.rawValue, count: stringValue.count)
        self.typeText(deleteString)
        self.typeText(text)
    }
}