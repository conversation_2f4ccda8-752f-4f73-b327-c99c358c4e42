import XCTest

/// End-to-End tests for macOS native integration features
class NativeIntegrationTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting", "--enable-native-features"]
        app.launch()
    }
    
    // MARK: - Browser Extension Tests
    
    func testBrowserExtensionCommunication() throws {
        // This tests the native messaging with browser extension
        
        // Check if extension is connected
        let statusBar = app.statusItems.firstMatch
        if statusBar.exists {
            statusBar.click()
            
            let extensionStatus = app.menuItems["Extension: Connected"]
            XCTAssertTrue(extensionStatus.exists || 
                         app.menuItems["Extension: Not Connected"].exists)
            
            // Dismiss menu
            app.click()
        }
    }
    
    func testSaveFromBrowserExtension() throws {
        // Simulate receiving article from browser extension
        // This would require the extension to be installed
        
        // Check for notification
        let notification = XCUIApplication(bundleIdentifier: "com.apple.notificationcenterui")
            .otherElements["NotificationShortLookView"]
        
        if notification.waitForExistence(timeout: 2) {
            // Verify notification content
            XCTAssertTrue(notification.staticTexts["Article Saved"].exists ||
                         notification.staticTexts["PocketNext"].exists)
        }
    }
    
    // MARK: - Services Menu Tests
    
    func testServicesMenuIntegration() throws {
        // Test macOS Services menu integration
        // This requires setting up a test environment with selectable text
        
        // For now, verify the service is registered
        app.menuBars.menuBarItems["PocketNext"].click()
        app.menuItems["Services"].hover()
        
        Thread.sleep(forTimeInterval: 0.5)
        
        // Check if our service appears
        let servicesSubmenu = app.menuItems["Services"].menuItems
        XCTAssertTrue(servicesSubmenu["Save to PocketNext"].exists ||
                     servicesSubmenu.count > 0)
    }
    
    // MARK: - Share Extension Tests
    
    func testShareExtension() throws {
        // Open an article
        navigateToArticle()
        
        // Click share button
        if app.buttons["Share"].exists {
            app.buttons["Share"].click()
            
            // Look for share menu
            let shareMenu = app.popovers.firstMatch
            if shareMenu.waitForExistence(timeout: 2) {
                // Verify share options
                XCTAssertTrue(shareMenu.buttons["Mail"].exists ||
                            shareMenu.buttons["Messages"].exists ||
                            shareMenu.buttons["Copy Link"].exists)
                
                // Dismiss
                app.click()
            }
        }
    }
    
    // MARK: - Spotlight Integration Tests
    
    func testSpotlightSearch() throws {
        // This tests if articles are indexed in Spotlight
        // Requires special permissions and setup
        
        // For unit testing, verify Core Spotlight integration exists
        let searchField = app.searchFields.firstMatch
        searchField.click()
        searchField.typeText("spotlight test")
        
        Thread.sleep(forTimeInterval: 1)
        
        // Clear search
        searchField.typeKey("a", modifierFlags: .command)
        searchField.typeKey(.delete, modifierFlags: [])
    }
    
    // MARK: - Touch Bar Tests (if applicable)
    
    func testTouchBarSupport() throws {
        // Skip if not running on MacBook Pro with Touch Bar
        guard app.touchBars.count > 0 else {
            XCTSkip("No Touch Bar available")
        }
        
        let touchBar = app.touchBars.firstMatch
        
        // Verify Touch Bar buttons
        XCTAssertTrue(touchBar.buttons["Archive"].exists ||
                     touchBar.buttons["Favorite"].exists ||
                     touchBar.buttons["Share"].exists)
    }
    
    // MARK: - Quick Look Tests
    
    func testQuickLook() throws {
        // Navigate to article list
        app.outlineRows["Feed"].click()
        
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        let article = articlesList.cells.firstMatch
        guard article.exists else {
            XCTSkip("No articles available")
        }
        
        // Select article and press space for Quick Look
        article.click()
        app.typeKey(.space, modifierFlags: [])
        
        // Verify Quick Look panel
        let quickLookPanel = app.windows["QLPreviewPanel"]
        if quickLookPanel.waitForExistence(timeout: 2) {
            XCTAssertTrue(quickLookPanel.exists)
            
            // Close Quick Look
            app.typeKey(.space, modifierFlags: [])
        }
    }
    
    // MARK: - Handoff Tests
    
    func testHandoffSupport() throws {
        // Open an article
        navigateToArticle()
        
        // Handoff indicator would appear in dock
        // This is hard to test in UI tests
        
        // Verify activity is registered (indirect test)
        XCTAssertTrue(app.windows.firstMatch.exists)
    }
    
    // MARK: - AppleScript Support Tests
    
    func testAppleScriptSupport() throws {
        // This would test AppleScript dictionary support
        // Requires running AppleScript which is beyond UI tests
        
        // For now, just verify app responds to basic commands
        app.menuBars.menuBarItems["Window"].click()
        XCTAssertTrue(app.menuItems["Minimize"].exists)
        app.click() // Dismiss menu
    }
    
    // MARK: - Notification Tests
    
    func testNotificationInteraction() throws {
        // Test notification actions
        // This requires permission and is hard to test directly
        
        // Check notification preferences
        app.typeKey(",", modifierFlags: .command)
        
        let prefsWindow = app.windows["Preferences"]
        if prefsWindow.waitForExistence(timeout: 2) {
            let toolbar = prefsWindow.toolbars.firstMatch
            
            if toolbar.buttons["Notifications"].exists {
                toolbar.buttons["Notifications"].click()
                
                // Verify notification settings
                XCTAssertTrue(prefsWindow.checkBoxes["Enable Notifications"].exists)
            }
            
            prefsWindow.buttons[XCUIIdentifierCloseWindow].click()
        }
    }
    
    // MARK: - Window Restoration Tests
    
    func testWindowRestoration() throws {
        // Open multiple windows
        app.typeKey("n", modifierFlags: .command)
        Thread.sleep(forTimeInterval: 0.5)
        
        // Open article in second window
        if app.windows.count > 1 {
            let secondWindow = app.windows.element(boundBy: 1)
            secondWindow.click()
            
            // Navigate to different view
            app.outlineRows["Chat"].click()
        }
        
        // Simulate app quit and relaunch
        app.terminate()
        app.launch()
        
        // Verify windows are restored
        XCTAssertTrue(app.windows.count >= 1)
    }
    
    // MARK: - Mission Control Integration
    
    func testMissionControlSpaces() throws {
        // Test full screen and spaces behavior
        navigateToArticle()
        
        // Enter full screen
        let window = app.windows.firstMatch
        window.buttons[XCUIIdentifierFullScreenWindow].click()
        
        Thread.sleep(forTimeInterval: 1)
        
        // Exit full screen
        app.typeKey("f", modifierFlags: [.control, .command])
    }
    
    // MARK: - File System Integration Tests
    
    func testExportToFiles() throws {
        // Test exporting articles to file system
        app.menuBars.menuBarItems["File"].click()
        
        if app.menuItems["Export Articles..."].exists {
            app.menuItems["Export Articles..."].click()
            
            // Save dialog should appear
            let saveDialog = app.sheets.firstMatch
            if saveDialog.waitForExistence(timeout: 2) {
                // Verify save options
                XCTAssertTrue(saveDialog.popUpButtons["Format"].exists)
                
                // Cancel
                saveDialog.buttons["Cancel"].click()
            }
        } else {
            app.click() // Dismiss menu
        }
    }
    
    func testImportFromFiles() throws {
        // Test importing articles
        app.menuBars.menuBarItems["File"].click()
        
        if app.menuItems["Import..."].exists {
            app.menuItems["Import..."].click()
            
            // Open dialog should appear
            let openDialog = app.sheets.firstMatch
            if openDialog.waitForExistence(timeout: 2) {
                // Cancel
                openDialog.buttons["Cancel"].click()
            }
        } else {
            app.click() // Dismiss menu
        }
    }
    
    // MARK: - Helper Methods
    
    private func navigateToArticle() {
        app.outlineRows["Feed"].click()
        
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            return
        }
        
        let firstArticle = articlesList.cells.firstMatch
        if firstArticle.exists {
            firstArticle.doubleClick()
        }
    }
}