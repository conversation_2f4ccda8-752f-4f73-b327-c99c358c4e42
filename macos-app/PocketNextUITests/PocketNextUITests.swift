import XCTest

/// End-to-End UI Tests for PocketNext macOS app
/// These tests verify complete user flows and interactions on macOS
class PocketNextUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launchEnvironment = ["TESTING": "1"]
        
        // Reset app state for consistent testing
        app.launchArguments.append("--reset-state")
        
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - App Launch Tests
    
    func testAppLaunchesSuccessfully() throws {
        // Verify main window exists
        let mainWindow = app.windows["MainWindow"]
        XCTAssertTrue(mainWindow.exists)
        
        // Verify sidebar exists
        let sidebar = app.outlineRows
        XCTAssertTrue(sidebar.count > 0)
        
        // Verify toolbar exists
        let toolbar = app.toolbars.firstMatch
        XCTAssertTrue(toolbar.exists)
    }
    
    // MARK: - Sidebar Navigation Tests
    
    func testSidebarNavigation() throws {
        // Get sidebar
        let sidebar = app.outlineRows
        
        // Verify sidebar sections exist
        XCTAssertTrue(sidebar["Feed"].exists)
        XCTAssertTrue(sidebar["Digest"].exists)
        XCTAssertTrue(sidebar["Search"].exists)
        XCTAssertTrue(sidebar["Chat"].exists)
        
        // Test navigation
        sidebar["Digest"].click()
        XCTAssertTrue(app.otherElements["DigestView"].exists)
        
        sidebar["Search"].click()
        XCTAssertTrue(app.searchFields.firstMatch.exists)
        
        sidebar["Chat"].click()
        XCTAssertTrue(app.otherElements["ChatView"].exists)
        
        sidebar["Feed"].click()
        XCTAssertTrue(app.collectionViews.firstMatch.exists)
    }
    
    // MARK: - Feed View Tests
    
    func testFeedViewLayouts() throws {
        // Ensure we're in feed view
        app.outlineRows["Feed"].click()
        
        // Test layout switching
        let layoutButton = app.popUpButtons["LayoutSelector"]
        if layoutButton.exists {
            layoutButton.click()
            
            // Select grid layout
            app.menuItems["Grid"].click()
            
            // Verify grid layout is active
            let feedCollection = app.collectionViews.firstMatch
            XCTAssertTrue(feedCollection.exists)
            
            // Switch to list layout
            layoutButton.click()
            app.menuItems["List"].click()
            
            // Verify list layout
            XCTAssertTrue(app.tables.firstMatch.exists || 
                         app.collectionViews.firstMatch.exists)
        }
    }
    
    func testArticleContextMenu() throws {
        // Navigate to feed
        app.outlineRows["Feed"].click()
        
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list found")
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTSkip("No articles available")
        }
        
        // Right-click for context menu
        firstArticle.rightClick()
        
        // Verify context menu items
        XCTAssertTrue(app.menuItems["Archive"].exists ||
                     app.menuItems["Delete"].exists ||
                     app.menuItems["Share"].exists)
        
        // Dismiss menu
        app.click()
    }
    
    // MARK: - Window Management Tests
    
    func testMultiWindowSupport() throws {
        // Open article in new window
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTSkip("No articles available")
        }
        
        // Double-click or use menu to open in new window
        firstArticle.rightClick()
        if app.menuItems["Open in New Window"].exists {
            app.menuItems["Open in New Window"].click()
            
            // Verify new window opened
            XCTAssertEqual(app.windows.count, 2)
            
            // Close the new window
            app.windows.element(boundBy: 1).buttons[XCUIIdentifierCloseWindow].click()
        }
    }
    
    func testFullScreenReading() throws {
        // Open article
        navigateToArticle()
        
        // Enter full screen
        app.menuBars.menuBarItems["View"].click()
        app.menuItems["Enter Full Screen"].click()
        
        Thread.sleep(forTimeInterval: 1)
        
        // Verify full screen mode
        // Exit full screen
        app.typeKey("f", modifierFlags: [.control, .command])
    }
    
    // MARK: - Search Tests
    
    func testGlobalSearch() throws {
        // Use Command+F for search
        app.typeKey("f", modifierFlags: .command)
        
        let searchField = app.searchFields.firstMatch
        XCTAssertTrue(searchField.waitForExistence(timeout: 2))
        
        // Type search query
        searchField.click()
        searchField.typeText("SwiftUI")
        
        // Verify search results
        let searchResults = app.tables["SearchResults"]
        _ = searchResults.waitForExistence(timeout: 3)
        
        // Clear search
        searchField.typeKey("a", modifierFlags: .command)
        searchField.typeKey(.delete, modifierFlags: [])
    }
    
    func testSearchFilters() throws {
        // Navigate to search
        app.outlineRows["Search"].click()
        
        let searchField = app.searchFields.firstMatch
        searchField.click()
        searchField.typeText("test")
        
        // Apply filters
        if app.checkBoxes["Unread Only"].exists {
            app.checkBoxes["Unread Only"].click()
        }
        
        if app.popUpButtons["ContentType"].exists {
            app.popUpButtons["ContentType"].click()
            app.menuItems["Articles"].click()
        }
    }
    
    // MARK: - Keyboard Shortcuts Tests
    
    func testKeyboardNavigation() throws {
        // Test common keyboard shortcuts
        
        // New article (if applicable)
        app.typeKey("n", modifierFlags: .command)
        Thread.sleep(forTimeInterval: 0.5)
        
        // Navigate between articles with arrow keys
        app.typeKey(.downArrow, modifierFlags: [])
        app.typeKey(.upArrow, modifierFlags: [])
        
        // Open selected article
        app.typeKey(.return, modifierFlags: [])
        
        // Go back
        app.typeKey("[", modifierFlags: .command)
    }
    
    // MARK: - Drag and Drop Tests
    
    func testDragAndDropToArchive() throws {
        // Get article and archive folder
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        let article = articlesList.cells.firstMatch
        guard article.exists else {
            XCTSkip("No articles available")
        }
        
        let archiveFolder = app.outlineRows["Archive"]
        guard archiveFolder.exists else {
            XCTSkip("No archive folder")
        }
        
        // Drag article to archive
        article.click(forDuration: 0.5, thenDragTo: archiveFolder)
        
        Thread.sleep(forTimeInterval: 1)
        
        // Verify article was archived
        archiveFolder.click()
        XCTAssertTrue(app.staticTexts.matching(identifier: "Archived").count > 0 ||
                     articlesList.cells.count > 0)
    }
    
    // MARK: - Preferences Tests
    
    func testOpenPreferences() throws {
        // Open preferences
        app.typeKey(",", modifierFlags: .command)
        
        let prefsWindow = app.windows["Preferences"]
        XCTAssertTrue(prefsWindow.waitForExistence(timeout: 2))
        
        // Test preference tabs
        let toolbar = prefsWindow.toolbars.firstMatch
        
        if toolbar.buttons["General"].exists {
            toolbar.buttons["General"].click()
            XCTAssertTrue(prefsWindow.checkBoxes.count > 0 ||
                         prefsWindow.popUpButtons.count > 0)
        }
        
        if toolbar.buttons["Reading"].exists {
            toolbar.buttons["Reading"].click()
            XCTAssertTrue(prefsWindow.sliders["TextSize"].exists ||
                         prefsWindow.popUpButtons["Font"].exists)
        }
        
        // Close preferences
        prefsWindow.buttons[XCUIIdentifierCloseWindow].click()
    }
    
    // MARK: - Menu Bar Integration Tests
    
    func testMenuBarExtra() throws {
        // Check if menu bar extra exists
        let menuBarExtras = app.menuBarItems
        let pocketNextExtra = menuBarExtras["PocketNext"]
        
        if pocketNextExtra.exists {
            pocketNextExtra.click()
            
            // Verify menu appears
            XCTAssertTrue(app.menuItems["Save from Clipboard"].exists ||
                         app.menuItems["Quick Save"].exists)
            
            // Dismiss menu
            app.click()
        }
    }
    
    // MARK: - Sync Tests
    
    func testManualSync() throws {
        // Trigger sync from menu
        app.menuBars.menuBarItems["File"].click()
        
        if app.menuItems["Sync Now"].exists {
            app.menuItems["Sync Now"].click()
            
            // Look for sync indicator
            let syncIndicator = app.progressIndicators["SyncProgress"]
            if syncIndicator.exists {
                // Wait for sync to complete
                _ = syncIndicator.waitForNonExistence(timeout: 10)
            }
        }
    }
    
    // MARK: - Performance Tests
    
    func testScrollPerformance() throws {
        // Navigate to feed with many articles
        app.outlineRows["Feed"].click()
        
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTSkip("No articles list")
        }
        
        measure {
            // Scroll down
            for _ in 0..<5 {
                app.scrollViews.firstMatch.scroll(byDeltaX: 0, deltaY: -100)
            }
            
            // Scroll back up
            for _ in 0..<5 {
                app.scrollViews.firstMatch.scroll(byDeltaX: 0, deltaY: 100)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func navigateToArticle() {
        app.outlineRows["Feed"].click()
        
        let articlesList = app.collectionViews.firstMatch
        guard articlesList.waitForExistence(timeout: 5) else {
            XCTFail("No articles list")
            return
        }
        
        let firstArticle = articlesList.cells.firstMatch
        guard firstArticle.exists else {
            XCTFail("No articles available")
            return
        }
        
        firstArticle.doubleClick()
    }
}

// MARK: - Helper Extensions

extension XCUIElement {
    /// Wait for element to not exist
    func waitForNonExistence(timeout: TimeInterval) -> Bool {
        let predicate = NSPredicate(format: "exists == false")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: self)
        let result = XCTWaiter().wait(for: [expectation], timeout: timeout)
        return result == .completed
    }
}