#!/bin/bash

# Build the app first
echo "Building the app..."
swift build

# Run the app to check for runtime errors
echo "Running manual test of tab navigation..."
echo "Please manually test the following:"
echo "1. Click on Home tab - verify it loads without crashing"
echo "2. <PERSON>lick on Digest tab - verify it loads without crashing"
echo "3. <PERSON>lick on Chat tab - verify it loads without crashing"
echo "4. <PERSON>lick on All Articles - verify it loads without crashing"
echo "5. <PERSON>lick on Unread - verify it loads without crashing"
echo "6. Click on Archived - verify it loads without crashing"
echo "7. Try searching for something - verify search results load"
echo ""
echo "Starting the app now..."

# Run the app
swift run PocketNext