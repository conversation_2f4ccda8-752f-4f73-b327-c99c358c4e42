#!/usr/bin/env swift

import Foundation

// Simple functional verification script
print("🔍 Starting Pocket-next Functional Verification")
print(String(repeating: "=", count: 50))

// Test 1: Parse Server Health Check
func testParseServer() async {
    print("\n1️⃣ Testing Parse Server...")
    
    let url = URL(string: "http://localhost:8000/health")!
    
    do {
        let (data, response) = try await URLSession.shared.data(from: url)
        
        if let httpResponse = response as? HTTPURLResponse {
            if httpResponse.statusCode == 200 {
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let status = json["status"] as? String {
                    print("✅ Parse server is running! Status: \(status)")
                } else {
                    print("❌ Parse server returned invalid response")
                }
            } else {
                print("❌ Parse server returned status code: \(httpResponse.statusCode)")
            }
        }
    } catch {
        print("⚠️  Parse server not running at localhost:8000")
        print("    Run: cd parse-server && python main.py")
    }
}

// Test 2: Article Parsing
func testArticleParsing() async {
    print("\n2️⃣ Testing Article Parsing...")
    
    let testHTML = """
    <html>
    <head><title>Test Article: Swift Concurrency</title></head>
    <body>
        <article>
            <h1>Understanding Swift Concurrency</h1>
            <p>By Jane Developer</p>
            <p>Published: January 2024</p>
            <div class="content">
                <p>Swift's async/await syntax revolutionizes how we write asynchronous code.</p>
                <p>With actors, we can ensure thread safety without complex locking mechanisms.</p>
            </div>
        </article>
    </body>
    </html>
    """
    
    let url = URL(string: "http://localhost:8000/parse")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    
    let requestBody: [String: Any] = [
        "url": "https://example.com/swift-concurrency",
        "html_content": testHTML,
        "content_type": "article",
        "metadata": ["user_id": "test_user"]
    ]
    
    do {
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        let (data, response) = try await URLSession.shared.data(for: request)
        
        if let httpResponse = response as? HTTPURLResponse,
           httpResponse.statusCode == 200,
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            
            print("✅ Article parsed successfully!")
            print("   Title: \(json["title"] ?? "N/A")")
            print("   Summary: \(String(describing: json["summary"] ?? "N/A").prefix(100))...")
            print("   Keywords: \(json["keywords"] ?? [])")
            print("   Reading time: \(json["reading_time"] ?? 0) minutes")
        } else {
            print("❌ Failed to parse article")
        }
    } catch {
        print("❌ Error parsing article: \(error)")
    }
}

// Test 3: Database Operations (simulated)
func testDatabaseOperations() {
    print("\n3️⃣ Testing Database Operations...")
    
    // Since we can't directly access the database from this script,
    // we'll document what should be tested
    print("   Database tests should verify:")
    print("   • Article CRUD operations")
    print("   • Full-text search functionality")
    print("   • Chat conversation storage")
    print("   • Digest generation queries")
    print("   • Sync status tracking")
}

// Test 4: Browser Extension
func testBrowserExtension() {
    print("\n4️⃣ Testing Browser Extension...")
    print("   Browser extension should:")
    print("   • Capture page content on click")
    print("   • Send to parse server")
    print("   • Communicate with native app via native messaging")
    print("   • Show success/error feedback")
}

// Test 5: Core Features Check
func checkCoreFeatures() {
    print("\n5️⃣ Core Features Implementation Status:")
    
    let features = [
        ("1-Click Capture", "Browser extension + Native messaging", true),
        ("AI Summarization", "Parse server with GPT-4o mini", true),
        ("Full-Text Search", "GRDB with FTS5", true),
        ("Chat Interface", "LLMService + RAGService", true),
        ("Daily/Weekly Digests", "DigestService", true),
        ("Cross-Platform Sync", "CloudKit (needs entitlements)", false),
        ("Offline Mode", "Local database + content caching", true),
        ("Authentication", "Not implemented", false)
    ]
    
    for (feature, implementation, ready) in features {
        let status = ready ? "✅" : "⚠️"
        print("   \(status) \(feature): \(implementation)")
    }
}

// Run all tests
Task {
    await testParseServer()
    await testArticleParsing()
    testDatabaseOperations()
    testBrowserExtension()
    checkCoreFeatures()
    
    print("\n" + String(repeating: "=", count: 50))
    print("📊 Verification Complete")
    print("\nNext Steps:")
    print("1. Start parse server if not running")
    print("2. Test browser extension with real web pages")
    print("3. Run macOS app and test article capture")
    print("4. Fix compilation errors in test files")
    print("5. Implement missing authentication")
    exit(0)
}

RunLoop.main.run()