{"configurations": [{"id": "MACOS-ALL-TESTS", "name": "All Tests", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 60, "maximumTestRepetitions": 3, "testRepetitionMode": "retryOnFailure", "codeCoverageEnabled": true}}, {"id": "MACOS-UNIT-TESTS", "name": "Unit Tests Only", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 30, "codeCoverageEnabled": true}}, {"id": "MACOS-INTEGRATION-TESTS", "name": "Integration Tests", "options": {"testTimeoutsEnabled": true, "maximumTestExecutionTimeAllowance": 120, "codeCoverageEnabled": true}}], "defaultOptions": {"codeCoverage": {"targets": [{"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNext", "name": "PocketNext"}]}, "commandLineArgumentEntries": [{"argument": "-com.apple.CoreData.SQLDebug 0"}, {"argument": "--u<PERSON>ting", "enabled": false}], "environmentVariableEntries": [{"key": "GRDB_SQLITE_SUPPRESS_WAL_JOURNAL_MODE_ERRORS", "value": "1"}, {"key": "TESTING", "value": "1"}], "testExecutionOrdering": "random"}, "testTargets": [{"parallelizable": true, "target": {"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNextTests", "name": "PocketNextTests"}, "selectedTests": ["ArticleTests", "AppStateTests", "DatabaseManagerTests", "CloudKitChatSyncTests", "ContentProcessorTests", "DigestServiceTests", "EmbeddingServiceTests", "LLMServiceTests", "LLMServiceMockTests", "NativeMessagingTests", "RAGServiceTests", "SearchEngineTests", "VectorStorageTests", "WebSearchServiceTests", "ContentViewTests", "HomeFeedViewTests", "ChatViewTests", "ReadingViewTests", "RealDatabaseIntegrationTests"]}, {"parallelizable": false, "target": {"containerPath": "container:PocketNext.xcodeproj", "identifier": "PocketNextUITests", "name": "PocketNextUITests"}, "selectedTests": ["PocketNextUITests", "PocketNextUITestsLaunchTests", "NativeIntegrationTests"]}], "version": 1}