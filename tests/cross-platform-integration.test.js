/**
 * Cross-platform integration tests
 * Tests the complete flow between browser extension, parse server, and native apps
 */

const { spawn } = require('child_process');
const puppeteer = require('puppeteer');
const axios = require('axios');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs').promises;

describe('Cross-Platform Integration', () => {
  let browser;
  let parseServer;
  let nativeAppMock;
  let wsServer;
  const TEST_PORT = 9876;
  const WS_PORT = 9877;
  
  beforeAll(async () => {
    // Start parse server
    parseServer = spawn('python', [
      path.join(__dirname, '../parse-server/main.py'),
      '--port', TEST_PORT.toString()
    ]);
    
    // Wait for parse server to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Start WebSocket server for real-time updates
    wsServer = new WebSocket.Server({ port: WS_PORT });
    
    wsServer.on('connection', (ws) => {
      ws.on('message', (message) => {
        // Broadcast messages to all connected clients
        wsServer.clients.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(message);
          }
        });
      });
    });
    
    // Launch browser with extension
    const extensionPath = path.join(__dirname, '../browser-extension');
    browser = await puppeteer.launch({
      headless: process.env.CI === 'true' ? 'new' : false,
      args: [
        `--disable-extensions-except=${extensionPath}`,
        `--load-extension=${extensionPath}`,
        '--no-sandbox'
      ]
    });
  }, 30000);
  
  afterAll(async () => {
    if (browser) await browser.close();
    if (parseServer) parseServer.kill();
    if (wsServer) wsServer.close();
    if (nativeAppMock) nativeAppMock.stop();
  });
  
  describe('Complete Article Flow', () => {
    test('should capture, parse, and sync article across platforms', async () => {
      const page = await browser.newPage();
      
      // 1. Navigate to test article
      await page.goto('https://example.com');
      await page.setContent(`
        <html>
          <head>
            <title>Cross-Platform Test Article</title>
            <meta name="author" content="Test Author">
          </head>
          <body>
            <h1>Cross-Platform Integration Test</h1>
            <p>This article tests the complete flow from browser to native apps.</p>
            <p>It includes multiple paragraphs to test content extraction.</p>
          </body>
        </html>
      `);
      
      // 2. Capture article with browser extension
      const extensionId = await getExtensionId(browser);
      const captureResult = await page.evaluate((extId) => {
        return new Promise((resolve) => {
          chrome.runtime.sendMessage(
            extId,
            { action: 'capture' },
            response => resolve(response)
          );
        });
      }, extensionId);
      
      expect(captureResult.success).toBe(true);
      
      // 3. Verify article was sent to parse server
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 4. Check parsed article via API
      const response = await axios.get(`http://localhost:${TEST_PORT}/status`);
      expect(response.status).toBe(200);
      
      // 5. Simulate native app receiving the article
      const wsClient = new WebSocket(`ws://localhost:${WS_PORT}`);
      
      const messageReceived = new Promise((resolve) => {
        wsClient.on('message', (data) => {
          const message = JSON.parse(data);
          if (message.type === 'article_synced') {
            resolve(message);
          }
        });
      });
      
      // 6. Trigger sync
      wsClient.send(JSON.stringify({
        type: 'sync_request',
        platform: 'ios'
      }));
      
      const syncMessage = await messageReceived;
      expect(syncMessage.article.title).toBe('Cross-Platform Test Article');
      
      wsClient.close();
      await page.close();
    });
    
    test('should handle offline sync across devices', async () => {
      // Simulate multiple devices with offline articles
      const devices = [
        { id: 'iphone-1', platform: 'ios', articles: [] },
        { id: 'mac-1', platform: 'macos', articles: [] },
        { id: 'browser-1', platform: 'chrome', articles: [] }
      ];
      
      // Each device captures articles while offline
      for (const device of devices) {
        for (let i = 0; i < 3; i++) {
          device.articles.push({
            url: `https://example.com/${device.id}-article-${i}`,
            title: `${device.platform} Article ${i}`,
            content: `Content from ${device.id}`,
            capturedAt: new Date().toISOString(),
            syncStatus: 'pending'
          });
        }
      }
      
      // When devices come online, they sync
      const syncPromises = devices.map(async (device) => {
        const wsClient = new WebSocket(`ws://localhost:${WS_PORT}`);
        
        await new Promise(resolve => {
          wsClient.on('open', resolve);
        });
        
        // Send all articles for sync
        for (const article of device.articles) {
          const parseResponse = await axios.post(
            `http://localhost:${TEST_PORT}/parse`,
            {
              url: article.url,
              title: article.title,
              htmlContent: `<html><body><p>${article.content}</p></body></html>`,
              metadata: {
                deviceId: device.id,
                platform: device.platform
              }
            }
          );
          
          expect(parseResponse.status).toBe(200);
          
          // Notify other devices
          wsClient.send(JSON.stringify({
            type: 'article_synced',
            article: parseResponse.data,
            deviceId: device.id
          }));
        }
        
        wsClient.close();
      });
      
      await Promise.all(syncPromises);
      
      // Verify all articles were synced
      const totalArticles = devices.reduce((sum, d) => sum + d.articles.length, 0);
      expect(totalArticles).toBe(9); // 3 devices × 3 articles
    });
  });
  
  describe('Conflict Resolution', () => {
    test('should resolve conflicts when same article saved on multiple devices', async () => {
      const articleUrl = 'https://example.com/conflict-test';
      
      // Device A saves article
      const deviceAVersion = await axios.post(
        `http://localhost:${TEST_PORT}/parse`,
        {
          url: articleUrl,
          title: 'Conflict Test - Device A',
          htmlContent: '<p>Content from Device A</p>',
          metadata: {
            deviceId: 'device-a',
            savedAt: '2024-01-01T10:00:00Z'
          }
        }
      );
      
      // Device B saves same article later with updates
      const deviceBVersion = await axios.post(
        `http://localhost:${TEST_PORT}/parse`,
        {
          url: articleUrl,
          title: 'Conflict Test - Device B (Updated)',
          htmlContent: '<p>Updated content from Device B</p>',
          metadata: {
            deviceId: 'device-b',
            savedAt: '2024-01-01T10:05:00Z'
          }
        }
      );
      
      // Conflict resolution service
      const resolvedVersion = resolveConflict(
        deviceAVersion.data,
        deviceBVersion.data
      );
      
      // Latest version should win
      expect(resolvedVersion.title).toBe('Conflict Test - Device B (Updated)');
      expect(resolvedVersion.metadata.savedAt).toBe('2024-01-01T10:05:00Z');
      
      // Notify all devices of resolution
      const ws = new WebSocket(`ws://localhost:${WS_PORT}`);
      await new Promise(resolve => ws.on('open', resolve));
      
      ws.send(JSON.stringify({
        type: 'conflict_resolved',
        article: resolvedVersion,
        conflictId: `${articleUrl}-conflict`
      }));
      
      ws.close();
    });
  });
  
  describe('Real-time Collaboration', () => {
    test('should sync highlights and notes across devices', async () => {
      const articleId = 'test-article-123';
      const wsClients = [];
      
      // Connect multiple devices
      for (let i = 0; i < 3; i++) {
        const ws = new WebSocket(`ws://localhost:${WS_PORT}`);
        await new Promise(resolve => ws.on('open', resolve));
        wsClients.push(ws);
      }
      
      // Device 1 adds a highlight
      const highlight = {
        type: 'highlight',
        articleId: articleId,
        text: 'Important passage',
        position: { start: 100, end: 150 },
        color: 'yellow',
        deviceId: 'device-1',
        timestamp: new Date().toISOString()
      };
      
      wsClients[0].send(JSON.stringify(highlight));
      
      // Other devices should receive the highlight
      const receivedHighlights = await Promise.all(
        wsClients.slice(1).map(ws => 
          new Promise(resolve => {
            ws.on('message', (data) => {
              const message = JSON.parse(data);
              if (message.type === 'highlight') {
                resolve(message);
              }
            });
          })
        )
      );
      
      expect(receivedHighlights).toHaveLength(2);
      expect(receivedHighlights[0].text).toBe('Important passage');
      
      // Device 2 adds a note
      const note = {
        type: 'note',
        articleId: articleId,
        text: 'This is a great point about testing',
        position: { paragraph: 2 },
        deviceId: 'device-2',
        timestamp: new Date().toISOString()
      };
      
      wsClients[1].send(JSON.stringify(note));
      
      // Clean up
      wsClients.forEach(ws => ws.close());
    });
  });
  
  describe('Performance and Scalability', () => {
    test('should handle high volume of simultaneous syncs', async () => {
      const startTime = Date.now();
      const numDevices = 20;
      const articlesPerDevice = 5;
      
      const syncTasks = [];
      
      for (let d = 0; d < numDevices; d++) {
        const deviceTask = (async () => {
          const articles = [];
          
          // Each device parses multiple articles
          for (let a = 0; a < articlesPerDevice; a++) {
            const response = await axios.post(
              `http://localhost:${TEST_PORT}/parse`,
              {
                url: `https://example.com/device-${d}/article-${a}`,
                title: `Device ${d} Article ${a}`,
                htmlContent: `<p>Content for device ${d} article ${a}</p>`,
                metadata: { deviceId: `device-${d}` }
              }
            );
            
            articles.push(response.data);
          }
          
          return articles;
        })();
        
        syncTasks.push(deviceTask);
      }
      
      const allResults = await Promise.all(syncTasks);
      const duration = Date.now() - startTime;
      
      // Verify all articles were processed
      const totalArticles = allResults.reduce((sum, articles) => sum + articles.length, 0);
      expect(totalArticles).toBe(numDevices * articlesPerDevice);
      
      // Should complete in reasonable time
      expect(duration).toBeLessThan(30000); // 30 seconds for 100 articles
      
      // Calculate throughput
      const articlesPerSecond = totalArticles / (duration / 1000);
      console.log(`Throughput: ${articlesPerSecond.toFixed(2)} articles/second`);
      expect(articlesPerSecond).toBeGreaterThan(3); // At least 3 articles/second
    });
  });
  
  describe('Data Consistency', () => {
    test('should maintain data consistency across all platforms', async () => {
      // Create test article
      const testArticle = {
        url: 'https://example.com/consistency-test',
        title: 'Data Consistency Test',
        content: 'Test content for consistency',
        tags: ['test', 'consistency'],
        readingProgress: 0.5,
        isFavorite: true
      };
      
      // Save on one platform
      const response = await axios.post(
        `http://localhost:${TEST_PORT}/parse`,
        {
          url: testArticle.url,
          title: testArticle.title,
          htmlContent: `<html><body><p>${testArticle.content}</p></body></html>`,
          metadata: {
            tags: testArticle.tags,
            readingProgress: testArticle.readingProgress,
            isFavorite: testArticle.isFavorite
          }
        }
      );
      
      const savedArticle = response.data;
      
      // Simulate retrieval from different platforms
      const platforms = ['ios', 'macos', 'browser'];
      
      for (const platform of platforms) {
        // Each platform should see the same data
        const checkData = {
          articleId: savedArticle.url,
          platform: platform
        };
        
        // In real scenario, this would query each platform's local database
        // Here we verify the data structure is consistent
        expect(savedArticle.title).toBe(testArticle.title);
        expect(savedArticle.keywords).toContain('test');
        expect(savedArticle.keywords).toContain('consistency');
      }
    });
  });
  
  // Helper functions
  
  async function getExtensionId(browser) {
    const targets = await browser.targets();
    const extensionTarget = targets.find(target => 
      target.type() === 'service_worker' && 
      target.url().includes('chrome-extension://')
    );
    
    if (extensionTarget) {
      const extensionUrl = extensionTarget.url();
      return extensionUrl.split('/')[2];
    }
    return null;
  }
  
  function resolveConflict(versionA, versionB) {
    // Simple last-write-wins strategy
    const timeA = new Date(versionA.metadata?.savedAt || 0);
    const timeB = new Date(versionB.metadata?.savedAt || 0);
    
    return timeB > timeA ? versionB : versionA;
  }
});

// Additional test utilities

class MockNativeApp {
  constructor(platform) {
    this.platform = platform;
    this.articles = new Map();
    this.wsClient = null;
  }
  
  async connect(wsUrl) {
    this.wsClient = new WebSocket(wsUrl);
    
    await new Promise((resolve, reject) => {
      this.wsClient.on('open', resolve);
      this.wsClient.on('error', reject);
    });
    
    this.wsClient.on('message', (data) => {
      const message = JSON.parse(data);
      this.handleMessage(message);
    });
  }
  
  handleMessage(message) {
    switch (message.type) {
      case 'article_synced':
        this.articles.set(message.article.url, message.article);
        break;
      
      case 'highlight':
      case 'note':
        // Update local article with annotation
        const article = this.articles.get(message.articleId);
        if (article) {
          article.annotations = article.annotations || [];
          article.annotations.push(message);
        }
        break;
    }
  }
  
  async syncArticle(article) {
    if (!this.wsClient) throw new Error('Not connected');
    
    this.wsClient.send(JSON.stringify({
      type: 'sync_article',
      platform: this.platform,
      article: article
    }));
  }
  
  disconnect() {
    if (this.wsClient) {
      this.wsClient.close();
      this.wsClient = null;
    }
  }
}